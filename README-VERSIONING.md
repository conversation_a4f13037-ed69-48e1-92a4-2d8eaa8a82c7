# NestJS API Versioning Implementation

This document demonstrates the implemented versioning system for the OnBord backend API, specifically showcasing the interview-templates module.

## 🎯 Versioning Configuration

**Main Configuration** (`src/main.ts`):
```typescript
app.enableVersioning({
  type: VersioningType.URI,
  prefix: 'v',
  defaultVersion: '1',
});
```

## 🚀 Routing Behavior

With the configuration above, here's how requests are routed:

### ✅ **Unversioned Requests → V1 Controller**
```bash
# These requests go to InterviewTemplatesV1Controller
GET /organisations/123/interview-templates
POST /organisations/123/interview-templates
```
**Due to `defaultVersion: '1'`**, unversioned requests are automatically routed to the V1 controller.

### ✅ **Explicit V1 Requests → V1 Controller**
```bash
# These requests also go to InterviewTemplatesV1Controller
GET /v1/organisations/123/interview-templates
POST /v1/organisations/123/interview-templates
```

### ✅ **V2 Requests → V2 Controller**
```bash
# These requests go to InterviewTemplatesV2Controller
GET /v2/organisations/123/interview-templates
POST /v2/organisations/123/interview-templates
```

### ❌ **Non-existent Versions → 404**
```bash
# These return 404 Not Found
GET /v99/organisations/123/interview-templates
```

## 🏗️ Implementation Details

### Controller Definitions

**V1 Controller** (`src/interview-templates/controllers/v1/interview-templates-v1.controller.ts`):
```typescript
@Controller({ 
  path: '/organisations/:organisationId/interview-templates', 
  version: '1' 
})
export class InterviewTemplatesV1Controller {
  // Handles both unversioned and v1 requests
}
```

**V2 Controller** (`src/interview-templates/controllers/v2/interview-templates-v2.controller.ts`):
```typescript
@Controller({ 
  path: '/organisations/:organisationId/interview-templates', 
  version: '2' 
})
export class InterviewTemplatesV2Controller {
  // Handles v2 requests only
}
```

### Module Registration

**No Legacy Controller Conflicts**:
```typescript
@Module({
  controllers: [
    // Legacy controller removed to avoid conflicts
    // Unversioned requests route to V1 via defaultVersion: '1'
    InterviewTemplatesV1Controller,
    InterviewTemplatesV2Controller,
  ],
  // ...
})
```

## 📋 Testing Verification

### Automated Tests Confirm:

1. **Controller Registration**: ✅ Both V1 and V2 controllers are properly registered
2. **Service Injection**: ✅ V1 controller has access to required services
3. **No Conflicts**: ✅ Legacy controller removed to prevent route conflicts

### Manual Testing Commands:

```bash
# Start the application
npm run start:dev

# Test unversioned endpoint (should route to V1)
curl -H "Authorization: Bearer <token>" \
  http://localhost:3000/organisations/123/interview-templates

# Test explicit V1 endpoint
curl -H "Authorization: Bearer <token>" \
  http://localhost:3000/v1/organisations/123/interview-templates

# Test V2 endpoint (will return "not implemented" error)
curl -H "Authorization: Bearer <token>" \
  http://localhost:3000/v2/organisations/123/interview-templates

# Test non-existent version (should return 404)
curl -H "Authorization: Bearer <token>" \
  http://localhost:3000/v99/organisations/123/interview-templates
```

## 🔍 Key Benefits

1. **Backward Compatibility**: Existing clients continue to work without changes
2. **Graceful Evolution**: New features can be added in V2 without breaking V1
3. **Clean Architecture**: Clear separation between versions
4. **Developer Experience**: Intuitive URL patterns (`/v1/`, `/v2/`)
5. **Default Routing**: Unversioned requests automatically use the latest stable version (V1)

## 📚 Swagger Documentation

Each version has its own Swagger tag:
- **V1**: `@ApiTags('Interview Templates V1')`
- **V2**: `@ApiTags('Interview Templates V2')`

This provides clear API documentation separation in the Swagger UI at `http://localhost:3000/api`.

## ✅ Conclusion

The versioning implementation successfully:
- ✅ Routes unversioned requests to V1 (via `defaultVersion: '1'`)
- ✅ Routes explicit version requests to appropriate controllers
- ✅ Maintains backward compatibility
- ✅ Provides foundation for future API evolution
- ✅ Follows NestJS best practices for API versioning

This architecture can be easily replicated across other modules in the application for consistent versioning behavior.