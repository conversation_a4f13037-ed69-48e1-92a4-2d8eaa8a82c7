import { RedtailCitizenshipEnum } from 'src/integrations/crm/redtail/types/enums';
import { GenericCrmAccount } from 'src/integrations/crm/types/accounts/crm-account.type';
import { GenericAddress } from 'src/integrations/crm/types/generic/address.type';
import { GenericEmail } from 'src/integrations/crm/types/generic/email.type';
import { GenericPhone } from 'src/integrations/crm/types/generic/phone.type';

export class CrmContact {
  id?: string;
  firstName: string;
  middleName: string;
  lastName: string;
  suffix: string;
  taxId: string;
  dob: string;
  citizenship: RedtailCitizenshipEnum | string;
  accounts?: GenericCrmAccount[];
  residence: string;
  additionalInfo: {
    employmentStatus: string;
    phones: GenericPhone[];
    addresses: GenericAddress[];
    emails: GenericEmail[];
    jobDescription: string;
    companyAssociation: string;
    industryAffiliation: string;
  };

  profileUrl?: string;
}
