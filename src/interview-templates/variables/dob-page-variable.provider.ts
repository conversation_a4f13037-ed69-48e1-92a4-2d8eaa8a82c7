import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

export class DobPageVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      dateOfBirth: {
        description: 'Full date of birth',
        type: VariableType.DATE,
        example: '1985-03-15',
      },
      birthYear: {
        description: 'Year of birth',
        type: VariableType.NUMBER,
        example: 1985,
      },
      birthMonth: {
        description: 'Month of birth',
        type: VariableType.NUMBER,
        example: 3,
      },
      birthDay: {
        description: 'Day of birth',
        type: VariableType.NUMBER,
        example: 15,
      },
      age: {
        description: 'Calculated age based on date of birth',
        type: VariableType.NUMBER,
        example: 38,
      },
    };
  }
}