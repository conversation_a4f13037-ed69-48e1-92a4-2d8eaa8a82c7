import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

export class PhonePageVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      phoneNumber: {
        description: 'Primary phone number',
        type: VariableType.STRING,
        example: '**********',
      },
      phoneType: {
        description: 'Type of phone (mobile, home, work)',
        type: VariableType.STRING,
        example: 'mobile',
      },
      formattedPhone: {
        description: 'Formatted phone number with proper spacing',
        type: VariableType.PHONE,
        example: '(*************',
      },
      areaCode: {
        description: 'Area code from phone number',
        type: VariableType.STRING,
        example: '555',
      },
    };
  }
}