import { Injectable } from '@nestjs/common';
import { Logger } from 'winston';
import {
  AddMemberRequestDto,
  AddMemberResponseDto,
} from 'src/integrations/crm/wealthbox/dto/household/addMember.dto';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { OperationType } from 'src/super-http/enums';

@Injectable()
export class HouseholdService {
  private readonly baseUrl: string;

  constructor(
    private httpService: SuperHttpService,
    private authHeader: object,
    private logger: Logger,
    private tenantId: string
  ) {
    this.baseUrl = process.env.WEALTHBOX_BASE_URL;
  }

  public async addMemberToHousehold(
    householdId: number,
    memberData: AddMemberRequestDto,
  ): Promise<AddMemberResponseDto> {
    const url = `${this.baseUrl}/v1/households/${householdId}/members`;
    return this.httpService.post<AddMemberResponseDto>(
      url,
      this.tenantId,
      memberData,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.CREATE, entityType: 'household', entityId: householdId.toString() },
      { headers: this.authHeader }
    );
  }

  public async deleteMemberFromHousehold(
    householdId: number,
    memberId: number,
  ): Promise<any> {
    const url = `${this.baseUrl}/v1/households/${householdId}/members/${memberId}`;
    return this.httpService.delete<void>(
      url,
      this.tenantId,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.DELETE, entityType: 'household', entityId: householdId.toString() },
      { headers: this.authHeader }
    );
  }

}
