import { Injectable } from '@nestjs/common';
import { Connection, Record as PractifiRecord } from 'jsforce';
import { PractifiHouseholdDto, PractifiIndividualDto } from '../dto/practifi.dto';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { generateRequestConfigs } from '../utils/generate-request-configs.util';
import { OperationType } from 'src/super-http/enums';
import { practifiQuery , formatName } from '../utils/practifi-query.util';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';
import { PRACTIFI_ACCOUNT_FIELDS } from '../utils/practifi-contact-fields.constants';
import { PractifiEntityTypes, AccountTypes } from '../types/enums';
import { commonConfigs } from '../types/types';

@Injectable()
export class PractifiAccountService {
  private connection: Connection;
  private baseUrl: string;
  private commonConfigs: commonConfigs;

  constructor(
    private readonly httpService: SuperHttpService,
    private tenantId: string
  ) {}

  // Standard Practifi Account fields
  private readonly STANDARD_FIELDS = PRACTIFI_ACCOUNT_FIELDS;

  setConnection(connection: Connection) {
    this.connection = connection;
    this.baseUrl = `${connection.instanceUrl}/services/data/v${connection.version}`;
    this.prepareRequestConfigs();
  }

  prepareRequestConfigs() {
    const requestConfigs = generateRequestConfigs(this.connection.accessToken);
    const { withInterceptors, ...configsWithoutInterceptors } = requestConfigs;
    this.commonConfigs = configsWithoutInterceptors;
  }

  async createAccount(accountData: PractifiHouseholdDto | PractifiIndividualDto): Promise<PractifiRecord> {
    const recordTypeId = await this.getRecordTypeId(accountData.Type);
    let soql;
    if (accountData.Type === AccountTypes.INDIVIDUAL) {
      soql = `SELECT Id FROM Account WHERE PersonEmail = '${formatName((accountData as PractifiIndividualDto).PersonEmail)}'`;
    }
    if (accountData.Type === AccountTypes.HOUSEHOLD_PROSPECT) {
      if ((accountData as PractifiHouseholdDto).practifi__Primary_Member__c) {
        soql = `SELECT Id, practifi__Primary_Member__c, practifi__Spouse__c FROM Account WHERE practifi__Primary_Member__c = '${(accountData as PractifiHouseholdDto).practifi__Primary_Member__c}' OR practifi__Spouse__c = '${(accountData as PractifiHouseholdDto).practifi__Primary_Member__c}'`;
      }
    }

    const result = await practifiQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, PractifiEntityTypes.ACCOUNT, this.tenantId);
    let recordIsPresent = false;
    let recordId;

    if (accountData.Type === AccountTypes.HOUSEHOLD_PROSPECT && result && result.records) {
      // if 2 contacts
      if ((accountData as PractifiHouseholdDto).practifi__Primary_Member__c && (accountData as PractifiHouseholdDto).practifi__Spouse__c) {
        for (let i = 0; i < result.records.length; i++) {
          const pMember = result.records[i].practifi__Primary_Member__c;
          const sMember = result.records[i].practifi__Spouse__c
          if (pMember && sMember) {
            if ((pMember === (accountData as PractifiHouseholdDto).practifi__Primary_Member__c && sMember === (accountData as PractifiHouseholdDto).practifi__Spouse__c) ||
            (pMember === (accountData as PractifiHouseholdDto).practifi__Spouse__c && sMember === (accountData as PractifiHouseholdDto).practifi__Primary_Member__c)
          ) {
            recordIsPresent = true;
            recordId = result.records[i].Id;
            }
          }
        }
      }
      // if 1 contact
      if ((accountData as PractifiHouseholdDto).practifi__Primary_Member__c && !(accountData as PractifiHouseholdDto).practifi__Spouse__c) {
        for (let i = 0; i < result.records.length; i++) {
          if (result.records[i].practifi__Primary_Member__c === (accountData as PractifiHouseholdDto).practifi__Primary_Member__c && !result.records[i].practifi__Spouse__c) {
            recordIsPresent = true;
            recordId = result.records[i].Id;
          }
        }
      }
    }

    if (recordIsPresent) {
      return {
        response: await this.httpService.patch(
          `${this.baseUrl}/sobjects/Account/${recordId}`,
          this.tenantId,
          {
            ...accountData,
          },
          {
            rateLimiter: 'practifi',
            integrationName: 'practifi',
            operation: OperationType.PATCH,
            entityType: PractifiEntityTypes.ACCOUNT,
          },
          this.commonConfigs
        ),
        id: recordId,
      };
    }

    return await this.httpService.post(
      `${this.baseUrl}/sobjects/Account/`,
      this.tenantId,
      {
        ...accountData,
        RecordTypeId: recordTypeId,
      },
      {
        rateLimiter: 'practifi',
        integrationName: 'practifi',
        operation: OperationType.CREATE,
        entityType: PractifiEntityTypes.ACCOUNT,
      },
      this.commonConfigs
    );
  }

  async getRecordTypeId(type: string): Promise<string> {
    const soql = `SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName = '${type}' LIMIT 1`;
    const result = await practifiQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, PractifiEntityTypes.RECORD_TYPE, this.tenantId);
    if (!result.records?.length) {
      throw new Error(`RecordType not found: Account / ${type}`);
    }
    return result.records[0].Id;
  }

  async getAccount(id: string): Promise<PractifiRecord> {
    return await this.httpService.get(
      `${this.baseUrl}/sobjects/Account/${id}`,
      this.tenantId,
      {
        rateLimiter: 'practifi',
        integrationName: 'practifi',
        operation: OperationType.GET,
        entityType: PractifiEntityTypes.ACCOUNT,
        entityId: id,
      },
      this.commonConfigs
    );
  }

  async updateAccount(id: string, accountData: Partial<PractifiHouseholdDto | PractifiIndividualDto>): Promise<PractifiRecord> {
    return await this.httpService.patch(
      `${this.baseUrl}/sobjects/Account/${id}`,
      this.tenantId,
      {
        ...accountData,
      },
      {
        rateLimiter: 'practifi',
        integrationName: 'practifi',
        operation: OperationType.PATCH,
        entityType: PractifiEntityTypes.ACCOUNT,
      },
      this.commonConfigs
    );
  }

  private getQueryFields(): string[] {
    return [
      ...this.STANDARD_FIELDS
    ];
  }

  async getAccounts(pagination?: PaginationQueryDto): Promise<{records: PractifiRecord[]}> {
    try {
      const queryFields = this.getQueryFields().join(', ');
        const sanitizedSearch = formatName(`%${pagination.search}%`);
        let soql = `SELECT ${queryFields} FROM Account WHERE Name LIKE '${sanitizedSearch}'`;

        if (pagination?.limit) {
          soql += `LIMIT ${pagination.limit}`;
        }

        if (pagination?.page) {
          const offset = (pagination.page - 1) * pagination.limit;
          soql += `OFFSET ${offset}`;
        }

        return await practifiQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, PractifiEntityTypes.ACCOUNT, this.tenantId);
    } catch (error) {
      throw new Error(`Error getting accounts from Practifi: ${error.message}`);
    }
  }
}
