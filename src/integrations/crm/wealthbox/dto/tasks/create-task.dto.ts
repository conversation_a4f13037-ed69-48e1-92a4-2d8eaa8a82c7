import { DateTime } from 'src/integrations/crm/wealthbox/types';
import { FieldTypeEnum } from 'src/integrations/crm/wealthbox/types/field.enum';
import { PriorityEnum } from 'src/integrations/crm/wealthbox/types/priority.enum';

export interface LinkedToDocument {
  id: number;
  type: string;
  name?: string;
}

export interface CustomFieldRequest {
  id: number;
  value: string;
}

export interface CreateTaskRequestDto {
  name: string;
  due_date: DateTime; // Assuming DateTime is already defined in your project
  complete?: boolean;
  category?: number;
  linked_to?: LinkedToDocument[];
  priority?: PriorityEnum;
  visible_to?: string;
  custom_fields?: CustomFieldRequest[];
  assigned_to?: number;
  description?: string;
}

export interface LinkedToDocumentResponse {
  id: number;
  type: string;
  name: string;
}

export interface CustomFieldResponse {
  id: number;
  name: string;
  value: string;
  document_type: string;
  field_type: FieldTypeEnum;
}

export interface CreateTaskResponseDto {
  id: number;
  creator: number;
  created_at: DateTime;
  updated_at: DateTime;
  name: string;
  due_date: DateTime;
  complete: boolean;
  category: number;
  linked_to: LinkedToDocumentResponse[];
  priority: PriorityEnum;
  visible_to: string;
  custom_fields: CustomFieldResponse[];
  frame: string;
  repeats: boolean;
  completer: number;
  description: string;
  description_html: string;
  assigned_to: number;
}
