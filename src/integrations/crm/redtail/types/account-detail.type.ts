export type RedtailAccountDetail = {
  id: number;
  account_id: number;
  application_date?: string;
  issue_date?: string;
  close_date?: string;
  delivery_date?: string;
  effective_date?: string;
  renewal_date?: string;
  cash_value?: string;
  cash_value_asof?: string;
  face_value?: string;
  surrender_value?: string;
  surrender_value_asof?: string;
  loan_amount?: string;
  loan_balance?: string;
  loan_interest?: string;
  is_rebalanced?: boolean;
  rebalance_frequency?: string;
  rebalance_last?: string;
  note?: string;
  rate_class?: string;
  employer_access?: string;
  employer_match?: string;
  employee_contribution?: string;
  employee_access?: string;
  ltc_duration?: string;
  ltc_limit?: string;
  ltc_inflation?: string;
  ltc_dailyamt?: string;
  ltc_prorate?: string;
  ltc_total?: string;
  tpa_name?: string;
  plan_name_401k?: string;
  contract_number_401k?: string;
  plan_year_401k?: string;
  trustee_401k?: string;
  eligibility_req_401k?: string;
  entry_dates_401k?: string;
  vesting_401k?: string;
  loan_hardship_401k?: string;
  deleted: boolean;
  created_at: string;
  updated_at: string;
};
