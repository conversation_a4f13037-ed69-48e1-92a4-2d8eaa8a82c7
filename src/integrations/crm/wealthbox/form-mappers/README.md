# Wealthbox CRM Mappers Explanation

The `wealthboxMappers` function configures a set of mappers for integrating form mapping with Wealthbox CRM, facilitating the conversion of interview page updates into a format compatible with Wealthbox. Each mapper is tasked with handling specific pieces of data, ensuring accurate and secure data processing.

## Overview of Mappers

Below is a detailed overview of each mapper and its designated function:

- **WealthboxNameMapper**: Maps name details to Wealthbox's format, handling first and last names, including prefixes and suffixes.

- **WealthboxAddressMapper**: Converts address data to fit Wealthbox's requirements, focusing on street addresses, cities, states, and postal codes.

- **WealthboxSSNMapper**: Securely formats Social Security Numbers for Wealthbox, ensuring compliance with data security standards.

- **WealthboxDOBMapper**: Transforms date of birth information to match Wealthbox's date format specifications.

- **WealthboxPhoneMapper**: Adjusts phone numbers to the format accepted by Wealthbox, including international to national formatting changes.

- **WealthboxConflictOfInterestMapper**: Maps conflict of interest data to a structure processable by Wealthbox, which may include boolean flags or detailed descriptions.

- **WealthboxUSCitizenMapper**: Converts U.S. citizenship status into Wealthbox's recognized format.

- **WealthboxEmploymentMapper**: Handles employment information mapping, including job titles, company names, and employment status.

- **WealthboxPrimaryBeneficiariesMapper** and **WealthboxContingentBeneficiariesMapper**: Map primary and contingent beneficiaries' information, ensuring it is structured according to Wealthbox's requirements.

- **WealthboxCompanyMapper**: Converts company-related information into a format suitable for Wealthbox, including names, industry types, and sizes.

- **WealthboxVIPMapper**: Identifies and maps VIP status, converting this into a recognized category within Wealthbox.

- **WealthboxJobDescriptionMapper**: Maps job descriptions, roles, and responsibilities to the required format for Wealthbox.

- **WealthboxCustomQuestionsMapper**: Handles the mapping of custom question responses, ensuring they are formatted and stored correctly within Wealthbox.

## Functionality and Purpose

Each mapper is provided with a reference to the `WealthboxCRM` instance, allowing access to CRM-specific functionalities necessary for the mapping process. The `wealthboxMappers` function returns a mapping of page types (via `PagesEnum`) to their corresponding data mappers, enabling dynamic data handling based on the updated interview page. This approach ensures that data transmitted to Wealthbox CRM is accurately formatted and meets the CRM's data structure requirements, showcasing a scalable solution for CRM data integration.
