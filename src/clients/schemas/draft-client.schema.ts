import { Factory } from 'nestjs-seeder';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import {
  BaseAccount,
  BaseClient,
  BaseClientContact,
} from './clients.base.schema';
import { NotificationTypeEnum } from '../types/notification-types.enum';

@Schema()
export class DraftAccount extends BaseAccount {
  // All field to be made optional are already optional in BaseAccount
}
export const DraftAccountSchema = SchemaFactory.createForClass(DraftAccount);

@Schema({ _id: false })
export class DraftClientContact extends BaseClientContact {
  // All field to be made optional are already optional in BaseClientContact
}
@Schema({ timestamps: true })
export class DraftClient extends BaseClient {
  // All field to be made optional are already optional in BaseClient
  @Prop({ type: Boolean })
  addAccountsSelected?: boolean;

  // Added properties with defaults
  @Factory([NotificationTypeEnum.EMAIL, NotificationTypeEnum.SMS])
  @Prop({
    type: [String],
    enum: Object.values(NotificationTypeEnum),
    default: [NotificationTypeEnum.EMAIL, NotificationTypeEnum.SMS]
  })
  notificationMethods?: NotificationTypeEnum[];

  @Factory([])
  @Prop({ type: [String], default: [] })
  customTemplates?: string[];

  @Factory(false)
  @Prop({ type: Boolean, default: false })
  doClientProfiling?: boolean;
}

export const DraftClientSchema = SchemaFactory.createForClass(DraftClient);
