import { RedtailUdfService } from 'src/integrations/crm/redtail/services/udf.service';
import { makeRequest } from 'src/utils/makeRequest';
import { HttpMethodsEnum } from 'src/shared/types/general/http-methods.enum';
import { CreateAccountRequestDto } from 'src/integrations/crm/redtail/dto/requests/accounts/create-account-request.dto';
import { AccountFeaturesEnum } from 'src/shared/types/accounts/account-features.enum';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { RedtailAccountService } from 'src/integrations/crm/redtail/services/account.service';
import {
  RedtailAccountTypeEnum,
  RedtailDBAccountType,
} from 'src/integrations/crm/redtail/types/enums';
import { SuperHttpService } from 'src/super-http/super-http.service';

jest.mock('src/utils/makeRequest');

describe('RedtailAccountService', () => {
  let service: RedtailAccountService;
  let udfService: RedtailUdfService;
  let mockHttpService: SuperHttpService;

  beforeEach(() => {
    mockHttpService = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      axiosRef: {
        get: jest.fn(),
        post: jest.fn(),
        put: jest.fn(),
        delete: jest.fn(),
      },
    } as unknown as SuperHttpService;
    
    udfService = new RedtailUdfService(mockHttpService, {}, 'test-tenant-id');
    service = new RedtailAccountService(mockHttpService, udfService, {
      Authorization: 'Bearer fake-token',
    }, 'test-tenant-id');
    jest.clearAllMocks();
  });

  describe('createAccount', () => {
    it('should create an account successfully and return the account ID', async () => {
      mockHttpService.post = jest.fn().mockResolvedValue({
        account: { id: 1 }
      });

      jest
        .spyOn(udfService, 'addAccountUdf')
        .mockResolvedValue({ accountUdf: { accountId: 1 } as any });

      const createAccountDto: CreateAccountRequestDto = {
        nameOnAccount: 'John Doe',
        accountType: RedtailDBAccountType.brokerage,
        accountLabel: 'Personal',
        ownership: 'Individual',
        advisoryRate: 1.5,
        features: [AccountFeaturesEnum.Acat],
      };

      const result = await service.createAccount('1', createAccountDto, 1, 2);

      expect(result).toBe(1);
      expect(mockHttpService.post).toHaveBeenCalled();
      expect(udfService.addAccountUdf).toHaveBeenCalledTimes(2);
    });
  });

  describe('updateAccount', () => {
    it('should update an account successfully', async () => {
      mockHttpService.put = jest.fn().mockResolvedValue({});
      jest.spyOn(udfService, 'upsertAccountUdf').mockImplementation(jest.fn());

      const updateAccountDto = {
        accountType: AccountTypeEnum.Ira,
        accountLabel: 'Personal',
        masterAccountNumber: '*********',
        ownership: 'Individual',
        advisoryRate: 1.5,
        features: [AccountFeaturesEnum.Acat],
        advisoryRateAccountUdfFieldId: 1,
        accountFeaturesUdfId: 2
      };

      const result = await service.updateAccount(
        '1',
        1,
        updateAccountDto as any,
      );

      expect(result).toBeTruthy();
      expect(mockHttpService.put).toHaveBeenCalled();
      expect(udfService.upsertAccountUdf).toHaveBeenCalledTimes(2);
    });
  });

  describe('syncContactAccounts', () => {
    it('should sync contact accounts successfully', async () => {
      jest.spyOn(service, 'getAccounts').mockResolvedValue([]);
      jest.spyOn(service, 'createAccount').mockResolvedValue(1);
      jest.spyOn(service, 'updateAccount').mockResolvedValue(true);

      const contactDto = {
        firstName: 'John',
        lastName: 'Doe',
        accounts: [
          {
            type: AccountTypeEnum.Ira,
            label: 'Personal',
            masterAccountNumber: '*********',
            ownership: 'Individual',
            advisoryRate: 1.5,
            features: [AccountFeaturesEnum.Acat],
          },
        ],
      };

      await service.syncContactAccounts(contactDto as any, '1', 1, 2);

      expect(service.createAccount).toHaveBeenCalled();
    });
  });

  describe('getAccountByAccountType', () => {
    it('should get account by account type successfully', async () => {
      mockHttpService.get = jest.fn().mockResolvedValue({
        accounts: [{ accountTypeId: 3, id: 1 }]
      });

      const result = await service.getAccounts(
        '1',
        RedtailAccountTypeEnum.BROKERAGE_ACCOUNT,
      );

      expect(result).toEqual([
        {
          accountTypeId: RedtailAccountTypeEnum.BROKERAGE_ACCOUNT,
          id: 1,
        },
      ]);
      expect(mockHttpService.get).toHaveBeenCalledTimes(1);
    });
  });
});
