import mongoose, { mongo } from 'mongoose';

export const defaultInterviewTemplateSeed = {
  templateName: 'default',
  default: true,
  pages: [
    {
      name: 'name',
      order: 10,
      elements: [
        {
          elementId: new mongoose.Types.ObjectId().toString(),
          elementType: 'text',
          elementLabel: 'element label',
          required: false,
        },
      ],
    },
    {
      name: 'address',
      order: 5,
      elements: [
        {
          elementId: new mongoose.Types.ObjectId().toString(),
          elementType: 'text',
          elementLabel: 'element label',
          required: false,
        },
      ],
    },
    {
      name: 'ssn',
      order: 2,
      elements: [
        {
          elementId: new mongoose.Types.ObjectId().toString(),
          elementType: 'text',
          elementLabel: 'element label',
          required: false,
        },
      ],
    },
    {
      name: 'dob',
      order: 6,
      elements: [
        {
          elementId: new mongoose.Types.ObjectId().toString(),
          elementType: 'text',
          elementLabel: 'element label',
          required: false,
        },
      ],
    },
    {
      name: 'phone',
      order: 2,
      elements: [
        {
          elementId: new mongoose.Types.ObjectId().toString(),
          elementType: 'text',
          elementLabel: 'element label',
          required: false,
        },
      ],
    },
    {
      name: 'employment',
      order: 1,
      elements: [
        {
          elementId: new mongoose.Types.ObjectId().toString(),
          elementType: 'text',
          elementLabel: 'element label',
          required: false,
        },
      ],
    },
    {
      name: 'vip',
      order: 9,
      elements: [
        {
          elementId: new mongoose.Types.ObjectId().toString(),
          elementType: 'text',
          elementLabel: 'element label',
          required: false,
        },
      ],
    },
    {
      name: 'conflict_of_interest',
      order: 4,
      elements: [
        {
          elementId: new mongoose.Types.ObjectId().toString(),
          elementType: 'text',
          elementLabel: 'element label',
          required: false,
        },
      ],
    },
    {
      name: 'primary_beneficiaries',
      order: 6,
      elements: [
        {
          elementId: new mongoose.Types.ObjectId().toString(),
          elementType: 'text',
          elementLabel: 'element label',
          required: false,
        },
      ],
    },
    {
      name: 'contingent_beneficiaries',
      order: 7,
      elements: [
        {
          elementId: new mongoose.Types.ObjectId().toString(),
          elementType: 'text',
          elementLabel: 'element label',
          required: false,
        },
      ],
    },
    {
      name: 'us_citizen',
      order: 1,
      elements: [
        {
          elementId: new mongoose.Types.ObjectId().toString(),
          elementType: 'text',
          elementLabel: 'element label',
          required: false,
        },
      ],
    },
    {
      name: 'company',
      elements: [
        {
          elementId: new mongoose.Types.ObjectId().toString(),
          elementType: 'text',
          elementLabel: 'element label',
          required: false,
        },
      ],
    },
    {
      name: 'job',
      elements: [
        {
          elementId: new mongoose.Types.ObjectId().toString(),
          elementType: 'text',
          elementLabel: 'element label',
          required: false,
        },
      ],
    },
  ],
};
