import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdvisorsController } from './advisors.controller';
import { Advisor, AdvisorSchema } from './schemas/advisors.schema';
import { OrganisationsModule } from 'src/organisations/organisations.module';
import { CognitoService } from 'src/shared/services/cognito.service';
import { CRMModule } from 'src/integrations/crm/crm.module';
import { RbacModule } from 'src/rbac/rbac.module';
import { ClsModule } from 'nestjs-cls';
import { AdvisorsCrudService } from './services/advisors.crud.service';
import { AdvisorsDocusignService } from './services/advisors.docusign.service';
import { AdvisorsCrmService } from './services/advisors.crm.service';
import { HttpModule } from '@nestjs/axios';
import { DocusignModule } from 'src/integrations/docusign/docusign.module';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { AssetsModule } from 'src/assets/assets.module';
import { ClientsModule } from 'src/clients/clients.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Advisor.name, schema: AdvisorSchema }]),
    forwardRef(() => CRMModule),
    forwardRef(() => OrganisationsModule),
    RbacModule,
    ClsModule,
    AssetsModule,
    HttpModule,
    forwardRef(() => DocusignModule),
    ClientsModule,
  ],
  controllers: [AdvisorsController],
  providers: [
    CognitoService,
    AdvisorsCrudService,
    AdvisorsDocusignService,
    AdvisorsCrmService,
    TransactionManager,
  ],
  exports: [AdvisorsCrmService, AdvisorsDocusignService, AdvisorsCrudService],
})
export class AdvisorsModule {}
