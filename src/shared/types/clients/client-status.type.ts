export enum ClientStatusEnum {
  Draft = 'Draft' /** Client creation is not yet finished  */,
  PendingReview = 'PendingReview' /** DocuSign envelope is pending Advisor review */,
  WaitingSignature = 'WaitingSignature' /** DocuSign envelope documents are awaiting Client signature */,
  Ready = 'Ready' /** Client is added to OnBord and ready to be sent an interview link */,
  Sent = 'Sent' /** Interview link sent to Client */,
  Signed = 'Signed' /** <PERSON>lient has finished signing all the documents */,
  Complete = 'Complete' /** <PERSON><PERSON> has finished the OnBord interview process without DocuSign enabled */,
  Archived = 'Archived' /** Client has been archived */,
  Creating = 'Creating' /** Client is being created */,
  Failed = 'Failed' /** Client creation failed */,
  Processing = 'Processing' /** Client send now / resend text functionality */
}
