import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Seeder } from 'nestjs-seeder';
import { Role } from 'src/rbac/schemas/role.schema';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { rolesPermissionsMapper } from '../permissions/roles-permissions-mapper';

@Injectable()
export class RolesSeeder implements Seeder {
  constructor(
    @InjectModel(Role.name) private readonly roleModel: Model<Role>,
  ) {}

  async seed(): Promise<any> {
    for (const role of Object.keys(RolesEnum)) {
      await this.roleModel.create({
        name: RolesEnum[role],
        permissions: rolesPermissionsMapper[RolesEnum[role]],
      });
    }
  }

  async drop(): Promise<any> {
    await this.roleModel.deleteMany({});
  }
}
