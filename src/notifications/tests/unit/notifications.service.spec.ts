import { Test, TestingModule } from '@nestjs/testing';
import { ClientsV1Service } from 'src/clients/clients.service';
import { InterviewsService } from 'src/interviews/interviews.service';
import { MutexService } from 'src/mutex/mutex.service';
import { MailService } from 'src/notifications/mail/mail.service';
import { NotificationsService } from 'src/notifications/notifications.service';
import { SmsService } from 'src/notifications/sms/sms.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';

describe('NotificationsService', () => {
  let service: NotificationsService;
  let mailService: MailService;
  let smsService: SmsService;
  let mutexService: MutexService;
  let clientsService: ClientsV1Service;
  let organisationsService: OrganisationsService;
  let interviewsService: InterviewsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationsService,
        {
          provide: MailService,
          useValue: {
            sendExample: jest.fn(),
          },
        },
        {
          provide: SmsService,
          useValue: {
            sendText: jest.fn(),
          },
        },
        {
          provide: MutexService,
          useValue: {
            lock: jest.fn(),
            unlock: jest.fn(),
          },
        },
        {
          provide: ClientsV1Service,
          useValue: {
            findAll: jest.fn(),
          },
        },
        {
          provide: OrganisationsService,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: InterviewsService,
          useValue: {
            find: jest.fn(),
          },
        },
        {
          provide: WINSTON_MODULE_PROVIDER,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
            info: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<NotificationsService>(NotificationsService);
    mailService = module.get<MailService>(MailService);
    smsService = module.get<SmsService>(SmsService);
    mutexService = module.get<MutexService>(MutexService);
    clientsService = module.get<ClientsV1Service>(ClientsV1Service);
    organisationsService =
      module.get<OrganisationsService>(OrganisationsService);
    interviewsService = module.get<InterviewsService>(InterviewsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
