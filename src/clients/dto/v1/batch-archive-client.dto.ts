import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty } from 'class-validator';

export class ClientBatchArchiveRequestDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsArray()
  clientIds: string[];
}

export class ClientBatchArchiveResponseDto {
  @ApiProperty({ required: true })
  @IsArray()
  clientsModified: number;

  @ApiProperty({ required: true })
  batchSize: number;
}
