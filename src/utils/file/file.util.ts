import { mkdir, unlink } from 'fs';
import { Readable } from 'stream';
import { promisify } from 'util';

export const streamToBuffer = async (stream: Readable): Promise<Buffer> => {
  const chunks: Buffer[] = [];

  for await (const chunk of stream) {
    chunks.push(chunk instanceof Buffer ? chunk : Buffer.from(chunk));
  }

  return Buffer.concat(chunks);
};
export const isStreamReadable = (stream: Readable): boolean => {
  // Check if the stream is an instance of Readable stream
  if (!(stream instanceof Readable)) {
    return false;
  }

  // Check if the stream is already consumed
  const isConsumed =
    stream.readableEnded || stream.readableFlowing || !stream.readable;

  return !isConsumed;
};

export function parseFileAsJson<T>(fileBuffer: Buffer): T {
  return JSON.parse(fileBuffer.toString()) as T;
}

export const mkdirAsync = promisify(mkdir);
export const unlinkAsync = promisify(unlink);
