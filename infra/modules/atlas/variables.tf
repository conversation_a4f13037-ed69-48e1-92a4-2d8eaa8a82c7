variable "cluster_name" {
  description = "The name of the MongoDB Atlas cluster"
  type        = string
}

variable "instance_size" {
  description = "The instance size of the MongoDB Atlas cluster: M0, M2 or M5"
  type        = string
}

variable "elastic_beanstalk_security_group_id" {
  description = "The security group ID for the Elastic Beanstalk environment"
  type        = string
}

variable "mongodb_atlas_org_id_ssm" {
  description = "The SSM parameter name for the MongoDB Atlas organization ID"
  type        = string
}

variable "mongodb_atlas_api_pub_key_ssm" {
  description = "The SSM parameter name for the MongoDB Atlas API public key"
  type        = string
}

variable "mongodb_atlas_api_pri_key_ssm" {
  description = "The SSM parameter name for the MongoDB Atlas API private key"
  type        = string
}

variable "mongodb_atlas_database_user_username_ssm" {
  description = "The SSM parameter name for the MongoDB Atlas database user username"
  type        = string
}

variable "mongodb_atlas_database_user_password_ssm" {
  description = "The SSM parameter name for the MongoDB Atlas database user password"
  type        = string
}

variable "environment" {
  description = "The environment name"
  type        = string
}

variable "ebs_env_name" {
  description = "The name of the Elastic Beanstalk application."
}

variable "nat_gateway_ip" {
  description = "Public IP addresses of the Elastic Beanstalk NAT Gateway"
  type        = string
}