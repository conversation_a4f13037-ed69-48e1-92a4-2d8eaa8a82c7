import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsArray } from 'class-validator';

export type CommunicatedClientResult = {
  clientId: string;
  communicated: boolean;
};

export class ClientBatchSendTextRequestDto {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsArray()
  clientIds: string[];
}

export class ClientBatchSendTextResponseDto {
  @ApiProperty({ required: true })
  @IsArray()
  clients: CommunicatedClientResult[];

  @ApiProperty({ required: true })
  batchSize: number;

  @ApiProperty({ required: true })
  totalClientsCommunicated: number;
}
