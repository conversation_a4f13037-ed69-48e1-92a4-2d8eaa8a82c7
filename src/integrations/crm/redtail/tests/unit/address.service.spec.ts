import { HttpService } from '@nestjs/axios';
import { makeRequest } from 'src/utils/makeRequest';

import { CreateContactAddressesResponseDto } from 'src/integrations/crm/redtail/dto/responses/address/create-contact-address-response.dto';
import { RedtailAddressService } from 'src/integrations/crm/redtail/services/address.service';
import { RedtailAddress } from 'src/integrations/crm/redtail/types/address.type';
import { RedtailAddressTypeEnum } from 'src/integrations/crm/redtail/types/enums';
import { AddressUpdateRequest } from 'src/integrations/crm/redtail/dto/requests/update-address.dto';
import { SuperHttpService } from 'src/super-http/super-http.service';

jest.mock('src/utils/makeRequest');

describe('RedtailAddressService', () => {
  let service: RedtailAddressService;
  let httpService: HttpService;
  let authHeader: object;
  const mockHttpService = {
    axiosRef: {
      get: jest.fn(),
      post: jest.fn(),
      delete: jest.fn(),
    },
    get: jest.fn(),
    post: jest.fn(),
    delete: jest.fn(),
    put: jest.fn(),
  } as unknown as SuperHttpService;

  beforeEach(async () => {
    authHeader = { Authorization: 'Bearer fake-token' };
    service = new RedtailAddressService(mockHttpService, authHeader, 'test-tenant-id');
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('updateContactAddresses', () => {
    it('should update contact addresses successfully', async () => {
      // Mock existing addresses
      const existingAddresses: RedtailAddress[] = [];
      jest
        .spyOn(service, 'getContactAddresses')
        .mockResolvedValue({ addresses: existingAddresses });

      jest.spyOn(service, 'deleteContactAddress').mockResolvedValue();
      jest
        .spyOn(service, 'addContactAddress')
        .mockResolvedValue({} as CreateContactAddressesResponseDto);

      const crmClientId = '1';
      const addressUpdates: AddressUpdateRequest[] = [
        {
          addressType: RedtailAddressTypeEnum.HOME,
          address: {
            addressableType: 'Contact',
            addressTypeDescription: 'Home',
            city: 'Anytown',
            country: 'USA',
            createdAt: '2021-01-01T00:00:00.000Z',
          },
        },
        {
          addressType: RedtailAddressTypeEnum.MAILING,
          address: {
            streetAddress: '123 Main St',
          },
        },
      ];

      const result = await service.updateContactAddresses(
        crmClientId,
        addressUpdates,
      );

      expect(result).toBeTruthy();
    });
  });
});
