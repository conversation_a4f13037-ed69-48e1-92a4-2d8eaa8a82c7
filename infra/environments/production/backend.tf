terraform {

  backend "s3" {
    bucket         = "onbord-backend-tf-state-production"
    key            = "terraform.tfstate"
    region         = "us-east-1"
    dynamodb_table = "onbord-backend-tf-state-lock-production"
    encrypt        = true
  }
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.0"
    }

    datadog = {
      source = "DataDog/datadog"
    }
  }
}