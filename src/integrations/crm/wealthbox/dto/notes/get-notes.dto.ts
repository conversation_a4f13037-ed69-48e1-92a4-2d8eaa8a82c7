import { Note } from 'src/integrations/crm/wealthbox/types/note.type';
import { WealthboxOrderEnum } from 'src/integrations/crm/wealthbox/types/order.type';
import { WealthboxPaginationMeta } from 'src/integrations/crm/wealthbox/types/pagination.type';

export interface GetNotesRequestDto {
  resource_id?: number;
  resource_type?: string;
  order?: WealthboxOrderEnum;
}

export interface GetNotesResponseDto {
  status_updates: Note[];
  meta: WealthboxPaginationMeta;
}
