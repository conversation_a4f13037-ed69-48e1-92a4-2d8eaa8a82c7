import { Prop, Schema } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { Address, AddressSchema } from './address.schema';
import { Factory } from 'nestjs-seeder';

@Schema({ _id: false })
export class OrganisationInfo {
  @Factory(
    (_, { organisation }) => new mongoose.Types.ObjectId(organisation.id),
  )
  @Prop({ required: true })
  organisationId: mongoose.Types.ObjectId;

  @Factory((_, { organisation }) => organisation.name)
  @Prop({ type: String })
  name: string;

  @Factory((_, { advisor }) => advisor.phone)
  @Prop({ type: String })
  phone: string;

  @Factory((_, { organisation }) => organisation.address)
  @Prop({ type: AddressSchema })
  address: Address;

  @Factory((_, { advisor }) => advisor.email)
  @Prop({ type: String })
  email: string;

  @Factory(() => true)
  @Prop({ type: Boolean })
  setupComplete: boolean;
}
