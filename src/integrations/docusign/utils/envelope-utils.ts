import {
  DocusignJsonSimWebhookDto,
  LegacyDocuSignWebhookDto,
} from 'src/integrations/docusign/dto/webhook.dto';

export type DocusignEnvelopeData = {
  status: string;
  envelopeId: string;
};

export function getDataFromEnvelope(
  envelopeData: LegacyDocuSignWebhookDto | DocusignJsonSimWebhookDto,
) {
  if ('status' in envelopeData) {
    return {
      envelopeId: envelopeData.envelopeId,
      status: envelopeData.status,
    };
  }

  if ('event' in envelopeData) {
    return {
      envelopeId: envelopeData?.data?.envelopeId,
      status: envelopeData?.event,
    };
  }
  throw new Error('Invalid envelope data');
}
