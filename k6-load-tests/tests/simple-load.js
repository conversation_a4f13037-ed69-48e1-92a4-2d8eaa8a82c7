import { check, sleep } from 'k6';
import http from 'k6/http';
import { Rate } from 'k6/metrics';
import { getAuthToken } from '../src/utils/auth.js';
import { generateClientData } from '../src/utils/clients.js';
import { VUs } from '../src/config/constants/k6.js';
import { BASE_URL } from '../src/config/constants/shared.js';

export const options = {
  vus: 2,
  duration: '30s',
  thresholds: {
    http_req_duration: ['p(95)<5000'], // 95% of requests should be below 5s
    failed_requests: ['rate<0.1'], // Less than 10% can fail
  },
};

const ERROR_RATE = new Rate('failed_requests');

export function setup() {
  const auth = getAuthToken();
  if (!auth) {
    throw new Error('Authentication failed');
  }
  return auth;
}

export default function(data) {
  const headers = {
    'Cookie': `access_token=${data.token}`,
    'Content-Type': 'application/json',
  };

  // Generate test client data
  const clientData = generateClientData({
    advisorId: data.advisorId
  });

  // Create a new client
  const createClientResponse = http.post(
    `${BASE_URL}/organisations/${data.organisationId}/clients`,
    JSON.stringify(clientData),
    { headers }
  );

  check(createClientResponse, {
    'client created successfully': (r) => r.status === 201,
  }) || ERROR_RATE.add(1);

  sleep(1);
} 