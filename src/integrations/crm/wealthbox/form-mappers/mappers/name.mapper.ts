import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { NamePageUpdateDto } from 'src/shared/types/pages/dto/name-update.dto';
import { isEmpty } from 'lodash';

export class WealthboxNameMapper implements FormUpdateMapper {
  constructor(private WealthboxCrm: WealthboxCRM) {}

  /**
   * Maps the page data to the corresponding fields in Wealthbox CRM for a given client.
   * @param clientCrmId The ID of the client in Wealthbox CRM.
   * @param organisation The organisation associated with the client.
   * @param pageData The page data to be mapped.
   * @returns A Promise that resolves when the mapping is complete.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: NamePageUpdateDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const { firstName, lastName } = pageData;

    await this.WealthboxCrm.contactService.updateContact(contact.crmClientId, {
      first_name: firstName,
      last_name: lastName,
      suffix: !isEmpty(pageData.suffix) ? pageData.suffix : undefined,
      middle_name: !isEmpty(pageData.middleName)
        ? pageData.middleName
        : undefined,
    });
  }
}
