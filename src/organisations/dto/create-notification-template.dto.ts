import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsEnum, IsOptional, IsBoolean } from 'class-validator';
import { EmailTemplateNameEnum } from 'src/templates/mail/types';
import { TextMessageTemplateNameEnum } from 'src/templates/sms/types';
import { TransitionTypeEnum } from 'src/transitions/types/transition-type.enum';

export class CreateNotificationTemplateDto {
  @ApiProperty()
  templateName: EmailTemplateNameEnum | TextMessageTemplateNameEnum;

  @IsEnum(['email', 'sms'])
  @ApiProperty()
  templateType: 'email' | 'sms';

  @IsString()
  @ApiProperty()
  template: string;

  @IsString()
  @IsOptional()
  @ApiProperty()
  subject?: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  default?: boolean;
}
