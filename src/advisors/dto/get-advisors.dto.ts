import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { UserStatusesEnum } from 'src/shared/types/rbac/statuses.enum';

export class GetAdvisorsQueryDto extends PaginationQueryDto {
  @IsOptional()
  @Type(() => String)
  @IsString()
  @IsEnum(UserStatusesEnum)
  @ApiProperty({ default: UserStatusesEnum.ARCHIVED })
  status?: UserStatusesEnum;

  @IsOptional()
  @Type(() => String)
  @IsString()
  @ApiProperty()
  name?: string;

  @IsOptional()
  @Type(() => String)
  @IsString()
  @IsEnum(RolesEnum)
  @ApiProperty({ default: RolesEnum.CompanyAdmin })
  role?: RolesEnum;
}
