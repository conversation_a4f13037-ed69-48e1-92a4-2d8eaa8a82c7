import { Logger } from 'winston';
import { CreateTaskRequestDto } from 'src/integrations/crm/wealthbox/dto/tasks/create-task.dto';
import { WealthboxPaginationDto } from 'src/integrations/crm/wealthbox/types/pagination.type';
import { Task } from 'src/integrations/crm/wealthbox/types/task.type';
import { TaskUpdateRequestDto } from 'src/integrations/crm/wealthbox/dto/tasks/update-task.dto';
import { GetTasksRequestDto } from 'src/integrations/crm/wealthbox/dto/tasks/get-task.dto';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { snakeify } from 'src/utils/snakeify';
import { snakeifyUrlParams } from 'src/utils/urlParams';
import { OperationType } from 'src/super-http/enums';

export class WealthboxTaskService {
  private readonly baseUrl: string;

  constructor(
    private httpService: SuperHttpService,
    private authHeader: object,
    private logger: Logger,
    private tenantId: string
  ) {
    this.baseUrl = process.env.WEALTHBOX_BASE_URL;
  }

  public async createTask(taskData: CreateTaskRequestDto): Promise<Task> {
    const url = `${this.baseUrl}/tasks`;
    return this.httpService.post<Task>(
      url,
      this.tenantId,
      taskData,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.CREATE, entityType: 'task' },
      { headers: this.authHeader }
    );
  }

  public async updateTask(
    taskId: number,
    taskData: TaskUpdateRequestDto,
  ): Promise<Task> {
    const url = `${this.baseUrl}/tasks/${taskId}`;
    return this.httpService.put<Task>(
      url,
      this.tenantId,
      snakeify(taskData),
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.UPDATE, entityType: 'task', entityId: taskId.toString() },
      { headers: this.authHeader }
    );
  }

  public async listTasks(
    queryParams?: GetTasksRequestDto,
    pagination: WealthboxPaginationDto = { page: 1, per_page: 25 },
  ): Promise<Task[]> {
    const query = snakeifyUrlParams({
      ...queryParams,
      ...pagination,
    }).toString();
    const url = `${this.baseUrl}/tasks${query ? `?${query}` : ''}`;

    return this.httpService.get<Task[]>(
      url,
      this.tenantId,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.LIST, entityType: 'task', cacheKeysObject: { queryParams, pagination } },
      { headers: this.authHeader }
    );
  }

  public async getTask(taskId: number): Promise<Task> {
    const url = `${this.baseUrl}/tasks/${taskId}`;
    return this.httpService.get<Task>(
      url,
      this.tenantId,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.GET, entityType: 'task', entityId: taskId.toString() },
      { headers: this.authHeader }
    );
  }

  public async deleteTask(taskId: number): Promise<void> {
    const url = `${this.baseUrl}/tasks/${taskId}`;
    return this.httpService.delete<void>(
      url,
      this.tenantId,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.DELETE, entityType: 'task', entityId: taskId.toString() },
      { headers: this.authHeader }
    );
  }

}
