import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsOptional, IsString } from 'class-validator';

export class AddClientDocumentsDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  advisorId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  organisationId?: string;

  @ApiProperty()
  @IsArray()
  files: Express.Multer.File[];

  @ApiProperty()
  @IsString()
  envelopeId: string | undefined;
}
