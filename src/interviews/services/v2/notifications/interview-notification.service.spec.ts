import { Test, TestingModule } from '@nestjs/testing';
import { InterviewNotificationService } from './interview-notification.service';
import { MailService } from 'src/notifications/mail/mail.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { ClientsV1Service } from 'src/clients/clients.service';
import { getModelToken } from '@nestjs/mongoose';
import { Interview } from '../../../schemas/v1/interview.schema';
import { Model, ClientSession } from 'mongoose';
import { HttpException, HttpStatus } from '@nestjs/common';
import { EmailTemplateNameEnum } from 'src/templates/mail/types';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { EnrichedInterview } from '../../../dto/v1/enriched-interview-dto';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';

jest.mock('src/utils/getFrontendDomain', () => ({
  getFrontendDomain: jest.fn(() => 'test.onbord.com'),
}));

describe('InterviewNotificationService', () => {
  let service: InterviewNotificationService;
  let mailService: jest.Mocked<MailService>;
  let organisationsService: jest.Mocked<OrganisationsService>;
  let clientsService: jest.Mocked<ClientsV1Service>;
  let interviewModel: jest.Mocked<Model<Interview>>;

  const mockOrganisation = {
    _id: 'org123',
    name: 'Test Organization',
  };

  const mockPrimaryContact = {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    accounts: [
      { label: 'Account 1', type: 'regular' },
      { label: 'Account 2', type: 'offline' },
      { label: 'Account 3', type: 'special' },
    ],
  };

  const mockSecondaryContact = {
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    accounts: [
      { label: 'Account A', type: 'regular' },
      { label: 'Account B', type: 'offline_account' },
    ],
  };

  const mockClient = {
    _id: 'client123',
    organisationId: 'org123',
    primaryContact: mockPrimaryContact,
    secondaryContact: mockSecondaryContact,
    primaryAdvisor: { id: 'advisor123' },
    primaryCSA: {
      firstName: 'CSA',
      lastName: 'Agent',
      email: '<EMAIL>',
    },
    status: ClientStatusEnum.Ready,
  };

  const mockInterview = {
    _id: 'interview123',
    client: 'client123',
    isPrimary: true,
    isComplete: false,
    populate: jest.fn().mockReturnThis(),
    toObject: jest.fn(),
  };

  const mockSession = {
    startTransaction: jest.fn(),
    commitTransaction: jest.fn(),
    abortTransaction: jest.fn(),
  } as unknown as ClientSession;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InterviewNotificationService,
        {
          provide: MailService,
          useValue: {
            sendEmail: jest.fn(),
          },
        },
        {
          provide: OrganisationsService,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: ClientsV1Service,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getModelToken(Interview.name),
          useValue: {
            findById: jest.fn(),
            findByIdAndUpdate: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<InterviewNotificationService>(InterviewNotificationService);
    mailService = module.get(MailService);
    organisationsService = module.get(OrganisationsService);
    clientsService = module.get(ClientsV1Service);
    interviewModel = module.get(getModelToken(Interview.name));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('sendDesktopInterviewEmail', () => {
    it('should send desktop interview email for primary contact', async () => {
      const expectedContext = {
        organisationName: mockOrganisation.name,
        interviewUrl: 'https://test.onbord.com/interview/interview123',
      };

      interviewModel.findById.mockReturnValue(mockInterview as any);
      clientsService.findOne.mockResolvedValue(mockClient as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      mailService.sendEmail.mockResolvedValue(undefined);

      await service.sendDesktopInterviewEmail('interview123');

      expect(interviewModel.findById).toHaveBeenCalledWith('interview123');
      expect(clientsService.findOne).toHaveBeenCalledWith({ _id: 'client123' });
      expect(organisationsService.findOne).toHaveBeenCalledWith('org123');
      expect(mailService.sendEmail).toHaveBeenCalledWith(
        {
          to: mockPrimaryContact.email,
          templateName: EmailTemplateNameEnum.DesktopInterview,
          context: expectedContext,
          organisation: mockOrganisation,
        },
        mockPrimaryContact,
        'advisor123',
      );
    });

    it('should send desktop interview email for secondary contact', async () => {
      const secondaryInterview = { ...mockInterview, isPrimary: false };
      interviewModel.findById.mockReturnValue(secondaryInterview as any);
      clientsService.findOne.mockResolvedValue(mockClient as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      mailService.sendEmail.mockResolvedValue(undefined);

      await service.sendDesktopInterviewEmail('interview123');

      expect(mailService.sendEmail).toHaveBeenCalledWith(
        {
          to: mockSecondaryContact.email,
          templateName: EmailTemplateNameEnum.DesktopInterview,
          context: expect.any(Object),
          organisation: mockOrganisation,
        },
        mockSecondaryContact,
        'advisor123',
      );
    });

    it('should throw HttpException when interview not found', async () => {
      interviewModel.findById.mockReturnValue({ populate: jest.fn().mockReturnValue(null) } as any);

      await expect(service.sendDesktopInterviewEmail('interview123')).rejects.toThrow(
        new HttpException('Interview not found', HttpStatus.NOT_FOUND),
      );
    });

    it('should use correct frontend domain in interview URL', async () => {
      interviewModel.findById.mockReturnValue(mockInterview as any);
      clientsService.findOne.mockResolvedValue(mockClient as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);

      await service.sendDesktopInterviewEmail('interview123');

      const callArgs = mailService.sendEmail.mock.calls[0][0];
      expect((callArgs.context as any).interviewUrl).toBe('https://test.onbord.com/interview/interview123');
    });

    it('should handle missing email address gracefully', async () => {
      const clientNoEmail = {
        ...mockClient,
        primaryContact: { ...mockPrimaryContact, email: null },
      };
      
      interviewModel.findById.mockReturnValue(mockInterview as any);
      clientsService.findOne.mockResolvedValue(clientNoEmail as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);

      await service.sendDesktopInterviewEmail('interview123');

      expect(mailService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({ to: null }),
        expect.any(Object),
        expect.any(String),
      );
    });
  });

  describe('sendNonCitizenEmail', () => {
    it('should send non-citizen email for primary contact', async () => {
      const expectedContext = {
        clientFirstName: mockPrimaryContact.firstName,
        clientLastName: mockPrimaryContact.lastName,
      };

      interviewModel.findById.mockReturnValue(mockInterview as any);
      interviewModel.findByIdAndUpdate.mockResolvedValue({});
      clientsService.findOne.mockResolvedValue(mockClient as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      mailService.sendEmail.mockResolvedValue(undefined);

      await service.sendNonCitizenEmail('interview123');

      expect(mailService.sendEmail).toHaveBeenCalledWith(
        {
          to: mockPrimaryContact.email,
          templateName: EmailTemplateNameEnum.NonCitizen,
          context: expectedContext,
          organisation: mockOrganisation,
        },
        mockPrimaryContact,
        'advisor123',
      );
    });

    it('should send non-citizen email for secondary contact', async () => {
      const secondaryInterview = { ...mockInterview, isPrimary: false };
      const expectedContext = {
        clientFirstName: mockSecondaryContact.firstName,
        clientLastName: mockSecondaryContact.lastName,
      };

      interviewModel.findById.mockReturnValue(secondaryInterview as any);
      interviewModel.findByIdAndUpdate.mockResolvedValue({});
      clientsService.findOne.mockResolvedValue(mockClient as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);

      await service.sendNonCitizenEmail('interview123');

      expect(mailService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: mockSecondaryContact.email,
          context: expectedContext,
        }),
        mockSecondaryContact,
        'advisor123',
      );
    });

    it('should update interview completion status', async () => {
      interviewModel.findById.mockReturnValue(mockInterview as any);
      interviewModel.findByIdAndUpdate.mockResolvedValue({});
      clientsService.findOne.mockResolvedValue(mockClient as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);

      await service.sendNonCitizenEmail('interview123');

      expect(interviewModel.findByIdAndUpdate).toHaveBeenCalledWith(
        'interview123',
        {
          $set: {
            isComplete: true,
            updatedAt: expect.any(Date),
          },
        },
      );
    });

    it('should throw HttpException when interview not found', async () => {
      interviewModel.findById.mockReturnValue({ populate: jest.fn().mockReturnValue(null) } as any);

      await expect(service.sendNonCitizenEmail('interview123')).rejects.toThrow(
        new HttpException('Interview not found', HttpStatus.NOT_FOUND),
      );
    });

    it('should use correct email context', async () => {
      interviewModel.findById.mockReturnValue(mockInterview as any);
      interviewModel.findByIdAndUpdate.mockResolvedValue({});
      clientsService.findOne.mockResolvedValue(mockClient as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);

      await service.sendNonCitizenEmail('interview123');

      const callArgs = mailService.sendEmail.mock.calls[0][0];
      expect(callArgs.context).toEqual({
        clientFirstName: mockPrimaryContact.firstName,
        clientLastName: mockPrimaryContact.lastName,
      });
    });
  });

  describe('sendReadyToSignEmail', () => {
    it('should filter DocuSign accounts correctly', async () => {
      const enrichedInterview: EnrichedInterview = {
        ...mockInterview,
        client: mockClient,
        isPrimary: true,
      } as any;

      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      mailService.sendEmail.mockResolvedValue(undefined);

      await service.sendReadyToSignEmail(enrichedInterview);

      const callArgs = mailService.sendEmail.mock.calls[0][0];
      expect((callArgs.context as any).docusignAccounts).toBe('<li>Account 1</li> <li>Account 3</li>');
      expect((callArgs.context as any).offlineAccounts).toBe('<li>Account 2</li>');
    });

    it('should filter offline accounts correctly', async () => {
      const enrichedInterview: EnrichedInterview = {
        ...mockInterview,
        client: { ...mockClient, isPrimary: false },
        isPrimary: false,
      } as any;

      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      mailService.sendEmail.mockResolvedValue(undefined);

      await service.sendReadyToSignEmail(enrichedInterview);

      const callArgs = mailService.sendEmail.mock.calls[0][0];
      expect((callArgs.context as any).docusignAccounts).toBe('<li>Account A</li>');
      expect((callArgs.context as any).offlineAccounts).toBe('<li>Account B</li>');
    });

    it('should send email to CSA', async () => {
      const enrichedInterview: EnrichedInterview = {
        ...mockInterview,
        client: mockClient,
        isPrimary: true,
      } as any;

      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      mailService.sendEmail.mockResolvedValue(undefined);

      await service.sendReadyToSignEmail(enrichedInterview);

      expect(mailService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          to: mockClient.primaryCSA.email,
          templateName: EmailTemplateNameEnum.ReadyToSign,
        }),
        mockPrimaryContact,
        'advisor123',
      );
    });

    it('should handle empty accounts array', async () => {
      const clientNoAccounts = {
        ...mockClient,
        primaryContact: { ...mockPrimaryContact, accounts: [] },
      };
      const enrichedInterview: EnrichedInterview = {
        ...mockInterview,
        client: clientNoAccounts,
        isPrimary: true,
      } as any;

      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      mailService.sendEmail.mockResolvedValue(undefined);

      await service.sendReadyToSignEmail(enrichedInterview);

      const callArgs = mailService.sendEmail.mock.calls[0][0];
      expect((callArgs.context as any).docusignAccounts).toBe('');
      expect((callArgs.context as any).offlineAccounts).toBe('');
    });

    it('should form correct email context', async () => {
      const enrichedInterview: EnrichedInterview = {
        ...mockInterview,
        client: mockClient,
        isPrimary: true,
      } as any;

      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      mailService.sendEmail.mockResolvedValue(undefined);

      await service.sendReadyToSignEmail(enrichedInterview);

      const callArgs = mailService.sendEmail.mock.calls[0][0];
      expect(callArgs.context).toEqual({
        csaFirstName: mockClient.primaryCSA.firstName,
        csaLastName: mockClient.primaryCSA.lastName,
        clientFirstName: mockPrimaryContact.firstName,
        clientLastName: mockPrimaryContact.lastName,
        docusignAccounts: '<li>Account 1</li> <li>Account 3</li>',
        offlineAccounts: '<li>Account 2</li>',
      });
    });
  });

  describe('sendNotification', () => {
    it('should send ready to sign email when client status is PendingReview', async () => {
      const pendingReviewClient = { ...mockClient, status: ClientStatusEnum.PendingReview };
      const mockInterviewWithSession = {
        ...mockInterview,
        session: jest.fn().mockReturnThis(),
        toObject: jest.fn().mockReturnValue(mockInterview),
      };

      interviewModel.findById.mockReturnValue(mockInterviewWithSession as any);
      clientsService.findOne.mockResolvedValue(pendingReviewClient as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      mailService.sendEmail.mockResolvedValue(undefined);

      await service.sendNotification('interview123', mockSession);

      expect(interviewModel.findById).toHaveBeenCalledWith('interview123');
      expect(mockInterviewWithSession.session).toHaveBeenCalledWith(mockSession);
      expect(clientsService.findOne).toHaveBeenCalledWith({ _id: 'client123' }, mockSession);
      expect(mailService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          templateName: EmailTemplateNameEnum.ReadyToSign,
        }),
        expect.any(Object),
        expect.any(String),
      );
    });

    it('should not send email when client status is not PendingReview', async () => {
      const mockInterviewWithSession = {
        ...mockInterview,
        session: jest.fn().mockReturnThis(),
        toObject: jest.fn().mockReturnValue(mockInterview),
      };

      interviewModel.findById.mockReturnValue(mockInterviewWithSession as any);
      clientsService.findOne.mockResolvedValue(mockClient as any);

      await service.sendNotification('interview123', mockSession);

      expect(mailService.sendEmail).not.toHaveBeenCalled();
    });

    it('should throw HttpException when interview not found', async () => {
      interviewModel.findById.mockReturnValue({ session: jest.fn().mockReturnValue(null) } as any);

      await expect(service.sendNotification('interview123', mockSession)).rejects.toThrow(
        new HttpException('Interview not found', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw HttpException when client not found', async () => {
      const mockInterviewWithSession = {
        ...mockInterview,
        session: jest.fn().mockReturnThis(),
      };

      interviewModel.findById.mockReturnValue(mockInterviewWithSession as any);
      clientsService.findOne.mockResolvedValue(null);

      await expect(service.sendNotification('interview123', mockSession)).rejects.toThrow(
        new HttpException('Client not found', HttpStatus.NOT_FOUND),
      );
    });

    it('should pass session to database queries', async () => {
      const mockInterviewWithSession = {
        ...mockInterview,
        session: jest.fn().mockReturnThis(),
        toObject: jest.fn().mockReturnValue(mockInterview),
      };

      interviewModel.findById.mockReturnValue(mockInterviewWithSession as any);
      clientsService.findOne.mockResolvedValue(mockClient as any);

      await service.sendNotification('interview123', mockSession);

      expect(mockInterviewWithSession.session).toHaveBeenCalledWith(mockSession);
      expect(clientsService.findOne).toHaveBeenCalledWith({ _id: 'client123' }, mockSession);
    });
  });

  describe('Edge cases', () => {
    it('should handle null contact data', async () => {
      const clientNullContact = {
        ...mockClient,
        primaryContact: null,
      };
      
      interviewModel.findById.mockReturnValue(mockInterview as any);
      clientsService.findOne.mockResolvedValue(clientNullContact as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);

      await expect(service.sendDesktopInterviewEmail('interview123')).rejects.toThrow();
    });

    it('should handle undefined secondary contact', async () => {
      const clientNoSecondary = {
        ...mockClient,
        secondaryContact: undefined,
      };
      const secondaryInterview = { ...mockInterview, isPrimary: false };
      
      interviewModel.findById.mockReturnValue(secondaryInterview as any);
      clientsService.findOne.mockResolvedValue(clientNoSecondary as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);

      await expect(service.sendDesktopInterviewEmail('interview123')).rejects.toThrow();
    });

    it('should handle empty organisation', async () => {
      interviewModel.findById.mockReturnValue(mockInterview as any);
      clientsService.findOne.mockResolvedValue(mockClient as any);
      organisationsService.findOne.mockResolvedValue(null);

      await expect(service.sendDesktopInterviewEmail('interview123')).rejects.toThrow();
    });

    it('should propagate mail service errors', async () => {
      const mailError = new Error('Mail service error');
      
      interviewModel.findById.mockReturnValue(mockInterview as any);
      clientsService.findOne.mockResolvedValue(mockClient as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      mailService.sendEmail.mockRejectedValue(mailError);

      await expect(service.sendDesktopInterviewEmail('interview123')).rejects.toThrow(mailError);
    });

    it('should handle advisor id as undefined', async () => {
      const clientNoAdvisor = {
        ...mockClient,
        primaryAdvisor: { id: undefined },
      };
      
      interviewModel.findById.mockReturnValue(mockInterview as any);
      clientsService.findOne.mockResolvedValue(clientNoAdvisor as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);

      await service.sendDesktopInterviewEmail('interview123');

      expect(mailService.sendEmail).toHaveBeenCalledWith(
        expect.any(Object),
        expect.any(Object),
        undefined,
      );
    });

    it('should handle accounts with various offline type formats', async () => {
      const clientVariousOffline = {
        ...mockClient,
        primaryContact: {
          ...mockPrimaryContact,
          accounts: [
            { label: 'Regular Account', type: 'standard' },
            { label: 'Offline 1', type: 'OFFLINE' },
            { label: 'Offline 2', type: 'Offline_Account' },
            { label: 'Offline 3', type: 'account_offline' },
          ],
        },
      };
      const enrichedInterview: EnrichedInterview = {
        ...mockInterview,
        client: clientVariousOffline,
        isPrimary: true,
      } as any;

      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      mailService.sendEmail.mockResolvedValue(undefined);

      await service.sendReadyToSignEmail(enrichedInterview);

      const callArgs = mailService.sendEmail.mock.calls[0][0];
      expect((callArgs.context as any).docusignAccounts).toBe('<li>Regular Account</li>');
      expect((callArgs.context as any).offlineAccounts).toBe('<li>Offline 1</li> <li>Offline 2</li> <li>Offline 3</li>');
    });
  });

  describe('forwardRef dependency injection', () => {
    it('should properly inject dependencies with forwardRef', () => {
      expect(service).toBeDefined();
      expect(service.organisationsService).toBeDefined();
      expect(service.clientsService).toBeDefined();
      expect(service.mailService).toBeDefined();
      expect(service.interviewModel).toBeDefined();
    });
  });
});