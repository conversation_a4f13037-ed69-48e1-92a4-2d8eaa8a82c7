import { Injectable } from '@nestjs/common';
import { Connection, Record as PractifiRecord } from 'jsforce';
import { PractifiAsset, PractifiRelationshipData, PractifiRoleDto, PractifiCompanyAddressDto } from '../dto/practifi.dto';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { generateRequestConfigs } from '../utils/generate-request-configs.util';
import { OperationType } from 'src/super-http/enums';
import { practifiQuery, formatName } from '../utils/practifi-query.util';
import { PractifiEntityTypes } from '../types/enums';
import { handleConvertPractifiTypeToUnified } from '../utils/convert-to-practifi-registration-types.util';
import { commonConfigs } from '../types/types';

@Injectable()
export class PractifiCustomObjectsService {
  private connection: Connection;
  private baseUrl: string;
  private commonConfigs: commonConfigs;

  constructor(
    private readonly httpService: SuperHttpService,
    private tenantId: string
  ) {}

  setConnection(connection: Connection) {
    this.connection = connection;
    this.baseUrl = `${connection.instanceUrl}/services/data/v${connection.version}`;
    this.prepareRequestConfigs();
  }

  prepareRequestConfigs() {
    const requestConfigs = generateRequestConfigs(this.connection.accessToken);
    const { withInterceptors, ...configsWithoutInterceptors } = requestConfigs;
    this.commonConfigs = configsWithoutInterceptors;
  }

  async createCustomAsset(practifiAssetData: PractifiAsset): Promise<PractifiRecord> {
    const recordTypeId = await this.getRecordTypeId('Asset');

    return await this.httpService.patch(
      `${this.baseUrl}/sobjects/practifi__Asset_Liability__c/practifi__External_Id__c/${practifiAssetData.practifi__Client__c}-${encodeURIComponent(practifiAssetData.Name)}`,
      this.tenantId,
      {
        ...practifiAssetData,
        RecordTypeId: recordTypeId,
      },
      {
        rateLimiter: 'practifi',
        integrationName: 'practifi',
        operation: OperationType.PATCH,
        entityType: PractifiEntityTypes.ASSET,
      },
      this.commonConfigs
    );
  }

  async getRecordTypeId(type: string): Promise<string> {
    const soql = `SELECT Id FROM RecordType WHERE SobjectType = 'practifi__Asset_Liability__c' AND DeveloperName = '${type}' LIMIT 1`;
    const result = await practifiQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, PractifiEntityTypes.ASSET, this.tenantId);
    if (!result.records?.length) {
      throw new Error(`RecordType not found: Asset / ${type}`);
    }
    return result.records[0].Id;
  }

  async createRelationship(practifiRelationshipData: PractifiRelationshipData): Promise<PractifiRecord> {
    return await this.httpService.patch(
      `${this.baseUrl}/sobjects/practifi__Relationship__c/practifi__External_Id__c/${practifiRelationshipData.practifi__From_Contact__c}-${practifiRelationshipData.practifi__To_Entity__c}`,
      this.tenantId,
      {
        ...practifiRelationshipData,
      },
      {
        rateLimiter: 'practifi',
        integrationName: 'practifi',
        operation: OperationType.PATCH,
        entityType: PractifiEntityTypes.RELATIONSHIP,
      },
      this.commonConfigs
    );
  }

  async createRelationshipType(practifiRelationshipTypeData: { name: string }): Promise<PractifiRecord> {
    const soql = `SELECT Id, Name FROM practifi__Relationship_Type__c WHERE Name = '${formatName(practifiRelationshipTypeData.name)}'`;
    const result = await practifiQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, PractifiEntityTypes.RELATIONSHIP, this.tenantId);
    const recordExists = result.totalSize > 0;

    if (recordExists) {
      return { id: result.records[0].Id };
    }

    return await this.httpService.post(
        `${this.baseUrl}/sobjects/practifi__Relationship_Type__c/`,
        this.tenantId,
        {
          ...practifiRelationshipTypeData,
        },
        {
          rateLimiter: 'practifi',
          integrationName: 'practifi',
          operation: OperationType.CREATE,
          entityType: PractifiEntityTypes.RELATIONSHIP,
        },
        this.commonConfigs
      );
  }

  async getAssetId(name: string, crmClientId: string) {
    const soql = `SELECT Id FROM practifi__Asset_Liability__c WHERE Name = '${formatName(name)}' AND practifi__External_Id_1__c = '${crmClientId}-${formatName(name)}' LIMIT 1`;

    const result = await practifiQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, PractifiEntityTypes.ASSET, this.tenantId);

    if (!result.records?.length) {
      return;
    }
    return result.records[0].Id;
  }

  async createAssetRole(practifiRoleData: PractifiRoleDto): Promise<PractifiRecord> {
    return await this.httpService.patch(
      `${this.baseUrl}/sobjects/practifi__Asset_Liability_Role__c/practifi__External_Id__c/${practifiRoleData.practifi__Asset_Liability__c}-${practifiRoleData.practifi__Person__c}`,
      this.tenantId,
      {
        ...practifiRoleData,
      },
      {
        rateLimiter: 'practifi',
        integrationName: 'practifi',
        operation: OperationType.PATCH,
        entityType: PractifiEntityTypes.ROLE,
      },
      this.commonConfigs
    );
  }

  async deleteLinkedBeneficiaries(recordId: string, name: string) {
    const soql = `SELECT Id FROM practifi__Asset_Liability_Role__c WHERE practifi__Asset_Liability__c = '${recordId}' AND Name = '${formatName(name)}'`;
    const result = await practifiQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, PractifiEntityTypes.ROLE, this.tenantId);
    const recordsExist = result.totalSize > 0;

    if (recordsExist) {
      const deleteRequests = result.records.map(el =>
        this.httpService.delete(
          `${this.baseUrl}/sobjects/practifi__Asset_Liability_Role__c/${el.Id}`,
          this.tenantId,
          {
            rateLimiter: 'practifi',
            integrationName: 'practifi',
            operation: OperationType.DELETE,
            entityType: PractifiEntityTypes.ROLE
          },
          this.commonConfigs
        )
      );
      return await Promise.all([...deleteRequests]);
    }
  }

  async getLinkedAssets(accountId: string) {
    const soql = `SELECT Id, Name, practifi__Registration_Type__c FROM practifi__Asset_Liability__c WHERE practifi__Client__c = '${accountId}'`;
    const result = await practifiQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, PractifiEntityTypes.ASSET, this.tenantId);
    const recordsExist = result.totalSize > 0;

    if (recordsExist) {
      return result.records.map(el => ({
        id: el.Id,
        name: el.Name,
        type: handleConvertPractifiTypeToUnified(el.practifi__Registration_Type__c) || '',
      }));
    }
    return [];
  }

  async getLinkedBeneficiaries(beneficiaryId: string) {
    const soql = `SELECT Id, Name, practifi__Person__c, practifi__Percentage__c FROM practifi__Asset_Liability_Role__c WHERE practifi__Asset_Liability__c = '${beneficiaryId}'`;
    const result = await practifiQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, PractifiEntityTypes.ROLE, this.tenantId);
    const recordsExist = result.totalSize > 0;

    const beneficiaries = [];

    if (recordsExist) {
      for await (const el of result.records) {
        if (el.practifi__Person__c) {
          const soql = `SELECT FirstName, LastName, Birthdate FROM Contact WHERE Id = '${el.practifi__Person__c}'`;
          const result = await practifiQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, PractifiEntityTypes.ROLE, this.tenantId);
          const recordExists = result.totalSize > 0;

          if (recordExists) {
            const benRecord = {
              fullName: `${result.records[0].FirstName} ${result.records[0].LastName}`,
              firstName: result.records[0].FirstName,
              lastName: result.records[0].LastName,
              percentage: el.practifi__Percentage__c,
              type: el.Name,
              dob: result.records[0].Birthdate,
            }
            beneficiaries.push(benRecord);
          }
        }
      }
    }
    return beneficiaries;
  }

  async createCompanyAddress(companyAddressData: PractifiCompanyAddressDto): Promise<PractifiRecord> {
    return await this.httpService.patch(
      `${this.baseUrl}/sobjects/ContactPointAddress/practifi__External_Id_1__c/${companyAddressData.practifi__Contact__c}`,
      this.tenantId,
      {
        ...companyAddressData,
      },
      {
        rateLimiter: 'practifi',
        integrationName: 'practifi',
        operation: OperationType.PATCH,
        entityType: PractifiEntityTypes.COMPANY_ADDRESS,
      },
      this.commonConfigs
    );
  }

  async getCompanyAddress(contactId: string): Promise<{
    id: string;
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    customTypeTitle: string;
  } | undefined> {
    const soql = `SELECT Id, Name, City, PostalCode, State, Street, Country FROM ContactPointAddress WHERE practifi__Contact__c = '${contactId}'`;
    const result = await practifiQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, PractifiEntityTypes.COMPANY_ADDRESS, this.tenantId);
    const recordIsPresent = result.records[0];

    if (recordIsPresent) {
      return  {
        id: recordIsPresent.Id,
        customTypeTitle: recordIsPresent.Name,
        city: recordIsPresent.City,
        zipCode: recordIsPresent.PostalCode,
        state: recordIsPresent.State,
        street: recordIsPresent.Street,
        country: recordIsPresent.Country,
      }
    }

    
  }
}