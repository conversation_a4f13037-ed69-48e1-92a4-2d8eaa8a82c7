import { parsePhoneNumber } from 'libphonenumber-js';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';

export const organisationGeneralMappers = {
  'organisation.name': {
    field: ['organisation.name'],
  },
  'organisation.riaName': {
    field: ['organisation.riaName'],
  },
  'organisation.phone': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const phone = interviewData.organisation.phone;
      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'organisation.email': {
    field: ['organisation.email'],
  },
  'organisation.address.street': {
    field: ['organisation.address.street'],
  },
  'organisation.address.city': {
    field: ['organisation.address.city'],
  },
  'organisation.address.state': {
    field: ['organisation.address.state'],
  },
  'organisation.address.zip': {
    field: ['organisation.address.zip'],
  },
};
