import { <PERSON>NotEmpty, <PERSON><PERSON><PERSON>al, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CreatePageDefDto } from './create-template-v2.dto';

export class AddPageV2Dto extends CreatePageDefDto {
  @ApiPropertyOptional({ description: 'Position to insert the page at (defaults to end)' })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Type(() => Number)
  insertAt?: number;

  @ApiPropertyOptional({ description: 'Page ID to insert after' })
  @IsOptional()
  @IsNotEmpty()
  insertAfterPageId?: string;
}