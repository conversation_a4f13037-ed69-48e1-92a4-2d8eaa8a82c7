import { HttpException, HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ClientSession, FilterQuery, Model } from 'mongoose';
import { GetClientsQueryDto } from 'src/advisors/dto/get-clients.dto';
import { LinkCrmDto } from 'src/advisors/dto/link-crm.dto';
import { Advisor } from 'src/advisors/schemas/advisors.schema';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { CRM } from 'src/integrations/crm/crm.interface';
import { CRMService } from 'src/integrations/crm/crm.service';
import {
  CreateContactDto,
  UpdateContactDto,
} from 'src/integrations/crm/types/create-contact.dto';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';

import { CRMEnum, IntegrationEnum } from 'src/shared/types/integrations';
import { addIntegration, decryptCredentials, encrypt } from 'src/utils';
import { GetCrmUsersQueryDto } from '../dto/get-crm-users.dto';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { ClientsV1Service } from 'src/clients/services/v1/clients-v1.service';
import { LogCommunicationDto } from 'src/integrations/crm/types/communications/log-communication.dto';
import { Credentials } from 'src/auth/auth.types';
import { isOAuth } from 'src/integrations/crm/utils/isOAuth';
import { CrmAuthException } from 'src/shared/exceptions/crm-auth.exception';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import { Integration } from 'src/shared/schemas/integration.schema';

@Injectable()
export class AdvisorsCrmService {
  constructor(
    @InjectModel(Advisor.name)
    protected advisorModel: Model<Advisor>,
    protected crmService: CRMService,
    protected advisorsCrudService: AdvisorsCrudService,
    protected organisationService: OrganisationsService,
    protected clientService: ClientsV1Service,
    private readonly transactionManager: TransactionManager,
  ) { }

  /**
   * Links a CRM to an advisor.
   * @param filter - The filter query to find the advisor.
   * @param linkCrmDto - The DTO containing the CRM credentials and type.
   * @returns A message indicating the successful linking of the CRM.
   * @throws HttpException if failed to authenticate CRM.
   */
  async linkCrm(
    filter: FilterQuery<Advisor>,
    linkCrmDto: LinkCrmDto,
    refresh = false,
  ) {
    const { credentials, crmType } = linkCrmDto;

    const advisor = await this.advisorsCrudService.findOne(filter);
    const orgId = advisor.organisation._id;

    const organisation = await this.organisationService.findOne(
      orgId.toString(),
    );
    
    const crm = await this.crmService.create(advisor, organisation, crmType, credentials);

    try {
      const authData = await crm.authenticate();
      const crmId = await crm.getAuthenticatedUserId();

      const credentialsToInsert = {
        ...credentials,
        ...authData,
      };

      const encryptedCredentials = await encrypt(
        JSON.stringify(credentialsToInsert),
      );

      const integration = {
        integrationType: IntegrationEnum.Crm,
        integrationConfig: {
          name: crmType,
          credentials: encryptedCredentials,
        },
      };

      await addIntegration<Model<Advisor>>(
        filter,
        integration,
        this.advisorModel,
        refresh,
      );

      if (!organisation.crmInitialized) {
        const config = await crm.initCrm();
        await this.organisationService.upsertConfig(orgId.toString(), config);
        await this.organisationService.update(orgId.toString(), {
          crmInitialized: true,
        });
      }

      await this.advisorModel.updateOne(filter, {
        crmId,
      });

      return { message: `${crmType} CRM linked successfully.` };
    } catch (error) {
      if (error instanceof CrmAuthException) {
        throw error;
      }
      throw new HttpException('Failed to authenticate CRM.', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Adds an integration to an advisor.
   *
   * @param filter - The filter query to find the advisor.
   * @param integrationType - The type of integration to add.
   * @param integrationConfig - The configuration for the integration.
   * @returns A promise that resolves to the updated advisor model.
   */
  async addIntegration(
    filter: FilterQuery<Advisor>,
    integrationType: IntegrationEnum,
    integrationConfig: any,
  ) {
    const integration = {
      integrationType,
      integrationConfig,
    };

    return addIntegration<Model<Advisor>>(
      filter,
      integration,
      this.advisorModel,
    );
  }

  /**
   * Unlinks the CRM integration for an advisor.
   *
   * @param filter - The filter to find the advisor.
   * @returns A message indicating the success of unlinking the CRM integration.
   */
  async unlinkCrm(filter: FilterQuery<Advisor>) {
    const { integrations, organisation } = await this.advisorModel.findOne(
      filter,
    );

    await this.advisorModel.updateOne(filter, {
      integrations: integrations.filter(
        ({ integrationType }) => integrationType !== IntegrationEnum.Crm,
      ),
      crmId: null,
      updatedAt: new Date(),
    });

    await this.organisationService.update(organisation._id.toString(), {
      crmInitialized: false,
    });

    return { message: `CRM unlinked successfully.` };
  }

  /**
   * Retrieves CRM Contacts based on the provided query.
   * @param query - The query parameters for retrieving CRM clients.
   * @returns A promise that resolves to an array of CRM clients.
   */
  async getCrmClients(query: GetClientsQueryDto, advisorId: string) {
    try {
      if (!advisorId) {
        throw new HttpException('No advisor ID provided', HttpStatus.BAD_REQUEST);
      }
      const crm = await this.getCrmInstance(advisorId);
      const crmContacts = await crm.getContacts({
        search: query.name,
      });

      const promises = crmContacts.map(async (contact) => {
        const statuses = [...Object.values(ClientStatusEnum)].filter((status) =>
          [
            ClientStatusEnum.Complete,
            ClientStatusEnum.Signed,
            ClientStatusEnum.WaitingSignature,
          ].includes(status),
        );

        const existingClient = await this.clientService
          .findOne({
            $and: [
              {
                $or: [
                  {
                    'primaryContact.crmClientId': contact.id,
                  },
                  {
                    'secondaryContact.crmClientId': contact.id,
                  },
                ],
              },
              {
                status: {
                  $in: statuses,
                },
              },
            ],
          })
          .catch(() => null);

        return { ...contact, isAlreadyOnbord: !!existingClient };
      });

      return Promise.all(promises);
    } catch (error) {
      if (error instanceof CrmAuthException) {
        throw error;
      }
      throw new CrmAuthException(error?.message, error?.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }

  }

  /**
   * Retrieves CRM Users based on the provided query.
   * @param query - The query parameters for retrieving CRM users.
   * @returns A Promise that resolves to an array of CRM users with additional information indicating if they are already onboarded.
   */
  async getCrmUsers(query: GetCrmUsersQueryDto, advisorId: string) {
    const crm = await this.getCrmInstance(advisorId);
    const crmUsers = await crm.getAllUsers(query);

    const onbordUsers = await this.getAdvisorsByCrmId(
      crmUsers.map((user) => user.crmId.toString()),
    );

    const updatedResult = crmUsers.map((user) => ({
      ...user,
      isAlreadyOnbord: onbordUsers.some(
        (advisor) => advisor.crmId === user.crmId,
      ),
    }));

    return updatedResult;
  }

  /**
   * Retrieves advisors by CRM ID.
   * @param query - An array of CRM IDs.
   * @returns A promise that resolves to an array of Advisor objects.
   */
  async getAdvisorsByCrmId(query: string[]): Promise<Advisor[]> {
    return await this.advisorModel.find({ crmId: { $in: query } });
  }

  /**
   * Retrieves the CRM instance for the specified advisor or the current advisor if no advisor ID is provided.
   * @param advisorId The ID of the advisor (optional).
   * @returns A Promise that resolves to the CRM instance.
   * @throws HttpException if the CRM is not linked or if authentication fails.
   */
  async getCrmInstance(advisorId: string, session?: ClientSession): Promise<CRM> {
    if (!advisorId) {
      throw new HttpException('No advisor ID provided', HttpStatus.BAD_REQUEST);
    }
    const advisor = await this.advisorsCrudService.findOne({ _id: advisorId }, session);
    const { integrations } = advisor;
    const crmIntegration = integrations.find(
      (integration) => integration.integrationType === IntegrationEnum.Crm,
    );

    if (!crmIntegration) {
      throw new HttpException('CRM not linked.', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    const credentials = await decryptCredentials(crmIntegration.integrationConfig.credentials);

    // Get the organization
    const organisation = await this.organisationService.findOne(advisor.organisation._id.toString(), session);

    const createdCrm = await this.crmService.create(
      advisor,
      organisation,
      CRMEnum[crmIntegration.integrationConfig.name],
      credentials,
    );

    try {
      const authData = await createdCrm.authenticate();

      if (isOAuth(authData)) {
        if (authData.shouldRefresh) {
          await this.refreshAuthCredentials(advisorId, authData);
        }
      }

      return createdCrm;
    } catch (error) {
      if (error instanceof CrmAuthException) {
        if (error.shouldUnlink) {
          await this.removeCrmIntegration(advisorId);
        }
        throw error;
      }
      throw new HttpException(error.message, error?.status || HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Removes the CRM integration for a specific advisor.
   * @param advisorId The ID of the advisor.
   * @returns A promise that resolves to the result of the update operation.
   */
  private async removeCrmIntegration(advisorId: string, session?: ClientSession) {
    return this.advisorModel.updateOne(
      { _id: advisorId },
      {
        $pull: {
          integrations: {
            integrationType: IntegrationEnum.Crm,
          },
        },
      },
      session,
    );
  }

  /**
   * Refreshes the authentication credentials for a specific advisor.
   *
   * @param advisorId - The ID of the advisor.
   * @param authData - The new authentication credentials.
   */
  private async refreshAuthCredentials(
    advisorId: string,
    authData: Credentials,
    session?: ClientSession,
  ) {
    const advisor = await this.advisorModel.findOne({ _id: advisorId }, session);
    const { integrations } = advisor;
    const crmIntegration = integrations.find(
      (integration) => integration.integrationType === IntegrationEnum.Crm,
    );
    const encryptedCredentials = await encrypt(JSON.stringify(authData));

    const integration = {
      integrationType: IntegrationEnum.Crm,
      integrationConfig: {
        ...crmIntegration.integrationConfig,
        credentials: encryptedCredentials,
      },
    };
    await addIntegration<Model<Advisor>>(
      { _id: advisorId },
      integration,
      this.advisorModel,
      true,
    );
  }

  /**
   * Updates a contact in the CRM.
   * @param updateDto The DTO containing the updated contact information.
   * @returns A promise that resolves to the updated contact.
   */
  async updateContact(updateDto: UpdateContactDto, advisorId: string) {
    const crm = await this.getCrmInstance(advisorId);
    return crm.updateContact(updateDto);
  }

  /**
   * Creates a CRM contact using the provided data.
   * @param createDto The data for creating the contact.
   * @returns A promise that resolves to the created contact.
   */
  async createCrmContact(createDto: CreateContactDto, advisorId: string) {
    const crm = await this.getCrmInstance(advisorId);
    return crm.createContact(createDto);
  }

  async logCommunication(
    crmClientId: string,
    logCommunicationDto: LogCommunicationDto,
    advisorId?: string,
  ) {
    const crm = await this.getCrmInstance(advisorId);
    return crm.logCommunication(crmClientId, logCommunicationDto);
  }

  /**
   * Gets the CRM integration from the provided advisor.
   * @param advisor - The advisor with role.
   * @returns The CRM integration.
   * @throws NotFoundException if no CRM integration is found.
   */
  getCRMIntegrationFromAdvisor(advisor: AdvisorWithRole): Integration {
    const crmIntegration = advisor.integrations?.find(
      (integration) => integration.integrationType === IntegrationEnum.Crm
    );

    if (!crmIntegration) {
      throw new NotFoundException('No CRM integration found for the advisor');
    }

    return crmIntegration;
  }
}
