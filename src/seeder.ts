import { seeder } from 'nestjs-seeder';
import { MongooseModule } from '@nestjs/mongoose';
import { Advisor, AdvisorSchema } from 'src/advisors/schemas/advisors.schema';
import { AdvisorsOrgSeeder } from 'src/advisors/seeders/advisors.seeder';
import {
  Organisation,
  OrganisationSchema,
} from './organisations/schemas/organisation.schema';
import { RolesSeeder } from './rbac/seeder/roles.seeder';
import { Role, RoleSchema } from './rbac/schemas/role.schema';
import { Client, ClientSchema } from './clients/schemas/clients.schema';
import {
  Interview,
  InterviewSchema,
} from './interviews/schemas/v1/interview.schema';
import {
  InterviewTemplate,
  InterviewTemplateSchema,
} from './interview-templates/schemas/v1/interview.template';
import { TransitionSeeder } from './transitions/seeders/transitions.seeder';
import {
  Transition,
  TransitionSchema,
} from 'src/transitions/schemas/transitions.schema';

seeder({
  imports: [
    MongooseModule.forRoot(
      process.env.DATABASE_URL ||
        'mongodb://mongodb:27017/?directConnection=true&replicaSet=rs0',
    ),
    MongooseModule.forFeature([{ name: Advisor.name, schema: AdvisorSchema }]),
    MongooseModule.forFeature([{ name: Client.name, schema: ClientSchema }]),
    MongooseModule.forFeature([{ name: Role.name, schema: RoleSchema }]),
    MongooseModule.forFeature([
      { name: Transition.name, schema: TransitionSchema },
    ]),
    MongooseModule.forFeature([
      { name: Interview.name, schema: InterviewSchema },
    ]),
    MongooseModule.forFeature([
      { name: InterviewTemplate.name, schema: InterviewTemplateSchema },
    ]),
    MongooseModule.forFeature([
      { name: Organisation.name, schema: OrganisationSchema },
    ]),
  ],
}).run([RolesSeeder, AdvisorsOrgSeeder, TransitionSeeder]);
