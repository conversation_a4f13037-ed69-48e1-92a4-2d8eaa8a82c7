import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Put,
  UseInterceptors,
  UploadedFiles,
  Query,
  UploadedFile,
  Patch,
} from '@nestjs/common';
import { Transaction } from 'src/shared/decorators/transaction.decorator';
import { OrganisationsService } from './organisations.service';
import { UpdateOrganisationDto } from './dto/update-organisation.dto';
import { Organisation } from './schemas/organisation.schema';
import { CreateOrganisationDto } from './dto/create-organisation.dto';
import { AuthGuard } from '@nestjs/passport';
import { Roles, RolesGuard } from 'src/shared/guards/roles.guard';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { AddMasterAccountDto } from 'src/organisations/dto/add-master-account.dto';
import { OrganisationGuard } from 'src/shared/guards/organisation.guard';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { UpdateDocusignAdvisoryDocumentsDto } from './dto/update-docusign-advisory-documents.dto';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import { UpdateDocusignAccountOwnershipDto } from './dto/update-docusign-account-ownership.dto';
import { UploadDocumentRequestDto } from 'src/organisations/dto/upload-document.dto';
import { DownloadDocumentRequestDto } from 'src/organisations/dto/download-document.dto';
import { CreateCustomQuestionDto } from 'src/organisations/dto/custom-questions/create-custom-question.dto';
import { UpdateCustomQuestionDto } from 'src/organisations/dto/custom-questions/update-custom-question.dto';
import { MAX_FILE_SIZE } from 'src/shared/types/general/asset-types.enum';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { GetOrganisationsQueryDto } from './dto/get-organisations.dto';
import {
  PaginationQueryDto,
  PaginationResponseDto,
} from 'src/shared/dto/pagination.dto';
import { ClientSession } from 'mongoose';
import { startOfDay, endOfDay } from 'date-fns';
import { DateRange } from 'src/shared/types/general/date-range.type';
import { OrganisationStatusEnum } from './types/organisation-status.enum';
import { ToggleFeatureDto } from 'src/organisations/dto/toggle-feature.dto';
import {
  OrganisationViewDTO,
  OrganisationViewListDTO,
  OrganisationViewListItemDTO,
} from 'src/organisations/dto/view-organisation.dto';
import { plainToInstance } from 'class-transformer';
import { ApiResponse } from '@nestjs/swagger';
import { UpdateDocusignAccountIdDto } from 'src/integrations/docusign/dto/update-docusign-account-id.dto';
import { CacheInterceptor } from '@nestjs/cache-manager';

@UseInterceptors(CacheInterceptor)
@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'), RolesGuard, OrganisationGuard)
@Controller('organisations')
export class OrganisationsController {
  constructor(
    private readonly organisationsService: OrganisationsService,
    public transactionManager: TransactionManager,
  ) {}

  @Get()
  @Roles(RolesEnum.SuperAdmin)
  @ApiResponse({ type: [OrganisationViewListDTO] })
  async findAll(
    @Query() query: GetOrganisationsQueryDto,
  ): Promise<OrganisationViewListDTO> {
    const { limit, page, to, from, ...filters } = query;
    const dateRange: DateRange | undefined =
      to || from
        ? {
            toDate: to ? endOfDay(new Date(to)) : undefined,
            fromDate: from ? startOfDay(new Date(from)) : undefined,
          }
        : undefined;

    const paginatedResults = await this.organisationsService.findAll(
      { limit, page },
      filters,
      dateRange,
    );
    return {
      totalResults: paginatedResults.totalResults,
      result: paginatedResults.result.map((org) =>
        plainToInstance(OrganisationViewListItemDTO, org, {
          excludeExtraneousValues: true,
        }),
      ),
    };
  }

  @Post()
  @Roles(RolesEnum.SuperAdmin)
  @Transaction()
  async create(
    @Body() organisation: CreateOrganisationDto,
    session?: ClientSession,
  ) {
    return this.organisationsService.create(organisation, session);
  }

  @Post(':organisationId/logo/upload')
  @UseInterceptors(FileInterceptor('logo'))
  async uploadLogo(
    @UploadedFile() logo,
    @Param('organisationId') id: string,
  ): Promise<string> {
    return this.organisationsService.uploadLogo(id, logo);
  }

  @Post(':organisationId/documents/upload')
  @UseInterceptors(
    FileInterceptor('file', {
      limits: { fileSize: MAX_FILE_SIZE },
    }),
  )
  async upload(
    @UploadedFile() file,
    @Param('organisationId') id: string,
    @Body() body: UploadDocumentRequestDto,
  ): Promise<string> {
    return this.organisationsService.uploadPrivateDocument(
      id,
      file,
      body.fileName,
      body.assetType,
    );
  }

  @Get(':organisationId/documents/download')
  async download(
    @Param('organisationId') id: string,
    @Query() query: DownloadDocumentRequestDto,
  ): Promise<string> {
    return this.organisationsService.downloadPrivateDocument(id, query);
  }

  @Get(':organisationId')
  @ApiResponse({ type: OrganisationViewDTO })
  async findOne(
    @Param('organisationId') id: string,
  ): Promise<OrganisationViewDTO> {
    const org = await this.organisationsService.findOne(id);
    return plainToInstance(OrganisationViewDTO, org, {
      excludeExtraneousValues: true,
    });
  }

  @Put(':organisationId')
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.SuperAdmin)
  @Transaction()
  update(
    @Param('organisationId') id: string,
    @Body() updateOrganisationDto: UpdateOrganisationDto,
    session?: ClientSession,
  ) {
    return this.organisationsService.update(id, updateOrganisationDto, session);
  }

  @Delete(':organisationId')
  @Roles(RolesEnum.SuperAdmin)
  @Transaction()
  async remove(
    @Param('organisationId') id: string,
    session?: ClientSession,
  ): Promise<OrganisationViewDTO> {
    const org = await this.organisationsService.remove(id, session);
    return plainToInstance(OrganisationViewDTO, org, {
      excludeExtraneousValues: true,
    });
  }

  @Post(':organisationId/master-accounts')
  @Roles(RolesEnum.CompanyAdmin)
  async addMasterAccount(
    @Param('organisationId') id: string,
    @Body() addMasterAccountDto: AddMasterAccountDto,
  ) {
    return this.organisationsService.addMasterAccount(id, addMasterAccountDto);
  }

  @Get(':organisationId/master-accounts')
  async getMasterAccounts(@Param('organisationId') id: string) {
    return this.organisationsService.getMasterAccounts(id);
  }

  @Delete(':organisationId/master-accounts/:masterAccountId')
  async deleteMasterAccount(
    @Param('organisationId') id: string,
    @Param('masterAccountId') masterAccountId: string,
  ) {
    return this.organisationsService.deleteMasterAccount(id, masterAccountId);
  }

  @Put(':organisationId/docusign/unlink')
  @Roles(RolesEnum.CompanyAdmin)
  async unlinkDocusign(@Param('organisationId') organisationId: string) {
    return this.organisationsService.unlinkDocusign(organisationId);
  }

  @Put(':organisationId/docusign/ownership')
  @Roles(RolesEnum.CompanyAdmin)
  async updateDocusignAccountOwnership(
    @Param('organisationId') organisationId: string,
    @Body() dto: UpdateDocusignAccountOwnershipDto,
  ) {
    return this.organisationsService.updateDocusignAccountOwnership(
      organisationId,
      dto,
    );
  }

  @Roles(RolesEnum.CompanyAdmin)
  @Put(':organisationId/docusign/account')
  async setDocusignAccount(
    @Param('organisationId') organisationId: string,
    @Body() accountInfo: UpdateDocusignAccountIdDto,
  ) {
    return this.organisationsService.setDocusignAccount(
      organisationId,
      accountInfo.accountId,
    );
  }

  @Put(':organisationId/docusign/documents')
  @Roles(RolesEnum.SuperAdmin)
  @UseInterceptors(FilesInterceptor('file', 1))
  async upsertDocusignAdvisoryDocuments(
    @Param('organisationId') organisationId: string,
    @Query('fileName') fileName: string,
    @UploadedFiles() file: Express.Multer.File[],
  ) {
    const dto: UpdateDocusignAdvisoryDocumentsDto = {
      fileName,
      file: file[0],
    };

    return this.organisationsService.upsertDocusignAdvisoryDocuments(dto);
  }

  @Get(':organisationId/docusign/documents')
  @Roles(RolesEnum.SuperAdmin)
  @UseInterceptors(FilesInterceptor('file', 1))
  async getDocusignAdvisoryDocuments(
    @Param('organisationId') organisationId: string,
  ) {
    return this.organisationsService.getDocusignAdvisoryDocuments(
      organisationId,
    );
  }

  @Post(':organisationId/custom-questions')
  @Roles(RolesEnum.CompanyAdmin)
  async addCustomQuestion(
    @Param('organisationId') organisationId: string,
    @Body() createCustomQuestionDto: CreateCustomQuestionDto,
  ) {
    return this.organisationsService.addCustomQuestion(
      organisationId,
      createCustomQuestionDto,
    );
  }

  @Put(':organisationId/custom-questions/:questionId')
  @Roles(RolesEnum.CompanyAdmin)
  async updateCustomQuestion(
    @Param('organisationId') organisationId: string,
    @Param('questionId') questionId: string,
    @Body() updateCustomQuestionDto: UpdateCustomQuestionDto,
  ) {
    return this.organisationsService.updateCustomQuestion(
      organisationId,
      questionId,
      updateCustomQuestionDto,
    );
  }

  @Delete(':organisationId/custom-questions/:questionId')
  @Roles(RolesEnum.CompanyAdmin)
  async deleteCustomQuestion(
    @Param('organisationId') organisationId: string,
    @Param('questionId') questionId: string,
  ) {
    return this.organisationsService.deleteCustomQuestion(
      organisationId,
      questionId,
    );
  }

  @Get(':organisationId/custom-questions')
  @Roles(RolesEnum.CompanyAdmin)
  async getCustomQuestions(
    @Param('organisationId') organisationId: string,
    @Query() paginationQueryDto: PaginationQueryDto,
  ) {
    return this.organisationsService.getCustomQuestions(
      organisationId,
      paginationQueryDto,
    );
  }

  @Post(':organisationId/status')
  @Roles(RolesEnum.SuperAdmin)
  async updateStatus(
    @Param('organisationId') organisationId: string,
    @Body() { status }: { status: OrganisationStatusEnum },
  ) {
    return this.organisationsService.toggleStatus(organisationId, status);
  }

  @Get(':organisationId/available-crms')
  @Roles(RolesEnum.CompanyAdmin)
  async getAvailableCRMs() {
    return this.organisationsService.getAvailableCRMs();
  }

  @Patch(':organisationId/toggle-feature')
  @Roles(RolesEnum.SuperAdmin, RolesEnum.CompanyAdmin)
  @ApiResponse({ type: OrganisationViewDTO })
  async toggleFeature(
    @Param('organisationId') organisationId: string,
    @Body() toggleFeatureDto: ToggleFeatureDto,
  ): Promise<OrganisationViewDTO> {
    const org = await this.organisationsService.toggleFeature(
      organisationId,
      toggleFeatureDto,
    );
    return plainToInstance(OrganisationViewDTO, org, {
      excludeExtraneousValues: true,
    });
  }
}
