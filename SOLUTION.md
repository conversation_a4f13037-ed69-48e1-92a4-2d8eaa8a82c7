# Interview Flow Builder V2 - Architecture Report & Implementation Plan

## Executive Summary

This report outlines the architectural approach for implementing a dynamic interview flow builder with branching logic while maintaining complete backward compatibility with the existing Vue.js application. The solution introduces a parallel V2 system that operates independently, ensuring zero regression risk to the current production system.

### Key Decisions
- **Parallel Architecture**: V1 (Vue) and V2 (Next.js) operate as separate systems
- **Client Segregation**: Clients are versioned at creation (no cross-visibility)
- **Schema Extension**: Shared database with V2-specific enhancements
- **Stateless Branching**: Page-level decisions without cumulative state
- **PATCH-Centric**: Maintains familiar API patterns with enhanced responses

## Table of Contents
1. [Architectural Overview](#architectural-overview)
2. [Design Theory & Principles](#design-theory--principles)
3. [Technical Implementation](#technical-implementation)
4. [Execution Plan](#execution-plan)
5. [Risk Mitigation](#risk-mitigation)
6. [Timeline & Phases](#timeline--phases)

## Architectural Overview

### System Architecture Diagram

```
┌─────────────────┐         ┌─────────────────┐
│   Vue.js App    │         │  Next.js App    │
│   (Legacy)      │         │   (New V2)      │
└────────┬────────┘         └────────┬────────┘
         │                           │
         ▼                           ▼
┌─────────────────┐         ┌─────────────────┐
│  API V1         │         │  API V2         │
│  Controllers    │         │  Controllers    │
└────────┬────────┘         └────────┬────────┘
         │                           │
         ▼                           ▼
┌─────────────────┐         ┌─────────────────┐
│  V1 Services    │         │  V2 Services    │
│                 │         │  (extends V1)   │
└────────┬────────┘         └────────┬────────┘
         │                           │
         └───────────┬───────────────┘
                     ▼
            ┌─────────────────┐
            │    MongoDB      │
            │  (Shared DB)    │
            └─────────────────┘
```

### Core Components

1. **Database Layer**: Shared MongoDB with version-aware schemas
2. **Service Layer**: V2 services extend V1 for code reuse where safe
3. **Controller Layer**: Completely separate V1 and V2 controllers
4. **Client Applications**: Isolated Vue and Next.js applications

## Design Theory & Principles

### 1. Separation of Concerns
The architecture strictly separates V1 and V2 concerns to prevent regression:
- **Data Isolation**: `clientVersion` field filters data access
- **Code Isolation**: Separate controllers prevent accidental changes
- **Feature Isolation**: V2 features invisible to V1 clients

### 2. Stateless Design Philosophy
Given the constraint of no cumulative state access:
- **Page-Level Decisions**: Branching logic evaluates only current page data
- **Navigation History**: Separate from answer state, purely for UI navigation
- **Idempotent Operations**: Each PATCH request contains complete page state

### 3. Progressive Enhancement
The schema design allows incremental feature addition:
- **Optional Fields**: V2 fields don't break V1 parsing
- **Backward Compatibility**: V1 clients ignore V2 fields
- **Forward Compatibility**: V2 can read V1 data

### 4. Identity Resolution
Solving the page identification problem:
- **Unique IDs**: Every page gets a MongoDB ObjectId
- **Type Preservation**: `name` field indicates page type
- **Reference Integrity**: Branching rules use IDs, not names

## Technical Implementation

### Schema Enhancements

#### 1. Client Schema Updates
```typescript
// File: src/schemas/client.schema.ts
@Prop({ 
  type: Number, 
  default: 1, 
  index: true,
  enum: [1, 2]
})
clientVersion: number;
```

#### 2. Interview Schema Updates
```typescript
// File: src/schemas/interview.schema.ts
@Prop({ type: [String], default: [] })
navigationHistory: string[];

@Prop({ type: Number, default: 1 })
flowVersion: number;

@Prop({ type: Date })
lastNavigationUpdate: Date;
```

#### 3. InterviewPage Schema Updates
```typescript
// File: src/schemas/interview-page.schema.ts
@Schema()
export class BranchingRule {
  @Prop({ type: Object, required: true })
  condition: {
    field: string;
    operator: 'equals' | 'notEquals' | 'exists' | 'notExists' | 'contains';
    value?: any;
  };
  
  @Prop({ required: true })
  targetPageId: string;
  
  @Prop({ default: 0 })
  priority: number;
}

// In InterviewPage class
@Prop({ 
  type: String, 
  default: () => new mongoose.Types.ObjectId().toString() 
})
_id: string;

@Prop({ type: [BranchingRuleSchema], required: false })
branchingRules?: BranchingRule[];

@Prop({ required: false })
componentType?: string;
```

### Service Architecture

#### Navigation Evaluation Logic
```typescript
// File: src/services/v2/navigation.service.ts
@Injectable()
export class NavigationService {
  evaluateBranchingRules(
    rules: BranchingRule[],
    pageData: any
  ): string | null {
    // Sort by priority
    const sortedRules = rules.sort((a, b) => b.priority - a.priority);
    
    for (const rule of sortedRules) {
      if (this.evaluateCondition(rule.condition, pageData)) {
        return rule.targetPageId;
      }
    }
    
    return null;
  }
  
  private evaluateCondition(
    condition: BranchingRule['condition'],
    data: any
  ): boolean {
    const fieldValue = this.getNestedValue(data, condition.field);
    
    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value;
      case 'notEquals':
        return fieldValue !== condition.value;
      case 'exists':
        return fieldValue !== undefined && fieldValue !== null;
      case 'notExists':
        return fieldValue === undefined || fieldValue === null;
      case 'contains':
        return Array.isArray(fieldValue) && 
               fieldValue.includes(condition.value);
      default:
        return false;
    }
  }
  
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((curr, prop) => curr?.[prop], obj);
  }
}
```

### API Response Structure

```typescript
// File: src/dto/v2/interview-response.dto.ts
export class NavigationMetadata {
  currentPageId: string;
  nextPageId?: string;
  previousPageId?: string;
  history: string[];
  canGoBack: boolean;
  canGoForward: boolean;
  isComplete: boolean;
  progress: {
    current: number;
    total: number;
    percentage: number;
    completedPages: number;
    remainingPages: number;
  };
  branchingApplied?: {
    ruleId?: string;
    condition?: string;
    targetPageId?: string;
  };
}

export class InterviewV2Response {
  interview: Interview;
  navigation: NavigationMetadata;
  timestamp: Date;
}
```

## Execution Plan

### Phase 1: Foundation (Week 1)

#### Day 1-2: Schema Updates
1. **File: `src/schemas/client.schema.ts`**
   - Add `clientVersion` field
   - Add index for performance
   - Update schema exports

2. **File: `src/schemas/interview.schema.ts`**
   - Add `navigationHistory` array
   - Add `flowVersion` field
   - Add `lastNavigationUpdate` timestamp

3. **File: `src/schemas/interview-page.schema.ts`**
   - Create `BranchingRule` sub-schema
   - Add `_id` field with auto-generation
   - Add `branchingRules` array
   - Add `componentType` field

4. **File: `src/schemas/interview-template.schema.ts`**
   - Add `flowVersion` to template
   - Add `v2Metadata` object for future extensions

#### Day 3-4: Service Layer
5. **File: `src/services/v2/navigation.service.ts`** (NEW)
   - Implement branching rule evaluation
   - Create navigation state management
   - Add progress calculation logic

6. **File: `src/services/v2/interviews-v2.service.ts`** (NEW)
   - Extend base InterviewsService
   - Override update method
   - Implement navigation history management
   - Add V2-specific query filters

7. **File: `src/services/v2/interview-templates-v2.service.ts`** (NEW)
   - Handle V2 template creation
   - Ensure page ID generation
   - Validate branching rule references

#### Day 5: DTOs and Validators
8. **File: `src/dto/v2/update-interview-v2.dto.ts`** (NEW)
   ```typescript
   export class UpdateInterviewV2Dto {
     @IsNotEmpty()
     @ValidateNested()
     @Type(() => InterviewPageV2Dto)
     page: InterviewPageV2Dto;
   }
   ```

9. **File: `src/dto/v2/interview-page-v2.dto.ts`** (NEW)
   ```typescript
   export class InterviewPageV2Dto {
     @IsNotEmpty()
     @IsString()
     _id: string; // Required in V2
     
     @IsString()
     name: string;
     
     @IsObject()
     data: any;
     
     @IsEnum(InterviewPageStatusEnum)
     status: InterviewPageStatusEnum;
     
     @IsBoolean()
     filled: boolean;
   }
   ```

10. **File: `src/dto/v2/navigation-metadata.dto.ts`** (NEW)
    - Define navigation response structure
    - Add validation decorators

### Phase 2: Controllers & Guards (Week 2)

#### Day 6-7: Controller Implementation
11. **File: `src/controllers/v2/interviews-v2.controller.ts`** (NEW)
    ```typescript
    @Controller('api/v2/interviews')
    @UseGuards(AuthGuard('jwt'), ClientVersionGuard)
    @ApiTags('interviews-v2')
    export class InterviewsV2Controller {
      // Implementation
    }
    ```

12. **File: `src/controllers/v2/interview-templates-v2.controller.ts`** (NEW)
    - CRUD operations for V2 templates
    - Branching rule validation endpoints

13. **File: `src/controllers/v2/clients-v2.controller.ts`** (NEW)
    - Filter to show only V2 clients
    - Ensure clientVersion = 2 on creation

#### Day 8: Guards and Interceptors
14. **File: `src/guards/client-version.guard.ts`** (NEW)
    ```typescript
    @Injectable()
    export class ClientVersionGuard implements CanActivate {
      canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();
        // Verify client version matches endpoint version
        return true;
      }
    }
    ```

15. **File: `src/interceptors/v2-response.interceptor.ts`** (NEW)
    - Add V2-specific response headers
    - Transform responses for V2 format

### Phase 3: Module Organization (Week 2)

#### Day 9-10: Module Structure
16. **File: `src/modules/v2/interviews-v2.module.ts`** (NEW)
    ```typescript
    @Module({
      imports: [
        MongooseModule.forFeature([
          { name: Interview.name, schema: InterviewSchema },
          { name: InterviewTemplate.name, schema: InterviewTemplateSchema }
        ]),
        NavigationModule,
      ],
      controllers: [InterviewsV2Controller],
      providers: [InterviewsV2Service, NavigationService],
      exports: [InterviewsV2Service]
    })
    export class InterviewsV2Module {}
    ```

17. **File: `src/modules/v2/v2-api.module.ts`** (NEW)
    - Aggregate all V2 modules
    - Configure V2-specific providers

18. **File: `src/app.module.ts`** (UPDATE)
    ```typescript
    @Module({
      imports: [
        // ... existing imports
        V2ApiModule, // Add V2 module
      ],
    })
    export class AppModule {}
    ```

### Phase 4: Migration & Testing (Week 3)

#### Day 11-12: Migration Scripts
19. **File: `scripts/migrate-v2-page-ids.ts`** (NEW)
    ```typescript
    // Script to add IDs to existing pages for V2 templates
    async function migratePageIds() {
      const templates = await InterviewTemplate.find({ flowVersion: 2 });
      for (const template of templates) {
        template.pages.forEach(page => {
          if (!page._id) {
            page._id = new mongoose.Types.ObjectId().toString();
          }
        });
        await template.save();
      }
    }
    ```

20. **File: `scripts/seed-v2-templates.ts`** (NEW)
    - Create sample V2 templates
    - Include branching examples

#### Day 13-14: Testing
21. **File: `src/services/v2/__tests__/navigation.service.spec.ts`** (NEW)
    - Unit tests for branching logic
    - Edge case handling

22. **File: `src/controllers/v2/__tests__/interviews-v2.e2e-spec.ts`** (NEW)
    - E2E tests for complete flows
    - Branching scenario tests

### Phase 5: Documentation & Deployment (Week 4)

#### Day 15-16: Documentation
23. **File: `docs/v2-api-specification.md`** (NEW)
    - OpenAPI specification for V2 endpoints
    - Migration guide for frontend

24. **File: `docs/branching-rules-guide.md`** (NEW)
    - Examples of branching configurations
    - Best practices

#### Day 17-18: Deployment Preparation
25. **File: `.env.example`** (UPDATE)
    ```bash
    # V2 Feature Flags
    ENABLE_V2_API=true
    V2_CLIENT_CREATION=true
    ```

26. **File: `src/config/feature-flags.config.ts`** (NEW)
    - Feature flag configuration
    - Gradual rollout support

## Risk Mitigation

### Technical Risks
1. **Schema Migration Failures**
   - Mitigation: Comprehensive migration scripts with rollback
   - Testing: Staging environment validation

2. **Performance Degradation**
   - Mitigation: Indexed `clientVersion` field
   - Monitoring: APM for V2 endpoints

3. **Branching Logic Errors**
   - Mitigation: Extensive unit testing
   - Validation: Rule consistency checks

### Business Risks
1. **Client Confusion**
   - Mitigation: Clear version indicators in UI
   - Training: Documentation and guides

2. **Data Inconsistency**
   - Mitigation: Transaction support for updates
   - Validation: Data integrity checks

## Timeline & Phases

### Development Timeline
- **Week 1**: Foundation - Schemas and core services
- **Week 2**: API Layer - Controllers and modules
- **Week 3**: Testing and migration tools
- **Week 4**: Documentation and deployment

### Rollout Strategy
1. **Phase 1**: Deploy V2 API (feature-flagged)
2. **Phase 2**: Internal testing with sample clients
3. **Phase 3**: Beta rollout to selected organizations
4. **Phase 4**: General availability

### Success Metrics
- Zero regression in V1 functionality
- V2 API response time < 200ms
- 100% backward compatibility maintained
- Successful branching in 95%+ of flows

## Conclusion

This architecture provides a robust foundation for implementing dynamic interview flows while maintaining complete backward compatibility. The parallel system approach ensures zero risk to existing clients while enabling rapid innovation in the V2 system. The stateless design aligns with modern API best practices and enables horizontal scaling as usage grows.

The file-by-file execution plan provides a clear roadmap for implementation, with each phase building upon the previous to minimize integration risks. By following this plan, we can deliver a powerful new interview flow system that meets all requirements while maintaining system stability and performance.