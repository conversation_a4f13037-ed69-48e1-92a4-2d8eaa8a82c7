import { PartialType } from '@nestjs/mapped-types';
import { CreateClientDto } from './create-client.dto';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { ApiProperty } from '@nestjs/swagger';
import {IsBoolean, IsDate, IsEnum, IsNumber, IsOptional} from 'class-validator';

export class UpdateClientDto extends PartialType(CreateClientDto) {
  @ApiProperty()
  @IsEnum(ClientStatusEnum)
  @IsOptional()
  status?: ClientStatusEnum;

  @ApiProperty()
  @IsDate()
  @IsOptional()
  completedAt?: Date;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  fileUploadsNo?: number;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  sendNow?: boolean;
}
