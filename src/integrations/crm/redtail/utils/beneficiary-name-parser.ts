import { BeneficiaryDto } from 'src/integrations/crm/redtail/form-mappers/dto/add-beneficiary-request.dto';

export const computeBeneficiaryName = (beneficiary: BeneficiaryDto) => {
  const { firstName, lastName, dob } = beneficiary;
  return `${lastName}, ${firstName}: ${dob}`;
};

export const getBeneficaryFullName = (beneficiaryName: string) => {
  if (!beneficiaryName || !beneficiaryName.includes(': ')) return '';
  const name = beneficiaryName.split(': ')[0];
  return name;
};

export const getBeneficaryFirstName = (beneficiaryName: string) => {
  if (!beneficiaryName || !beneficiaryName.includes(', ')) return '';

  const firstName = beneficiaryName.split(', ')[1];

  return firstName.includes(':') ? firstName.split(':')[0] : firstName;
};

export const getBeneficaryLastName = (beneficiaryName: string) => {
  if (!beneficiaryName || !beneficiaryName.includes(', ')) return '';

  const lastName = beneficiaryName.split(', ')[0];
  return lastName.includes(':') ? lastName.split(':')[0] : lastName;
};

export const getDobFromBeneficiaryName = (beneficiaryName: string) => {
  if (!beneficiaryName || !beneficiaryName.includes(': ')) return '';
  const dob = beneficiaryName.split(': ')[1];
  return dob;
};
