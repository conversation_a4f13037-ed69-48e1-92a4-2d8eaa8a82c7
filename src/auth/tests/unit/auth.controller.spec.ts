import { UnauthorizedException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { AuthController } from 'src/auth/auth.controller';
import { AuthService } from 'src/auth/auth.service';
import { InviteUserDto } from 'src/auth/dto/invite.dto';
import { MfaDto } from 'src/auth/dto/mfa.dto';
import { SetNewPasswordDto } from 'src/auth/dto/new-password.dto';
import { ResetPasswordDto } from 'src/auth/dto/reset.dto';
import { SetPasswordDto } from 'src/auth/dto/set-password.dto';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';

describe('AuthController', () => {
  let authController: AuthController;
  let authService: jest.Mocked<AuthService>;

  beforeEach(async () => {
    const AuthServiceMock = {
      authenticateUser: jest.fn().mockReturnValue({
        accessToken: 'token',
        refreshToken: 'refreshToken',
      }),
      inviteUser: jest.fn().mockResolvedValue(undefined),
      resetPassword: jest.fn().mockResolvedValue(undefined),
      setPassword: jest.fn().mockResolvedValue(undefined),
      respondToMfaChallenge: jest.fn().mockResolvedValue({
        accessToken: 'token',
        refreshToken: 'refreshToken',
      }),
      newUserPassword: jest.fn().mockResolvedValue(undefined),
      refreshToken: jest.fn().mockResolvedValue({
        accessToken: 'newToken',
        refreshToken: 'newRefreshToken',
      }),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [{ provide: AuthService, useValue: AuthServiceMock }],
    }).compile();

    authController = module.get<AuthController>(AuthController);
    authService = module.get<AuthService>(
      AuthService,
    ) as jest.Mocked<AuthService>;
  });

  describe('login', () => {
    let response: any;

    beforeEach(() => {
      response = {
        cookie: jest.fn(),
      };
    });

    it('should call AuthService authenticateUser method and set a cookie with its result', async () => {
      authService.authenticateUser.mockResolvedValue({
        accessToken: 'token',
        refreshToken: 'refreshToken',
      } as any);

      await authController.login(
        { email: 'email', password: 'password' },
        response,
      );

      expect(authService.authenticateUser).toHaveBeenCalledWith(
        expect.objectContaining({
          email: 'email',
          password: 'password',
        }),
        response,
      );
    });

    it('should throw UnauthorizedException if AuthService throws UnauthorizedException', async () => {
      authService.authenticateUser.mockRejectedValueOnce(
        new UnauthorizedException(),
      );

      await expect(
        authController.login(
          { email: 'email', password: 'password' },
          response,
        ),
      ).rejects.toThrow(UnauthorizedException);
    });
  });

  describe('adminResetUserPassword', () => {
    it('should call AuthService resetPassword method', async () => {
      const dto: ResetPasswordDto = { email: '<EMAIL>' };
      await authController.adminResetUserPassword(dto);
      expect(authService.resetPassword).toHaveBeenCalledWith(dto);
    });
  });

  describe('setPassword', () => {
    it('should call AuthService setPassword method', async () => {
      const dto: SetPasswordDto = {
        email: '<EMAIL>',
        password: 'newpassword',
        confirmationCode: 'random-token',
      };
      await authController.setPassword(dto);
      expect(authService.setPassword).toHaveBeenCalledWith(dto);
    });
  });

  describe('verifyMfa', () => {
    const response: any = {};

    it('should call AuthService respondToMfaChallenge method', async () => {
      const dto: MfaDto = {
        mfaCode: '12321321',
        session: '123456',
        email: '<EMAIL>',
      };
      await authController.verifyMfa(dto, response);
      expect(authService.respondToMfaChallenge).toHaveBeenCalled();
    });
  });

  describe('setNewPassword', () => {
    it('should call AuthService newUserPassword method', async () => {
      const dto: SetNewPasswordDto = {
        email: '<EMAIL>',
        newPassword: 'newpassword',
        oldPassword: 'oldpassword',
      };
      await authController.setNewPassword(dto);
      expect(authService.newUserPassword).toHaveBeenCalled();
    });
  });

  describe('refreshToken', () => {
    const expressResponse: any = {};

    it('should call AuthService refreshToken method and return the new tokens', async () => {
      const response = await authController.refreshToken(
        {
          refreshToken: 'oldRefreshToken',
        },
        expressResponse,
      );
      expect(authService.refreshToken).toHaveBeenCalledWith(
        { refreshToken: 'oldRefreshToken' },
        expressResponse,
      );
    });
  });
});
