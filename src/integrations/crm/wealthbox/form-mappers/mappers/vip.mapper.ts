import { HttpException } from '@nestjs/common';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { VipRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/vip-request.dto';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { GenericUserDefinedFieldsEnum } from 'src/integrations/crm/types/generic/user-defined-fields.enum';
import { computeCompanyAssociationValue } from 'src/integrations/crm/redtail/utils/company-association-parser';
import {
  insertIntoUserContentField,
  removeFromUser<PERSON>ontentField,
} from 'src/integrations/crm/wealthbox/utils/data-parsing/general-text-field';

export class WealthboxVIPMapper implements FormUpdateMapper {
  constructor(private WealthboxCrm: WealthboxCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: VipRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    const crmContact = await this.WealthboxCrm.contactService.getContact(
      contact.crmClientId,
    );

    if (!pageData.companyName) {
      const newBackgroundInfo = removeFromUserContentField(
        crmContact.background_info,
        GenericUserDefinedFieldsEnum.COMPANY_ASSOCIATION,
      );

      if (newBackgroundInfo) {
        await this.WealthboxCrm.contactService.updateContact(
          contact.crmClientId,
          {
            background_information: newBackgroundInfo,
          },
        );
      }
      return;
    }

    await this.WealthboxCrm.contactService.updateContact(contact.crmClientId, {
      background_information: insertIntoUserContentField(
        crmContact.background_info,
        {
          [GenericUserDefinedFieldsEnum.COMPANY_ASSOCIATION]:
            computeCompanyAssociationValue(pageData),
        },
      ),
    });
  }
}
