import { InjectQueue } from '@nestjs/bullmq';
import {
    HttpException,
    HttpStatus,
    Inject,
    Injectable,
    forwardRef,
} from '@nestjs/common';
import { FlowProducer, Job, JobNode, Queue } from 'bullmq';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { CreateClientFlowInput } from 'src/clients/types/client-flow-input';
import { ClientQueueJobType } from 'src/clients/types/client-queue-job.enum';
import { CRM_QUEUE } from 'src/integrations/crm/constants/crm.constants';
import { QueueRegistryService } from 'src/queue-log/queue-registry.service';
import { CLIENT_QUEUE } from 'src/shared/constants/client.constant';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { Logger } from 'winston';
import { Client } from '../schemas/clients.schema';
import { generateClientHash } from '../utils/queue.utils';

@Injectable()
export class ClientFlowService {
  private readonly flowProducer: FlowProducer;

  constructor(
    @InjectQueue(CLIENT_QUEUE.NAME) private readonly clientQueue: Queue,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    @Inject(forwardRef(() => AdvisorsCrmService))
    private readonly advisorsCrmService: AdvisorsCrmService,
    private readonly queueRegistryService: QueueRegistryService,
  ) {
    this.flowProducer = new FlowProducer({ connection: this.clientQueue.opts.connection });
  }

  /**
   * Returns the FlowProducer instance
   */
  getFlowProducer(): FlowProducer {
    return this.flowProducer;
  }

  /**
   * Finds the first failed job in a flow by traversing the job tree
   * 
   * @param flow - The flow to search for failed jobs
   * @returns Promise with the failed job and its queue, or null if none found
   */
  async findFailedJobInFlow(flow: JobNode): Promise<{ job: Job, queue: Queue } | null> {
    const jobsToCheck: JobNode[] = [flow];
    
    while (jobsToCheck.length > 0) {
      const currentJob = jobsToCheck.shift();
      
      // Get the queue for this job
      const queue = this.queueRegistryService.getQueue(currentJob.job.queueName);
      if (!queue) {
        this.logger.warn(`Queue ${currentJob.job.queueName} not found for job ${currentJob.job.name}`);
        continue;
      }
      
      // Get the job instance
      try {
        const jobId = currentJob.job.opts?.jobId || currentJob.job.id || '';
        if (!jobId) {
          this.logger.warn(`No job ID found for flow job ${currentJob.job.name}`);
          continue;
        }
        const job = await queue.getJob(jobId);
        if (job) {
          const state = await job.getState();
          if (state === 'failed') {
            return { job, queue };
          }
        }
      } catch (error) {
        this.logger.error(`Error retrieving job for ${currentJob.job.name}: ${error.message}`);
      }
      
      // Add child jobs to the queue
      if (currentJob.children && currentJob.children.length > 0) {
        jobsToCheck.push(...currentJob.children);
      }
    }
    
    return null;
  }

  /**
   * Creates a client flow with jobs for updating status, sending notifications,
   * creating interviews, and integrating with CRM.
   *
   * @param input - Data needed to create the client flow
   * @returns A promise that resolves when the flow is created
   * @throws HttpException if a flow for this client is already in progress
   */
  async createClientFlow(input: CreateClientFlowInput) {
    const idempotencyKey = generateClientHash(input.organisationId, {
      organisationId: input.organisationId,
      primaryContact: input.primaryContact,
      primaryAdvisor: { id: input.primaryAdvisor._id },
    });

    // Check for existing flow
    const existingFlow = await this.flowProducer.getFlow({
      id: idempotencyKey,
      queueName: CLIENT_QUEUE.NAME,
    });

    if (existingFlow) {
      throw new HttpException('Client already in progress', HttpStatus.BAD_REQUEST);
    }

    const crmIntegration = this.advisorsCrmService.getCRMIntegrationFromAdvisor(input.primaryAdvisor);
    const crm = crmIntegration?.integrationConfig?.name?.toUpperCase();

    await this.flowProducer.add({
      name: ClientQueueJobType.UPDATE_STATUS,
      queueName: CLIENT_QUEUE.NAME,
      data: { clientId: input.clientId, status: ClientStatusEnum.Sent },
      opts: {
        jobId: idempotencyKey,
      },
      children: [
        {
          name: ClientQueueJobType.SEND_NOTIFICATION,
          queueName: CLIENT_QUEUE.NAME,
          data: {
            clientId: input.clientId,
            notificationMethods: input.notificationMethods
          },
          opts: {
            jobId: `${idempotencyKey}-notification`,
          },
          children: [
            {
              name: ClientQueueJobType.CREATE_INTERVIEW,
              data: {
                clientId: input.clientId,
                organisationId: input.organisationId,
                createClientDto: {
                  primaryContact: input.primaryContact,
                  secondaryContact: input.secondaryContact,
                  customTemplates: input.customTemplates,
                  doClientProfiling: input.doClientProfiling
                }
              },
              queueName: CLIENT_QUEUE.NAME,
              opts: {
                jobId: `${idempotencyKey}-interview`,
              },
              children: [
                {
                  name: CRM_QUEUE[crm].JOBS.UPSERT_TO_CRM,
                  data: {
                    clientId: input.clientId,
                    organisationId: input.organisationId,
                    data: {
                      primaryContact: input.primaryContact,
                      secondaryContact: input.secondaryContact,
                      primaryAdvisor: input.primaryAdvisor,
                      primaryCSA: input.primaryCSA,
                    }
                  },
                  queueName: CRM_QUEUE[crm].NAME,
                  opts: {
                    jobId: `${idempotencyKey}-crm`,
                  },
                },
              ],
            },
          ],
        },
      ],
    });
  }

  /**
   * Cleans up flow jobs associated with a client.
   * This method attempts to remove all queue jobs related to the client
   * based on the idempotency key generated from client information.
   * 
   * @param client - The client whose flow jobs need to be cleaned up
   * @returns A promise that resolves when cleanup is complete
   */
  async cleanUpClientFlowJobs(client: Client): Promise<void> {
    try {
      // Only attempt cleanup if we have all the necessary data
      if (!client.organisationId || !client.primaryContact || !client.primaryAdvisor?.id) {
        this.logger.warn(`Missing required data for client ${client._id} to clean up flow jobs`);
        return;
      }

      const idempotencyKey = generateClientHash(
        client.organisationId.toString(),
        {
          organisationId: client.organisationId.toString(),
          primaryContact: client.primaryContact as any,
          primaryAdvisor: { id: client.primaryAdvisor.id.toString() },
        }
      );

      // Try to remove the flow using the queue's obliterate method
      try {
        // In BullMQ, we need to get the flow and then obliterate it
        const flow = await this.flowProducer.getFlow({
          id: idempotencyKey,
          queueName: CLIENT_QUEUE.NAME,
        });
        
        if (flow) {
          // If FlowProducer doesn't have removeFlow, we can remove the parent job
          // which should cascade to children
          await this.clientQueue.remove(idempotencyKey);
        }
      } catch (flowError) {
        this.logger.debug(`Error removing flow: ${flowError.message}`);
      }

      // Also clean up related jobs individually
      const jobIds = [
        `${idempotencyKey}-notification`,
        `${idempotencyKey}-status`,
        `${idempotencyKey}-interview`,
        `${idempotencyKey}-crm`,
      ];

      await Promise.allSettled(
        jobIds.map(jobId => 
          this.clientQueue.remove(jobId).catch(e => 
            this.logger.debug(`Job ${jobId} may have already been processed: ${e.message}`)
          )
        )
      );

      this.logger.info(`Cleaned up flow jobs for client ${client._id}`);
    } catch (error) {
      // Log the error but don't fail the client operation
      this.logger.warn(`Failed to clean up flow job for client ${client._id}: ${error.message}`);
    }
  }
} 