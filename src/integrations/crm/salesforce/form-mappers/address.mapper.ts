import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { SalesforceCRM } from 'src/integrations/crm/salesforce/crm.salesforce';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { SalesforceAddressUpdateRequestDto } from '../dto/salesforce.dto';
import { Country } from 'src/shared/types/general/country.enum';

export class SalesforceAddressMapper implements FormUpdateMapper {
  constructor(private salesforceCRM: SalesforceCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    update: SalesforceAddressUpdateRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;    

    let addressBody;
    if (update.mailingAddress) {
      addressBody = {
        MailingStreet: `${update.mailingAddress.line1}${update.mailingAddress.line2 ? update.mailingAddress.line2 : ''}`,
        MailingCity: update.mailingAddress.city,
        MailingState: update.mailingAddress.state,
        MailingPostalCode: update.mailingAddress.zip,
        MailingCountry: Country.US,
        OtherStreet: `${update.legalAddress.line1}${update.legalAddress.line2 ? update.legalAddress.line2 : ''}`,
        OtherCity: update.legalAddress.city,
        OtherState: update.legalAddress.state,
        OtherPostalCode: update.legalAddress.zip,
        OtherCountry: Country.US,
      }
    }
    if (!update.mailingAddress) {
      addressBody = {
        MailingStreet: `${update.legalAddress.line1}${update.legalAddress.line2 ? update.legalAddress.line2 : ''}`,
        MailingCity: update.legalAddress.city,
        MailingState: update.legalAddress.state,
        MailingPostalCode: update.legalAddress.zip,
        MailingCountry: Country.US,
      }
    }
    
    // Standard contact - update mailing address fields
    await this.salesforceCRM.contactService.updateContact(contact.crmClientId, addressBody);
  }
} 