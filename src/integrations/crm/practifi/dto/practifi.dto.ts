import { DefaultTagsEnum, AccountStage } from "../types/enums";
export interface PractifiContactDto {
  // Standard Salesforce Contact fields
  Id?: string;
  FirstName: string;
  LastName: string;
  Email?: string;
  Description?: string;
  practifi__Work_Phone__c?: string;
  MobilePhone?: string;
  HomePhone?: string;
  OtherPhone?: string;
  Title?: string;
  Birthdate?: string;
  AccountId?: string;
  
  // Standard Mailing Address fields
  MailingStreet?: string;
  MailingCity?: string;
  MailingState?: string;
  MailingPostalCode?: string;
  MailingCountry?: string;
  OtherStreet?: string;
  OtherCity?: string;
  OtherState?: string;
  OtherPostalCode?: string;
  OtherCountry?: string;
  practifi__Exclude_Postal_Address_from_Sync__c?: boolean;
  practifi__Exclude_Location_Address_from_Sync__c?: boolean;

  // Custom Practifi fields (with __c suffix)
  practifi__SSN__c?: string;
  practifi__Country_Of_Citizenship__c?: string;
  practifi__Country_Of_Residence__c?: string;
  practifi__Citizenship_Status__c?: string;
  practifi__Middle_Name__c?: string;
  practifi__Suffix__c?: string;
  practifi__Employment_Status__c?: string;
  practifi__Employer__c?: string;
  practifi__Occupation__c?: string;

  // Dynamic fields for beneficiaries
  [key: string]: any; // Allow dynamic field names for beneficiaries and custom questions
}

export interface PractifiHouseholdDto {
  Name: string;
  Type: string;
  practifi__Primary_Member__c?: string;
  practifi__Spouse__c?: string;
  practifi__Client_Stage__c: AccountStage;
  RecordTypeId?: string;
}

export interface PractifiIndividualDto {
  FirstName: string;
  LastName: string;
  PersonEmail: string;
  practifi__Email__c: string;
  PersonMobilePhone: string;
  PersonHomePhone?: string;
  practifi__Work_Phone__pc?: string;
  PersonOtherPhone?: string;
  Type: string;
  RecordTypeId?: string;
  practifi__Country_Of_Citizenship__pc?: string;
  practifi__Country_Of_Residence__pc?: string;
  practifi__Citizenship_Status__pc?: string;
  practifi__Middle_Name__pc?: string;
  practifi__Suffix__pc?: string;
  PersonMailingStreet?: string;
  PersonMailingCity?: string;
  PersonMailingState?: string;
  PersonMailingPostalCode?: string;
  PersonMailingCountry?: string;
  PersonOtherStreet?: string;
  PersonOtherCity?: string;
  PersonOtherState?: string;
  PersonOtherPostalCode?: string;
  PersonOtherCountry?: string;
  practifi__SSN__pc?: string;
  PersonBirthdate?: string;
  practifi__Employment_Status__pc?: string;
  practifi__Employer__pc?: string;
  PersonTitle?: string;
  practifi__Occupation__pc?: string;
  Description?: string;
}

export interface PractifiAsset {
  Name: string;
  practifi__Client__c: string;
  practifi__Category__c: string;
  practifi__Registration_Type__c: string;
  practifi__Account_Number__c: string;
  practifi__Owner_1__c?: string;
  practifi__Source__c: DefaultTagsEnum,
  practifi__External_Id_1__c: string;
}

export interface PractifiRelationshipData {
  practifi__From_Entity__c?: string;
  practifi__From_Contact__c?: string;
  practifi__To_Entity__c: string;
  practifi__Relationship_Type__c?: string; 
}

export interface PractifiRoleDto {
  Name: string;
  practifi__Asset_Liability__c?: string;
  practifi__Person__c?: string;
  practifi__Entity__c?: string;
  practifi__Role__c: string;
  practifi__Percentage__c: string;
}

export interface PractifiAddressUpdateRequestDto {
  legalAddress: {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    zip: string;
  };

  mailingAddress?: {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    zip: string;
  };
}

export interface PractifiCompanyAddressDto {
  Name: string;
  City: string;
  PostalCode: string;
  State: string;
  Street: string;
  Country: string;
  UsageType: string;
  practifi__Contact__c: string;
}