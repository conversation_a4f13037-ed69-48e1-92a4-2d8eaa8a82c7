import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { AddressUpdateRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/company-request.dto';
import { RedtailCompanyMapper } from 'src/integrations/crm/redtail/form-mappers/company.mapper';
import { RedtailAddressTypeEnum } from 'src/integrations/crm/redtail/types/enums';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { Country } from 'src/shared/types/general/country.enum';
import mongoose from 'mongoose';

// Mock the RedtailCRM class and its methods
jest.mock('src/integrations/crm/redtail/crm.redtail', () => {
  return {
    RedtailCRM: jest.fn().mockImplementation(() => {
      return {
        redtailAddressService: {
          updateContactAddresses: jest.fn(),
        },
      };
    }),
  };
});

describe('RedtailCompanyMapper', () => {
  let redtailCompanyMapper: RedtailCompanyMapper;
  let redtailCrmMock: jest.Mocked<RedtailCRM>;

  beforeEach(() => {
    redtailCrmMock = new RedtailCRM(
      {
        _id: new mongoose.Types.ObjectId(),
      } as any,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
    ) as jest.Mocked<RedtailCRM>;
    redtailCompanyMapper = new RedtailCompanyMapper(redtailCrmMock);
  });

  describe('mapPageData', () => {
    it('should call updateContactAddresses with the correct arguments', async () => {
      const client = { primaryContact: { crmClientId: '1' } } as EnrichedClient;
      const interview = { isPrimary: true } as EnrichedInterview;
      const organisationMock = {} as Organisation;
      const addressUpdateMock: AddressUpdateRequestDto = {
        address: {
          line1: '123 Main St',
          city: 'Test City',
          state: 'TS',
          zip: '12345',
        },
        name: 'Test Company',
      };

      await redtailCompanyMapper.mapPageData(
        interview,
        client,
        organisationMock,
        addressUpdateMock,
      );

      expect(
        redtailCrmMock.redtailAddressService.updateContactAddresses,
      ).toHaveBeenCalledWith(
        client.primaryContact.crmClientId,
        expect.arrayContaining([
          expect.objectContaining({
            addressType: RedtailAddressTypeEnum.WORK,
            address: expect.objectContaining({
              isPrimary: false,
              streetAddress: '123 Main St',
              city: 'Test City',
              state: 'TS',
              zip: '12345',
              country: Country.US,
            }),
          }),
        ]),
      );
    });
  });
});
