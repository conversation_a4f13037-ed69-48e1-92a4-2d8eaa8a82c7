import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PractifiCRM } from '../crm.practifi';

interface VIPPageData {
  companyName: string;
  tickerSymbol?: string;
}

export class PractifiVIPMapper implements FormUpdateMapper {
  constructor(private practifiCRM: PractifiCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: VIPPageData,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    const crmClientId = contact.crmClientId;

    const record = await this.practifiCRM.contactService.getContact(crmClientId);

    // if user answers no
    if(!pageData.companyName && !pageData.tickerSymbol) {
      return;
    }

    // Practifi does not have these fields atm
    await this.practifiCRM.contactService.updateContact(crmClientId, {
      Description: `${record.Description ? record.Description : ''}\n Company name: ${pageData.companyName} Ticker Symbol: ${pageData.tickerSymbol} \n`,
    });
  }
}
