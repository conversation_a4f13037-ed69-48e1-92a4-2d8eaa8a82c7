import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { SalesforceCRM } from 'src/integrations/crm/salesforce/crm.salesforce';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

interface NamePageData {
  firstName: string;
  lastName: string;
  middleName?: string;
  suffix?: string;
}

export class SalesforceNameMapper implements FormUpdateMapper {
  constructor(private salesforceCRM: SalesforceCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: NamePageData,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    await this.salesforceCRM.contactService.updateContact(contact.crmClientId, {
      // FirstName: pageData.firstName,
      // LastName: pageData.lastName,
      // MiddleName: pageData.middleName,
      // Suffix: pageData.suffix,
      Onbord_Legal_Name__c: `${pageData.firstName} ${pageData.middleName ? pageData.middleName : ''} ${pageData.lastName} ${pageData.suffix ? pageData.suffix : ''}`,
    });
  }
}