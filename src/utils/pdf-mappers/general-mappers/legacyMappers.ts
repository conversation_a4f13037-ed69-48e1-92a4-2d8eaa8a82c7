import {
  RedtailAccountOwnershipEnum,
  RedtailCitizenshipEnum,
} from 'src/integrations/crm/redtail/types/enums';
import { AddressTypeEnum } from 'src/integrations/crm/types/generic/address.type';
import { EmailTypeEnum } from 'src/integrations/crm/types/generic/email.type';
import { GenericEmploymentStatusEnum } from 'src/integrations/crm/types/generic/employment.status.enum';
import { PhoneTypeEnum } from 'src/integrations/crm/types/generic/phone.type';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { getAccountByFileName } from 'src/utils/pdf-mappers/utils/getAccountByFileName';

export const tabFillingMappers = {
  'clients[0].Form[0].section0[0].border[0].IA-Box-01_Contact[0].IA[0].ia_firm_name[0]':
    {
      field: ['organisation.firmName'],
    },
  'clients[0].Form[0].section0[0].border[0].IA-Box-01_Contact[0].IA[0].ia_master_acct_num[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        return account.masterAccountNumber;
      },
    },
  'clients[0].Form[0].section0[0].border[0].No_Advisor[0]': {
    staticValue: '1',
  },
  'clients[0].Form[0].section2[0].border[0].SchwabOne[0]': {
    staticValue: '1',
  },
  'clients[0].Form[0].section3[0].border[0].Individual[0]': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return account.ownership;
    },
  },
  'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline1[0].legalAddressDetails[0].stateTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const client = interviewData.primaryContact;
        const address = client.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        );
        return address.state;
      },
    },
  'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline1[0].legalAddressDetails[0].zipCodeTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const client = interviewData.primaryContact;
        const address = client.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        );
        return address.zip;
      },
    },
  'account.ownership': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return account.ownership;
    },
  },
  'clients[0].Form[0].section3[0].border[0].JointTenantswithR[0]': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return account.ownership === RedtailAccountOwnershipEnum.JTWROS
        ? '2'
        : 'Off';
    },
  },
  'clients[0].Form[0].section3[0].border[0].TenantsinCommon[0]': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return account.ownership === RedtailAccountOwnershipEnum.JTIC
        ? '3'
        : 'Off';
    },
  },
  'clients[0].Form[0].section3[0].border[0].TenantsbytheEntir[0]': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return account.ownership === RedtailAccountOwnershipEnum.entirety
        ? '4'
        : 'Off';
    },
  },
  'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline1[0].fullName[0].firstNameTF[0]':
    {
      field: ['primaryContact.firstName'],
    },
  'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline1[0].fullName[0].middleNameTF[0]':
    {
      field: ['primaryContact.middleName'],
    },
  'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline1[0].fullName[0].lastNameTF[0]':
    {
      field: ['primaryContact.lastName'],
    },
  'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline1[0].fullName[0].SuffixTF[0]':
    {
      field: ['primaryContact.suffix'],
    },
  'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline1[0].ssnTaxIDTF[0]':
    {
      field: ['primaryContact.taxId'],
    },
  'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline1[0].dobOrTrustDate[0]':
    {
      field: ['primaryContact.dob'],
    },
  'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline1[0].emailAddress[0].emailAddressTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const client = interviewData.primaryContact;
        const email = client.additionalInfo.emails.find(
          (email) => email.type === EmailTypeEnum.HOME,
        );
        return email.address;
      },
    },
  'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline1[0].phoneNumbers[0].homePhoneTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const client = interviewData.primaryContact;
        const phone = client.additionalInfo.phones.find(
          (phone) => phone.type === PhoneTypeEnum.HOME,
        );
        return phone?.number || '';
      },
    },
  'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline1[0].phoneNumbers[0].workPhoneTF[0]':
    {
      // No workPhone field in your data structure
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const client = interviewData.primaryContact;
        const phone = client.additionalInfo.phones.find(
          (phone) => phone.type === PhoneTypeEnum.WORK,
        );
        return phone?.number || '';
      },
    },
  'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline1[0].phoneNumbers[0].mobilePhoneTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const client = interviewData.primaryContact;
        const phone = client.additionalInfo.phones.find(
          (phone) => phone.type === PhoneTypeEnum.MOBILE,
        );
        return phone?.number || '';
      },
    },
  'clients[0].Form[0].section4[0].border[0].Country_ID_baseline1[0].countryIdBaselineInfo[0].citizenshipAndResidence[0].citizenship[0].checkCitizenship[0].USA[0]':
    {
      staticValue: '{CASE}USA:USA{DEFAULT}Off',
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.residence === 'USA' ? 'USA' : 'Off';
      },
    },
  'clients[0].Form[0].section4[0].border[0].Country_ID_baseline1[0].countryIdBaselineInfo[0].citizenshipAndResidence[0].residence[0].checkResidence[0].USA[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.residence === 'USA' ? 'USA' : 'Off';
      },
    },
  'clients[0].Form[0].section4[0].border[0].EmploymentOccupation1[0].employmentInfoCheckBoxes[0].employed[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo?.employmentStatus ===
          GenericEmploymentStatusEnum.EMPLOYED
          ? GenericEmploymentStatusEnum.EMPLOYED
          : 'Off';
      },
    },
  'clients[0].Form[0].section4[0].border[0].EmploymentOccupation1[0].employmentInfoCheckBoxes[0].selfEmployed[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo?.employmentStatus ===
          GenericEmploymentStatusEnum.SELF_EMPLOYED
          ? GenericEmploymentStatusEnum.SELF_EMPLOYED
          : 'Off';
      },
    },
  'clients[0].Form[0].section4[0].border[0].EmploymentOccupation1[0].employmentInfoCheckBoxes[0].retired[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo?.employmentStatus ===
          GenericEmploymentStatusEnum.RETIRED
          ? GenericEmploymentStatusEnum.RETIRED
          : 'Off';
      },
    },
  'clients[0].Form[0].section4[0].border[0].EmploymentOccupation1[0].employmentInfoCheckBoxes[0].homemaker[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo?.employmentStatus ===
          GenericEmploymentStatusEnum.HOMEMAKER
          ? GenericEmploymentStatusEnum.HOMEMAKER
          : 'Off';
      },
    },
  'clients[0].Form[0].section4[0].border[0].EmploymentOccupation1[0].employmentInfoCheckBoxes[0].student[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo?.employmentStatus ===
          GenericEmploymentStatusEnum.STUDENT
          ? GenericEmploymentStatusEnum.STUDENT
          : 'Off';
      },
    },
  'clients[0].Form[0].section4[0].border[0].EmploymentOccupation1[0].employmentInfoCheckBoxes[0].notEmployed[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo?.employmentStatus ===
          GenericEmploymentStatusEnum.NOTEMPLOYED
          ? GenericEmploymentStatusEnum.NOTEMPLOYED
          : 'Off';
      },
    },
  'clients[0].Form[0].section4[0].border[0].EmploymentOccupation1[0].Occupation[0].businessAddressDetails[0].employerName[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const workAddress =
          interviewData.primaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        return workAddress ? workAddress.title : '';
      },
    },
  'clients[0].Form[0].section4[0].border[0].EmploymentOccupation1[0].Occupation[0].businessAddressDetails[0].businessStreet[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const workAddress =
          interviewData.primaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        if (!workAddress) {
          return '';
        }
        return workAddress ? workAddress.street : '';
      },
    },
  'clients[0].Form[0].section4[0].border[0].EmploymentOccupation1[0].Occupation[0].businessAddressDetails[0].City[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const workAddress =
          interviewData.primaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        return workAddress ? workAddress.city : '';
      },
    },
  'clients[0].Form[0].section4[0].border[0].EmploymentOccupation1[0].Occupation[0].businessAddressDetails[0].StateorProvince[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const workAddress =
          interviewData.primaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        return workAddress ? workAddress.state : '';
      },
    },
  'clients[0].Form[0].section4[0].border[0].EmploymentOccupation1[0].Occupation[0].businessAddressDetails[0].ZiporPostalCode[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const workAddress =
          interviewData.primaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        return workAddress ? workAddress.zip : '';
      },
    },
  'clients[0].Form[0].section4[0].border[0].EmploymentOccupation1[0].Occupation[0].businessAddressDetails[0].country[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.residence === 'USA' ? 'USA' : 'Off';
      },
    },
  'clients[0].Form[0].section4[0].border[0].FINRARuleemploymentQues_MA_SA1[0].border[0].FINRARuleEmployemntQues_MA_SA[0].Checkboxes[0].Yes[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo.industryAffiliation
          ? 'true'
          : 'Off';
      },
    },
  'clients[0].Form[0].section4[0].border[0].FINRARuleemploymentQues_MA_SA1[0].border[0].FINRARuleEmployemntQues_MA_SA[0].Checkboxes[0].No[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo.industryAffiliation
          ? 'true'
          : 'Off';
      },
    },
  'clients[0].Form[0].section4[0].border[0].FINRARuleemploymentQues_MA_SA1[0].border[0].FINRARuleEmployemntQues_MA_SA[0].Checkboxes[0].ListOfCompany[0]':
    {
      field: ['primaryContact.industryAffiliation'],
    },
  'clients[0].Form[0].section4[0].border[0].FINRA_Rule_10_percent_Shareholder1[0].#subform[0].Checkboxes[0].No[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo.companyAssociation
          ? 'true'
          : 'Off';
      },
    },
  'clients[0].Form[0].section4[0].border[0].FINRA_Rule_10_percent_Shareholder1[0].#subform[0].Checkboxes[0].Yes[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo.companyAssociation
          ? 'true'
          : 'Off';
      },
    },
  'clients[0].Form[0].section4[0].border[0].FINRA_Rule_10_percent_Shareholder1[0].#subform[0].companyName[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const companyAssociationUdfValue =
          interviewData.primaryContact.additionalInfo.companyAssociation;
        const [companyTicker, companyName] =
          companyAssociationUdfValue.split(':');
        return companyName;
      },
    },
  'clients[0].Form[0].section4[0].border[0].FINRA_Rule_10_percent_Shareholder1[0].#subform[0].tradingSymbol[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const companyAssociationUdfValue =
          interviewData.primaryContact.additionalInfo.companyAssociation;
        const [companyTicker, companyName] =
          companyAssociationUdfValue.split(':');
        return companyTicker;
      },
    },
  // Coapplicant section mappings:
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].fullName[0].firstNameTF[0]':
    {
      field: ['secondaryContact.firstName'],
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].fullName[0].middleNameTF[0]':
    {
      field: ['secondaryContact.middleName'],
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].fullName[0].lastNameTF[0]':
    {
      field: ['secondaryContact.lastName'],
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].fullName[0].SuffixTF[0]':
    {
      field: ['secondaryContact.suffix'],
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].legalAddressTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const address =
          interviewData.secondaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.HOME,
          );
        return address.street;
      },
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].cityTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const address =
          interviewData.secondaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.HOME,
          );
        return address.city;
      },
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].stateTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const address =
          interviewData.secondaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.HOME,
          );
        return address.state;
      },
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].zipCodeTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const address =
          interviewData.secondaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.HOME,
          );
        return address.zip;
      },
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].country[0]':
    {
      staticValue: 'USA',
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].mailingAddressTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const address =
          interviewData.secondaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.MAILING,
          );
        return address.street;
      },
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].cityTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const address =
          interviewData.secondaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.MAILING,
          );
        return address.city;
      },
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].stateTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const address =
          interviewData.secondaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.MAILING,
          );
        return address.state;
      },
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].zipCodeTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const address =
          interviewData.secondaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.MAILING,
          );
        return address.zip;
      },
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].country[0]':
    {
      staticValue: 'USA',
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].ssnTaxIDTF[0]': {
    field: ['secondaryContact.taxId'],
  },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].dobOrTrustDate[0]':
    {
      field: ['secondaryContact.dob'],
    },

  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].emailAddress[0].emailAddressTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) =>
        interviewData.secondaryContact.additionalInfo.emails.find(
          (email) => email.type === EmailTypeEnum.HOME,
        )?.address || '',
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].phoneNumbers[0].homePhoneTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) =>
        interviewData.secondaryContact.additionalInfo.phones.find(
          (phone) => phone.type === PhoneTypeEnum.HOME,
        )?.number || '',
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].phoneNumbers[0].workPhoneTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) =>
        interviewData.secondaryContact.additionalInfo.phones.find(
          (phone) => phone.type === PhoneTypeEnum.WORK,
        )?.number || '',
    },
  'form_data[0].section4[0].border[0].ContactInfo_baseline[0].phoneNumbers[0].mobilePhoneTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) =>
        interviewData.secondaryContact.additionalInfo.phones.find(
          (phone) => phone.type === PhoneTypeEnum.MOBILE,
        )?.number || '',
    },

  // ... more mappings for other fields like citizenship, residence, and employment status ...

  'form_data[0].section4[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].selfEmployed[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.secondaryContact.additionalInfo
          ?.employmentStatus === GenericEmploymentStatusEnum.SELF_EMPLOYED
          ? GenericEmploymentStatusEnum.SELF_EMPLOYED
          : 'Off';
      },
    },
  // Employment status mappings with callbacks for secondaryContact
  'form_data[0].section4[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].employed[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.secondaryContact.additionalInfo
          ?.employmentStatus === GenericEmploymentStatusEnum.EMPLOYED
          ? GenericEmploymentStatusEnum.EMPLOYED
          : 'Off';
      },
    },

  'form_data[0].section4[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].retired[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.secondaryContact.additionalInfo
          ?.employmentStatus === GenericEmploymentStatusEnum.RETIRED
          ? GenericEmploymentStatusEnum.RETIRED
          : 'Off';
      },
    },
  // Citizenship and residence status mappings
  'form_data[0].section4[0].border[0].Country_ID_baseline[0].countryIdBaselineInfo[0].citizenshipAndResidence[0].citizenship[0].checkCitizenship[0].USA[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) =>
        interviewData.secondaryContact.citizenship === RedtailCitizenshipEnum.US
          ? 'USA'
          : 'Off',
    },

  'form_data[0].section4[0].border[0].Country_ID_baseline[0].countryIdBaselineInfo[0].citizenshipAndResidence[0].residence[0].checkResidence[0].USA[0]':
    {
      staticValue: 'USA',
    },
  // Job Description Mappings with callbacks for secondaryContact
  'form_data[0].section4[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].homemaker[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) =>
        interviewData.secondaryContact.additionalInfo.employmentStatus ===
        GenericEmploymentStatusEnum.HOMEMAKER
          ? 'Homemaker'
          : 'Off',
    },

  'form_data[0].section4[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].student[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) =>
        interviewData.secondaryContact.additionalInfo.employmentStatus ===
        GenericEmploymentStatusEnum.STUDENT
          ? 'Student'
          : 'Off',
    },

  'form_data[0].section4[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].notEmployed[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) =>
        interviewData.secondaryContact.additionalInfo.employmentStatus ===
        GenericEmploymentStatusEnum.NOTEMPLOYED
          ? 'Not Employed'
          : 'Off',
    },

  // // Business Address Details with callbacks for secondaryContact
  'form_data[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].employerName[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const workAddress =
          interviewData.secondaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        return workAddress ? workAddress || '' : '';
      },
    },

  'form_data[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].businessStreet[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const workAddress =
          interviewData.secondaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        return workAddress?.street || '';
      },
    },

  'form_data[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].City[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const workAddress =
          interviewData.secondaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        return workAddress ? workAddress.city || '' : '';
      },
    },

  'form_data[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].StateorProvince[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const workAddress =
          interviewData.secondaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        return workAddress ? workAddress.state || '' : '';
      },
    },

  'form_data[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].ZiporPostalCode[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const workAddress =
          interviewData.secondaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        return workAddress ? workAddress.zip || '' : '';
      },
    },
  // // Job Description Mappings

  // 'form_data[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].businessOwner[0]':
  //   {
  //     field: ['secondaryContact.jobDescription'],
  //     callback: 'businessOwner',
  //   },
  // 'form_data[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].othersInput[0]':
  //   {
  //     field: ['secondaryContact.jobDescriptionOther'],
  //   },
  'form_data[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].country[0]':
    {
      staticValue: 'USA',
    },
  // FINRA Rule employment questions
  'form_data[0].section4[0].border[0].FINRARuleemploymentQues_MA_SA[0].border[0].FINRARuleEmployemntQues_MA_SA[0].Checkboxes[0].Yes[0]':
    {
      field: ['secondaryContact.industryAffiliationCheck'],
      callback: 'true',
    },
  'form_data[0].section4[0].border[0].FINRARuleemploymentQues_MA_SA[0].border[0].FINRARuleEmployemntQues_MA_SA[0].Checkboxes[0].No[0]':
    {
      field: ['secondaryContact.industryAffiliationCheck'],
      callback: 'false',
    },
  'form_data[0].section4[0].border[0].FINRARuleemploymentQues_MA_SA[0].border[0].FINRARuleEmployemntQues_MA_SA[0].Checkboxes[0].ListOfCompany[0]':
    {
      field: ['secondaryContact.industryAffiliation'],
    },
  // FINRA Rule 10 percent Shareholder
  'form_data[0].section4[0].border[0].FINRA_Rule_10_percent_Shareholder[0].#subform[0].Checkboxes[0].No[0]':
    {
      field: ['secondaryContact.publicCompanyAssociationCheck'],
      callback: 'false',
    },
  'form_data[0].section4[0].border[0].FINRA_Rule_10_percent_Shareholder[0].#subform[0].Checkboxes[0].Yes[0]':
    {
      field: ['secondaryContact.publicCompanyAssociationCheck'],
      callback: 'true',
    },
  'form_data[0].section4[0].border[0].FINRA_Rule_10_percent_Shareholder[0].#subform[0].companyName[0]':
    {
      field: ['secondaryContact.publicCompanyAssociationName'],
    },
  'form_data[0].section4[0].border[0].FINRA_Rule_10_percent_Shareholder[0].#subform[0].tradingSymbol[0]':
    {
      field: ['secondaryContact.publicCompanyAssociationSymbol'],
    },

  //   // Coapplicant section mappings: TODO: This should be the secondary contact
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].fullName[0].firstNameTF[0]':
  //     {
  //       field: ['coapplicant.firstName'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].fullName[0].middleNameTF[0]':
  //     {
  //       field: ['coapplicant.middleName'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].fullName[0].lastNameTF[0]':
  //     {
  //       field: ['coapplicant.lastName'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].fullName[0].SuffixTF[0]':
  //     {
  //       field: ['coapplicant.suffix'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].legalAddressTF[0]':
  //     {
  //       field: ['coapplicant.homeAddress.combinedLines'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].cityTF[0]':
  //     {
  //       field: ['coapplicant.homeAddress.city'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].stateTF[0]':
  //     {
  //       field: ['coapplicant.homeAddress.state'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].zipCodeTF[0]':
  //     {
  //       field: ['coapplicant.homeAddress.zip'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].country[0]':
  //     {
  //       staticValue: 'USA',
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].mailingAddressTF[0]':
  //     {
  //       field: ['coapplicant.mailingAddress.combinedLines'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].cityTF[0]':
  //     {
  //       field: ['coapplicant.mailingAddress.city'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].stateTF[0]':
  //     {
  //       field: ['coapplicant.mailingAddress.state'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].zipCodeTF[0]':
  //     {
  //       field: ['coapplicant.mailingAddress.zip'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].country[0]':
  //     {
  //       staticValue: 'USA',
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].ssnTaxIDTF[0]':
  //     {
  //       field: ['coapplicant.taxId'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].dobOrTrustDate[0]':
  //     {
  //       field: ['coapplicant.dob'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].emailAddress[0].emailAddressTF[0]':
  //     {
  //       field: ['coapplicant.emailAddress'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].phoneNumbers[0].homePhoneTF[0]':
  //     {
  //       field: ['coapplicant.homePhone'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].phoneNumbers[0].workPhoneTF[0]':
  //     {
  //       field: ['coapplicant.workPhone'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].ContactInfo_baseline[0].phoneNumbers[0].mobilePhoneTF[0]':
  //     {
  //       field: ['coapplicant.mobilePhone'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].Country_ID_baseline[0].countryIdBaselineInfo[0].citizenshipAndResidence[0].citizenship[0].checkCitizenship[0].USA[0]':
  //     {
  //       staticValue: '{CASE}USA:USA{DEFAULT}Off',
  //     },
  //   'clients[0].Form[0].section4[0].border[0].Country_ID_baseline[0].countryIdBaselineInfo[0].citizenshipAndResidence[0].residence[0].checkResidence[0].USA[0]':
  //     {
  //       staticValue: '{CASE}USA:USA{DEFAULT}Off',
  //     },
  //   // Employment Status Mappings
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].employed[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.employmentStatus === RedtailEmploymentStatusEnum.EMPLOYED
  //           ? RedtailEmploymentStatusEnum.EMPLOYED
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].selfEmployed[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.employmentStatus === 'Self-Employed'
  //           ? 'Self-Employed'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].retired[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.employmentStatus === RedtailEmploymentStatusEnum.RETIRED
  //           ? RedtailEmploymentStatusEnum.RETIRED
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].homemaker[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.employmentStatus === RedtailEmploymentStatusEnum.HOMEMAKER
  //           ? RedtailEmploymentStatusEnum.HOMEMAKER
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].student[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.employmentStatus === 'Student'
  //           ? 'Student'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].notEmployed[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.employmentStatus === RedtailEmploymentStatusEnum.NOTEMPLOYED
  //           ? RedtailEmploymentStatusEnum.NOTEMPLOYED
  //           : 'Off';
  //       },
  //     },

  //   // Job Description Mappings
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].businessOwner[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription === 'Business Owner'
  //           ? 'Business Owner/Self Employed'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].othersInput[0]':
  //     {
  //       field: ['coapplicant.jobDescriptionOther'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].financialServices[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription === 'Financial Services'
  //           ? 'Financial Services/Banking Professional'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].military[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription === 'Military'
  //           ? 'Military'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].consultant[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription === 'Consultant'
  //           ? 'Consultant'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].seniorMgt[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription === 'Executive'
  //           ? 'Executive/Senior Management'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].ITProfessional[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription === 'IT Professional'
  //           ? 'Information Technology Professional'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].educator[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription === 'Educator'
  //           ? 'Educator'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].medicalProfessional[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription ===
  //           'Medical Professional'
  //           ? 'Medical Professional'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].otherProfessional[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription === 'Other Professional'
  //           ? 'Other Professional'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].salesMarketing[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription === 'Sales Marketing'
  //           ? 'Sales/Marketing'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].legalProfessional[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription === 'Legal Professional'
  //           ? 'Legal Professional'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].clericalAdministrativeServices[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription ===
  //           'Administrative Services'
  //           ? 'Clerical/Administrative Services'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].USGovt[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription ===
  //           'US Government Employee'
  //           ? 'U.S. Government Employee'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].accountProfessional[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription ===
  //           'Accounting Professional'
  //           ? 'Accounting Professional'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].foreignGovt[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription ===
  //           'Foreign Government Employee'
  //           ? 'Foreign Government Employee'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].tradeService[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription === 'Trade'
  //           ? 'Trade/Service Career'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].others[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.jobDescription === 'Other'
  //           ? 'Other'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].employerName[0]':
  //     {
  //       field: ['coapplicant.workAddress.companyName'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].businessStreet[0]':
  //     {
  //       field: ['coapplicant.workAddress.combinedLines'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].City[0]':
  //     {
  //       field: ['coapplicant.workAddress.city'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].StateorProvince[0]':
  //     {
  //       field: ['coapplicant.workAddress.state'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].ZiporPostalCode[0]':
  //     {
  //       field: ['coapplicant.workAddress.zip'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].country[0]':
  //     {
  //       staticValue: 'USA',
  //     },
  //   // FINRA Rule employment questions
  //   'clients[0].Form[0].section4[0].border[0].FINRARuleemploymentQues_MA_SA[0].border[0].FINRARuleEmployemntQues_MA_SA[0].Checkboxes[0].Yes[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.industryAffiliationCheck === true
  //           ? 'true'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].FINRARuleemploymentQues_MA_SA[0].border[0].FINRARuleEmployemntQues_MA_SA[0].Checkboxes[0].No[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.industryAffiliationCheck === false
  //           ? 'false'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].FINRARuleemploymentQues_MA_SA[0].border[0].FINRARuleEmployemntQues_MA_SA[0].Checkboxes[0].ListOfCompany[0]':
  //     {
  //       field: ['coapplicant.industryAffiliation'],
  //     },
  //   // FINRA Rule 10 percent Shareholder
  //   'clients[0].Form[0].section4[0].border[0].FINRA_Rule_10_percent_Shareholder[0].#subform[0].Checkboxes[0].No[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.publicCompanyAssociationCheck === false
  //           ? 'false'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].FINRA_Rule_10_percent_Shareholder[0].#subform[0].Checkboxes[0].Yes[0]':
  //     {
  //       callback: (
  //         fileName: string,
  //         interviewData: InterviewDataWithCrmInfo,
  //         accountIndex: number,
  //       ) => {
  //         return interviewData.coapplicant.publicCompanyAssociationCheck === true
  //           ? 'true'
  //           : 'Off';
  //       },
  //     },
  //   'clients[0].Form[0].section4[0].border[0].FINRA_Rule_10_percent_Shareholder[0].#subform[0].companyName[0]':
  //     {
  //       field: ['coapplicant.publicCompanyAssociationName'],
  //     },
  //   'clients[0].Form[0].section4[0].border[0].FINRA_Rule_10_percent_Shareholder[0].#subform[0].tradingSymbol[0]':
  //     {
  //       field: ['coapplicant.publicCompanyAssociationSymbol'],
  //     },
  // Section 6
  'clients[0].Form[0].section7[0].border[0].SourceOfFunds_Baseline[0].org[0].Checkboxes[0].SalaryWagesSavings[0]':
    {
      staticValue: 'true',
    },
  'clients[0].Form[0].section7[0].border[0].PurposeOfAccountSubForm[0].pop_trust[0].MAMP[0].GeneralInvesting[0]':
    {
      staticValue: 'true',
    },
  // Section 8
  'clients[0].Form[0].section9[0].border[0].IA_Authorizations[0].AuthorizationsInstructions[0].Checkboxes[0].InstructionOne[0]':
    {
      staticValue: 'tradingAndDisbursement',
    },
  'clients[0].Form[0].section9[0].border[0].IA_Authorizations[0].AuthorizationsInstructions[0].Checkboxes[0].InstructionThree[0]':
    {
      staticValue: 'true',
    },
  // Section 9
  'clients[0].Form[0].section10[0].border[0].Issuer_Communications_Custodial[0].Issuer_Communications_Custodial[0].CheckboxesA1[0].accountHolder[0]':
    {
      staticValue: 'accountHolder',
    },
  'clients[0].Form[0].section10[0].border[0].Issuer_Communications_Custodial[0].Issuer_Communications_Custodial[0].CheckboxesA2[0].accountHolder[0]':
    {
      staticValue: 'accountHolder',
    },
  'clients[0].Form[0].section10[0].border[0].Issuer_Communications_Custodial[0].Issuer_Communications_Custodial[0].CheckboxesB1[0].accountHolder[0]':
    {
      staticValue: 'accountHolder',
    },
  'clients[0].Form[0].section10[0].border[0].Issuer_Communications_Custodial[0].Issuer_Communications_Custodial[0].CheckboxesB2[0].accountHolder[0]':
    {
      staticValue: 'accountHolder',
    },
  'clients[0].Form[0].section10[0].border[0].Issuer_Communications_Custodial[0].Issuer_Communications_Custodial[0].CheckboxesC[0].accountHolder[0]':
    {
      staticValue: 'accountHolder',
    },
  'clients[0].Form[0].section10[0].border[0].Issuer_Communications_Custodial[0].Issuer_Communications_Custodial[0].Objection[0].Checkboxes[0].yes[0]':
    {
      staticValue: 'yes',
    },
  // Section 11
  'clients[0].Form[0].section12[0].border[0].Signature_Section_DocuSign[0].SIGBLK_01_AccountHolder_Generic_00[0].SignatureFields[0].PrintName[0].PrintNameFull_DocuSignIgnoreTransform[0]':
    {
      field: ['primaryContact.firstName', 'primaryContact.lastName'],
    },
  'clients[0].Form[0].section12[0].border[0].Signature_Section[0].SignatureSection1[0].signer1_FullName[0]':
    {
      field: ['secondaryContact.firstName', 'secondaryContact.lastName'],
    },
  'clients[0].Form[0].InvestmentAdvisorInformation[0].border[0].IA-Box-01_Contact[0].IA[0].ia_firm_name[0]':
    {
      field: ['organisation.firmName'],
    },
  'clients[0].Form[0].InvestmentAdvisorInformation[0].border[0].IA-Box-01_Contact[0].IA[0].ia_master_acct_num[0]':
    {
      field: ['account.masterAccountNumber'],
    },
  'clients[0].Form[0].Section1[0].border[0].SelectIRAType[0].traditional[0].Checkboxes[0].contributory[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        return account.type.toLowerCase() === AccountTypeEnum.Ira ? '1' : 'Off';
      },
    },
  'clients[0].Form[0].Section1[0].border[0].SelectIRAType[0].roth[0].Checkboxes[0].contributory[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        return account.type.toLowerCase() === AccountTypeEnum.RothIra
          ? '1'
          : 'Off';
      },
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].fullName[0].SuffixTF[0]':
    {
      field: ['suffix'],
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].legalAddressTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const client = interviewData.primaryContact;
        const address = client.additionalInfo.addresses.find((address) => {
          return address.type === AddressTypeEnum.HOME;
        });
        if (!address) {
          return '';
        }
        return address.street;
      },
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].cityTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const client = interviewData.primaryContact;
        const address = client.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        );
        return address.city;
      },
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].stateTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const client = interviewData.primaryContact;
        const address = client.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        );
        return address.state;
      },
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].zipCodeTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const client = interviewData.primaryContact;
        const address = client.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        );
        return address.zip;
      },
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].legalAddressDetails[0].country[0]':
    {
      staticValue: 'USA',
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].mailingAddressTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        // TODO: What is the fallback for this?
        const client = interviewData.primaryContact;
        const address = client.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.MAILING,
        );
        if (!address) return '';
        return address.street;
      },
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].cityTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const client = interviewData.primaryContact;
        const address = client.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.MAILING,
        );
        if (!address) return '';
        return address.city;
      },
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].stateTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const client = interviewData.primaryContact;
        const address = client.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.MAILING,
        );
        if (!address) return '';
        return address.state;
      },
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].zipCodeTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const client = interviewData.primaryContact;
        const address = client.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.MAILING,
        );
        if (!address) return '';
        return address.zip;
      },
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].mailingAddressDetails[0].country[0]':
    {
      constant: 'USA',
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].phoneNumbers[0].homePhoneTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return (
          interviewData.primaryContact.additionalInfo.phones.find(
            (phoneNumber) => phoneNumber.type === PhoneTypeEnum.HOME,
          )?.number || ''
        );
      },
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].phoneNumbers[0].workPhoneTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) =>
        interviewData.primaryContact.additionalInfo.phones.find(
          (phoneNumber) => phoneNumber.type === PhoneTypeEnum.WORK,
        )?.number || '',
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].phoneNumbers[0].mobilePhoneTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) =>
        interviewData.primaryContact.additionalInfo.phones.find(
          (phoneNumber) => phoneNumber.type === PhoneTypeEnum.MOBILE,
        )?.number || '',
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].ssnTaxIDTF[0]':
    {
      field: ['primaryContact.taxId'],
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].dobOrTrustDate[0]':
    {
      field: ['primaryContact.dob'],
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].emailAddress[0].emailAddressTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) =>
        interviewData.primaryContact.additionalInfo.emails.find(
          (email) => email.type === EmailTypeEnum.HOME,
        )?.address || '',
    },
  'clients[0].Form[0].Section2[0].border[0].Country_ID_baseline[0].countryIdBaselineInfo[0].citizenshipAndResidence[0].citizenship[0].checkCitizenship[0].USA[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.residence === 'USA' ? 'USA' : 'Off';
      },
    },
  'clients[0].Form[0].Section2[0].border[0].Country_ID_baseline[0].countryIdBaselineInfo[0].citizenshipAndResidence[0].residence[0].checkResidence[0].USA[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.residence === 'USA' ? 'USA' : 'Off';
      },
    },
  // Employment Status Mappings
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].employed[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo?.employmentStatus ===
          GenericEmploymentStatusEnum.EMPLOYED
          ? GenericEmploymentStatusEnum.EMPLOYED
          : 'Off';
      },
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].selfEmployed[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo?.employmentStatus ===
          GenericEmploymentStatusEnum.SELF_EMPLOYED
          ? GenericEmploymentStatusEnum.SELF_EMPLOYED
          : 'Off';
      },
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].retired[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo?.employmentStatus ===
          GenericEmploymentStatusEnum.RETIRED
          ? GenericEmploymentStatusEnum.RETIRED
          : 'Off';
      },
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].homemaker[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo?.employmentStatus ===
          GenericEmploymentStatusEnum.HOMEMAKER
          ? GenericEmploymentStatusEnum.HOMEMAKER
          : 'Off';
      },
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].student[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo?.employmentStatus ===
          GenericEmploymentStatusEnum.STUDENT
          ? GenericEmploymentStatusEnum.STUDENT
          : 'Off';
      },
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].employmentInfoCheckBoxes[0].notEmployed[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo?.employmentStatus ===
          GenericEmploymentStatusEnum.NOTEMPLOYED
          ? GenericEmploymentStatusEnum.NOTEMPLOYED
          : 'Off';
      },
    },

  // Employer Name Mapping
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].employerName[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const workAddress =
          interviewData.primaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        return workAddress ? workAddress.title : '';
      },
    },

  // Mappings for the Contact Info Section2
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].fullName[0].firstNameTF[0]':
    {
      field: ['primaryContact.firstName'],
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].fullName[0].middleNameTF[0]':
    {
      field: ['primaryContact.middleName'],
    },
  'clients[0].Form[0].Section2[0].border[0].ContactInfo_baseline[0].fullName[0].lastNameTF[0]':
    {
      field: ['primaryContact.lastName'],
    },
  'account#Applicant1FullName': {
    field: ['primaryContact.firstName', 'primaryContact.lastName'],
  },
  'account#Applicant1FirstName': {
    field: ['primaryContact.firstName'],
  },
  'account#Applicant1LastName': {
    field: ['primaryContact.lastName'],
  },
  'account#Applicant1TaxId': {
    field: ['primaryContact.taxId'],
  },
  applicant1IndustryAffiliationCheck: {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.primaryContact.additionalInfo.companyAssociation
        ? 'true'
        : 'Off';
    },
  },
  'account#Name': {
    field: ['primaryContact.accounts.#.name'],
  },
  'account#Fee': {
    field: ['primaryContact.accounts.#.advisoryRate'],
  },
  'account#Description': {
    field: ['primaryContact.accounts.#.label'],
  },
  'clients[0].Form[0].Section7[0].border[0].#subform[0].AccountOpenBeneInformation1[0].typeOfBeneficiary[0].Primary[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.firstName === 'Prmry' ? 'true' : 'Off',
    },
  'clients[0].Form[0].Section7[0].border[0].#subform[0].AccountOpenBeneInformation1[0].typeOfBeneficiary[0].Contingent[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.firstName === 'Cntngnt' ? 'false' : 'Off',
    },
  'clients[0].Form[0].Section7[0].border[0].#subform[0].AccountOpenBeneInformation1[0].typeOfBeneficiary[0].portionPercentage[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        return account?.beneficiaries?.[0]?.percentage ?? '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].#subform[0].AccountOpenBeneInformation1[0].fullName[0].firstNameTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        const firstName = account?.beneficiaries?.[0]?.name?.split(',')?.[1];
        return firstName ?? '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].#subform[0].AccountOpenBeneInformation1[0].fullName[0].lastNameTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        const lastName = account?.beneficiaries?.[0]?.name?.split(',')?.[0];

        return lastName ?? '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].#subform[0].AccountOpenBeneInformation1[0].dobOrTrustDate[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        const dob = account?.beneficiaries?.[0]?.name?.split(':')?.[1];
        return dob || '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].AccountOpenBeneInformation2[0].typeOfBeneficiary[0].Primary[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.firstName === 'Prmry' ? 'true' : 'Off',
    },
  'clients[0].Form[0].Section7[0].border[0].AccountOpenBeneInformation2[0].typeOfBeneficiary[0].Contingent[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.firstName === 'Cntngnt' ? 'false' : 'Off',
    },
  'clients[0].Form[0].Section7[0].border[0].AccountOpenBeneInformation2[0].typeOfBeneficiary[0].portionPercentage[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        return account?.beneficiaries?.[1]?.percentage ?? '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].AccountOpenBeneInformation2[0].fullName[0].firstNameTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        const firstName = account?.beneficiaries?.[1]?.name?.split(',')?.[1];

        return firstName ?? '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].AccountOpenBeneInformation2[0].fullName[0].lastNameTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        const lastName = account?.beneficiaries?.[1]?.name?.split(',')?.[0];
        return lastName ?? '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].AccountOpenBeneInformation2[0].dobOrTrustDate[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        const dob = account?.beneficiaries?.[1]?.name?.split(':')?.[1];
        return dob ?? '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].#subform[3].AccountOpenBeneInformation3[0].typeOfBeneficiary[0].Primary[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        return account?.beneficiaries?.[2]?.type === 'Primary' ? 'true' : 'Off';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].#subform[3].AccountOpenBeneInformation3[0].typeOfBeneficiary[0].Contingent[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        return account?.beneficiaries?.[2]?.type === 'Contingent'
          ? 'true'
          : 'Off';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].#subform[3].AccountOpenBeneInformation3[0].typeOfBeneficiary[0].portionPercentage[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );

        return account?.beneficiaries?.[2]?.percentage ?? '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].#subform[3].AccountOpenBeneInformation3[0].fullName[0].firstNameTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        const firstName = account?.beneficiaries?.[2]?.name?.split(',')?.[1];

        return firstName ?? '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].#subform[3].AccountOpenBeneInformation3[0].fullName[0].lastNameTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        const lastName = account?.beneficiaries?.[2]?.name?.split(',')?.[0];
        return lastName ?? '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].#subform[3].AccountOpenBeneInformation3[0].dobOrTrustDate[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        const dob = account?.beneficiaries?.[2]?.name?.split(':')?.[1];

        return dob ?? '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].AccountOpenBeneInformation4[0].typeOfBeneficiary[0].Primary[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        return account?.beneficiaries?.[3]?.type === 'Primary' ? 'true' : 'Off';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].AccountOpenBeneInformation4[0].typeOfBeneficiary[0].Contingent[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        return account?.beneficiaries?.[3]?.type === 'Contingent'
          ? 'true'
          : 'Off';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].AccountOpenBeneInformation4[0].typeOfBeneficiary[0].portionPercentage[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        return account?.beneficiaries?.[3]?.percentage ?? '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].AccountOpenBeneInformation4[0].fullName[0].firstNameTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        const firstName = account?.beneficiaries?.[3]?.name?.split(',')?.[1];

        return firstName ?? '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].AccountOpenBeneInformation4[0].fullName[0].lastNameTF[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        const lastName = account?.beneficiaries?.[3]?.name?.split(',')?.[0];

        return lastName ?? '';
      },
    },
  'clients[0].Form[0].Section7[0].border[0].AccountOpenBeneInformation4[0].dobOrTrustDate[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        const dob = account?.beneficiaries?.[3]?.name?.split(':')?.[1];
        return dob ?? '';
      },
    },
  'clients[0].Form[0].Section8[0].border[0].Signature_Section_DocuSign[0].SIGBLK_01_AccountHolder_Generic_00[0].SignatureFields[0].PrintName[0].PrintNameFull_DocuSignIgnoreTransform[0]':
    {
      field: ['fullName'],
    },
  'form_data[0].Form[0].Welcome[0].IA_info[0].Border[0].IA-Box-01_Contact[0].IA[0].ia_firm_name[0]':
    {
      field: ['organisation.firmName'],
    },
  'form_data[0].Form[0].Welcome[0].IA_info[0].Border[0].IA-Box-01_Contact[0].IA[0].ia_master_acct_num[0]':
    {
      field: ['account.masterAccountNumber'],
    },
  'form_data[0].Form[0].Section1[0].Border[0].NameList[0].Name[0]': {
    field: ['account.description'],
  },
  'form_data[0].Form[0].Section4[0].Border[0].TRNSFR_02AccountInfo_01[0].Account[0].Right[0].RB3[0]':
    {
      staticValue: '1',
    },
  'form_data[0].Form[0].Section7[0].Border[0].Signature_Section_DocuSign[0].SIGBLK_01_AccountHolder_Generic_00[0].SignatureFields[0].PrintName[0].PrintNameFull_DocuSignIgnoreTransform[0]':
    {
      field: ['fullName'],
    },
  'form_data[0].Form[0].Section7[0].Border[0].Signature_Section[0].SignatureSection1[0].signer1_FullName[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const secondaryContact = interviewData.secondaryContact;
        if (!secondaryContact) {
          return ''; // or some placeholder if necessary
        }
        // Assuming you want to concatenate the first and last names
        const fullName = `${secondaryContact.firstName} ${secondaryContact.lastName}`;
        return fullName.trim();
      },
    },
  'client[0].ToA[0].section1[0].subFormCSAccountDetails[0].accountNames[0]': {
    field: ['account.allNames'],
  },
  'client[0].ToA[0].section1[0].subFormCSAccountDetails[0].accountType[0]': {
    field: ['account.description'],
  },
  'client[0].ToA[0].section1[0].subFormCSAccountDetails[0].SSN[0]': {
    field: ['account.allTaxIds'],
  },
  'client[0].ToA[0].section7Continued[0].Section7Continued[0].chkAccStmtCopyAttached[0]':
    {
      value: '1',
    },
  'client[0].ToA[0].section7Continued[0].Section7Continued[0].Signature_Section_DocuSign[0].SIGBLK_01_AccountHolder_Generic_00[0].SignatureFields[0].PrintName[0].PrintNameFull_DocuSignIgnoreTransform[0]':
    {
      field: ['fullName'],
    },
  // For the coapplicant's full name
  'client[0].ToA[0].section7Continued[0].Section7Continued[0].Signature_Section1[0].SignatureSection1[0].signer1_FullName[0]':
    {
      callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
        const secondaryContact = interviewData.secondaryContact;
        if (!secondaryContact) {
          return '';
        }
        return `${secondaryContact.firstName} ${secondaryContact.lastName}`.trim();
      },
    },
  // For the work address
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].businessStreet[0]':
    {
      callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
        const workAddress =
          interviewData.primaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        return workAddress ? workAddress.title : '';
      },
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].City[0]':
    {
      callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
        const workAddress =
          interviewData.primaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        return workAddress ? workAddress.city : '';
      },
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].StateorProvince[0]':
    {
      callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
        const workAddress =
          interviewData.primaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        return workAddress ? workAddress.state : '';
      },
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].ZiporPostalCode[0]':
    {
      callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
        const workAddress =
          interviewData.primaryContact.additionalInfo.addresses.find(
            (address) => address.type === AddressTypeEnum.WORK,
          );
        return workAddress ? workAddress.zip : '';
      },
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].businessAddressDetails[0].country[0]':
    {
      staticValue: 'USA',
    },
  // FINRA Rule employment questions
  'clients[0].Form[0].Section2[0].border[0].FINRARuleemploymentQues_MA_SA[0].border[0].FINRARuleEmployemntQues_MA_SA[0].Checkboxes[0].Yes[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo.industryAffiliation
          ? 'true'
          : 'Off';
      },
    },
  'clients[0].Form[0].Section2[0].border[0].FINRARuleemploymentQues_MA_SA[0].border[0].FINRARuleEmployemntQues_MA_SA[0].Checkboxes[0].No[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        return interviewData.primaryContact.additionalInfo.industryAffiliation
          ? 'true'
          : 'Off';
      },
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].businessOwner[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription ===
        'Business Owner'
          ? 'Business Owner/Self Employed'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].financialServices[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription ===
        'Financial Services'
          ? 'Financial Services/Banking Professional'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].military[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription ===
        'Military'
          ? 'Military'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].consultant[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription ===
        'Consultant'
          ? 'Consultant'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].seniorMgt[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription ===
        'Executive'
          ? 'Executive/Senior Management'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].ITProfessional[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription ===
        'IT Professional'
          ? 'Information Technology Professional'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].educator[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription ===
        'Educator'
          ? 'Educator'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].medicalProfessional[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription ===
        'Medical Professional'
          ? 'Medical Professional'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].otherProfessional[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription ===
        'Other Professional'
          ? 'Other Professional'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].salesMarketing[0]':
    {
      callback: (fileName, interviewData) => {
        return interviewData.primaryContact.additionalInfo?.jobDescription ===
          'Sales Marketing'
          ? 'Sales/Marketing'
          : 'Off';
      },
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].legalProfessional[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription ===
        'Legal Professional'
          ? 'Legal Professional'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].clericalAdministrativeServices[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription ===
        'Administrative Services'
          ? 'Clerical/Administrative Services'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].USGovt[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription ===
        'US Government Employee'
          ? 'U.S. Government Employee'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].accountProfessional[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription ===
        'Accounting Professional'
          ? 'Accounting Professional'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].foreignGovt[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription ===
        'Foreign Government Employee'
          ? 'Foreign Government Employee'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].tradeService[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription === 'Trade'
          ? 'Trade/Service Career'
          : 'Off',
    },
  'clients[0].Form[0].Section2[0].border[0].EmploymentOccupation[0].Occupation[0].occupation[0].col1[0].others[0]':
    {
      callback: (fileName, interviewData) =>
        interviewData.primaryContact.additionalInfo?.jobDescription === 'Other'
          ? 'Other'
          : 'Off',
    },
  // Assuming additionalInfo maps directly to a Redtail custom field structure with 'industryAffiliation', etc.
  'clients[0].Form[0].Section2[0].border[0].FINRARuleemploymentQues_MA_SA[0].border[0].FINRARuleEmployemntQues_MA_SA[0].Checkboxes[0].ListOfCompany[0]':
    {
      callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
        return (
          interviewData.primaryContact.additionalInfo?.industryAffiliation || ''
        );
      },
    },

  'clients[0].Form[0].Section2[0].border[0].FINRA_Rule_10_percent_Shareholder[0].#subform[0].Checkboxes[0].Yes[0]':
    {
      callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
        return interviewData.primaryContact.additionalInfo?.companyAssociation
          ? 'true'
          : 'Off';
      },
    },

  'clients[0].Form[0].Section2[0].border[0].FINRA_Rule_10_percent_Shareholder[0].#subform[0].Checkboxes[0].No[0]':
    {
      callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
        return !interviewData.primaryContact.additionalInfo.companyAssociation
          ? 'true'
          : 'Off';
      },
    },

  'clients[0].Form[0].Section2[0].border[0].FINRA_Rule_10_percent_Shareholder[0].#subform[0].companyName[0]':
    {
      callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
        const companyAssociation =
          interviewData.primaryContact.additionalInfo.companyAssociation;
        const companyName = companyAssociation?.split(':')?.[1];
        return companyName ?? '';
      },
    },

  'clients[0].Form[0].Section2[0].border[0].FINRA_Rule_10_percent_Shareholder[0].#subform[0].tradingSymbol[0]':
    {
      callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
        const companyAssociation =
          interviewData.primaryContact.additionalInfo.companyAssociation;
        const tickerSymbol = companyAssociation?.split(':')?.[0];
        return tickerSymbol ?? '';
      },
    },
  'clients[0].Form[0].Section5[0].border[0].IA_Authorizations[0].AuthorizationsInstructions[0].Checkboxes[0].InstructionOne[0]':
    {
      staticValue: 'tradingAndDisbursement',
    },

  'clients[0].Form[0].Section5[0].border[0].IA_Authorizations[0].AuthorizationsInstructions[0].Checkboxes[0].InstructionThree[0]':
    {
      staticValue: 'true',
    },
  'clients[0].Form[0].Section6[0].border[0].Issuer_Communications_Custodial[0].Issuer_Communications_Custodial[0].CheckboxesA1[0].accountHolder[0]':
    {
      staticValue: 'accountHolder',
    },

  'clients[0].Form[0].Section6[0].border[0].Issuer_Communications_Custodial[0].Issuer_Communications_Custodial[0].CheckboxesA2[0].accountHolder[0]':
    {
      staticValue: 'accountHolder',
    },

  'clients[0].Form[0].Section6[0].border[0].Issuer_Communications_Custodial[0].Issuer_Communications_Custodial[0].CheckboxesB1[0].accountHolder[0]':
    {
      staticValue: 'accountHolder',
    },

  'clients[0].Form[0].Section6[0].border[0].Issuer_Communications_Custodial[0].Issuer_Communications_Custodial[0].CheckboxesB2[0].accountHolder[0]':
    {
      staticValue: 'accountHolder',
    },

  'clients[0].Form[0].Section6[0].border[0].Issuer_Communications_Custodial[0].Issuer_Communications_Custodial[0].CheckboxesC[0].accountHolder[0]':
    {
      staticValue: 'accountHolder',
    },

  'clients[0].Form[0].Section6[0].border[0].Issuer_Communications_Custodial[0].Issuer_Communications_Custodial[0].Objection[0].Checkboxes[0].yes[0]':
    {
      staticValue: 'yes',
    },

  allApplicantNames: {
    field: [
      'primaryContact.firstName',
      'primaryContact.lastName',
      'secondaryContact.firstName',
      'secondaryContact.lastName',
    ],
  },
  applicant1FullName: {
    field: ['primaryContact.firstName', 'primaryContact.lastName'],
  },
  applicant1FirstName: {
    field: ['primaryContact.firstName'],
  },
  applicant1LastName: {
    field: ['primaryContact.lastName'],
  },
  applicant2FullName: {
    field: ['secondaryContact.firstName', 'secondaryContact.lastName'],
  },
  applicant2FirstName: {
    field: ['secondaryContact.firstName'],
  },
  applicant2LastName: {
    field: ['secondaryContact.lastName'],
  },
  financialAdvisorFullName: {
    field: ['primaryAdvisor.firstName', 'primaryAdvisor.lastName'],
  },
  financialAdvisorFirstName: {
    field: ['primaryAdvisor.firstName'],
  },
  financialAdvisorLastName: {
    field: ['primaryAdvisor.lastName'],
  },
  'account#Custodian': {
    staticValue: 'Charles Schwab',
  },
  'clients[0].Form[0].IASubform[0].border[0].IA-Box-01_Contact[0].IA[0].ia_firm_name[0]':
    {
      field: ['organisation.firmName'],
    },
  'clients[0].Form[0].IASubform[0].border[0].IA-Box-01_Contact[0].IA[0].ia_master_acct_num[0]':
    {
      callback: (
        fileName: string,
        interviewData: InterviewDataWithCrmInfo,
        accountIndex: number,
      ) => {
        const { account, applicant, coapplicant } = getAccountByFileName(
          interviewData,
          fileName,
        );
        return account.masterAccountNumber;
      },
    },
  'clients[0].Form[0].section1[0].border[0].typeOfAccount[0].IRAAccountTypes[0].traditional[0]':
    {
      staticValue: 'Rollover',
    },
  'clients[0].Form[0].section1[0].border[0].schwabAccountSSN[0].SSNTaxID[0]': {
    field: ['primaryContact.taxId'],
  },
  'clients[0].Form[0].section1[0].border[0].accountHolderName[0].first[0]': {
    field: ['primaryContact.firstName'],
  },
  'clients[0].Form[0].section1[0].border[0].accountHolderName[0].middle[0]': {
    field: ['primaryContact.middleName'],
  },
  'clients[0].Form[0].section1[0].border[0].accountHolderName[0].last[0]': {
    field: ['primaryContact.lastName'],
  },
  'clients[0].Form[0].section2[0].border[0].reasonForDistribution[0].reasonForDistribution[0].newDistRequest[0]':
    {
      staticValue: 'newDistributionRequest',
    },
  'clients[0].Form[0].section4[0].border[0].distributionMethodA[0].schwab_accnt_details[0].namesOnSchwabAccnt[0]':
    {
      field: ['primaryContact.firstName', 'primaryContact.lastName'],
    },
  'account.type': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return account.type;
    },
  },
  'account#Type': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      if (!account) {
        return undefined;
      }
      switch (interviewData.primaryContact.accounts[accountIndex].type) {
        case AccountTypeEnum.Ira:
          return 'IRA';
        case AccountTypeEnum.RothIra:
          return 'RothIRA';
        case AccountTypeEnum.JointNameBrokerage:
          return 'SchwabOne';
        default:
          return undefined;
      }
    },
  },
};
