import { DateTime } from 'src/integrations/crm/wealthbox/types';
import { PriorityEnum } from 'src/integrations/crm/wealthbox/types/priority.enum';

export enum TaskDocumentType {
  Contact = 'Contact',
  Opportunity = 'Opportunity',
  Project = 'Project',
  Task = 'Task',
  Event = 'Event',
  ManualInvestmentAccount = 'ManualInvestmentAccount',
}
export type TaskFieldType =
  | 'single_select'
  | 'multi_check_box'
  | 'text_field'
  | 'text_area'
  | 'check_box'
  | 'date';

export interface DocumentLinkedToTask {
  id: number;
  type: string;
  name?: string;
}

export interface CustomFieldForTask {
  id: number;
  name: string;
  value: string;
  document_type: TaskDocumentType;
  field_type: TaskFieldType;
}

export interface Task {
  id: number;
  creator: number;
  created_at: DateTime;
  updated_at: DateTime;
  name: string;
  due_date: DateTime;
  complete: boolean;
  category: number;
  linked_to: DocumentLinkedToTask[];
  priority: PriorityEnum;
  visible_to: string;
  custom_fields: CustomFieldForTask[];
  frame?: string;
  repeats?: boolean;
  completer?: number;
  description?: string;
  description_html?: string;
  assigned_to: number;
}
