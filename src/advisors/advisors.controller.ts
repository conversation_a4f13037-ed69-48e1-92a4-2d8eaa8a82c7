import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  UseGuards,
  Query,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { Transaction } from 'src/shared/decorators/transaction.decorator';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { AdvisorsCrudService } from './services/advisors.crud.service';
import { AdvisorsDocusignService } from './services/advisors.docusign.service';
import { Roles, RolesGuard } from 'src/shared/guards/roles.guard';
import { AuthGuard } from '@nestjs/passport';
import { SelfGuard } from 'src/shared/guards/self.guard';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { OrganisationGuard } from 'src/shared/guards/organisation.guard';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { GetClientsQueryDto } from './dto/get-clients.dto';
import { GetCrmUsersQueryDto } from './dto/get-crm-users.dto';
import { CreateAdvisorDto } from './dto/create-advisor.dto';
import { UpdateAdvisorDto } from './dto/update-advisor.dto';
import { LinkCrmDto } from './dto/link-crm.dto';
import { UpdateDocusignAccountOwnershipDto } from './dto/update-docusign-account-ownership.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import { UploadDocumentRequestDto } from 'src/advisors/dto/upload-document.dto';
import { DownloadDocumentRequestDto } from 'src/advisors/dto/download-document.dto';
import { GetAdvisorsQueryDto } from './dto/get-advisors.dto';
import { fileFilter } from 'src/utils/filters/files.filter';
import { MAX_FILE_SIZE } from 'src/shared/types/general/asset-types.enum';
import mongoose, { ClientSession, FilterQuery } from 'mongoose';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { Advisor } from './schemas/advisors.schema';
import _ from 'lodash';
import {
  AdvisorViewDTO,
  AdvisorViewListDTO,
  AdvisorViewListItemDTO,
} from 'src/advisors/dto/advisors-view.dto';
import { ApiResponse } from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { UpdateDocusignAccountIdDto } from 'src/integrations/docusign/dto/update-docusign-account-id.dto';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { ClsService } from 'nestjs-cls';

@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'), RolesGuard, OrganisationGuard)
@Controller('organisations/:organisationId/advisors')
export class AdvisorsController {
  constructor(
    private readonly advisorsCrmService: AdvisorsCrmService,
    private readonly advisorsCrudService: AdvisorsCrudService,
    private readonly advisorsDocusignService: AdvisorsDocusignService,
    private transactionManager: TransactionManager,
    private cls: ClsService,
  ) {}

  @Post()
  @Roles(RolesEnum.CompanyAdmin)
  @Transaction()
  create(@Body() createAdvisorDto: CreateAdvisorDto, session?: ClientSession) {
    return this.advisorsCrudService.create(createAdvisorDto, session);
  }

  @Post('with-adv2b')
  @Roles(RolesEnum.CompanyAdmin)
  @UseInterceptors(
    FileInterceptor('adv2B', {
      limits: { fileSize: MAX_FILE_SIZE },
      fileFilter,
    }),
  )
  @Transaction()
  createWithAdv2b(
    @UploadedFile() file: Express.Multer.File,
    @Body() createAdvisorDto: CreateAdvisorDto,
    @Param('organisationId') organisationId: string,
    session?: ClientSession,
  ) {
    return this.advisorsCrudService.createWithAdv2b(
      createAdvisorDto,
      organisationId,
      file,
      session,
    );
  }

  @Get()
  @ApiResponse({ status: 200, type: AdvisorViewListDTO, isArray: true })
  async findAll(
    @Param('organisationId') orgId: string,
    @Query() query: GetAdvisorsQueryDto,
  ) {
    const { limit, page, status, role, name } = query;
    const filters: FilterQuery<Advisor> = _.omitBy(
      { status, role },
      _.isUndefined,
    );
    const additionalFilters: FilterQuery<Advisor> = {
      organisation: new mongoose.Types.ObjectId(orgId),
    };

    if (name) {
      additionalFilters.$expr = {
        $regexMatch: {
          input: {
            $concat: ['$personalInfo.firstName', ' ', '$personalInfo.lastName'],
          },
          regex: name,
          options: 'i',
        },
      };
    }

    const result = await this.advisorsCrudService.findAll(
      { limit, page },
      {
        ...filters,
        ...additionalFilters,
      },
    );

    return {
      ...result,
      result: result.result.map((advisor) => {
        return plainToInstance(AdvisorViewListItemDTO, advisor, {
          excludeExtraneousValues: true,
        });
      }),
    };
  }

  @Get(':advisorId')
  @ApiResponse({ status: 200, type: AdvisorViewDTO })
  async findOne(@Param('advisorId') advisorId: string) {
    const result = await this.advisorsCrudService.findOne({ _id: advisorId });
    return plainToInstance(AdvisorViewDTO, result, {
      excludeExtraneousValues: true,
    });
  }

  @UseGuards(SelfGuard)
  @Put(':advisorId')
  @Transaction()
  update(
    @Param('advisorId') advisorId: string,
    @Body() updateAdvisorDto: UpdateAdvisorDto,
    session?: ClientSession,
  ) {
    return this.advisorsCrudService.update(
      { _id: advisorId },
      updateAdvisorDto,
      session,
    );
  }

  @UseGuards(SelfGuard)
  @Delete(':advisorId')
  @Transaction()
  remove(@Param('advisorId') advisorId: string, session?: ClientSession) {
    return this.advisorsCrudService.remove({ _id: advisorId }, session);
  }

  @UseGuards(SelfGuard)
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.Representative)
  @Put(':advisorId/crm/link')
  linkCrm(
    @Param('advisorId') advisorId: string,
    @Body() linkCrmDto: LinkCrmDto,
  ) {
    return this.advisorsCrmService.linkCrm({ _id: advisorId }, linkCrmDto);
  }

  @UseGuards(SelfGuard)
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.Representative)
  @Put(':advisorId/crm/unlink')
  unlinkCrm(@Param('advisorId') advisorId: string) {
    return this.advisorsCrmService.unlinkCrm({ _id: advisorId });
  }

  @Get('crm/clients')
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.Representative)
  getClients(@Query() query: GetClientsQueryDto) {
    const advisor = this.cls.get(ClsDataEnum.Advisor);
    return this.advisorsCrmService.getCrmClients(query, advisor._id);
  }

  @Get('crm/users')
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.Representative)
  getCrmUsers(@Query() query: GetCrmUsersQueryDto) {
    const advisor = this.cls.get(ClsDataEnum.Advisor);
    return this.advisorsCrmService.getCrmUsers(query, advisor._id);
  }

  @UseGuards(SelfGuard)
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.Representative)
  @Put(':advisorId/docusign/unlink')
  unlinkDocusign(@Param('advisorId') advisorId: string) {
    return this.advisorsDocusignService.unlinkDocusign(advisorId);
  }

  @UseGuards(SelfGuard)
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.Representative)
  @Put(':advisorId/docusign/account')
  async setDocusignAccount(
    @Param('advisorId') advisorId: string,
    @Body() accountInfo: UpdateDocusignAccountIdDto,
  ) {
    return this.advisorsDocusignService.setDocusignAccount(
      advisorId,
      accountInfo.accountId,
    );
  }

  @Put(':advisorId/docusign/ownership')
  @UseGuards(SelfGuard)
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.Representative)
  async updateDocusignAccountOwnership(
    @Param('organisationId') organisationId: string,
    @Param('advisorId') advisorId: string,
    @Body()
    updateDocusignAccountOwnershipDto: UpdateDocusignAccountOwnershipDto,
  ) {
    return this.advisorsDocusignService.updateDocusignAccountOwnership(
      advisorId,
      updateDocusignAccountOwnershipDto,
    );
  }

  @Post(':advisorId/documents/upload')
  @UseInterceptors(
    FileInterceptor('file', {
      limits: { fileSize: MAX_FILE_SIZE },
    }),
  )
  async upload(
    @UploadedFile() file,
    @Param('organisationId') organisationId: string,
    @Param('advisorId') advisorId: string,
    @Body() body: UploadDocumentRequestDto,
  ): Promise<string> {
    return this.advisorsCrudService.uploadPrivateDocument(
      organisationId,
      advisorId,
      file,
      body.fileName,
      body.assetType,
    );
  }

  @Get(':advisorId/documents/download')
  async download(
    @Param('advisorId') id: string,
    @Query() query: DownloadDocumentRequestDto,
  ): Promise<string> {
    return this.advisorsCrudService.downloadPrivateDocument(id, query);
  }
}
