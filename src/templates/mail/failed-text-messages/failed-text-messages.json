{"templateName": "FailedTextMessage", "subject": "Text message delivery failed", "html": "<html>\n  <head>\n    <title>Text message delivery failed</title>\n  </head>\n  <body style='background-color: #EEE'>\n    <div\n      style='font-family: Roboto,sans-serif;color:#5c5c5c;padding:50px;max-width:500px;border: 2px solid #CCC;border-radius:10px;background-color:white;margin:auto'\n    >\n      <div>\n        <img\n          width='120'\n          src='https://onbord-global-assets.s3.amazonaws.com/onbord-default-logo.png'\n        />\n      </div>\n      <p>There was an error delivering a Text Message to your mobile device\n        (phone number\n        <b>{{clientMobilePhone}}</b>)\n      </p>\n\n      <p>Please Text START from your mobile device to\n        <a\n          clicktracking='off'\n          th:href=\"@{'sms:'+{{onbordMobilePhone}}+'?&body=START'}\"\n          th:text='{{onbordMobilePhone}}'\n        >{{onbordMobilePhone}}</a>\n        to enable Text Messages from us again.</p>\n\n      <p>The original undeliverable Text Message content is included below.</p>\n\n      <pre style='white-space: pre-wrap;'>\n        {{failedTextBody}}\n      </pre>\n    </div>\n  </body>\n</html>"}