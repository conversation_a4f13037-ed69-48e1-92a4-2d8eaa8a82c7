import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery } from 'mongoose';
import { InterviewTemplate } from '../../schemas/v1/interview.template';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';

/**
 * Shared service containing common business logic for all versions
 * of the interview templates API. This promotes code reuse and
 * maintains consistency across versions.
 */
@Injectable()
export class InterviewTemplatesSharedService {
  constructor(
    @InjectModel(InterviewTemplate.name)
    private model: Model<InterviewTemplate>,
  ) {}

  /**
   * Common method to get paginated results with shared filtering logic
   */
  async findWithPagination(
    filter: FilterQuery<InterviewTemplate>,
    paginationQuery: PaginationQueryDto,
  ) {
    const { page = 1, limit = 10 } = paginationQuery;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.model
        .find(filter)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.model.countDocuments(filter).exec(),
    ]);

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * Common validation logic that can be reused across versions
   */
  async validateTemplateExists(templateId: string): Promise<InterviewTemplate> {
    const template = await this.model.findById(templateId).exec();
    if (!template) {
      throw new Error(`Interview template with ID ${templateId} not found`);
    }
    return template;
  }

  /**
   * Common method to check if template belongs to organization
   */
  async validateTemplateOwnership(
    templateId: string,
    organisationId: string,
  ): Promise<boolean> {
    const template = await this.model
      .findOne({
        _id: templateId,
        organisationId,
      })
      .exec();
    
    return !!template;
  }
}