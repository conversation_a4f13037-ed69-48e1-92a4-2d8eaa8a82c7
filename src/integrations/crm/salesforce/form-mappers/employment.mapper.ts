import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { SalesforceCRM } from 'src/integrations/crm/salesforce/crm.salesforce';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';

interface EmploymentPageData {
  status: string;
}

export class SalesforceEmploymentMapper implements FormUpdateMapper {
  constructor(private salesforceCRM: SalesforceCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: EmploymentPageData,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    
    // Remove company and job pages if the user is retired, a student, or a homemaker
    if (
      ['retired', 'student', 'home-maker', 'homemaker'].includes(
        pageData.status.toLowerCase(),
      )
    ) {
      await this.salesforceCRM.interviewsService.removePageFromInterview(
        interview._id.toString(),
        PagesEnum.COMPANY,
      );
      await this.salesforceCRM.interviewsService.removePageFromInterview(
        interview._id.toString(),
        PagesEnum.JOB,
      );
    } else {
      // Add company and job pages if the user is not retired, a student, or a homemaker
      await this.salesforceCRM.interviewsService.addPageToInterview(
        interview._id.toString(),
        PagesEnum.COMPANY,
      );
      await this.salesforceCRM.interviewsService.addPageToInterview(
        interview._id.toString(),
        PagesEnum.JOB,
      );
    }

    await this.salesforceCRM.contactService.updateContact(contact.crmClientId, {
      Onbord_Employment_Status__c: pageData.status,
    });
  }
} 