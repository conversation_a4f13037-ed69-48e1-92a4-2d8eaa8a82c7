import mongoose from 'mongoose';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { RedtailNameMapper } from '../name.mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { SuffixEnum } from 'src/shared/types/general/suffix.enum';
import { NamePageUpdateDto } from 'src/shared/types/pages/dto/name-update.dto';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';

// Mock RedtailCRM class
jest.mock('src/integrations/crm/redtail/crm.redtail');

describe('RedtailNameMapper', () => {
  let redtailCRM: jest.Mocked<RedtailCRM>;
  let nameMapper: RedtailNameMapper;

  const mockOrganisation = { 
    _id: 'org123',
    name: 'Test Organisation'
  } as Organisation;
  
  const mockAdvisor = { 
      _id: 'adv123',
      firstName: 'Test',
      lastName: 'Advisor'
  } as unknown as AdvisorWithRole;

  // Mock SuperHttpService
  const mockHttpService = {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    patch: jest.fn(),
    rateLimiters: new Map(),
    defaultRateLimiterKey: 'default',
    logger: { log: jest.fn(), error: jest.fn(), warn: jest.fn(), debug: jest.fn() },
    initialized: true,
    getRateLimiterStats: jest.fn(),
    invalidateEntityTypeCache: jest.fn(),
    invalidateSpecificCache: jest.fn(),
    checkLimiterHealth: jest.fn(),
    addInterceptor: jest.fn(),
    clearInterceptors: jest.fn()
  };

  // Mock InterviewsService
  const mockInterviewsService = {
    flowProducer: {},
    assetModel: {},
    interviewModel: {},
    interviewTemplateService: {},
    createInterview: jest.fn(),
    getInterview: jest.fn(),
    getInterviews: jest.fn(),
    updateInterview: jest.fn(),
    deleteInterview: jest.fn()
  };

  // Mock MailService
  const mockMailService = {
    mailerService: {},
    configService: {},
    logger: { log: jest.fn(), error: jest.fn(), warn: jest.fn(), debug: jest.fn() },
    advisorsCrmService: {},
    notificationsService: {},
    clientsService: {},
    organisationService: {},
    sendClientWelcomeEmail: jest.fn(),
    sendClientInterviewEmailDesktop: jest.fn(),
    sendUserMfaTokenEmail: jest.fn()
  };

  // Mock Logger
  const mockLogger = { 
    error: jest.fn(), 
    warn: jest.fn(), 
    info: jest.fn(),
    debug: jest.fn(),
    log: jest.fn()
  };

  beforeEach(() => {
    redtailCRM = new RedtailCRM(
      mockAdvisor,
      mockOrganisation,
      {
        username: 'test',
        password: 'test',
        apiKey: 'test',
        userKey: 'test',
        databaseId: 'test',
        userId: 'test'
      },
      mockHttpService as any,
      mockInterviewsService as any,
      mockMailService as any,
      mockLogger as any
    ) as jest.Mocked<RedtailCRM>;

    // Setup mock for updateContactName method
    redtailCRM.updateContactName = jest.fn().mockResolvedValue({
      last_name: 'Doe',
      first_name: 'John'
    });

    // Setup mock for patchContact method which is called by updateContactName
    redtailCRM.patchContact = jest.fn().mockResolvedValue({
      last_name: 'Doe',
      first_name: 'John'
    });

    nameMapper = new RedtailNameMapper(redtailCRM);
  });

  describe('mapPageData', () => {
    it('should call updateContactName with correct arguments', async () => {
      // Mock data
      const mockInterview = {
        isPrimary: true
      } as EnrichedInterview;
      
      const mockClient = {
        primaryContact: {
          crmClientId: '123'
        }
      } as EnrichedClient;
      
      const pageData: NamePageUpdateDto = {
        firstName: 'John',
        lastName: 'Doe',
        middleName: 'M',
        suffix: SuffixEnum.SR
      };

      // Call method
      await nameMapper.mapPageData(
        mockInterview,
        mockClient,
        mockOrganisation,
        pageData
      );

      // Check if updateContactName was called with correct arguments
      expect(redtailCRM.updateContactName).toHaveBeenCalledWith(
        '123',
        'John',
        'Doe',
        'M',
        SuffixEnum.SR
      );
    });

    it('should use secondaryContact when interview is not primary', async () => {
      // Mock data
      const mockInterview = {
        isPrimary: false
      } as EnrichedInterview;
      
      const mockClient = {
        primaryContact: {
          crmClientId: '123'
        },
        secondaryContact: {
          crmClientId: '456'
        }
      } as EnrichedClient;
      
      const pageData: NamePageUpdateDto = {
        firstName: 'Jane',
        lastName: 'Doe',
        middleName: 'A',
        suffix: SuffixEnum.JR
      };

      // Call method
      await nameMapper.mapPageData(
        mockInterview,
        mockClient,
        mockOrganisation,
        pageData
      );

      // Check if updateContactName was called with correct arguments for secondary contact
      expect(redtailCRM.updateContactName).toHaveBeenCalledWith(
        '456',
        'Jane',
        'Doe',
        'A',
        SuffixEnum.JR
      );
    });

    it('should return response from updateContactName', async () => {
      // Mock data
      const mockInterview = {
        isPrimary: true
      } as EnrichedInterview;
      
      const mockClient = {
        primaryContact: {
          crmClientId: '123'
        }
      } as EnrichedClient;
      
      const pageData: NamePageUpdateDto = {
        firstName: 'John',
        lastName: 'Doe',
        middleName: 'M',
        suffix: SuffixEnum.SR
      };

      // Call method
      await nameMapper.mapPageData(
        mockInterview,
        mockClient,
        mockOrganisation,
        pageData
      );

      // The method doesn't return a value according to its definition (Promise<void>)
      // So we just verify that the mock was called correctly
      expect(redtailCRM.updateContactName).toHaveBeenCalled();
    });
  });
});
