# redis_minimal.tf

# Subnet group for placing Redis in your private subnets
resource "aws_elasticache_subnet_group" "redis_subnet_group" {
  name       = "onbord-redis-${var.environment}-subnet-group"
  subnet_ids = var.subnet_ids
}

# Security group: allow inbound from your EB SG on port 6379
resource "aws_security_group" "redis_sg" {
  name_prefix = "onbord-ec-redis-${var.environment}"
  description = "Security group for Redis cluster (no TLS)"
  vpc_id      = var.vpc_id

  ingress {
    from_port       = 6379
    to_port         = 6379
    protocol        = "tcp"
    security_groups = [var.elastic_beanstalk_security_group_id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "onbord-redis-${var.environment}"
    Environment = var.environment
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Generate a random password to use as Redis auth token
resource "random_password" "redis_auth" {
  length  = 32
  special = false
}

# Single-node replication group with password enforcement
resource "aws_elasticache_replication_group" "redis" {
  replication_group_id          = "onbord-redis-${var.environment}"
  description                   = "Redis cluster for Onbord ${var.environment} environment"
  engine                        = "redis"
  engine_version                = "7.0"

  # Single node only (primary, 0 replicas), with TLS
  # 'auth_token' requires encryption in transit to be enabled
  auth_token                   = random_password.redis_auth.result
  at_rest_encryption_enabled   = true
  transit_encryption_enabled   = true
  automatic_failover_enabled   = false
  num_node_groups              = 1
  replicas_per_node_group      = 0
  node_type                    = var.node_type
  security_group_ids           = [aws_security_group.redis_sg.id]
  subnet_group_name            = aws_elasticache_subnet_group.redis_subnet_group.name
  port                         = 6379
  apply_immediately            = true
  auto_minor_version_upgrade   = true

  # If it's production, keep backups
  snapshot_retention_limit = var.environment == "production" ? 7 : 0

  tags = {
    Name        = "onbord-redis-${var.environment}"
    Environment = var.environment
  }
}

# Store the primary endpoint and port in SSM so EB can read them
resource "aws_ssm_parameter" "redis_host" {
  name  = "/onbord/backend/${var.environment}/REDIS_HOST"
  type  = "String"
  value = aws_elasticache_replication_group.redis.primary_endpoint_address
}

resource "aws_ssm_parameter" "redis_port" {
  name  = "/onbord/backend/${var.environment}/REDIS_PORT"
  type  = "String"
  value = tostring(aws_elasticache_replication_group.redis.port)
}

# Also store the password in SSM (SecureString)
resource "aws_ssm_parameter" "redis_password" {
  name  = "/onbord/backend/${var.environment}/REDIS_PASSWORD"
  type  = "SecureString"
  value = random_password.redis_auth.result
} 