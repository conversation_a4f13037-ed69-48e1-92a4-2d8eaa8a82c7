import { HttpException } from '@nestjs/common';
import { isEmpty } from 'lodash';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { CustomQuestionPayloadDto } from 'src/integrations/crm/redtail/form-mappers/dto/custom-question-request.dto';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

export class RedtailCustomQuestionsMapper {
  constructor(private redtailCrm: RedtailCRM) {}

  /**
   * Updates the contact company and address in Redtail CRM.
   * @param userId The ID of the user.
   * @param redtailContactId The ID of the contact in Redtail CRM.
   * @param companyName The name of the company.
   * @param address The address to update.
   * @returns A Promise that resolves to a boolean indicating success.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    payload: CustomQuestionPayloadDto,
  ): Promise<void> {
    if (!payload.answer) {
      throw new HttpException('Answer is required', 400);
    }
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const existingInfo =
      await this.redtailCrm.redtailImportantInformationService.getImportantInformation(
        contact.crmClientId.toString(),
      );

    const newContent = this.updateImportantInfoContent(
      existingInfo.importantInformation.content,
      payload.question.question,
      payload.answer,
    );
    await this.redtailCrm.redtailImportantInformationService.updateImportantInformation(
      contact.crmClientId.toString(),
      {
        content: newContent,
      },
    );
  }

  private updateImportantInfoContent(
    existingContent: string,
    newQuestion: string,
    newAnswer: string,
  ): string {
    const questionAnswerPattern = `Question: ${newQuestion} - Answer: `;
    const newQuestionAnswerPair = `${questionAnswerPattern}${newAnswer}<br>`;

    if (isEmpty(existingContent)) {
      return newQuestionAnswerPair;
    }

    const lines = existingContent.split('<br>');

    let updated = false;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith(questionAnswerPattern)) {
        lines[i] = newQuestionAnswerPair.trim(); // Replace existing line
        updated = true;
        break;
      }
    }

    if (!updated) {
      // If not updated, append new question-answer pair
      if (!existingContent.endsWith('<br>') && existingContent.length > 0) {
        existingContent += '<br>';
      }
      existingContent += newQuestionAnswerPair;
    } else {
      existingContent = lines.join('<br>');
    }

    return existingContent;
  }
}
