import {
  HttpException,
  HttpStatus,
  InternalServerErrorException,
} from '@nestjs/common';
import { createReadStream } from 'fs';
import { PassThrough, Readable } from 'stream';
import { isStreamReadable } from 'src/utils/file/file.util';

/**
 * Retrieves the file stream from the file object.
 *
 * @param file - The file object containing the stream, path, or buffer.
 * @returns The readable file stream.
 * @throws InternalServerErrorException if the file could not be found in memory or disk.
 */
export function getReadableStreamFromFile(file: Express.Multer.File): Readable {
  let fileStream: Readable;

  if (file.stream && isStreamReadable(file.stream)) {
    // Clone the stream
    const cloneStream = new PassThrough();
    const maintenanceStream = new PassThrough();
    file.stream.pipe(cloneStream);
    file.stream.pipe(maintenanceStream);
    file.stream = maintenanceStream;
    return cloneStream;
  } else if (file.path) {
    const diskPath = file.path;
    fileStream = createReadStream(diskPath);
    fileStream.on('error', (err) => {
      throw new HttpException(
        `Error reading file: ${err.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    });
  } else if (file.buffer) {
    fileStream = Readable.from(file.buffer);
  } else {
    throw new InternalServerErrorException(
      `File could not be found in memory or disk. Error for file ${file.originalname}`,
    );
  }

  return fileStream;
}
