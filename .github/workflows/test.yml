name: Linting and Testing

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    types:
      - opened
    branches:
      - main
      - feature/*
      - bugfix/*
      - poc/*

env:
  AWS_REGION: us-east-1

jobs:
  test:
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: .

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up JDK 11
        uses: actions/setup-java@v2
        with:
          java-version: "11"
          distribution: "adopt"

      - name: Cache SonarCloud packages
        uses: actions/cache@v2
        with:
          path: ~/.sonar/cache
          key: ${{ runner.os }}-sonar
          restore-keys: ${{ runner.os }}-sonar

      - name: Use Node.js
        uses: actions/setup-node@v1
        with:
          node-version: 18

      - name: Cache Node.js modules
        uses: actions/cache@v2
        with:
          path: ~/.npm
          key: ${{ runner.OS }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: ${{ runner.OS }}-node-

      - name: Install Dependencies
        run: npm i && npm uninstall sharp && npm install sharp

      - name: Run Lint
        run: npm run lint

      - name: Run Tests
        run: npm run test

      # - name: SonarCloud Scan
      #   uses: SonarSource/sonarcloud-github-action@master
      #   env:
      #     GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      #     SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
