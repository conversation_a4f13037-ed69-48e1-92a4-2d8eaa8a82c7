import { Test, TestingModule } from '@nestjs/testing';
import { HealthController } from 'src/health/health.controller';
import { HealthService } from 'src/health/health.service';

describe('HealthController', () => {
  let healthController: HealthController;
  let healthService: jest.Mocked<HealthService>;

  beforeEach(async () => {
    const healthServiceMock = {
      checkHealth: jest.fn().mockReturnValue({ status: 'ok' }),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [HealthController],
      providers: [{ provide: HealthService, useValue: healthServiceMock }],
    }).compile();

    healthController = module.get<HealthController>(HealthController);
    healthService = module.get<HealthService>(
      HealthService,
    ) as jest.Mocked<HealthService>;
  });

  it('should call HealthService checkHealth method and return its result', async () => {
    const result = { status: 'ok' };

    expect(await healthController.check()).toEqual(result);
    expect(healthService.checkHealth).toBeCalled();
  });
});
