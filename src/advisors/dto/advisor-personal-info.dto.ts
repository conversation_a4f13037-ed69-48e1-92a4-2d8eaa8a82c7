import { IsEmail, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { AddressDto } from '../../shared/dto/address.dto';
import { ApiProperty } from '@nestjs/swagger';

export class AdvisorPersonalInfoDto {
  @IsString()
  @ApiProperty()
  @IsOptional()
  firstName?: string;

  @IsString()
  @ApiProperty()
  @IsOptional()
  lastName?: string;

  @ValidateNested()
  @Type(() => AddressDto)
  @ApiProperty()
  @IsOptional()
  address?: AddressDto;

  @IsString()
  @IsOptional()
  @ApiProperty()
  phone?: string;

  @IsEmail()
  @ApiProperty()
  @IsOptional()
  email?: string;
}
