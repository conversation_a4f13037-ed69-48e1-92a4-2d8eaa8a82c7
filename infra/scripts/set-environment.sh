#!/bin/bash

echo "TF_VAR_commit_sha=$COMMIT_SHA" >> $GITHUB_ENV

function set_env() {
    echo "TF_VAR_environment=$1" >> $GITHUB_ENV
}

function set_infra_env() {
    echo "infra_environment=$1" >> $GITHUB_ENV
}

function create_backend_file() {
    # Create the ephemeral backend.tf file based on the backend.template file
    # Get the path to the script
    local script_path="$( cd "$(dirname "$0")" ; pwd -P )"
    # Replace the placeholder in the backend template with the environment name
    sed 's/%ENVIRONMENT%/'"$1"'/g' "${script_path}/../environments/ephemeral/backend.template" > "${script_path}/../environments/ephemeral/backend.tf"
}

# This is a bash script used to set the environment variable `TF_VAR_environment` 
# for Terraform depending on the branch name in a GitHub Actions workflow.
branch=$(git rev-parse --abbrev-ref HEAD)

# Sanitize the branch name by converting slashes to dashes and removing special characters
sanitized_branch=$(echo $branch | tr '/' '-' | sed 's/[^a-zA-Z0-9\-]//g')

# Check if an environment argument is passed
if [ -n "$1" ]; then
    case "$1" in
        production)
            set_env "production"
            set_infra_env "production"
            ;;
        staging)
            set_env "staging"
            set_infra_env "staging"
            ;;
        *)
            echo "Invalid environment argument. Only 'production' or 'staging' are valid." >&2
            exit 1
            ;;
    esac
else
    if [[ "$branch" == "main" || "$branch" == "staging" ]]; then
        set_env "staging"
        set_infra_env "staging"
    elif [[ "$branch" =~ ^feature/.*$ ]]; then
        set_env "ephemeral-${sanitized_branch#feature-}"
        set_infra_env "ephemeral"
        create_backend_file "ephemeral-${sanitized_branch#feature-}"
    else
        echo "The branch name does not match any known patterns." >&2
        exit 1
    fi
fi
