import { EmailTemplateNameEnum } from 'src/templates/mail/types';
import { DesktopInterviewEmailContext } from 'src/templates/mail/desktop-interview/desktop-interview.context';
import { FailedTextMessagesEmailContext } from 'src/templates/mail/failed-text-messages/failed-text-messages.context';
import { NonCitizenEmailContext } from 'src/templates/mail/non-citizen/non-citizen.context';
import { ReadyToSignEmailContext } from 'src/templates/mail/ready-to-sign/ready-to-sign.context';
import { ClientWelcomeEmailContext } from 'src/templates/mail/client-welcome/client-welcome.context';
import { StatementUploadedContext } from 'src/templates/mail/statement-uploaded/statement-uploaded.context';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

export type EmailContext =
  | EmailTemplateNameEnum
  | DesktopInterviewEmailContext
  | FailedTextMessagesEmailContext
  | NonCitizenEmailContext
  | ReadyToSignEmailContext
  | ClientWelcomeEmailContext
  | StatementUploadedContext;

export type S3Attachment = {
  key: string;
  name: string;
};

export type SendEmailOptions = {
  to: string;
  subject?: string;
  text?: string;
  templateName?: EmailTemplateNameEnum;
  context?: object & EmailContext;
  attachments?: S3Attachment[] | Express.Multer.File[];
  organisation: Organisation; // Make organisation a required parameter
};
