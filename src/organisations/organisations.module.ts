import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CRMModule } from 'src/integrations/crm/crm.module';
import { CognitoService } from 'src/shared/services/cognito.service';
import { DocusignModule } from 'src/integrations/docusign/docusign.module';
import { OrganisationsService } from './organisations.service';
import { OrganisationsController } from './organisations.controller';
import {
  Organisation,
  OrganisationSchema,
} from './schemas/organisation.schema';
import { Asset, AssetSchema } from 'src/shared/schemas/asset.schema';
import { ConfigModule } from '@nestjs/config';
import { AssetsModule } from 'src/assets/assets.module';
import { ClsModule } from 'nestjs-cls';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { AdvisorsModule } from 'src/advisors/advisors.module';
import { OrganisationsNotificationsService } from './organisations.notifications.service';
import { CacheModule } from '@nestjs/cache-manager';
import { EmailTemplatesService } from './services/email-templates.service';
import { EmailTemplatesController } from './controllers/email-templates.controller';
import { EmailTemplate, EmailTemplateSchema } from './schemas/email-template.schema';
import { NotificationsModule } from 'src/notifications/notifications.module';

@Module({
  imports: [
    CacheModule.register(),
    MongooseModule.forFeature([
      { name: Organisation.name, schema: OrganisationSchema },
      { name: EmailTemplate.name, schema: EmailTemplateSchema },
    ]),
    MongooseModule.forFeature([{ name: Asset.name, schema: AssetSchema }]),
    forwardRef(() => DocusignModule),
    ConfigModule,
    AssetsModule,
    ClsModule,
    forwardRef(() => AdvisorsModule),
    forwardRef(() => NotificationsModule),
  ],
  controllers: [OrganisationsController, EmailTemplatesController],
  providers: [
    OrganisationsService,
    OrganisationsNotificationsService,
    EmailTemplatesService,
    CognitoService,
    TransactionManager,
  ],
  exports: [
    OrganisationsService,
    OrganisationsNotificationsService,
    EmailTemplatesService,
  ],
})
export class OrganisationsModule {}
