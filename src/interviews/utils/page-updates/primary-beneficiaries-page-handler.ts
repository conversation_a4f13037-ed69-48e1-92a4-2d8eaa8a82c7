import { isEmpty } from 'lodash';
import { InterviewPage } from 'src/interview-templates/schemas/v1/interview.template';
import { GenericPageHandler } from 'src/interviews/utils/page-updates/generic-page-handler';

/**
 * A class that handles updates for the primary beneficiaries page in an interview.
 * Extends the GenericPageHandler class.
 */
export class PrimaryBeneficiariesPageHandler extends GenericPageHandler {
  /**
   * Determines whether the page should be updated based on the provided InterviewPage object.
   * @param page - The InterviewPage object to check for updates.
   * @returns A boolean indicating whether the page should be updated.
   */
  protected shouldUpdate(page: InterviewPage): boolean {
    if (isEmpty(this.updateDto.page.data?.instance)) {
      return false;
    }
    return (
      super.shouldUpdate(page) &&
      page.data?.instance === this.updateDto.page.data?.instance?.name &&
      page.data?.label === this.updateDto.page.data?.instance?.label
    );
  }

  /**
   * Handles updates for the provided InterviewPage object.
   * @param page - The InterviewPage object to handle updates for.
   * @returns The updated InterviewPage object.
   */
  handle(page: InterviewPage): InterviewPage {
    super.handle(page);
    if (
      this.shouldUpdate(page) &&
      isEmpty(this.updateDto.page.data?.beneficiaries)
    ) {
      this.context.fillContingentBeneficiaries = true;
    }
    return page;
  }
}
