import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { OrganisationGuard } from 'src/shared/guards/organisation.guard';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { ApiTags, ApiResponse } from '@nestjs/swagger';
import { InterviewV2Facade } from '../../services/v2/interview-v2.facade';
import { CreateInterviewV2Dto } from '../../dto/v2/create-interview-v2.dto';
import { SubmitPageV2Dto } from '../../dto/v2/submit-page-v2.dto';
import { NavigationStateV2Dto } from '../../dto/v2/navigation-state-v2.dto';
import { PageNavigationResultV2Dto } from '../../dto/v2/page-navigation-result-v2.dto';
import { InterviewResponseV2Dto } from '../../dto/v2/interview-response-v2.dto';

@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'), OrganisationGuard)
@Controller({ path: 'interviews', version: '2' })
@ApiTags('Interviews V2')
export class InterviewsV2Controller {
  constructor(private readonly facade: InterviewV2Facade) {}

  @Post()
  @ApiResponse({ type: InterviewResponseV2Dto })
  create(@Body() dto: CreateInterviewV2Dto) {
    return this.facade.create(dto);
  }

  @Get(':interviewId')
  @ApiResponse({ type: InterviewResponseV2Dto })
  async findOne(@Param('interviewId') interviewId: string) {
    const interview = await this.facade.findById(interviewId);
    return interview;
  }

  @Get('/client/:clientId')
  async findByClientId(@Param('clientId') clientId: string) {
    const interviews = await this.facade.findByClient(clientId);
    return interviews;
  }

  @Post(':interviewId/pages/submit')
  @ApiResponse({ type: PageNavigationResultV2Dto })
  async submitPage(
    @Param('interviewId') interviewId: string,
    @Body() dto: SubmitPageV2Dto,
  ) {
    return this.facade.submitPage(interviewId, dto);
  }

  @Get(':interviewId/navigation/state')
  @ApiResponse({ type: NavigationStateV2Dto })
  async getNavigationState(@Param('interviewId') interviewId: string) {
    return this.facade.getCurrentState(interviewId);
  }

  @Post(':interviewId/navigation/back')
  @ApiResponse({ type: PageNavigationResultV2Dto })
  async navigateBack(@Param('interviewId') interviewId: string) {
    return this.facade.navigateBack(interviewId);
  }

  @Get(':interviewId/pages/:pageId')
  async getPage(
    @Param('interviewId') interviewId: string,
    @Param('pageId') pageId: string,
  ) {
    return this.facade.getPage(interviewId, pageId);
  }

  @Post(':interviewId/finish')
  finish(@Param('interviewId') id: string) {
    return this.facade.complete(id);
  }

  @Get(':interviewId/sendDesktopInterviewEmail')
  async sendDesktopInterviewEmail(@Param('interviewId') interviewId: string) {
    return this.facade.sendDesktopInterviewEmail(interviewId);
  }

  @Get(':interviewId/sendNonCitizenEmail')
  async sendNonCitizenEmail(@Param('interviewId') interviewId: string) {
    return this.facade.sendNonCitizenEmail(interviewId);
  }
}