import { IsString, IsOptional } from 'class-validator';

export interface SalesforceContactDto {
  // Standard Salesforce Contact fields
  Id?: string;
  FirstName?: string;
  LastName?: string;
  Email?: string;
  Phone?: string;
  MobilePhone?: string;
  HomePhone?: string;
  OtherPhone?: string;
  Title?: string;
  Birthdate?: string;
  AccountId?: string;
  
  // Standard Mailing Address fields
  MailingStreet?: string;
  MailingCity?: string;
  MailingState?: string;
  MailingPostalCode?: string;
  MailingCountry?: string;
  OtherStreet?: string;
  OtherCity?: string;
  OtherState?: string;
  OtherPostalCode?: string;
  OtherCountry?: string;

  // Custom fields (with __c suffix)
  Onbord_Legal_Name__c?: string;
  Onbord_SSN__c?: string;
  Onbord_US_Citizen__c?: boolean;
  Onbord_US_Resident__c?: boolean;
  Onbord_Employment_Status__c?: string;
  Onbord_Industry_Affiliation__c?: string | null;
  Onbord_Company__c?: string;
  Onbord_Company_Address__c?: string;
  Onbord_Title__c?: string;
  Onbord_Job_Description__c?: string;
  Onbord_Affiliated_Company__c?: string | null;
  Onbord_Ticker__c?: string | null;
  External_Id__c?: string;

  // Dynamic fields for beneficiaries
  [key: string]: any; // Allow dynamic field names for beneficiaries and custom questions
}

export interface SalesforceTaskDto {
  Subject: string;
  Description?: string;
  Status: string;
  Priority: string;
  ActivityDate: string;
  WhoId?: string;
  WhatId?: string;
  OwnerId?: string;
}

export class SalesforceFinancialAccountDto {
  @IsString()
  Name: string;

  @IsString()
  @IsOptional()
  FinancialAccountType?: string;

  @IsString()
  @IsOptional()
  FinancialAccountNumber?: string;

  @IsString()
  @IsOptional()
  Status?: string;

  @IsString()
  @IsOptional()
  Currency?: string;
}

export interface SalesforceCustomAccountDto {
  Id?: string;
  Name: string;

  // Custom fields (with __c suffix)
  Idempotency_key__c: string;
  label__c: string
  advisory_rate__c: string
  type__c: string
  ownership__c: string
  masterAccountNumber__c: string
  features__c?: string;
}

export interface SalesforceBeneficiaryDto {
  Name: string;
  Percentage__c: string;
  Date_of_Birth__c: string;
}

export interface SalesforceCustomQuestion {
  Question__c: string;
  Answer__c: string;
}

export interface SalesforceAddressUpdateRequestDto {
  legalAddress: {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    zip: string;
  };

  mailingAddress?: {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    zip: string;
  };
}