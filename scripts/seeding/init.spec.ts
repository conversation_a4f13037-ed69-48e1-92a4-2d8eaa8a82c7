import { connect, model, connection } from 'mongoose';
import { initializationScript } from './init';
import { cleanUpMongo, seed } from './seed';
import { getSSM } from './ssm';

jest.mock('mongoose', () => ({
  connect: jest.fn(),
  model: jest.fn().mockReturnValue({
    findById: jest.fn().mockResolvedValue(null),
    create: jest.fn(),
    findByIdAndUpdate: jest.fn(),
  }),
  connection: {
    close: jest.fn(),
  },
  Schema: jest.fn(),
  Document: jest.fn(),
}));

jest.mock('./seed', () => ({
  cleanUpMongo: jest.fn(),
  seed: jest.fn(),
}));

jest.mock('./ssm', () => ({
  getSSM: jest.fn(),
}));

jest.mock('./mongo_hosts.json', () => ({
  testEnv: 'mongoHostTest',
}));

describe.skip('initializationScript', () => {
  beforeEach(() => {
    (getSSM as jest.Mock).mockImplementation(() =>
      Promise.resolve('testValue'),
    );
    (model as jest.Mock).mockReturnValue({
      findById: jest.fn().mockResolvedValue(null),
      create: jest.fn(),
      findByIdAndUpdate: jest.fn(),
    });
  });

  it('should perform initialization if environment is set', async () => {
    process.env.ENV = 'testEnv';

    await initializationScript();

    expect(cleanUpMongo).toHaveBeenCalled();
    expect(seed).toHaveBeenCalled();
    expect(connect).toHaveBeenCalledWith(
      '****************************************************************************',
    );
  });
});
