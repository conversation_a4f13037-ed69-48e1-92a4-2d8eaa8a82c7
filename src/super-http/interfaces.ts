import { OperationType } from "src/super-http/enums";

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
  ttl?: number;
  tls?: boolean;
}

export interface RateLimitConfig {
  maxConcurrent?: number;
  minTime?: number;
  reservoir?: number;
  timeout?: number;
  highWater?: number;
  reservoirRefreshAmount?: number;
  reservoirRefreshInterval?: number;
}

export interface RateLimiterGroupConfig {
  [key: string]: RateLimitConfig;
}

export interface SuperHttpModuleConfig {
  redis: RedisConfig;
  rateLimiters: RateLimiterGroupConfig;
  defaultRateLimiter?: string;
  timeout?: number; // Request timeout in milliseconds, defaults to 30000 (30 seconds)
  axios?: Record<string, any>;
} 

export interface CacheOptions {
    ttl?: number;
    forceFresh?: boolean;
    cacheKeysObject?: Record<string, any>;
    entityType: string;
    operation: OperationType;
    entityId?: string;
    integrationName: string;
    wideCacheInvalidation?: boolean;
}

export interface RequestOptions extends CacheOptions {
  rateLimiter: string;
}

export interface RateLimiterConfig {
    minTime: number;
    maxConcurrent: number;
    reservoir?: number;
    reservoirRefreshAmount?: number;
    reservoirRefreshInterval?: number;
    highWater?: number;
    timeout?: number;
}