import { VariableType } from './variable-type.enum';

export interface VariableDefinition {
  /** Human-readable description of what this variable represents */
  description: string;
  
  /** The data type of this variable */
  type: VariableType;
  
  /** Optional example value for documentation */
  example?: any;
  
  /** Whether this variable is always available or conditional */
  isConditional?: boolean;
}