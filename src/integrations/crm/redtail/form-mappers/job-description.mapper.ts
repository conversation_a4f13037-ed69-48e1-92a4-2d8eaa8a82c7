import { HttpException } from '@nestjs/common';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { JobDescriptionStatusRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/job-description-request.dto';
import { RedtailContactUdfNameEnum } from 'src/integrations/crm/redtail/types/enums';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

export class RedtailJobDescriptionMapper implements FormUpdateMapper {
  constructor(private redtailCrm: RedtailCRM) {}

  /**
   * Maps the page data for a job description status request to a Redtail CRM contact UDF field.
   * @param clientCrmId The ID of the client in the Redtail CRM.
   * @param organisation The organisation object containing the configuration for the Redtail CRM.
   * @param pageData The job description status request DTO containing the page data to be mapped.
   * @throws HttpException if the job description UDF is not configured.
   * @returns A Promise that resolves when the contact UDF has been updated in the Redtail CRM.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: JobDescriptionStatusRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    const contactUdfFieldId =
      organisation.configuration[RedtailContactUdfNameEnum.JOB_DESCRIPTION];

    if (!contactUdfFieldId) {
      throw new HttpException('Job description UDF not configured', 500);
    }

    let jobDescription = pageData.description;

    if (pageData.type && pageData.type !== '') {
      jobDescription = pageData.type;
    }

    await this.redtailCrm.redtailUdfService.upsertContactUdf(
      contact.crmClientId,
      {
        contactUdfFieldId,
        fieldValue: jobDescription,
      },
    );
  }
}
