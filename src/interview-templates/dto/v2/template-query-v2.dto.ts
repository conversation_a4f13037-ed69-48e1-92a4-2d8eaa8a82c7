import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>num, <PERSON><PERSON><PERSON>y, IsString, IsBoolean, IsN<PERSON>ber, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { TemplateTypeEnum } from '../../types/template-type.enum';

export class TemplateQueryV2Dto {
  @ApiPropertyOptional({ enum: ['draft', 'published', 'archived', 'deprecated'] })
  @IsOptional()
  @IsEnum(['draft', 'published', 'archived', 'deprecated'])
  status?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiPropertyOptional()
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isDefault?: boolean;

  @ApiPropertyOptional({ enum: TemplateTypeEnum })
  @IsOptional()
  @IsEnum(TemplateTypeEnum)
  templateType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  accountType?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  page?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  limit?: number;
}