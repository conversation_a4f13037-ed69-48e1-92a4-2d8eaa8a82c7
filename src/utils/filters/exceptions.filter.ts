import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  Inject,
} from '@nestjs/common';
import { isAxiosError } from '@nestjs/terminus/dist/utils';
import { Request, Response } from 'express';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  catch(exception: unknown, host: ArgumentsHost) {
    try {
      const ctx = host.switchToHttp();
      const response = ctx.getResponse<Response>();
      const request = ctx.getRequest<Request>();
      const status =
        exception instanceof HttpException ? exception.getStatus() : 500;
      const stack = exception instanceof Error ? exception.stack : null;
      const isAxiosException = isAxiosError(exception);
      const errorResponse = {
        statusCode: status,
        timestamp: new Date().toISOString(),
        path: request.url,
        exceptionMessage:
          exception instanceof Error ? exception.message : 'Unknown error',
        message:
          exception instanceof HttpException
            ? exception.getResponse()
            : 'Internal Server Error',
      };

      // Log the exception details using Winston
      this.logger.error(
        `[${request.method}] ${request.url} - ${JSON.stringify(
          isAxiosException
            ? {
                ...errorResponse,
                exceptionMessage:
                  `External: ${exception.response?.data?.message}` ||
                  errorResponse.exceptionMessage,
              }
            : errorResponse,
        )} Stack: ${stack}`,
      );

      response.status(status).json(errorResponse);
    } catch (error) {
      this.logger.error("Couldn't log request", error);
    }
  }
}
