import { ObjectId } from 'src/shared/types/mongoose';
import { InterviewStatusViewDto } from './interview-status.dto';
import { ContactViewDto } from './contact.dto';
import { Expose, Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class ClientViewDto {
  @ApiProperty()
  @Expose()
  @Type(() => InterviewStatusViewDto)
  interviewStatus: InterviewStatusViewDto;

  @ApiProperty()
  @Expose()
  @Type(() => String)
  organisationId: ObjectId;

  @ApiProperty()
  @Expose()
  @Type(() => ContactViewDto)
  primaryContact: ContactViewDto;

  @ApiProperty()
  @Expose()
  @Type(() => ContactViewDto)
  secondaryContact: ContactViewDto;
}