# Redtail CRM Mappers Overview

The `redtailMappers` function is designed to configure a suite of mappers for the Redtail CRM, ensuring that data from interview page updates is appropriately formatted and structured for integration with Redtail. Each mapper focuses on a specific piece of data, providing a targeted approach to data transformation.

## Mappers and Their Functions

Below is a summary of each mapper included in the `redtailMappers` function and its specific role:

- **RedtailNameMapper**: Maps name information, ensuring that names are formatted correctly for Redtail CRM, including handling first and last names, and potentially middle names, prefixes, and suffixes.

- **RedtailAddressMapper**: Transforms address data to meet the specific format requirements of Redtail CRM, which may include structuring street addresses, cities, states, and postal codes.

- **RedtailSSNMapper**: Ensures Social Security Numbers are correctly formatted or encrypted for secure transmission to Redtail CRM.

- **RedtailDOBMapper**: Converts dates of birth to the format required by Redtail, which may involve changing date formats to comply with CRM standards.

- **RedtailPhoneMapper**: Adjusts phone numbers to fit the format accepted by Redtail CRM, including formatting changes and standardization.

- **RedtailConflictOfInterestMapper**: Maps conflict of interest information into a structure that Redtail CRM can process, potentially involving boolean values or detailed descriptions.

- **RedtailUSCitizenMapper**: Translates U.S. citizenship status into a format recognized by Redtail CRM, often requiring conversion.

- **RedtailEmploymentMapper**: Handles the mapping of employment details, including job titles, company names, and employment status, ensuring they are formatted for Redtail CRM.

- **RedtailPrimaryBeneficiariesMapper** and **RedtailContingentBeneficiariesMapper**: Map information about primary and contingent beneficiaries, respectively, structuring the data according to Redtail CRM's requirements.

- **RedtailCompanyMapper**: Transforms company-related information into a format suitable for Redtail CRM, which may include details such as company name, industry type, and size.

- **RedtailVIPMapper**: Identifies and maps VIP status, translating this information into a recognizable category or flag within Redtail CRM.

- **RedtailJobDescriptionMapper**: Maps detailed job descriptions to the format required by Redtail CRM, ensuring clarity and compliance with CRM data structures.

- **RedtailCustomQuestionsMapper**: Handles the conversion of answers to custom questions into a format that can be stored and processed by Redtail CRM, ensuring that custom data is accurately represented.

## Functionality and Integration

Each mapper is instantiated with a reference to the `RedtailCRM` instance, granting access to CRM-specific methods or configurations necessary for the mapping process. The `redtailMappers` function returns a `PageUpdateMapper` that associates each page type (defined in `PagesEnum`) with its corresponding data mapper. This architecture facilitates dynamic data handling based on the type of interview page being updated, ensuring seamless and accurate data integration with Redtail CRM.
