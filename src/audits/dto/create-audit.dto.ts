import { LogLevel } from '@nestjs/common';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { Types } from 'mongoose';
import { AuditSource } from 'src/audits/types/audit';

export class CreateAuditDto {
  @IsNotEmpty()
  @IsString()
  message: string;

  @IsNotEmpty()
  @IsEnum(AuditSource)
  context: AuditSource;

  @IsNotEmpty()
  @IsString()
  level: LogLevel;

  @IsNotEmpty()
  organisationId: Types.ObjectId;

  @IsOptional()
  entityId?: Types.ObjectId;

  @IsOptional()
  @IsString()
  entityType?: string;
}
