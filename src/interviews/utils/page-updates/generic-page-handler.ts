import { InterviewPage } from 'src/interview-templates/schemas/v1/interview.template';
import { InterviewPageStatusEnum } from 'src/interview-templates/types/interview-page-status.enum';
import { UpdateInterviewDto } from 'src/interviews/dto/v1/update-interview.dto';
import { <PERSON><PERSON>and<PERSON> } from 'src/interviews/types/page-handler.interface';
import { PageUpdateContext } from 'src/interviews/types/page-update-context.type';

/**
 * A class that handles updates for a generic interview page.
 */
export class GenericPageHandler implements PageHandler {
  protected context: PageUpdateContext;
  protected updateDto: UpdateInterviewDto;
  protected pageStatus: InterviewPageStatusEnum;
  /**
   * Creates an instance of GenericPageHandler.
   * @param {PageUpdateContext} context - The context of the page update.
   * @param {UpdateInterviewDto} updateDto - The DTO containing the update information.
   */
  constructor(context: PageUpdateContext, updateDto: UpdateInterviewDto, pageStatus: InterviewPageStatusEnum) {
    this.context = context;
    this.updateDto = updateDto;
    this.pageStatus = pageStatus;
  }

  /**
   * Determines if the provided page should be updated.
   * @param {InterviewPage} page - The page to check.
   * @returns {boolean} - True if the page should be updated, false otherwise.
   */
  protected shouldUpdate(page: InterviewPage): boolean {
    return page.name === this.updateDto.page.name;
  }

  /**
   * Handles the provided interview page.
   * @param {InterviewPage} page - The page to handle.
   * @returns {InterviewPage} - The updated page.
   */
  handle(page: InterviewPage): InterviewPage {
    if (this.shouldUpdate(page)) {
      page.filled = true;
      page.status = this.pageStatus;
    }
    return page;
  }
}
