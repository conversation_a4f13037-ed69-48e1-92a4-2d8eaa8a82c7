import { PhoneTypeEnum } from 'src/integrations/crm/types/generic/phone.type';
import { WealthboxPhoneNumberKind } from 'src/integrations/crm/wealthbox/dto/phone.dto';

export function phoneNumberKindToPhoneType(
  phoneKind: WealthboxPhoneNumberKind,
) {
  switch (phoneKind) {
    case WealthboxPhoneNumberKind.Home:
      return PhoneTypeEnum.HOME;
    case WealthboxPhoneNumberKind.Work:
      return PhoneTypeEnum.WORK;
    case WealthboxPhoneNumberKind.Mobile:
      return PhoneTypeEnum.MOBILE;
    case WealthboxPhoneNumberKind.Other:
      return PhoneTypeEnum.OTHER;
    default:
      return PhoneTypeEnum.OTHER;
  }
}

export function phoneTypeToPhoneNumberKind(phoneType: PhoneTypeEnum) {
  switch (phoneType) {
    case PhoneTypeEnum.HOME:
      return WealthboxPhoneNumberKind.Home;
    case PhoneTypeEnum.WORK:
      return WealthboxPhoneNumberKind.Work;
    case PhoneTypeEnum.MOBILE:
      return WealthboxPhoneNumberKind.Mobile;
    case PhoneTypeEnum.OTHER:
      return WealthboxPhoneNumberKind.Other;
    default:
      return WealthboxPhoneNumberKind.Other;
  }
}
