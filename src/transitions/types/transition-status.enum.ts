export enum TransitionStatusEnum {
  Draft = 'Draft' /** Text message is not yet sent */,
  EmailPending = 'EmailPending' /** Text message is sent */,
  NonSolicitPending = 'NonSolicitPending' /** If Non Protocol this is needed to move forward */,
  StatementPending = 'StatementPending' /** Client has to upload the statement  */,
  StatementUploaded = 'StatementUploaded' /** Statement is uploaded */,
  NonSolicitVerified = 'NonSolicitVerified' /** Non Solicit is verified */,
  Transitioned = 'Transitioned' /** Client has been transitioned */,
}
