import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import mongoose, { ClientSession, FilterQuery, Model } from 'mongoose';
import { dotNotate } from 'src/utils';
import { InjectModel } from '@nestjs/mongoose';
import { CRMService } from 'src/integrations/crm/crm.service';
import { CognitoService } from 'src/shared/services/cognito.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { RbacService } from 'src/rbac/rbac.service';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';
import { CreateAdvisorDto } from 'src/advisors/dto/create-advisor.dto';
import {
  RequestTypeEnum,
  UpdateAdvisorDto,
} from 'src/advisors/dto/update-advisor.dto';
import { Role } from 'src/rbac/schemas/role.schema';
import { Advisor } from 'src/advisors/schemas/advisors.schema';
import { UpsertAdvisorDetails } from 'src/advisors/types';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import { UpdateUserDto } from 'src/auth/dto/update.dto';
import { InviteUserDto } from 'src/auth/dto/invite.dto';
import { AssetTypeEnum } from 'src/shared/types/general/asset-types.enum';
import { DownloadDocumentRequestDto } from 'src/advisors/dto/download-document.dto';
import { AssetsService } from 'src/assets/assets.service';
import { UserStatusesEnum } from 'src/shared/types/rbac/statuses.enum';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { AdvisorWithClientCountAggregation } from '../aggregations/advisor-client-count.aggregation';
import { AdvisorWithClientsCount } from '../dto/advisor-with-clients-count.dto';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { ClsService } from 'nestjs-cls';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';

@Injectable()
export class AdvisorsCrudService {
  constructor(
    @InjectModel(Advisor.name)
    protected advisorModel: Model<Advisor>,
    @Inject(forwardRef(() => CRMService))
    protected crmService: CRMService,
    protected cognitoService: CognitoService,
    @Inject(forwardRef(() => OrganisationsService))
    protected organisationService: OrganisationsService,
    protected rbacService: RbacService,
    protected assetsService: AssetsService,
    private readonly clsService: ClsService,
    private transactionManager: TransactionManager,
  ) {}

  async create(createAdvisorDto: CreateAdvisorDto, session?: ClientSession) {
    const { email, firstName, lastName, phone } = createAdvisorDto.personalInfo;
    const { role, organisationId } = createAdvisorDto;
    const existingAdvisor = await this.advisorModel
      .findOne({
        'personalInfo.email': email,
      })
      .session(session);
    if (existingAdvisor) {
      throw new HttpException('Advisor already exists.', HttpStatus.CONFLICT);
    }

    const organisation = await this.organisationService.findOne(
      organisationId,
      session,
    );
    if (!organisation) {
      throw new HttpException('Organisation not found.', HttpStatus.NOT_FOUND);
    }

    const dbRole = await this.rbacService.findOneRole({ name: role }, session);
    if (!dbRole) {
      throw new HttpException('Role not found.', HttpStatus.NOT_FOUND);
    }

    const cognitoUser = await this.cognitoService.getCognitoUser(email);
    if (!cognitoUser) {
      await this.cognitoService.adminCreateUser({
        email,
        role,
        firstName,
        lastName,
        organisationId,
        phoneNumber: phone,
      });

      const created = await this.advisorModel.create(
        [
          {
            personalInfo: createAdvisorDto.personalInfo,
            role,
            organisation: organisationId,
            status: UserStatusesEnum.PENDING,
          },
        ],
        { session },
      );

      const advisor = created[0];

      return { message: 'New advisor created!', advisor };
    } else {
      if (cognitoUser) {
        throw new HttpException('Advisor already exists', HttpStatus.CONFLICT);
      }
    }
  }

  async createWithAdv2b(
    createAdvisorDto: CreateAdvisorDto,
    organisationId: string,
    file: Express.Multer.File,
    session?: ClientSession,
  ) {
    const { advisor } = await this.create(createAdvisorDto, session);

    if (!advisor || !advisor.id) {
      throw new HttpException(
        'Advisor was not created.',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (file) {
      try {
        const presignedUrl = await this.uploadPrivateDocument(
          organisationId,
          advisor.id,
          file,
          file.originalname,
          AssetTypeEnum.ADV2B,
          session,
        );

        if (!presignedUrl) {
          throw new HttpException(
            'Document was not attached.',
            HttpStatus.BAD_REQUEST,
          );
        }
      } catch (error) {
        throw new HttpException(
          'Failed to upload document.',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    }

    return { message: 'Advisor created', advisorId: advisor.id };
  }

  async findAll(
    paginationOptions: PaginationQueryDto,
    filters: mongoose.FilterQuery<Advisor>,
  ) {
    const { limit = 5, page = 1 } = paginationOptions;
    const skip = limit * (page - 1);

    const totalResults = await this.advisorModel.countDocuments(filters);
    const result: AdvisorWithClientsCount[] = await this.advisorModel
      .aggregate(AdvisorWithClientCountAggregation(filters))
      .skip(skip)
      .limit(limit)
      .exec();

    return {
      result,
      totalResults,
    };
  }

  async findOne(filter: FilterQuery<Advisor>, session?: ClientSession): Promise<AdvisorWithRole> {
    const advisor = await this.advisorModel
      .findOne(filter)
      .populate({
        path: 'organisation',
        select: '_id name phone address email status',
      })
      .session(session)
      .exec();

    if (!advisor) {
      throw new HttpException('Advisor not found.', HttpStatus.NOT_FOUND);
    }

    const role = await this.rbacService.findOneRole({ name: advisor.role });

    if (!role) {
      throw new HttpException(
        'User does not have a defined role.',
        HttpStatus.NOT_FOUND,
      );
    }

    return {
      ...advisor.toObject(),
      role,
    };
  }

  async update(
    filter: FilterQuery<Advisor>,
    updateAdvisorDto: UpdateAdvisorDto,
    session?: ClientSession,
  ) {
    const loggedInAdvisor: AdvisorWithRole = this.clsService.get(ClsDataEnum.Advisor);

    const existingAdvisor = await this.advisorModel.findOne(filter).populate({
      path: 'organisation',
      select: 'name phone address email setupComplete status', // Fields to include
    });

    if (!existingAdvisor) {
      throw new HttpException('Advisor not found.', HttpStatus.NOT_FOUND);
    }

    const {
      personalInfo: {
        email: oldEmail,
        firstName: oldFirstName,
        lastName: oldLastName,
        phone: oldPhone,
      },
      role: oldRole,
      status: oldStatus,
    } = existingAdvisor;
    const {
      personalInfo: {
        email = oldEmail,
        firstName = oldFirstName,
        lastName = oldLastName,
        phone = oldPhone,
      } = {},
      role = oldRole,
      status = oldStatus,
    } = updateAdvisorDto || {};
    const { _id: organisationId } = existingAdvisor.organisation;
    const isResetEmailRequest = oldEmail !== email;
    const isNameOrRoleChangeRequest =
      firstName !== oldFirstName ||
      lastName !== oldLastName ||
      role !== oldRole;
    const isPhoneChangeRequest = phone !== oldPhone;
    const isStatusChangeRequest = status !== oldStatus;

    const predicate: [RequestTypeEnum, boolean] = [
      [RequestTypeEnum.isResetEmailRequest, isResetEmailRequest],
      [RequestTypeEnum.isStatusChangeRequest, isStatusChangeRequest],
      [RequestTypeEnum.isNameOrRoleChangeRequest, isNameOrRoleChangeRequest],
      [RequestTypeEnum.isPhoneChangeRequest, isPhoneChangeRequest],
    ].find(([_, pred]) => !!pred) as [RequestTypeEnum, boolean];

    if (!predicate?.length) {
      return { message: 'No update requested', query: filter };
    }
    const requestData = {
      [RequestTypeEnum.isResetEmailRequest]: {
        newEmail: email,
        firstName,
        lastName,
        phone,
        organisationId,
        role,
        oldEmail,
      },
      [RequestTypeEnum.isNameOrRoleChangeRequest]: {
        firstName,
        lastName,
        email,
        phone,
        role,
      },
      [RequestTypeEnum.isPhoneChangeRequest]: {
        firstName,
        lastName,
        email,
        phone,
      },
      [RequestTypeEnum.isStatusChangeRequest]: {
        firstName,
        lastName,
        email,
        phone,
        status,
      },
    }[predicate[0]];

    const data = {
      ...updateAdvisorDto,
      updatedAt: new Date(),
    };

    if (updateAdvisorDto.role && updateAdvisorDto.role !== oldRole) {
      if (loggedInAdvisor.role.name === RolesEnum.Representative) {
        throw new HttpException(
          'Representative cannot update role.',
          HttpStatus.FORBIDDEN,
        );
      }

      if (
        updateAdvisorDto.role === RolesEnum.SuperAdmin &&
        loggedInAdvisor.role.name !== RolesEnum.SuperAdmin
      ) {
        throw new HttpException(
          'Only Super Admin can update role to Super Admin.',
          HttpStatus.FORBIDDEN,
        );
      }
      const role = await this.validateRole(updateAdvisorDto.role);
      data.role = role.name;
    }

    await this.upsertCognitoUser(requestData, predicate[0]);

    const updateObject = {};
    dotNotate(updateObject, data);

    await this.advisorModel
      .updateOne(filter, {
        $set: updateObject,
      })
      .session(session);

    return {
      message: 'Advisor updated.',
      query: filter,
    };
  }

  async remove(filter: FilterQuery<Advisor>, session?: ClientSession) {
    await this.findOne(filter);
    await this.advisorModel.deleteOne(filter).session(session);

    return {
      message: `Advisor removed.`,
      query: filter,
    };
  }

  private async upsertCognitoUser(
    details: UpsertAdvisorDetails,
    requestType?: RequestTypeEnum,
  ) {
    if (requestType === RequestTypeEnum.isResetEmailRequest) {
      const params: InviteUserDto = {
        email: details.newEmail,
        firstName: details.firstName,
        lastName: details.lastName,
        phoneNumber: details.phone,
        organisationId: details.organisationId.toString(),
        role: details.role,
      };

      return this.cognitoService.upsertUser(params, details.oldEmail);
    } else if (requestType === RequestTypeEnum.isStatusChangeRequest) {
      return details.status === UserStatusesEnum.ARCHIVED
        ? this.cognitoService.adminDisableUser(details.email)
        : this.cognitoService.adminEnableUser(details.email);
    }

    const params: UpdateUserDto = {
      firstName: details?.firstName,
      lastName: details?.lastName,
      email: details?.email,
      phoneNumber: details?.phone,
    };

    return this.cognitoService.upsertUser(params);
  }

  private async validateRole(roleName: string): Promise<Role> {
    const role = await this.rbacService.findOneRole({ name: roleName });

    if (!role) {
      throw new HttpException('Role not found.', HttpStatus.NOT_FOUND);
    }

    return role;
  }

  async uploadPrivateDocument(
    organisationId: string,
    advisorId: string,
    file: Express.Multer.File,
    fileName: string,
    assetType: AssetTypeEnum,
    session?: ClientSession,
  ): Promise<string> {
    const prefix = `organisation/${organisationId}/advisors/${advisorId}/documents`;

    if(!fileName && !file?.originalname) {
      throw new BadRequestException('File name is required.');
    }
    
    const { key, location } = await this.assetsService.uploadPrivateFile(
      file.buffer,
      fileName || file?.originalname,
      file.mimetype,
      prefix,
    );

    await this.advisorModel
      .updateOne(
        { _id: advisorId },
        {
          $pull: {
            assets: { assetType },
          },
        },
      )
      .session(session);

    await this.advisorModel
      .updateOne(
        { _id: advisorId },
        {
          $push: {
            assets: {
              assetId: fileName,
              assetType,
              isPublic: false,
              assetKey: key,
            },
          },
        },
      )
      .session(session);

    const presignedUrl = await this.assetsService.generatePresignedUrl(key);

    return presignedUrl;
  }

  async downloadPrivateDocument(
    advisorId: string,
    dto: DownloadDocumentRequestDto,
  ) {
    const { assetType, assetId } = dto;
    let predicate;

    const advisor = await this.findOne({
      _id: advisorId,
    });

    if (!assetType && !assetId) {
      throw new BadRequestException(
        'Either assetType or assetId should be provided.',
      );
    }

    if (assetType) {
      predicate = (asset) => asset.assetType === assetType;
    }

    if (assetId) {
      predicate = (asset) => asset.assetId === assetId;
    }

    const asset = advisor.assets.find(predicate);

    if (!asset) {
      throw new HttpException('Asset not found', HttpStatus.NOT_FOUND);
    }

    const { assetKey } = asset;

    const presignedUrl = await this.assetsService.generatePresignedUrl(
      assetKey,
    );

    return presignedUrl;
  }
}
