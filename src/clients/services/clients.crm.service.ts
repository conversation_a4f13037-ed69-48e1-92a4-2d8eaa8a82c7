import { Injectable, Inject, forwardRef, NotFoundException } from '@nestjs/common';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import { Integration } from 'src/shared/schemas/integration.schema';
import { IntegrationEnum } from 'src/shared/types/integrations';
import { CRM } from 'src/integrations/crm/crm.interface';
import { ClientSession } from 'mongoose';
import { Client } from '../schemas/clients.schema';
import { 
  CreateContactDto, 
  CreateContactResponseDto, 
  UpdateContactResponseDto 
} from 'src/integrations/crm/types/create-contact.dto';
import { EnrichedClient } from '../dto/v1/get-clients.dto';

@Injectable()
export class ClientsCrmService {
  constructor(
    @Inject(forwardRef(() => AdvisorsCrmService))
    private readonly advisorsCrmService: AdvisorsCrmService,
  ) {}

  /**
   * Upserts a client in the CRM.
   * If the client already exists in the CRM, it updates the contact information.
   * If the client does not exist, it creates a new contact in the CRM.
   * @param data The data of the client to be upserted.
   * @returns A promise that resolves to an instance of UpdateContactResponseDto or CreateContactResponseDto.
   */
  async upsertClientInCrm(
    data: CreateContactDto,
  ): Promise<UpdateContactResponseDto | CreateContactResponseDto> {
    const crmClientId = data?.primaryContact?.crmClientId;
    const { primaryAdvisor: { _id: advisorId } } = data;

    if (crmClientId) {
      return this.advisorsCrmService.updateContact(data, advisorId);
    }

    return this.advisorsCrmService.createCrmContact(data, advisorId);
  }

  /**
   * Gets a CRM instance for a specific advisor ID.
   * @param advisorId - The ID of the advisor.
   * @param session - Optional client session.
   * @returns A promise that resolves to a CRM instance.
   */
  async getCrmInstance(advisorId: string, session?: ClientSession): Promise<CRM> {
    return this.advisorsCrmService.getCrmInstance(advisorId, session);
  }

  /**
   * Enriches a client document with CRM data.
   * @param clientDoc - The client document to enrich.
   * @param shouldThrowOnEnrichFail - Whether to throw an error if enrichment fails.
   * @returns The enriched client.
   */
  async enrichClientWithCrmData(
    clientDoc: Client,
    shouldThrowOnEnrichFail: boolean = false,
  ): Promise<EnrichedClient> {
    try {
      this.validateClientContacts(clientDoc);

      // Initialize CRM instance
      const advisorId =
        clientDoc.primaryAdvisor?.id?.toString() ||
        clientDoc.createdBy?._id?.toString();
      
      if (!advisorId) {
        throw new Error('No advisor ID available for CRM lookup');
      }
      
      const crm = await this.getCrmInstance(advisorId);

      // Get contact details for primary and secondary contacts
      return this.getEnrichedClientDetails(clientDoc, crm);
    } catch (error) {
      if (shouldThrowOnEnrichFail) {
        throw error;
      }
      return this.createFallbackEnrichedClient(clientDoc);
    }
  }

  /**
   * Validates the client contacts.
   * @param clientDoc - The client document to validate.
   * @throws Error if validation fails.
   */
  private validateClientContacts(clientDoc: Client): void {
    if (!clientDoc.primaryContact.crmClientId) {
      throw new Error('Client has no primary contact CRM ID');
    }

    if (!clientDoc.primaryAdvisor && !clientDoc.createdBy) {
      throw new Error(`Client ${clientDoc._id} has no primary advisor`);
    }

    if (clientDoc.secondaryContact && !clientDoc.secondaryContact.crmClientId) {
      throw new Error(
        `Client ${clientDoc._id} secondary contact is not existent in the CRM`,
      );
    }
  }

  /**
   * Retrieves the enriched details of a client by combining the client document with CRM data.
   * @param clientDoc - The client document.
   * @param crm - The CRM instance.
   * @returns A promise that resolves to the enriched client details.
   */
  private async getEnrichedClientDetails(
    clientDoc: Client,
    crm: CRM,
  ): Promise<EnrichedClient> {
    const [primaryContactMobile, primaryContactEmail] = await Promise.all([
      crm.getMainMobileOfContact(clientDoc.primaryContact.crmClientId),
      crm.getMainEmailOfContact(clientDoc.primaryContact.crmClientId),
    ]);

    const enrichedClient = clientDoc.toObject({
      getters: true,
      virtuals: true,
    });
    enrichedClient.primaryContact = {
      ...enrichedClient.primaryContact,
      email: primaryContactEmail?.address,
      mobile: primaryContactMobile,
    };

    if (clientDoc.secondaryContact) {
      const [secondaryContactMobile, secondaryContactEmail] = await Promise.all(
        [
          crm.getMainMobileOfContact(clientDoc.secondaryContact.crmClientId),
          crm.getMainEmailOfContact(clientDoc.secondaryContact.crmClientId),
        ],
      );
      enrichedClient.secondaryContact = {
        ...enrichedClient.secondaryContact,
        email: secondaryContactEmail?.address,
        mobile: secondaryContactMobile,
      };
    }

    return enrichedClient;
  }

  /**
   * Creates a fallback enriched client based on the provided client document.
   * @param clientDoc - The client document to create the fallback from.
   * @returns The fallback enriched client.
   */
  public createFallbackEnrichedClient(clientDoc: Client): EnrichedClient {
    const fallbackEnrichedClient = clientDoc.toObject({
      getters: true,
      virtuals: true,
    });
    fallbackEnrichedClient.primaryContact = {
      ...fallbackEnrichedClient.primaryContact,
      email: clientDoc.primaryContact?.email || '',
      mobile: clientDoc.primaryContact?.mobile || '',
    };
    if (fallbackEnrichedClient.secondaryContact) {
      fallbackEnrichedClient.secondaryContact = {
        ...fallbackEnrichedClient.secondaryContact,
        email: clientDoc.secondaryContact?.email || '',
        mobile: clientDoc.secondaryContact?.mobile || '',
      };
    }
    return fallbackEnrichedClient;
  }
}