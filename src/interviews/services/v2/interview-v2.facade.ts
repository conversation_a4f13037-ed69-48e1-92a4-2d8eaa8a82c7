// src/interviews/services/v2/interview-v2.facade.ts
import { Injectable, Logger } from '@nestjs/common';
import { InterviewCoreService } from './core/interview-core.service';
import { InterviewV2NavigationService } from './navigation/interview-navigation.service';
import { InterviewDocumentsService } from './documents/interview-documents.service';
import { InterviewNotificationService } from './notifications/interview-notification.service';
import { CreateInterviewV2Dto } from '../../dto/v2/create-interview-v2.dto';
import { SubmitPageV2Dto } from '../../dto/v2/submit-page-v2.dto';
import { PageNavigationResultV2Dto } from '../../dto/v2/page-navigation-result-v2.dto';
import { NavigationStateV2Dto } from '../../dto/v2/navigation-state-v2.dto';
import { InterviewResponseV2Dto } from '../../dto/v2/interview-response-v2.dto';
import { ClientSession } from 'mongoose';

/**
 * Facade for V2 interview operations
 * Provides a clean API for controllers
 */
@Injectable()
export class InterviewV2Facade {
  private readonly logger = new Logger(InterviewV2Facade.name);

  constructor(
    private readonly core: InterviewCoreService,
    private readonly navigation: InterviewV2NavigationService,
    private readonly documentsService: InterviewDocumentsService,
    private readonly notificationService: InterviewNotificationService,
  ) {}

  // Core operations
  create(dto: CreateInterviewV2Dto, session?: ClientSession): Promise<InterviewResponseV2Dto> {
    return this.core.create(dto, session);
  }

  findById(id: string, session?: ClientSession) {
    return this.core.findById(id, session);
  }

  findByClient(clientId: string) {
    return this.core.find({ client: clientId });
  }

  remove(id: string, session?: ClientSession) {
    return this.core.remove(id, session);
  }

  complete(id: string, session?: ClientSession) {
    return this.core.markComplete(id, session);
  }

  // Navigation operations - Synchronous
  submitPage(
    interviewId: string, 
    dto: SubmitPageV2Dto
  ): Promise<PageNavigationResultV2Dto> {
    return this.navigation.submitPageAndNavigate(interviewId, dto);
  }

  getCurrentState(interviewId: string): Promise<NavigationStateV2Dto> {
    return this.navigation.getCurrentNavigationState(interviewId);
  }

  navigateBack(interviewId: string): Promise<PageNavigationResultV2Dto> {
    return this.navigation.navigateBack(interviewId);
  }

  getPage(interviewId: string, pageId: string) {
    return this.navigation.getPageDefinition(interviewId, pageId);
  }

  // Document operations
  async syncInterviewDocuments(interviewId: string): Promise<void> {
    // V2 does not sync documents - they are streamed
    this.logger.log(`Document sync requested for interview ${interviewId} - no-op in V2`);
    return Promise.resolve();
  }

  // Notification operations
  async sendDesktopInterviewEmail(interviewId: string): Promise<void> {
    return this.notificationService.sendDesktopInterviewEmail(interviewId);
  }

  async sendNonCitizenEmail(interviewId: string): Promise<void> {
    return this.notificationService.sendNonCitizenEmail(interviewId);
  }
}