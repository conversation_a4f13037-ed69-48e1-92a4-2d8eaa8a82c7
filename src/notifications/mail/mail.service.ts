import { ISendMailOptions, MailerService } from '@nestjs-modules/mailer';
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import * as Handlebars from 'handlebars';
import * as AWS from 'aws-sdk';
import { SendEmailOptions, S3Attachment } from './mail.types';
import { isEmpty } from 'lodash';
import { ConfigService } from '@nestjs/config';
import { emailTemplateMapper } from 'src/templates/mail';
import { MimeTypeEnum } from 'src/shared/types/general/mime.enum';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { CommunicationTypeEnum } from 'src/integrations/crm/types/communications/communication-type.enum';
import { EnrichedContact } from 'src/clients/dto/v1/get-clients.dto';
import { ClientContact } from 'src/clients/schemas/clients.schema';
import { ClsService } from 'nestjs-cls';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { EmailTemplatesService } from 'src/organisations/services/email-templates.service';

@Injectable()
export class MailService {
  constructor(
    private readonly mailerService: MailerService,
    private readonly configService: ConfigService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    @Inject(forwardRef(() => AdvisorsCrmService))
    private readonly advisorsCrmService: AdvisorsCrmService,
    private readonly clsService: ClsService,
    @Inject(forwardRef(() => EmailTemplatesService))
    private readonly emailTemplatesService: EmailTemplatesService,
  ) {}

  async sendEmail(
    {
      to,
      subject,
      text,
      templateName,
      context,
      attachments = [],
      organisation, // Now required by type definition
    }: SendEmailOptions,
    contact?: ClientContact,
    advisorId?: string,
  ) {

    let customTemplate = null;

    // If organisation exists and templateName is provided, try to find a custom template
    if (organisation && templateName) {
      customTemplate = await this.emailTemplatesService.findByType(
        organisation._id.toString(),
        templateName,
      );
    }

    // Use custom template if found, otherwise use default template
    let template;
    let compiledTemplate;

    if (customTemplate) {
      template = {
        subject: subject || customTemplate.emailTemplateName,
        html: customTemplate.emailTemplateContent,
      };
      compiledTemplate = Handlebars.compile(template.html, {
        noEscape: true,
      });
    } else {
      template = emailTemplateMapper(templateName);
      compiledTemplate = Handlebars.compile(template.html, {
        noEscape: true,
      });
    }

    let attachmentsToSend = [];
    if (!isEmpty(attachments)) {
      attachmentsToSend = await this.getAttachmentFiles(
        attachments as S3Attachment[],
      );
    }

    const emailSubject = template?.subject || subject;

    await this.mailerService.sendMail({
      to,
      subject: emailSubject,
      html:
        !!template &&
        compiledTemplate({ ...context, subject: template.subject }),
      text: !template && text,
      attachments: attachmentsToSend,
    });

    if (contact) {
      await this.advisorsCrmService.logCommunication(
        contact.crmClientId.toString(),
        {
          communicationType: CommunicationTypeEnum.Email,
          communicationDetails: `to ${to}, subject: ${
            template?.subject || subject
          }`,
        },
        advisorId,
      );
    }
  }

  async sendEmailWithFileAttachments(
    { to, subject, text, templateName, context, attachments, organisation }: SendEmailOptions,
    contact?: EnrichedContact,
    advisorId?: string,
  ) {

    let customTemplate = null;

    // If organisation exists and templateName is provided, try to find a custom template
    if (organisation && templateName) {
      customTemplate = await this.emailTemplatesService.findByType(
        organisation._id.toString(),
        templateName,
      );
    }

    // Use custom template if found, otherwise use default template
    let template;
    let compiledTemplate;

    if (customTemplate) {
      template = {
        subject: subject || customTemplate.emailTemplateName,
        html: customTemplate.emailTemplateContent,
      };
      compiledTemplate = Handlebars.compile(template.html, {
        noEscape: true,
      });
    } else {
      template = emailTemplateMapper(templateName);
      compiledTemplate = Handlebars.compile(template.html, {
        noEscape: true,
      });
    }

    const attachmentsToSend: ISendMailOptions['attachments'] = attachments.map(
      (attachment) => {
        return {
          filename: attachment.originalname,
          content: attachment.buffer,
          contentType: attachment.mimetype,
        };
      },
    );

    const emailSubject = template?.subject || subject;

    await this.mailerService.sendMail({
      to,
      subject: emailSubject,
      html:
        !!template &&
        compiledTemplate({ ...context, subject: template.subject }),
      text: !template && text,
      attachments: attachmentsToSend,
    });

    if (contact) {
      await this.advisorsCrmService.logCommunication(
        contact.crmClientId.toString(),
        {
          communicationType: CommunicationTypeEnum.Email,
          communicationDetails: `to ${to}, subject: ${
            template?.subject || subject
          }`,
        },
        advisorId,
      );
    }
  }

  private async getAttachmentFiles(attachments: S3Attachment[]) {
    const s3 = new AWS.S3();

    const promises = attachments.map(async ({ key, name }) => {
      const params: AWS.S3.Types.GetObjectRequest = {
        Bucket: this.configService.get<string>('ORG_DOCS_BUCKET_NAME'),
        Key: key,
      };

      return s3
        .getObject(params)
        .promise()
        .then((data) => {
          return {
            filename: `${name.replace('.pdf', '')}.pdf`,
            content: data.Body,
            contentType: MimeTypeEnum.PDF,
          };
        })
        .catch((error) => {
          this.logger.error(`Error fetching attachment ${key}:`, error);
          throw error;
        });
    });

    return Promise.all(promises);
  }
}
