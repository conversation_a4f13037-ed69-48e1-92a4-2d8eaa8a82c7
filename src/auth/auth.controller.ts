import {
  Controller,
  Post,
  UseGuards,
  Body,
  Res,
  HttpCode,
  Req,
  Get,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AuthService } from './auth.service';

import { Throttle } from '@nestjs/throttler';
import { Api<PERSON><PERSON>erAuth, ApiCookieAuth } from '@nestjs/swagger';
import { LoginDto } from './dto/login.dto';
import { MfaRequiredResponseDto, MfaDto } from './dto/mfa.dto';
import {
  NewPasswordRequiredResponseDto,
  NonMfaLoginResponseDto,
  SetNewPasswordDto,
} from './dto/new-password.dto';
import { ResetPasswordDto } from './dto/reset.dto';
import { SetPasswordDto } from './dto/set-password.dto';
import { SignoutDto } from './dto/logout.dto';
import { RefreshTokenDto } from './dto/refresh.dto';
import { Response, Request } from 'express';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('login')
  @HttpCode(200)
  async login(
    @Body() loginDto: LoginDto,
    @Res({ passthrough: true }) response: Response,
  ): Promise<
    | MfaRequiredResponseDto
    | NewPasswordRequiredResponseDto
    | NonMfaLoginResponseDto
  > {
    return this.authService.authenticateUser(loginDto, response);
  }

  @HttpCode(200)
  @Post('reset')
  @Throttle(3, 60)
  async adminResetUserPassword(@Body() body: ResetPasswordDto) {
    return this.authService.resetPassword(body);
  }

  @HttpCode(200)
  @Post('set-password')
  async setPassword(@Body() body: SetPasswordDto) {
    return this.authService.setPassword(body);
  }

  @HttpCode(200)
  @Throttle(10, 60)
  @Post('login/verify')
  async verifyMfa(
    @Body() body: MfaDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    return this.authService.respondToMfaChallenge(body, response);
  }

  @HttpCode(200)
  @Post('set-new-password')
  async setNewPassword(@Body() setNewPasswordDto: SetNewPasswordDto) {
    return this.authService.newUserPassword(setNewPasswordDto);
  }

  @HttpCode(200)
  @Post('refresh')
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    return this.authService.refreshToken(refreshTokenDto, response);
  }

  @HttpCode(200)
  @Post('logout')
  @ApiBearerAuth()
  @ApiCookieAuth()
  @UseGuards(AuthGuard('jwt'))
  async logout(
    @Body() signoutDto: SignoutDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    return this.authService.signOut(signoutDto, response);
  }

  @Post('change-password')
  @HttpCode(200)
  async changePassword(
    @Body() setNewPasswordDto: SetNewPasswordDto,
    @Req() request: Request,
  ) {
    return this.authService.changePassword(setNewPasswordDto, request);
  }
}
