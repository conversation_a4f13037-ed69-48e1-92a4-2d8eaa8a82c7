import { HttpException, HttpStatus, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { Model, FilterQuery } from 'mongoose';
import cronParser from 'cron-parser';
import { Mutex } from './schema/mutex.schema';
import { CreateMutexDto } from './dto/create-mutex.dto';

@Injectable()
export class MutexService {
  constructor(
    @InjectModel(Mutex.name)
    private mutexModel: Model<Mutex>,
  ) {}

  async create(createMutexDto: CreateMutexDto) {
    try {
      const mutex = await this.mutexModel.create({
        pid: createMutexDto.pid,
        isLocked: false,
        lockedAt: null,
        createdAt: new Date(),
      });

      return mutex;
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: 'Error creating mutex',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findOne(filter: FilterQuery<Mutex>) {
    try {
      let mutex = await this.mutexModel.findOne(filter);

      if (!mutex) {
        mutex = await this.create({ pid: filter.pid });
      }

      return mutex;
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: 'Error finding mutex',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async remove(filter: FilterQuery<Mutex>) {
    try {
      await this.mutexModel.deleteOne(filter);

      return {
        message: `Mutex removed.`,
        query: filter,
      };
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: 'Error removing mutex',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async lock(pid: string, cron: string, session?: mongoose.ClientSession) {
    try {
      const DEADLOCK_TIMEOUT = 1000 * 60 * 10; // 10 minutes
      const { lockedAt, isLocked } = await this.findOne({ pid });

      // Check if the mutex is currently locked and if the lockedAt time has expired
      if (isLocked) {
        // If lockedAt is null, we consider the mutex as not locked
        const timeout = lockedAt
          ? new Date(lockedAt.getTime() + DEADLOCK_TIMEOUT)
          : new Date(0);

        if (lockedAt && lockedAt < timeout) {
          throw new HttpException('Mutex is locked', HttpStatus.CONFLICT);
        }
      }

      const interval = cronParser.parseExpression(cron);
      const nextDate = interval.next().toDate();

      if (!lockedAt || nextDate > lockedAt) {
        const mutex = await this.mutexModel.findOneAndUpdate(
          { pid },
          { $set: { isLocked: true, lockedAt: new Date() } },
          { session },
        );

        return mutex;
      }
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: `Error locking mutex. Message: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async unlock(pid: string, session?: mongoose.ClientSession) {
    try {
      const mutex = await this.mutexModel.findOneAndUpdate(
        { pid },
        { $set: { isLocked: false } },
        { session },
      );

      return mutex;
    } catch (error) {
      throw new HttpException(
        {
          status: HttpStatus.INTERNAL_SERVER_ERROR,
          error: 'Error unlocking mutex',
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
