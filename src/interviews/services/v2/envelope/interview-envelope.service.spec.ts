import { Test, TestingModule } from '@nestjs/testing';
import { InterviewEnvelopeService } from './interview-envelope.service';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { ClientsV1Service } from 'src/clients/clients.service';
import { AdvisorsDocusignService } from 'src/advisors/services/advisors.docusign.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { InterviewDocumentsService } from '../documents/interview-documents.service';
import { InterviewTemplatesService } from 'src/interview-templates/v2/interview-templates.service';
import { ConfigService } from '@nestjs/config';
import { ClsService } from 'nestjs-cls';
import { getModelToken } from '@nestjs/mongoose';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Interview } from '../../../schemas/v1/interview.schema';
import { Model, ClientSession } from 'mongoose';
import { DocusignAccountOwnershipEnum } from 'src/integrations/docusign/docusign.types';
import { CreateEnvelopeDto } from 'src/integrations/docusign/dto/create-envelope.dto';
import { AddAdvisorDocumentsDto } from 'src/integrations/docusign/dto/add-advisor-documents.dto';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { CRMEnum } from 'src/shared/types/integrations';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { RedtailAccountOwnershipEnum, RedtailDBAccountType } from 'src/integrations/crm/redtail/types/enums';
import { AccountAdvisoryDocumentsEnum } from 'src/shared/types/accounts/account-documents.enum';
import { HttpException, HttpStatus } from '@nestjs/common';
import { AccountDto } from 'src/shared/types/accounts/account.dto';
import { GenericCrmAccount } from 'src/integrations/crm/types/accounts/crm-account.type';

describe('InterviewEnvelopeService', () => {
  let service: InterviewEnvelopeService;
  let docusignService: jest.Mocked<DocusignService>;
  let clientsService: jest.Mocked<ClientsV1Service>;
  let advisorsDocusignService: jest.Mocked<AdvisorsDocusignService>;
  let organisationsService: jest.Mocked<OrganisationsService>;
  let advisorsCrudService: jest.Mocked<AdvisorsCrudService>;
  let advisorsCrmService: jest.Mocked<AdvisorsCrmService>;
  let documentsService: jest.Mocked<InterviewDocumentsService>;
  let interviewTemplateService: jest.Mocked<InterviewTemplatesService>;
  let configService: jest.Mocked<ConfigService>;
  let clsService: jest.Mocked<ClsService>;
  let interviewModel: jest.Mocked<Model<Interview>>;
  let logger: jest.Mocked<any>;

  const mockSession = {
    startTransaction: jest.fn(),
    commitTransaction: jest.fn(),
    abortTransaction: jest.fn(),
    endSession: jest.fn(),
  } as unknown as ClientSession;

  const mockInterview = {
    _id: 'interview123',
    client: 'client123',
    envelopeId: null,
    docusignSelected: true,
    isComplete: true,
    toObject: jest.fn().mockReturnThis(),
  };

  const mockClient = {
    _id: 'client123',
    organisationId: { toString: () => 'org123' },
    primaryAdvisor: {
      id: { toString: () => 'advisor123' },
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      mobile: '**********',
    },
    primaryContact: {
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      mobile: '**********',
      crmClientId: 'crm123',
      accounts: [{
        id: 'acc1',
        type: AccountTypeEnum.Ira,
        label: 'IRA Account',
        ownership: 'Individual',
      }],
    },
    secondaryContact: {
      firstName: 'Bob',
      lastName: 'Smith',
      email: '<EMAIL>',
      mobile: '**********',
      crmClientId: 'crm456',
      accounts: [{
        id: 'acc2',
        type: AccountTypeEnum.SingleNameBrokerage,
        label: 'Individual Account',
        ownership: 'Individual',
      }],
    },
    fileUploadsNo: 2,
  };

  const mockOrganisation = {
    _id: 'org123',
    name: 'Test Organisation',
  };

  const mockAdvisor = {
    _id: 'advisor123',
    organisation: { _id: 'org123' },
    firstName: 'John',
    lastName: 'Doe',
  };

  const mockCrmInstance = {
    getType: jest.fn().mockResolvedValue(CRMEnum.Redtail),
    getContact: jest.fn(),
  };

  const mockCrmContactData = {
    firstName: 'Jane',
    lastName: 'Smith',
    dob: '1990-01-01',
    accounts: [{
      type: 'ira',
      name: 'Traditional IRA Account',
      accountNumber: '12345',
    }],
  };

  const mockFiles = [
    {
      originalname: 'advisory-agreement.pdf',
      buffer: Buffer.from('mock pdf content'),
      mimetype: 'application/pdf',
    } as Express.Multer.File,
  ];

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InterviewEnvelopeService,
        {
          provide: DocusignService,
          useValue: {
            getAccountInfo: jest.fn(),
            createEnvelope: jest.fn(),
            getEnvelopDocuments: jest.fn(),
            addAdvisorDocuments: jest.fn(),
          },
        },
        {
          provide: ClientsV1Service,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: AdvisorsDocusignService,
          useValue: {
            findDocusignIntegration: jest.fn(),
          },
        },
        {
          provide: OrganisationsService,
          useValue: {
            findDocusignIntegration: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: AdvisorsCrudService,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: AdvisorsCrmService,
          useValue: {
            getCrmInstance: jest.fn(),
          },
        },
        {
          provide: InterviewDocumentsService,
          useValue: {
            getAccountOpeningAdvisoryFiles: jest.fn(),
            getFilesFromS3: jest.fn(),
          },
        },
        {
          provide: InterviewTemplatesService,
          useValue: {},
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: ClsService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: getModelToken(Interview.name),
          useValue: {
            findById: jest.fn(),
            find: jest.fn(),
            updateMany: jest.fn(),
          },
        },
        {
          provide: WINSTON_MODULE_PROVIDER,
          useValue: {
            info: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<InterviewEnvelopeService>(InterviewEnvelopeService);
    docusignService = module.get(DocusignService);
    clientsService = module.get(ClientsV1Service);
    advisorsDocusignService = module.get(AdvisorsDocusignService);
    organisationsService = module.get(OrganisationsService);
    advisorsCrudService = module.get(AdvisorsCrudService);
    advisorsCrmService = module.get(AdvisorsCrmService);
    documentsService = module.get(InterviewDocumentsService);
    interviewTemplateService = module.get(InterviewTemplatesService);
    configService = module.get(ConfigService);
    clsService = module.get(ClsService);
    interviewModel = module.get(getModelToken(Interview.name));
    logger = module.get(WINSTON_MODULE_PROVIDER);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createDocusignEnvelope', () => {
    beforeEach(() => {
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(mockInterview),
      } as any);
      clientsService.findOne.mockResolvedValue(mockClient as any);
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
      } as any);
      advisorsDocusignService.findDocusignIntegration.mockResolvedValue(null);
      organisationsService.findDocusignIntegration.mockResolvedValue(null);
      configService.get.mockReturnValue('https://webhook.url');
      docusignService.createEnvelope.mockResolvedValue('envelope123');
      interviewModel.updateMany.mockResolvedValue({ acknowledged: true } as any);
    });

    it('should successfully create a new envelope', async () => {
      const result = await service.createDocusignEnvelope('interview123', mockSession);

      expect(result).toBe('envelope123');
      expect(interviewModel.findById).toHaveBeenCalledWith('interview123');
      expect(clientsService.findOne).toHaveBeenCalledWith({ _id: 'client123' });
      expect(docusignService.createEnvelope).toHaveBeenCalledWith(
        expect.objectContaining({
          advisorId: 'advisor123',
          organisationId: 'org123',
          primaryAdvisorName: 'John Doe',
          primaryAdvisorEmail: '<EMAIL>',
          primaryAdvisorPhone: '**********',
          applicant: {
            name: 'Jane Smith',
            email: '<EMAIL>',
            phone: '**********',
          },
          coapplicant: {
            name: 'Bob Smith',
            email: '<EMAIL>',
            phone: '**********',
          },
          envelopeId: null,
          isSchwabOwnedDocusignAccount: false,
          shouldSend: false,
          webhookUrl: 'https://webhook.url',
          files: [],
          schwabTemplateId: undefined,
        }),
        mockSession,
      );
      expect(interviewModel.updateMany).toHaveBeenCalledWith(
        { client: 'client123' },
        {
          $set: {
            envelopeId: 'envelope123',
            updatedAt: expect.any(Date),
          },
        },
        { session: mockSession },
      );
    });

    it('should handle existing envelope (should not create new)', async () => {
      const interviewWithEnvelope = { ...mockInterview, envelopeId: 'existing123' };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithEnvelope),
      } as any);

      const result = await service.createDocusignEnvelope('interview123', mockSession);

      expect(result).toBe('envelope123');
      expect(docusignService.createEnvelope).toHaveBeenCalledWith(
        expect.objectContaining({
          envelopeId: 'existing123',
        }),
        mockSession,
      );
    });

    it('should handle Schwab template ID', async () => {
      const integrationConfig = { schwabTemplateId: 'schwab-template-123' };
      advisorsDocusignService.findDocusignIntegration.mockResolvedValue(integrationConfig as any);

      await service.createDocusignEnvelope('interview123', mockSession);

      expect(docusignService.createEnvelope).toHaveBeenCalledWith(
        expect.objectContaining({
          schwabTemplateId: 'schwab-template-123',
        }),
        mockSession,
      );
    });

    it('should handle without secondary contact', async () => {
      const clientWithoutSecondary = { ...mockClient, secondaryContact: null };
      clientsService.findOne.mockResolvedValue(clientWithoutSecondary as any);

      await service.createDocusignEnvelope('interview123', mockSession);

      expect(docusignService.createEnvelope).toHaveBeenCalledWith(
        expect.objectContaining({
          coapplicant: false,
        }),
        mockSession,
      );
    });

    it('should throw error when interview not found', async () => {
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(service.createDocusignEnvelope('interview123', mockSession)).rejects.toThrow(
        new HttpException('Interview not found', HttpStatus.NOT_FOUND),
      );
    });

    it('should handle Schwab owned DocuSign account', async () => {
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Schwab,
      } as any);

      await service.createDocusignEnvelope('interview123', mockSession);

      expect(docusignService.createEnvelope).toHaveBeenCalledWith(
        expect.objectContaining({
          isSchwabOwnedDocusignAccount: true,
        }),
        mockSession,
      );
    });

    it('should fallback to organisation integration config when advisor config is null', async () => {
      const orgConfig = { schwabTemplateId: 'org-template-123' };
      advisorsDocusignService.findDocusignIntegration.mockResolvedValue(null);
      organisationsService.findDocusignIntegration.mockResolvedValue(orgConfig as any);

      await service.createDocusignEnvelope('interview123', mockSession);

      expect(docusignService.createEnvelope).toHaveBeenCalledWith(
        expect.objectContaining({
          schwabTemplateId: 'org-template-123',
        }),
        mockSession,
      );
    });
  });

  describe('prepareDocusignEnvelope', () => {
    beforeEach(() => {
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(mockInterview),
      } as any);
      interviewModel.find.mockResolvedValue([mockInterview] as any);
      clientsService.findOne.mockResolvedValue(mockClient as any);
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
      } as any);
      docusignService.getEnvelopDocuments.mockResolvedValue({
        envelopeDocuments: ['existing-doc.pdf'],
      } as any);
      documentsService.getAccountOpeningAdvisoryFiles.mockResolvedValue([]);
      documentsService.getFilesFromS3.mockResolvedValue(mockFiles);
      docusignService.addAdvisorDocuments.mockResolvedValue(undefined);
      advisorsCrudService.findOne.mockResolvedValue(mockAdvisor as any);
      advisorsCrmService.getCrmInstance.mockResolvedValue(mockCrmInstance as any);
      mockCrmInstance.getContact.mockResolvedValue(mockCrmContactData);
      clsService.get.mockReturnValue(null);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
    });

    it('should handle DocuSign selected with no existing envelope', async () => {
      const interviewWithoutEnvelope = { ...mockInterview, envelopeId: null };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithoutEnvelope),
      } as any);
      docusignService.createEnvelope.mockResolvedValue('new-envelope123');
      interviewModel.updateMany.mockResolvedValue({ acknowledged: true } as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(service.createDocusignEnvelope).toBeDefined();
      expect(docusignService.addAdvisorDocuments).toHaveBeenCalled();
    });

    it('should handle DocuSign selected with existing envelope', async () => {
      const interviewWithEnvelope = { ...mockInterview, envelopeId: 'existing123' };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithEnvelope),
      } as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(docusignService.getEnvelopDocuments).toHaveBeenCalledWith({
        envelopeId: 'existing123',
        organisationId: 'org123',
        advisorId: 'advisor123',
      });
      expect(docusignService.addAdvisorDocuments).toHaveBeenCalled();
    });

    it('should handle both interviews finished scenario', async () => {
      interviewModel.find.mockResolvedValue([
        { ...mockInterview, isComplete: true },
        { ...mockInterview, isComplete: true },
      ] as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(docusignService.addAdvisorDocuments).toHaveBeenCalledWith(
        expect.objectContaining({
          advisorId: 'advisor123',
          organisationId: 'org123',
          envelopeId: undefined,
          files: mockFiles,
          interviewData: expect.any(Object),
          previousDocumentUploadNo: 2,
        }),
      );
    });

    it('should handle only one interview finished', async () => {
      interviewModel.find.mockResolvedValue([
        { ...mockInterview, isComplete: true },
        { ...mockInterview, isComplete: false },
      ] as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(docusignService.addAdvisorDocuments).not.toHaveBeenCalled();
    });

    it('should handle DocuSign not selected', async () => {
      const interviewNotDocusignSelected = { ...mockInterview, docusignSelected: false };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewNotDocusignSelected),
      } as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(docusignService.createEnvelope).not.toHaveBeenCalled();
      expect(docusignService.addAdvisorDocuments).not.toHaveBeenCalled();
    });

    it('should filter files that already exist in envelope', async () => {
      docusignService.getEnvelopDocuments.mockResolvedValue({
        envelopeDocuments: ['advisory-agreement.pdf'],
      } as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(docusignService.addAdvisorDocuments).toHaveBeenCalledWith(
        expect.objectContaining({
          files: [],
        }),
      );
    });

    it('should handle interview not found error', async () => {
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(null),
      } as any);

      await expect(service.prepareDocusignEnvelope('interview123', mockSession)).rejects.toThrow(
        'Interview interview123 not found',
      );
    });

    it('should handle client not found error', async () => {
      const interviewWithoutClient = { ...mockInterview, client: null };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithoutClient),
      } as any);

      await expect(service.prepareDocusignEnvelope('interview123', mockSession)).rejects.toThrow(
        'Client not found for interview interview123',
      );
    });

    it('should handle createDocusignEnvelope error gracefully', async () => {
      const interviewWithoutEnvelope = { ...mockInterview, envelopeId: null };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithoutEnvelope),
      } as any);
      const createError = new Error('DocuSign service error');
      jest.spyOn(service, 'createDocusignEnvelope').mockRejectedValue(createError);

      await expect(service.prepareDocusignEnvelope('interview123', mockSession)).rejects.toThrow(createError);
      expect(logger.error).toHaveBeenCalledWith('Failed to create DocuSign envelope: DocuSign service error');
    });
  });

  describe('getInterviewDataWithCrmInfo', () => {
    const mockInterviewData = {
      interview: mockInterview,
      primaryContact: mockClient.primaryContact,
      secondaryContact: mockClient.secondaryContact,
      primaryAdvisor: mockClient.primaryAdvisor,
    };

    beforeEach(() => {
      advisorsCrudService.findOne.mockResolvedValue(mockAdvisor as any);
      advisorsCrmService.getCrmInstance.mockResolvedValue(mockCrmInstance as any);
      mockCrmInstance.getType.mockResolvedValue(CRMEnum.Redtail);
      mockCrmInstance.getContact.mockResolvedValue(mockCrmContactData);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
    });

    it('should enrich primary contact data', async () => {
      const result = await (service as any).getInterviewDataWithCrmInfo(
        mockInterviewData,
        'advisor123',
        mockSession,
      );

      expect(mockCrmInstance.getContact).toHaveBeenCalledWith('crm123', true, true);
      expect(result.primaryContact).toMatchObject({
        ...mockClient.primaryContact,
        ...mockCrmContactData,
        dob: '01/01/1990',
      });
    });

    it('should enrich secondary contact data', async () => {
      const secondaryCrmData = {
        ...mockCrmContactData,
        firstName: 'Bob',
        accounts: [{
          type: 'brokerage',
          name: 'Single-Name Brokerage Account',
          accountNumber: '67890',
        }]
      };
      mockCrmInstance.getContact
        .mockResolvedValueOnce(mockCrmContactData)
        .mockResolvedValueOnce(secondaryCrmData);

      const result = await (service as any).getInterviewDataWithCrmInfo(
        mockInterviewData,
        'advisor123',
        mockSession,
      );

      expect(mockCrmInstance.getContact).toHaveBeenCalledTimes(2);
      expect(mockCrmInstance.getContact).toHaveBeenNthCalledWith(2, 'crm456', true, true);
      expect(result.secondaryContact).toMatchObject({
        ...mockClient.secondaryContact,
        ...secondaryCrmData,
        dob: '01/01/1990',
      });
    });

    it('should use organization from CLS if available', async () => {
      clsService.get.mockReturnValue(mockOrganisation);

      const result = await (service as any).getInterviewDataWithCrmInfo(
        mockInterviewData,
        'advisor123',
        mockSession,
      );

      expect(clsService.get).toHaveBeenCalledWith(ClsDataEnum.Organisation);
      expect(organisationsService.findOne).not.toHaveBeenCalled();
      expect(result.organisation).toEqual(mockOrganisation);
    });

    it('should fetch organization if not in CLS', async () => {
      clsService.get.mockReturnValue(null);

      const result = await (service as any).getInterviewDataWithCrmInfo(
        mockInterviewData,
        'advisor123',
        mockSession,
      );

      expect(organisationsService.findOne).toHaveBeenCalledWith('org123', mockSession);
      expect(result.organisation).toEqual(mockOrganisation);
    });

    it('should merge accounts with correct CRM type', async () => {
      const crmAccounts = [{
        type: 'ira',
        name: 'Traditional IRA Account',
        accountNumber: '12345',
      }];
      mockCrmInstance.getContact.mockResolvedValue({
        ...mockCrmContactData,
        accounts: crmAccounts,
      });

      const result = await (service as any).getInterviewDataWithCrmInfo(
        mockInterviewData,
        'advisor123',
        mockSession,
      );

      expect(result.primaryContact.accounts).toHaveLength(1);
      expect(result.primaryContact.accounts[0]).toMatchObject({
        ...mockClient.primaryContact.accounts[0],
        ...crmAccounts[0],
      });
    });

    it('should handle missing secondary contact', async () => {
      const dataWithoutSecondary = {
        ...mockInterviewData,
        secondaryContact: null,
      };

      const result = await (service as any).getInterviewDataWithCrmInfo(
        dataWithoutSecondary,
        'advisor123',
        mockSession,
      );

      expect(mockCrmInstance.getContact).toHaveBeenCalledTimes(1);
      expect(result.secondaryContact).toBeNull();
    });

    it('should format dates correctly', async () => {
      mockCrmInstance.getContact.mockResolvedValue({
        ...mockCrmContactData,
        dob: '1990-01-01T00:00:00Z',
      });

      const result = await (service as any).getInterviewDataWithCrmInfo(
        mockInterviewData,
        'advisor123',
        mockSession,
      );

      expect(result.primaryContact.dob).toBe('01/01/1990');
    });
  });

  describe('mergeAccounts', () => {
    const dbAccounts = [
      {
        type: AccountTypeEnum.Ira,
        label: 'IRA Account',
        ownership: 'Individual',
        masterAccountNumber: 'master123',
        features: [],
        advisoryRate: 0.5,
      },
      {
        type: AccountTypeEnum.JointNameBrokerage,
        label: 'Joint Account',
        ownership: 'Joint',
        masterAccountNumber: 'master456',
        features: [],
        advisoryRate: 0.5,
      },
    ] as AccountDto[];

    const crmAccounts: GenericCrmAccount[] = [
      {
        type: 'ira',
        name: 'Traditional IRA Account',
        accountNumber: '12345',
      } as any,
      {
        type: 'joint',
        name: 'Joint-Name Brokerage Account',
        accountNumber: '67890',
      } as any,
    ];

    it('should handle Redtail CRM matching logic', async () => {
      const result = await service.mergeAccounts(crmAccounts, dbAccounts, CRMEnum.Redtail);

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        ...dbAccounts[0],
        ...crmAccounts[0],
      });
    });

    it('should handle Practifi/Salesforce matching logic', async () => {
      const practifiCrmAccounts: GenericCrmAccount[] = [
        {
          type: 'ira',
          name: 'IRA Account', // This should match dbAccounts[0].label
          accountNumber: '12345',
        } as any,
        {
          type: 'joint',
          name: 'Joint Account', // This should match dbAccounts[1].label
          accountNumber: '67890',
        } as any,
      ];

      const result = await service.mergeAccounts(practifiCrmAccounts, dbAccounts, CRMEnum.Practifi);

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        ...dbAccounts[0],
        ...practifiCrmAccounts[0],
      });
    });

    it('should handle joint name brokerage special case', async () => {
      const jointAccount = {
        type: AccountTypeEnum.JointNameBrokerage,
        label: 'Joint Brokerage',
        ownership: RedtailAccountOwnershipEnum.JTWROS,
        masterAccountNumber: 'master789',
        features: [],
        advisoryRate: 0.5,
      } as AccountDto;

      const jointCrmAccount: GenericCrmAccount = {
        type: 'joint',
        name: 'Account with JTWROS ownership',
        accountNumber: '11111',
      } as any;

      const result = await service.mergeAccounts([jointCrmAccount], [jointAccount], CRMEnum.Redtail);

      expect(result[0]).toMatchObject({
        ...jointAccount,
        ...jointCrmAccount,
      });
    });

    it('should return unmatched accounts with empty CRM data', async () => {
      const unmatchedDbAccount = {
        type: AccountTypeEnum.SingleNameBrokerage,
        label: 'Unmatched Account',
        ownership: 'Individual',
        masterAccountNumber: 'master999',
        features: [],
        advisoryRate: 0.5,
      } as AccountDto;

      const result = await service.mergeAccounts([], [unmatchedDbAccount], CRMEnum.Redtail);

      expect(result[0]).toMatchObject(unmatchedDbAccount);
    });

    it('should handle empty arrays', async () => {
      const result = await service.mergeAccounts([], [], CRMEnum.Redtail);
      expect(result).toEqual([]);
    });

    it('should handle Salesforce CRM type with label matching', async () => {
      const salesforceCrmAccounts: GenericCrmAccount[] = [
        {
          type: 'ira',
          name: 'IRA Account',
          accountNumber: '12345',
        } as any,
      ];

      const result = await service.mergeAccounts(
        salesforceCrmAccounts,
        dbAccounts,
        CRMEnum.Salesforce,
      );

      expect(result[0]).toMatchObject({
        ...dbAccounts[0],
        ...salesforceCrmAccounts[0],
      });
    });
  });

  describe('getEnvelopeFiles', () => {
    const mockAdvisoryFiles = [
      {
        originalname: 'advisory-agreement.pdf',
        buffer: Buffer.from('advisory content'),
        mimetype: 'application/pdf',
      } as Express.Multer.File,
    ];

    const mockAccountFiles = [
      {
        originalname: 'account-doc.pdf',
        buffer: Buffer.from('account content'),
        mimetype: 'application/pdf',
      } as Express.Multer.File,
    ];

    beforeEach(() => {
      documentsService.getAccountOpeningAdvisoryFiles.mockResolvedValue(mockAccountFiles);
      documentsService.getFilesFromS3.mockResolvedValue(mockAdvisoryFiles);
    });

    it('should get files for primary and secondary contacts', async () => {
      const result = await service.getEnvelopeFiles(
        DocusignAccountOwnershipEnum.Firm,
        'org123',
        mockClient.primaryContact as any,
        mockClient.secondaryContact as any,
      );

      expect(documentsService.getAccountOpeningAdvisoryFiles).toHaveBeenCalledTimes(2);
      expect(documentsService.getAccountOpeningAdvisoryFiles).toHaveBeenNthCalledWith(
        1,
        mockClient.primaryContact.accounts,
        'org123',
      );
      expect(documentsService.getAccountOpeningAdvisoryFiles).toHaveBeenNthCalledWith(
        2,
        mockClient.secondaryContact.accounts,
        'org123',
      );
      expect(result).toEqual([...mockAccountFiles, ...mockAccountFiles, ...mockAdvisoryFiles]);
    });

    it('should get advisory agreement files', async () => {
      const result = await service.getEnvelopeFiles(
        DocusignAccountOwnershipEnum.Firm,
        'org123',
        mockClient.primaryContact as any,
        mockClient.secondaryContact as any,
      );

      expect(documentsService.getFilesFromS3).toHaveBeenCalledWith(
        [AccountAdvisoryDocumentsEnum.AdvisoryAgreement],
        'org123',
      );
      expect(result).toContain(mockAdvisoryFiles[0]);
    });

    it('should handle empty accounts arrays', async () => {
      const contactWithoutAccounts = { ...mockClient.primaryContact, accounts: [] };

      await service.getEnvelopeFiles(
        DocusignAccountOwnershipEnum.Firm,
        'org123',
        contactWithoutAccounts as any,
        null as any,
      );

      expect(documentsService.getAccountOpeningAdvisoryFiles).toHaveBeenCalledWith([], 'org123');
    });

    it('should handle null secondary contact', async () => {
      const result = await service.getEnvelopeFiles(
        DocusignAccountOwnershipEnum.Firm,
        'org123',
        mockClient.primaryContact as any,
        null,
      );

      expect(documentsService.getAccountOpeningAdvisoryFiles).toHaveBeenCalledTimes(2);
      expect(documentsService.getAccountOpeningAdvisoryFiles).toHaveBeenNthCalledWith(2, [], 'org123');
    });

    it('should handle undefined accounts', async () => {
      const contactWithUndefinedAccounts = { ...mockClient.primaryContact, accounts: undefined };

      await service.getEnvelopeFiles(
        DocusignAccountOwnershipEnum.Firm,
        'org123',
        contactWithUndefinedAccounts as any,
        null,
      );

      expect(documentsService.getAccountOpeningAdvisoryFiles).toHaveBeenCalledWith([], 'org123');
    });
  });

  describe('circular dependency handling', () => {
    it('should handle forwardRef dependencies correctly', () => {
      expect(service).toBeDefined();
      expect(service.docusignService).toBeDefined();
      expect(service.clientsService).toBeDefined();
      expect(service.organisationsService).toBeDefined();
      expect(service.advisorsCrudService).toBeDefined();
      expect(service.advisorsDocusignService).toBeDefined();
      expect(service.documentsService).toBeDefined();
    });
  });

  describe('edge cases and null checks', () => {
    it('should handle interview with client as string ID', async () => {
      const interviewWithStringClient = {
        ...mockInterview,
        client: 'client123',
      };
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(interviewWithStringClient),
      } as any);

      // Mock the client lookup to return a valid client
      clientsService.findOne.mockResolvedValue(mockClient as any);
      // Mock the getAccountInfo method
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
      } as any);

      await service.createDocusignEnvelope('interview123', mockSession);

      expect(clientsService.findOne).toHaveBeenCalledWith({ _id: 'client123' });
    });

    it('should handle empty secondary contact object', async () => {
      const clientWithEmptySecondary = {
        ...mockClient,
        secondaryContact: {},
      };
      clientsService.findOne.mockResolvedValue(clientWithEmptySecondary as any);
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(mockInterview),
      } as any);
      // Mock the getAccountInfo method
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
      } as any);

      await service.createDocusignEnvelope('interview123', mockSession);

      expect(docusignService.createEnvelope).toHaveBeenCalledWith(
        expect.objectContaining({
          coapplicant: false,
        }),
        mockSession,
      );
    });

    it('should handle null envelope documents response', async () => {
      docusignService.getEnvelopDocuments.mockResolvedValue(null as any);
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(mockInterview),
      } as any);
      // Mock the interviews find method
      interviewModel.find.mockResolvedValue([
        { isComplete: true },
        { isComplete: true },
      ] as any);
      // Mock the client lookup to return a valid client
      clientsService.findOne.mockResolvedValue(mockClient as any);
      // Mock the advisor lookup
      advisorsCrudService.findOne.mockResolvedValue({
        organisation: { _id: 'org123' },
      } as any);
      // Mock the CRM instance
      advisorsCrmService.getCrmInstance.mockResolvedValue(mockCrmInstance as any);
      // Mock the documents service
      documentsService.getAccountOpeningAdvisoryFiles.mockResolvedValue([]);
      documentsService.getFilesFromS3.mockResolvedValue(mockFiles);
      // Mock the getAccountInfo method
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
      } as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(docusignService.addAdvisorDocuments).toHaveBeenCalledWith(
        expect.objectContaining({
          files: mockFiles,
        }),
      );
    });

    it('should handle envelope documents with mixed types', async () => {
      docusignService.getEnvelopDocuments.mockResolvedValue({
        envelopeDocuments: [
          'advisory-agreement.pdf', // This should match the mock file and filter it out
          { name: 'object-doc.pdf' },
          null,
          undefined,
          { name: null },
        ],
      } as any);
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(mockInterview),
      } as any);
      // Mock the interviews find method
      interviewModel.find.mockResolvedValue([
        { isComplete: true },
        { isComplete: true },
      ] as any);
      // Mock the client lookup to return a valid client
      clientsService.findOne.mockResolvedValue(mockClient as any);
      // Mock the advisor lookup
      advisorsCrudService.findOne.mockResolvedValue({
        organisation: { _id: 'org123' },
      } as any);
      // Mock the CRM instance
      advisorsCrmService.getCrmInstance.mockResolvedValue(mockCrmInstance as any);
      // Mock the documents service
      documentsService.getAccountOpeningAdvisoryFiles.mockResolvedValue([]);
      documentsService.getFilesFromS3.mockResolvedValue(mockFiles);
      // Mock the getAccountInfo method
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
      } as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      const addAdvisorCall = docusignService.addAdvisorDocuments.mock.calls[0][0];
      expect(addAdvisorCall.files).toHaveLength(0);
    });

    it('should handle missing fileUploadsNo in client data', async () => {
      const clientWithoutFileUploads = { ...mockClient, fileUploadsNo: undefined };
      clientsService.findOne.mockResolvedValue(clientWithoutFileUploads as any);
      interviewModel.findById.mockReturnValue({
        session: jest.fn().mockResolvedValue(mockInterview),
      } as any);
      // Mock the interviews find method
      interviewModel.find.mockResolvedValue([
        { isComplete: true },
        { isComplete: true },
      ] as any);
      // Mock the advisor lookup
      advisorsCrudService.findOne.mockResolvedValue({
        organisation: { _id: 'org123' },
      } as any);
      // Mock the CRM instance
      advisorsCrmService.getCrmInstance.mockResolvedValue(mockCrmInstance as any);
      // Mock the documents service
      documentsService.getAccountOpeningAdvisoryFiles.mockResolvedValue([]);
      documentsService.getFilesFromS3.mockResolvedValue(mockFiles);
      // Mock the getAccountInfo method
      docusignService.getAccountInfo.mockResolvedValue({
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
      } as any);

      await service.prepareDocusignEnvelope('interview123', mockSession);

      expect(docusignService.addAdvisorDocuments).toHaveBeenCalledWith(
        expect.objectContaining({
          previousDocumentUploadNo: 0,
        }),
      );
    });
  });
});