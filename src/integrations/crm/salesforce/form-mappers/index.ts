import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { SalesforceNameMapper } from './name.mapper';
import { SalesforceAddressMapper } from './address.mapper';
import { SalesforceSSNMapper } from './ssn.mapper';
import { SalesforceDOBMapper } from './dob.mapper';
import { SalesforcePhoneMapper } from './phone.mapper';
import { SalesforceUSCitizenMapper } from './us-citizen.mapper';
import { SalesforceEmploymentMapper } from './employment.mapper';
import { SalesforcePrimaryBeneficiariesMapper } from './primary-beneficiaries.mapper';
import { SalesforceContingentBeneficiariesMapper } from './contingent-beneficiaries.mapper';
import { SalesforceCompanyMapper } from './company.mapper';
import { SalesforceVIPMapper } from './vip.mapper';
import { SalesforceJobDescriptionMapper } from './job-description.mapper';
import { SalesforceCustomQuestionsMapper } from './custom-questions.mapper';
import { SalesforceConflictOfInterestMapper } from './conflict-of-interest.mapper';
import { SalesforceCRM } from '../crm.salesforce';
import { PageUpdateMapper } from '../../crm.interface';

export function salesforceMappers(
  this: InstanceType<typeof SalesforceCRM>,
): PageUpdateMapper {
  return {
    [PagesEnum.NAME]: new SalesforceNameMapper(this),
    [PagesEnum.ADDRESS]: new SalesforceAddressMapper(this),
    [PagesEnum.SSN]: new SalesforceSSNMapper(this),
    [PagesEnum.DOB]: new SalesforceDOBMapper(this),
    [PagesEnum.PHONE]: new SalesforcePhoneMapper(this),
    [PagesEnum.US_CITIZEN]: new SalesforceUSCitizenMapper(this),
    [PagesEnum.EMPLOYMENT]: new SalesforceEmploymentMapper(this),
    [PagesEnum.PRIMARY_BENEFICIARIES]: new SalesforcePrimaryBeneficiariesMapper(this),
    [PagesEnum.CONTINGENT_BENEFICIARIES]: new SalesforceContingentBeneficiariesMapper(this),
    [PagesEnum.COMPANY]: new SalesforceCompanyMapper(this),
    [PagesEnum.VIP]: new SalesforceVIPMapper(this),
    [PagesEnum.JOB]: new SalesforceJobDescriptionMapper(this),
    [PagesEnum.CUSTOM_QUESTIONS]: new SalesforceCustomQuestionsMapper(this),
    [PagesEnum.CONFLICTS_OF_INTEREST]: new SalesforceConflictOfInterestMapper(this),
  };
} 