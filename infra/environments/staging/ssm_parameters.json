{"TWILIO_ACCOUNT_SID": "/onbord/backend/staging/TWILIO_ACCOUNT_SID", "TWILIO_AUTH_TOKEN": "/onbord/backend/staging/TWILIO_AUTH_TOKEN", "TWILIO_PHONE_NUMBER": "/onbord/backend/staging/TWILIO_PHONE_NUMBER", "DB_USERNAME": "/onbord/backend/staging/MONGODB_ATLAS_DB_USERNAME", "DB_PASSWORD": "/onbord/backend/staging/MONGODB_ATLAS_DB_PASSWORD", "DATABASE_URL": "/onbord/backend/staging/DB_URL", "DD_API_KEY": "/onbord/backend/DD_API_KEY", "DD_APP_KEY": "/onbord/backend/DD_APP_KEY", "DD_SITE": "/onbord/backend/DD_SITE", "DD_LOGS_INJECTION": "/onbord/backend/DD_LOGS_INJECTION", "REDTAIL_FRONTEND_URL": "/onbord/backend/staging/REDTAIL_FRONTEND_URL", "REDTAIL_API_KEY": "/onbord/backend/production/REDTAIL_API_KEY", "REDTAIL_BASE_URL": "/onbord/backend/production/REDTAIL_BASE_URL", "DOCUSIGN_CLIENT_ID": "/onbord/backend/staging/DOCUSIGN_CLIENT_ID", "DOCUSIGN_CLIENT_SECRET": "/onbord/backend/staging/DOCUSIGN_CLIENT_SECRET", "DOCUSIGN_API_BASE_URL": "/onbord/backend/staging/DOCUSIGN_API_BASE_URL", "DOCUSIGN_OAUTH_ACCESS_TOKEN_URI": "/onbord/backend/staging/DOCUSIGN_OAUTH_ACCESS_TOKEN_URI", "DOCUSIGN_OAUTH_REDIRECT_URI": "/onbord/backend/staging/DOCUSIGN_OAUTH_REDIRECT_URI", "DOCUSIGN_CALLBACK_REDIRECT_URI": "/onbord/backend/staging/DOCUSIGN_CALLBACK_REDIRECT_URI", "DOCUSIGN_CALLBACK_ERROR_REDIRECT_URI": "/onbord/backend/staging/DOCUSIGN_CALLBACK_ERROR_REDIRECT_URI", "DOCUSIGN_STATUS_WEBHOOK_URI": "/onbord/backend/staging/DOCUSIGN_STATUS_WEBHOOK_URI", "MAIL_HOST": "/onbord/backend/MAIL_HOST", "MAIL_USER": "/onbord/backend/MAIL_USER", "MAIL_PASSWORD": "/onbord/backend/MAIL_PASSWORD", "MAIL_FROM": "/onbord/backend/MAIL_FROM", "MAIL_PORT": "/onbord/backend/MAIL_PORT", "WEALTHBOX_FRONTEND_URL": "/onbord/backend/staging/WEALTHBOX_FRONTEND_URL", "WEALTHBOX_BASE_URL": "/onbord/backend/staging/WEALTHBOX_BASE_URL", "WEALTHBOX_BASE_AUTH_URL": "/onbord/backend/staging/WEALTHBOX_BASE_AUTH_URL", "WEALTHBOX_OAUTH_REDIRECT_URI": "/onbord/backend/staging/WEALTHBOX_OAUTH_REDIRECT_URI", "WEALTHBOX_CLIENT_ID": "/onbord/backend/staging/WEALTHBOX_CLIENT_ID", "WEALTHBOX_CLIENT_SECRET": "/onbord/backend/staging/WEALTHBOX_CLIENT_SECRET", "WEALTHBOX_CALLBACK_REDIRECT_URI": "/onbord/backend/staging/WEALTHBOX_CALLBACK_REDIRECT_URI", "WEALTHBOX_CALLBACK_ERROR_REDIRECT_URI": "/onbord/backend/staging/WEALTHBOX_CALLBACK_ERROR_REDIRECT_URI", "REDIS_HOST": "/onbord/backend/staging/REDIS_HOST", "REDIS_PORT": "/onbord/backend/staging/REDIS_PORT", "REDIS_PASSWORD": "/onbord/backend/staging/REDIS_PASSWORD", "SALESFORCE_CLIENT_ID": "/onbord/backend/staging/SALESFORCE_CLIENT_ID", "SALESFORCE_CLIENT_SECRET": "/onbord/backend/staging/SALESFORCE_CLIENT_SECRET", "SALESFORCE_AUTH_URL": "/onbord/backend/staging/SALESFORCE_AUTH_URL", "SALESFORCE_API_URL": "/onbord/backend/staging/SALESFORCE_API_URL", "SALESFORCE_TOKEN_URL": "/onbord/backend/staging/SALESFORCE_TOKEN_URL", "SALESFORCE_OAUTH_REDIRECT_URI": "/onbord/backend/staging/SALESFORCE_OAUTH_REDIRECT_URI", "SALESFORCE_CALLBACK_REDIRECT_URI": "/onbord/backend/staging/SALESFORCE_CALLBACK_REDIRECT_URI", "SALESFORCE_CALLBACK_ERROR_REDIRECT_URI": "/onbord/backend/staging/SALESFORCE_CALLBACK_ERROR_REDIRECT_URI", "PRACTIFI_CLIENT_ID": "/onbord/backend/staging/PRACTIFI_CLIENT_ID", "PRACTIFI_CLIENT_SECRET": "/onbord/backend/staging/PRACTIFI_CLIENT_SECRET", "PRACTIFI_OAUTH_REDIRECT_URI": "/onbord/backend/staging/PRACTIFI_OAUTH_REDIRECT_URI"}