export const mockCognitoService = {
  initiateAuth: jest.fn().mockReturnValue({
    AuthenticationResult: { AccessToken: 'testToken' },
  }),
  respondToAuthChallenge: jest.fn().mockReturnValue({
    ChallengeName: 'PASSWORD_VERIFIER',
  }),
  adminCreateUser: jest.fn().mockReturnValue({
    User: { Username: 'testuser', Attributes: {} },
  }),
  adminResetUserPassword: jest.fn().mockReturnValue({}),
  confirmForgotPassword: jest.fn().mockReturnValue({}),
  adminUserGlobalSignOut: jest.fn().mockReturnValue({}),
  adminDeleteUser: jest.fn().mockReturnValue({}),
  upsertUser: jest.fn().mockReturnValue({}),
};
