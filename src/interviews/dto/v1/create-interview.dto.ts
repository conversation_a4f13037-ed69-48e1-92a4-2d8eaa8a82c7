import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsNotEmpty,
  IsString,
} from 'class-validator';
import { AccountDto } from 'src/shared/types/accounts/account.dto';

export class CreateInterviewDto {
  @IsNotEmpty()
  @ApiProperty()
  clientId: string;

  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsEmail()
  @ApiProperty()
  email: string;

  @IsArray()
  @ApiProperty({ type: AccountDto, isArray: true })
  accounts: AccountDto[];

  @IsArray()
  @ApiProperty()
  customTemplates?: string[];

  @IsBoolean()
  @ApiProperty()
  docusignSelected: boolean;

  @IsBoolean()
  @ApiProperty()
  doClientProfiling?: boolean;

  @IsString()
  @ApiProperty()
  isPrimary?: boolean;

  @IsBoolean()
  @ApiProperty()
  isAlreadyOnbord?: boolean;
}
