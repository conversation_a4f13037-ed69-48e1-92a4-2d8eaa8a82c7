import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Model } from 'mongoose';
import {
  advisorMock,
  advisorMockObj,
} from 'src/advisors/tests/mocks/advisor.mock';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { Advisor } from 'src/advisors/schemas/advisors.schema';
import { LinkCrmDto } from 'src/advisors/dto/link-crm.dto';
import { CRMService } from 'src/integrations/crm/crm.service';
import { crmMock } from 'src/integrations/crm/tests/mocks/crm.mock';
import { CRMEnum, IntegrationEnum } from 'src/shared/types/integrations';
import { mockRbacService } from 'src/rbac/mocks/rbac.service.mock';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { ClsService } from 'nestjs-cls';
import * as encryption from 'src/utils/encryption';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { ClientsV1Service } from 'src/clients/clients.service';
import { CrmAuthException } from 'src/shared/exceptions/crm-auth.exception';
import { GetClientsQueryDto } from '../../dto/get-clients.dto';
import { GetCrmUsersQueryDto } from '../../dto/get-crm-users.dto';
import { LogCommunicationDto } from 'src/integrations/crm/types/communications/log-communication.dto';
import * as utils from 'src/utils';
import { CRM } from 'src/integrations/crm/crm.interface';
import { CreateContactDto } from 'src/integrations/crm/types/create-contact.dto';
import { CommunicationTypeEnum } from 'src/integrations/crm/types/communications/communication-type.enum';

jest.mock('aws-sdk', () => {
  const mKMS = {
    encrypt: jest.fn().mockReturnValue({
      promise: jest.fn().mockReturnValue({ CiphertextBlob: 'encrypted' }),
    }),
    decrypt: jest.fn().mockReturnValue({
      promise: jest.fn().mockReturnValue({ Plaintext: 'decrypted' }),
    }),
    promise: jest.fn(),
  };
  return { KMS: jest.fn(() => mKMS) };
});

describe('AdvisorsCrmService', () => {
  const mockExec = jest.fn();

  let model: jest.Mocked<Model<Advisor>>;

  let service: AdvisorsCrmService;
  let crmService: CRMService;
  let clsService: ClsService;
  let advisorsCrudService: AdvisorsCrudService;
  let organisationService: OrganisationsService;
  let clientService: ClientsV1Service;
  let spyEncrypt: jest.SpyInstance;
  let spyAddIntegration: jest.SpyInstance;
  let spyDecryptCredentials: jest.SpyInstance;

  const advisorId = '60a83f52c18e190a4d3e417b';
  const organismId = '60a83f52c18e190a4d3e417c';

  const advisor = {
    _id: advisorId,
    organisation: {
      _id: organismId,
    },
    integrations: [],
  };

  const organisation = {
    _id: organismId,
    crmInitialized: false,
    name: 'Organisation',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdvisorsCrmService,
        {
          provide: AdvisorsCrudService,
          useValue: {
            findOne: jest.fn().mockImplementation((filter) => {
              return {
                ...advisor,
                _id: filter._id || advisorId,
                organisation: {
                  _id: organismId,
                }
              };
            }),
          },
        },
        {
          provide: OrganisationsService,
          useValue: {
            findOne: jest.fn().mockResolvedValue(organisation),
            upsertConfig: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: TransactionManager,
          useValue: {
            runTransaction: jest.fn(async (work) => {
              return work({});
            }),
          },
        },
        {
          provide: getModelToken(Advisor.name),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn().mockImplementation(() => {
              return {
                skip: jest.fn().mockReturnThis(),
                limit: jest.fn().mockReturnThis(),
                exec: mockExec,
              };
            }),
            create: jest.fn().mockResolvedValue(advisorMockObj),
            updateOne: jest.fn(),
            deleteOne: jest.fn(),
          },
        },
        {
          provide: CRMService,
          useValue: {
            create: jest.fn(),
          },
        },
        {
          provide: ClsService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: ClientsV1Service,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: Logger,
          useValue: {
            setContext: jest.fn(),
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
          },
        },
      ],
    }).compile();

    model = module.get<jest.Mocked<Model<Advisor>>>(
      getModelToken(Advisor.name),
    );

    service = module.get<AdvisorsCrmService>(AdvisorsCrmService);
    crmService = module.get<CRMService>(CRMService);
    clsService = module.get<ClsService>(ClsService);
    advisorsCrudService = module.get<AdvisorsCrudService>(AdvisorsCrudService);
    organisationService = module.get<OrganisationsService>(OrganisationsService);
    clientService = module.get<ClientsV1Service>(ClientsV1Service);

    spyEncrypt = jest.spyOn(utils, 'encrypt');
    spyAddIntegration = jest.spyOn(utils, 'addIntegration');
    spyDecryptCredentials = jest.spyOn(utils, 'decryptCredentials');
    
    // Mock CRM methods
    crmMock.getContacts = jest.fn().mockResolvedValue([]);
    crmMock.createContact = jest.fn().mockResolvedValue({ id: '1' });
    crmMock.logCommunication = jest.fn().mockResolvedValue(undefined);

    // Set up the advisor mock with organization
    const mockAdvisorWithOrg = {
      ...advisorMockObj,
      organisation: {
        _id: organismId,
      }
    };

    // Update the model.findOne to include proper organization
    (model.findOne as jest.Mock).mockResolvedValue(mockAdvisorWithOrg);

    // Clear and reset the mockExec function for each test
    mockExec.mockReset();
  });

  afterEach(() => {
    jest.clearAllMocks();

    jest.spyOn(global, 'Date').mockRestore();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('linkCrm', () => {
    const today = new Date();

    const credentials = {
      username: 'username',
      password: 'password',
    };

    const dto = {
      crmType: CRMEnum.Redtail,
      credentials,
    } as LinkCrmDto;

    const authData = {
      databaseId: '123',
      userId: '456',
      userKey: 'userKey',
      username: 'username',
      password: 'password',
    };

    const data = {
      ...advisorMockObj,
      integrations: [
        {
          integrationType: IntegrationEnum.Crm,
          integrationConfig: {
            name: CRMEnum.Redtail,
            credentials: 'encrypted',
          },
        },
      ],
      updatedAt: today,
    } as any;

    it('should throw an error if CRM is already linked', async () => {
      const dto: LinkCrmDto = { 
        crmType: CRMEnum.Redtail,
        credentials: {
          username: 'username',
          password: 'password'
        }
      };
      
      // First, make the service find a properly mocked advisor with valid organisation
      (advisorsCrudService.findOne as jest.Mock).mockResolvedValueOnce({
        ...advisorMockObj,
        _id: advisorId,
        organisation: {
          _id: organismId,
          toString: () => organismId,
          integrations: {
            crm: {
              redtail: {
                token: 'token',
              },
            },
          },
        },
      });

      // Then mock the service to throw the expected error
      (crmService.create as jest.Mock).mockRejectedValueOnce(
        new HttpException('Failed to authenticate CRM.', HttpStatus.INTERNAL_SERVER_ERROR)
      );

      await expect(
        service.linkCrm({ _id: advisorMockObj._id }, dto),
      ).rejects.toThrow('Failed to authenticate CRM.');
    });

    it('should throw an error if CRM fails to authenticate', async () => {
      (crmService.create as jest.Mock).mockResolvedValue(crmMock);
      jest
        .spyOn(crmMock, 'authenticate')
        .mockRejectedValue(
          new HttpException('Error', HttpStatus.INTERNAL_SERVER_ERROR),
        );
      jest.spyOn(model, 'updateOne').mockResolvedValue(data);
      
      // Mock the advisorsCrudService.findOne specifically for this test case
      (advisorsCrudService.findOne as jest.Mock).mockResolvedValueOnce({
        ...advisor,
        _id: advisorMockObj._id,
        organisation: {
          _id: organismId,
        },
        integrations: [],
      });

      try {
        await service.linkCrm({ _id: advisorMockObj._id }, dto);
      } catch (e) {
        expect(e).toBeInstanceOf(HttpException);
        expect(e.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      }
    });

    it('should link an advisor to CRM', async () => {
      const args = [
        { _id: advisorMockObj._id },
        {
          integrations: [
            {
              integrationType: IntegrationEnum.Crm,
              integrationConfig: {
                name: CRMEnum.Redtail,
                credentials: 'encrypted',
              },
            },
          ],
          crmId: '456',
          updatedAt: new Date(),
        },
      ];

      (spyEncrypt as jest.Mock).mockResolvedValue('encrypted');
      (spyAddIntegration as jest.Mock).mockResolvedValue({});
      
      // Mock the advisorsCrudService.findOne specifically for this test case
      (advisorsCrudService.findOne as jest.Mock).mockResolvedValueOnce({
        ...advisor,
        _id: advisorMockObj._id,
        organisation: {
          _id: organismId,
        },
        integrations: [],
      });
      
      (organisationService.findOne as jest.Mock).mockResolvedValue(organisation);
      (crmService.create as jest.Mock).mockResolvedValue(crmMock);
      jest.spyOn(crmMock, 'authenticate').mockResolvedValue(authData);
      jest.spyOn(crmMock, 'getAuthenticatedUserId').mockResolvedValue('456');
      jest.spyOn(crmMock, 'initCrm').mockResolvedValue(new Map());
      jest.spyOn(model, 'updateOne').mockResolvedValue({
        nModified: 1,
      } as any);

      await service.linkCrm({ _id: advisorMockObj._id }, dto);

      expect(organisationService.update).toBeCalledWith(organismId.toString(), {
        crmInitialized: true,
      });
    });
  });

  describe('unlinkCrm', () => {
    it('should throw an error if CRM fails to unlink', async () => {
      (advisorsCrudService.findOne as jest.Mock).mockResolvedValueOnce({
        ...advisor,
        organisation: {
          _id: organismId,
        },
      });
      (organisationService.findOne as jest.Mock).mockResolvedValue(organisation);

      jest.spyOn(mockRbacService, 'findOneRole').mockResolvedValueOnce({
        name: 'CompanyAdmin',
      });

      jest
        .spyOn(crmService, 'create')
        .mockRejectedValue(
          // @ts-ignore - HttpException type issues in test mocks
          new HttpException('Error', HttpStatus.INTERNAL_SERVER_ERROR),
        );

      try {
        await service.unlinkCrm({ _id: advisorMockObj._id });
      } catch (e) {
        expect(e).toBeInstanceOf(HttpException);
        expect(e.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      }
    });

    it('should unlink an advisor from CRM', async () => {
      const today = new Date();

      (advisorsCrudService.findOne as jest.Mock).mockResolvedValueOnce({
        ...advisor, 
        organisation: {
          _id: organismId,
        },
        integrations: [
          {
            integrationType: IntegrationEnum.Crm,
            integrationConfig: {
              name: CRMEnum.Redtail,
              credentials: 'encrypted',
            },
          },
        ],
      });
      (organisationService.findOne as jest.Mock).mockResolvedValue(organisation);

      jest.spyOn(global, 'Date').mockImplementationOnce(() => today);
      // @ts-ignore - CRM type issues in test mocks
      jest.spyOn(crmService, 'create').mockResolvedValueOnce(crmMock);
      jest.spyOn(mockRbacService, 'findOneRole').mockResolvedValueOnce({
        name: RolesEnum.CompanyAdmin,
      });
      jest.spyOn(model, 'updateOne').mockResolvedValueOnce({} as any);

      const result = await service.unlinkCrm({ _id: advisorMockObj._id });

      expect(model.updateOne).toHaveBeenCalledWith(
        { _id: advisorMockObj._id },
        { integrations: [], crmId: null, updatedAt: today },
      );
      expect(result).toStrictEqual({
        message: 'CRM unlinked successfully.',
      });
    });
  });

  describe('getCrmClients', () => {
    const advisorId = '60a83f52c18e190a4d3e417b';
    const query = new GetClientsQueryDto();

    const mockClients = [
      {
        id: '1',
        name: 'Client 1',
      },
      {
        id: '2',
        name: 'Client 2',
      },
    ];

    beforeEach(() => {
      (advisorsCrudService.findOne as jest.Mock).mockResolvedValue({
        ...advisor,
        integrations: [
          {
            integrationType: IntegrationEnum.Crm,
            integrationConfig: {
              name: CRMEnum.Redtail,
              credentials: 'encrypted',
            },
          },
        ],
      });
      (spyDecryptCredentials as jest.Mock).mockResolvedValue({
        username: 'username',
        password: 'password',
      });
      (organisationService.findOne as jest.Mock).mockResolvedValue(organisation);
      (crmService.create as jest.Mock).mockResolvedValue(crmMock);
    });

    it('should return an array of CRM clients', async () => {
      (crmMock.getContacts as jest.Mock).mockResolvedValue(mockClients);
      (clientService.findOne as jest.Mock).mockResolvedValue(null);

      const clients = await service.getCrmClients(query, advisorId);

      expect(clients).toHaveLength(2);
      expect(clients[0].isAlreadyOnbord).toBe(false);
    });

    it('should throw an exception if CRM query fails', async () => {
      (crmMock.getContacts as jest.Mock).mockRejectedValue(new Error('Error'));

      await expect(service.getCrmClients(query, advisorId)).rejects.toThrow(
        CrmAuthException,
      );
    });
  });

  describe('getCrmUsers', () => {
    const advisorId = '60a83f52c18e190a4d3e417b';
    const query = new GetCrmUsersQueryDto();

    const mockUsers = [
      {
        crmId: '1',
        name: 'User 1',
        firstName: 'User',
        lastName: '1'
      },
      {
        crmId: '2',
        name: 'User 2',
        firstName: 'User',
        lastName: '2'
      },
    ];

    it('should return an array of CRM users', async () => {
      (advisorsCrudService.findOne as jest.Mock).mockResolvedValue({
        ...advisor,
        integrations: [
          {
            integrationType: IntegrationEnum.Crm,
            integrationConfig: {
              name: CRMEnum.Redtail,
              credentials: 'encrypted',
            },
          },
        ],
      });
      (spyDecryptCredentials as jest.Mock).mockResolvedValue({
        username: 'username',
        password: 'password',
      });
      (organisationService.findOne as jest.Mock).mockResolvedValue(organisation);
      (crmService.create as jest.Mock).mockResolvedValue(crmMock);
      jest.spyOn(crmMock, 'getAllUsers').mockResolvedValue(mockUsers);
      jest.spyOn(service, 'getAdvisorsByCrmId').mockResolvedValue([]);

      const users = await service.getCrmUsers(query, advisorId);

      expect(users).toHaveLength(2);
      expect(users[0].isAlreadyOnbord).toBe(false);
    });
  });

  describe('getAdvisorsByCrmId', () => {
    it('should return an array of advisors', async () => {
      const crmIds = ['1', '2'];
      const mockAdvisors = [
        { _id: '1', crmId: '1' },
        { _id: '2', crmId: '2' },
      ];
      
      // Mock the method called directly by getAdvisorsByCrmId
      model.find = jest.fn().mockResolvedValue(mockAdvisors);

      const advisors = await service.getAdvisorsByCrmId(crmIds);

      expect(advisors).toEqual(mockAdvisors);
      expect(model.find).toHaveBeenCalledWith({ crmId: { $in: crmIds } });
    });
  });

  describe('getCrmInstance', () => {
    it('should throw an exception if no integrations found', async () => {
      (advisorsCrudService.findOne as jest.Mock).mockResolvedValue(advisor);

      await expect(service.getCrmInstance(advisorId)).rejects.toThrow(
        new HttpException('CRM not linked.', HttpStatus.INTERNAL_SERVER_ERROR),
      );
    });

    it('should return a CRM instance', async () => {
      (advisorsCrudService.findOne as jest.Mock).mockResolvedValue({
        ...advisor,
        integrations: [
          {
            integrationType: IntegrationEnum.Crm,
            integrationConfig: {
              name: CRMEnum.Redtail,
              credentials: 'encrypted',
            },
          },
        ],
      });
      (spyDecryptCredentials as jest.Mock).mockResolvedValue({
        username: 'username',
        password: 'password',
      });
      (organisationService.findOne as jest.Mock).mockResolvedValue(organisation);
      (crmService.create as jest.Mock).mockResolvedValue(crmMock);

      const crm = await service.getCrmInstance(advisorId);

      expect(crm).toBeDefined();
      expect(crm.getContacts).toBeDefined();
    });
  });

  describe('createCrmContact', () => {
    const advisorId = '60a83f52c18e190a4d3e417b';
    const mockContact = {
      primaryContact: {
        firstName: 'John',
        lastName: 'Doe',
      },
    };

    it('should create a CRM contact', async () => {
      (advisorsCrudService.findOne as jest.Mock).mockResolvedValue({
        ...advisor,
        integrations: [
          {
            integrationType: IntegrationEnum.Crm,
            integrationConfig: {
              name: CRMEnum.Redtail,
              credentials: 'encrypted',
            },
          },
        ],
      });
      (spyDecryptCredentials as jest.Mock).mockResolvedValue({
        username: 'username',
        password: 'password',
      });
      (organisationService.findOne as jest.Mock).mockResolvedValue(organisation);
      (crmService.create as jest.Mock).mockResolvedValue(crmMock);
      (crmMock.createContact as jest.Mock).mockResolvedValue({ id: '1', ...mockContact });

      await expect(
        service.createCrmContact(mockContact as CreateContactDto, advisorId),
      ).resolves.toEqual({ id: '1', ...mockContact });
    });
  });

  describe('logCommunication', () => {
    const crmClientId = '1';
    const logCommunicationDto: LogCommunicationDto = {
      communicationType: CommunicationTypeEnum.Phone,
      communicationDetails: 'Test subject: Test description',
    };
    const advisorId = '60a83f52c18e190a4d3e417b';

    it('should log a communication', async () => {
      (advisorsCrudService.findOne as jest.Mock).mockResolvedValue({
        ...advisor,
        integrations: [
          {
            integrationType: IntegrationEnum.Crm,
            integrationConfig: {
              name: CRMEnum.Redtail,
              credentials: 'encrypted',
            },
          },
        ],
      });
      (spyDecryptCredentials as jest.Mock).mockResolvedValue({
        username: 'username',
        password: 'password',
      });
      (organisationService.findOne as jest.Mock).mockResolvedValue(organisation);
      (crmService.create as jest.Mock).mockResolvedValue(crmMock);

      await service.logCommunication(crmClientId, logCommunicationDto, advisorId);

      expect(crmMock.logCommunication).toHaveBeenCalledWith(
        crmClientId,
        logCommunicationDto,
      );
    });
  });
});
