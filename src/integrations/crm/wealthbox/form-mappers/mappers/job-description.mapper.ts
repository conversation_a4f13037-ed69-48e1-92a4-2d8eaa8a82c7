import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';
import { JobDescriptionStatusRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/job-description-request.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { insertIntoUserContentField } from 'src/integrations/crm/wealthbox/utils/data-parsing/general-text-field';
import { GenericUserDefinedFieldsEnum } from 'src/integrations/crm/types/generic/user-defined-fields.enum';

export class WealthboxJobDescriptionMapper implements FormUpdateMapper {
  constructor(private WealthboxCrm: WealthboxCRM) {}

  /**
   * Maps the page data for a job description status request to a Wealthbox CRM contact UDF field.
   * @param clientCrmId The ID of the client in the Wealthbox CRM.
   * @param organisation The organisation object containing the configuration for the Wealthbox CRM.
   * @param pageData The job description status request DTO containing the page data to be mapped.
   * @throws HttpException if the job description UDF is not configured.
   * @returns A Promise that resolves when the contact UDF has been updated in the Wealthbox CRM.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: JobDescriptionStatusRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    await this.WealthboxCrm.contactService.updateContact(contact.crmClientId, {
      occupation: {
        name: `${pageData.description}: ${pageData.type}`,
        start_date: '2000-01-01',
      },
    });
  }
}
