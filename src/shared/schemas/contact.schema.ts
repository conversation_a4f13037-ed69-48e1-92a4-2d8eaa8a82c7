import { Document } from 'mongoose';
import { Prop, Schema } from '@nestjs/mongoose';
import { Address } from 'src/shared/schemas/address.schema';

@Schema({ _id: false })
export class Contact extends Document {
  @Prop({ required: true })
  firstName: string;

  @Prop({ required: true })
  lastName: string;

  @Prop({ required: false })
  address?: Address;

  @Prop({ required: false })
  email?: string;

  @Prop({ required: false })
  mobile?: string;

  @Prop({ required: false, default: null })
  statementUploadedAt?: string;

  @Prop({ required: false, default: null })
  nonSolicitAgreementVerifiedAt?: string;

  @Prop({ required: false, default: null })
  crmClientId?: string;
}
