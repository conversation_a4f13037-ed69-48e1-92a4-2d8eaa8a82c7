import { Test, TestingModule } from '@nestjs/testing';
import { AdvisorsController } from 'src/advisors/advisors.controller';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { AdvisorsDocusignService } from 'src/advisors/services/advisors.docusign.service';
import { LinkCrmDto } from 'src/advisors/dto/link-crm.dto';
import { advisorMock } from 'src/advisors/tests/mocks/advisor.mock';
import { UpdateAdvisorDto } from 'src/advisors/dto/update-advisor.dto';
import { CRMEnum } from 'src/shared/types/integrations';
import { userMock } from 'src/integrations/crm/redtail/tests/mocks/user.mock';
import { contactMock } from 'src/integrations/crm/redtail/tests/mocks/contact.mock';
import { createAdvisorDtoMock } from 'src/advisors/tests/mocks/create-advisor.dto.mock';
import {
  mockAdvisorsCrudService,
  mockAdvisorsCrmService,
  mockAdvisorsDocusignService,
} from 'src/advisors/tests/mocks/advisors.service.mock';
import mongoose from 'mongoose';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { ClsService } from 'nestjs-cls';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';

describe('AdvisorsController', () => {
  let controller: AdvisorsController;
  let advisorsCrudService: AdvisorsCrudService;
  let advisorsCrmService: AdvisorsCrmService;
  let advisorsDocusignService: AdvisorsDocusignService;
  let transactionManager: TransactionManager;

  const credentials = {
    username: 'username',
    password: 'password',
  };

  const id = advisorMock._id;

  const updateAdvisorDto = advisorMock as any;
  const linkCrmDto = {
    credentials,
  } as LinkCrmDto;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AdvisorsController],
      providers: [
        { provide: AdvisorsCrudService, useValue: mockAdvisorsCrudService },
        { provide: AdvisorsCrmService, useValue: mockAdvisorsCrmService },
        {
          provide: AdvisorsDocusignService,
          useValue: mockAdvisorsDocusignService,
        },
        {
          provide: TransactionManager,
          useValue: {
            runTransaction: jest.fn(),
          },
        },
        {
          provide: ClsService,
          useValue: {
            get: jest.fn().mockImplementation((key: string) => {
              if (key === ClsDataEnum.Advisor) {
                return advisorMock;
              }
              return undefined;
            }),
            run: jest.fn((cb) => cb()),
            set: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<AdvisorsController>(AdvisorsController);
    advisorsCrudService = module.get<AdvisorsCrudService>(AdvisorsCrudService);
    advisorsCrmService = module.get<AdvisorsCrmService>(AdvisorsCrmService);
    advisorsDocusignService = module.get<AdvisorsDocusignService>(
      AdvisorsDocusignService,
    );
    transactionManager = module.get<TransactionManager>(TransactionManager);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should create a new advisor', async () => {
    const response = { message: 'New advisor created!', advisorMock };

    mockAdvisorsCrudService.create.mockResolvedValue(response);
    (transactionManager.runTransaction as jest.Mock).mockImplementation(() =>
      mockAdvisorsCrudService.create(createAdvisorDtoMock),
    );

    expect(await controller.create(createAdvisorDtoMock)).toStrictEqual(
      response,
    );
    expect(mockAdvisorsCrudService.create).toHaveBeenCalledWith(
      createAdvisorDtoMock,
    );
  });

  it.skip('should find all advisors', async () => {
    const response = [advisorMock];

    mockAdvisorsCrudService.findAll.mockResolvedValue(response);

    expect(
      await controller.findAll(new mongoose.Types.ObjectId().toString(), {}),
    ).toBe(response);
    expect(mockAdvisorsCrudService.findAll).toHaveBeenCalled();
  });

  it.skip('should find one advisor', async () => {
    const response = advisorMock;

    mockAdvisorsCrudService.findOne.mockResolvedValue(response);

    expect(await controller.findOne(id)).toContainEqual(response);
    expect(mockAdvisorsCrudService.findOne).toHaveBeenCalledWith({ _id: id });
  });

  it('should update an advisor', async () => {
    const response = {
      message: 'Advisor updated.',
      query: {
        _id: advisorMock._id,
      },
    };

    mockAdvisorsCrudService.update.mockResolvedValue(response);
    (transactionManager.runTransaction as jest.Mock).mockImplementation(() =>
      mockAdvisorsCrudService.update({ _id: id }, updateAdvisorDto),
    );

    expect(await controller.update(id, updateAdvisorDto)).toStrictEqual(
      response,
    );
    expect(mockAdvisorsCrudService.update).toHaveBeenCalledWith(
      { _id: id },
      updateAdvisorDto,
    );
  });

  it('should remove an advisor', async () => {
    const response = {
      message: 'Advisor removed.',
      query: {
        _id: advisorMock._id,
      },
    };

    mockAdvisorsCrudService.remove.mockResolvedValue(response);
    (transactionManager.runTransaction as jest.Mock).mockImplementation(() =>
      mockAdvisorsCrudService.remove({ _id: id }),
    );

    expect(await controller.remove(id)).toStrictEqual(response);
    expect(mockAdvisorsCrudService.remove).toHaveBeenCalledWith({ _id: id });
  });

  it('should link crm', async () => {
    const response = {
      message: `${CRMEnum.Redtail} CRM linked successfully.`,
    };

    mockAdvisorsCrmService.linkCrm.mockResolvedValue(response);

    expect(await controller.linkCrm(id, linkCrmDto)).toEqual(response);
    expect(mockAdvisorsCrmService.linkCrm).toHaveBeenCalledWith(
      { _id: id },
      linkCrmDto,
    );
  });

  it('should unlink crm', async () => {
    const response = {
      message: `${CRMEnum.Redtail} CRM unlinked successfully.`,
    };

    mockAdvisorsCrmService.unlinkCrm.mockResolvedValue(response);

    expect(await controller.unlinkCrm(id)).toEqual(response);
    expect(mockAdvisorsCrmService.unlinkCrm).toHaveBeenCalledWith({ _id: id });
  });

  it('should unlink docusign', async () => {
    mockAdvisorsDocusignService.unlinkDocusign.mockResolvedValue({
      message: 'success',
    });

    expect(await controller.unlinkDocusign(id)).toStrictEqual({
      message: 'success',
    });
  });

  it('should get CRM contacts', async () => {
    const query = { name: 'John Doe' };
    const response = [contactMock];

    mockAdvisorsCrmService.getCrmClients.mockResolvedValue(response);

    expect(await controller.getClients(query)).toEqual(response);

    expect(mockAdvisorsCrmService.getCrmClients).toHaveBeenCalled();
  });

  it('should get CRM advisors', async () => {
    const response = [userMock];

    mockAdvisorsCrmService.getCrmUsers.mockResolvedValue(response);

    expect(await controller.getCrmUsers({})).toEqual(response);

    expect(mockAdvisorsCrmService.getCrmUsers).toHaveBeenCalled();
  });
});
