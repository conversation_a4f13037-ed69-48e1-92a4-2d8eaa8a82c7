import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { PageVariableProvider } from './page-variable-provider.interface';
import { AddressPageVariableProvider } from './address-page-variable.provider';
import { CompanyPageVariableProvider } from './company-page-variable.provider';
import { ConflictsOfInterestPageVariableProvider } from './conflicts-of-interest-page-variable.provider';
import { ContingentBeneficiariesPageVariableProvider } from './contingent-beneficiaries-page-variable.provider';
import { CustomQuestionsPageVariableProvider } from './custom-questions-page-variable.provider';
import { DobPageVariableProvider } from './dob-page-variable.provider';
import { EmploymentPageVariableProvider } from './employment-page-variable.provider';
import { JobPageVariableProvider } from './job-page-variable.provider';
import { NamePageVariableProvider } from './name-page-variable.provider';
import { PhonePageVariableProvider } from './phone-page-variable.provider';
import { PrimaryBeneficiariesPageVariableProvider } from './primary-beneficiaries-page-variable.provider';
import { SsnPageVariableProvider } from './ssn-page-variable.provider';
import { UsCitizenPageVariableProvider } from './us-citizen-page-variable.provider';
import { VipPageVariableProvider } from './vip-page-variable.provider';

import { VariableDefinition } from './types/variable-definition.interface';

export function getPageVariableProvider(
  pageName: PagesEnum,
): PageVariableProvider {
  return (
    {
      [PagesEnum.NAME]: new NamePageVariableProvider(),
      [PagesEnum.ADDRESS]: new AddressPageVariableProvider(),
      [PagesEnum.SSN]: new SsnPageVariableProvider(),
      [PagesEnum.DOB]: new DobPageVariableProvider(),
      [PagesEnum.PHONE]: new PhonePageVariableProvider(),
      [PagesEnum.JOB]: new JobPageVariableProvider(),
      [PagesEnum.EMPLOYMENT]: new EmploymentPageVariableProvider(),
      [PagesEnum.VIP]: new VipPageVariableProvider(),
      [PagesEnum.COMPANY]: new CompanyPageVariableProvider(),
      [PagesEnum.CONFLICTS_OF_INTEREST]: new ConflictsOfInterestPageVariableProvider(),
      [PagesEnum.PRIMARY_BENEFICIARIES]: new PrimaryBeneficiariesPageVariableProvider(),
      [PagesEnum.CONTINGENT_BENEFICIARIES]: new ContingentBeneficiariesPageVariableProvider(),
      [PagesEnum.US_CITIZEN]: new UsCitizenPageVariableProvider(),
      [PagesEnum.CUSTOM_QUESTIONS]: new CustomQuestionsPageVariableProvider(),
    }[pageName] || { getAvailableVariables: (): Record<string, VariableDefinition> => ({}) }
  );
}