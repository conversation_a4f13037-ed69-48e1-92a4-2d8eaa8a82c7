import { welcomeTextTemplate } from './welcome-text/welcome-text.template';
import { clientIncompleteTextTemplate } from './client-incomplete/client-incomplete.template';
import { clientStartTextTemplate } from './client-start/client-start.template';
import { readyToSignTextTemplate } from './ready-to-sign/ready-to-sign.template';
import { reminderToSignTextTemplate } from './reminder-to-sign/reminder-to-sign.template';
import {
  ITextMessageContext,
  TextMessageTemplateNameEnum,
  TextMessageTemplates,
} from './types';
import { transitionStatementTextTemplate } from './transition-statement/transition-statement.template';
import { transitionEmailRegisterTemplate } from 'src/templates/sms/transition-email-register/transition-email-register.template';

/**
 * Factory function that returns a text message template function based on the provided type.
 * @template T - The type of the text message template.
 * @param {T} type - The type of the text message template to be returned.
 * @param {ITextMessageContext} context - The context object to be used by the text message template function.
 * @returns {string} - The text message generated by the text message template function.
 * @throws {Error} - If the template function for the provided type is not found.
 */
export function smsTemplateMapper<T extends keyof TextMessageTemplates>(
  type: T,
  context: ITextMessageContext,
) {
  const templates: TextMessageTemplates = {
    [TextMessageTemplateNameEnum.WelcomeText]: welcomeTextTemplate,
    [TextMessageTemplateNameEnum.ClientIncomplete]:
      clientIncompleteTextTemplate,
    [TextMessageTemplateNameEnum.ClientStart]: clientStartTextTemplate,
    [TextMessageTemplateNameEnum.ReadyToSign]: readyToSignTextTemplate,
    [TextMessageTemplateNameEnum.ReminderToSign]: reminderToSignTextTemplate,
    [TextMessageTemplateNameEnum.NonProtocolTransitionRequestEmail]:
      transitionEmailRegisterTemplate,
    [TextMessageTemplateNameEnum.TransitionStatement]:
      transitionStatementTextTemplate,
  };

  const templateFn = templates[type];

  if (!templateFn) {
    throw new Error(`Template for type ${type} not found.`);
  }

  return templateFn(context);
}
