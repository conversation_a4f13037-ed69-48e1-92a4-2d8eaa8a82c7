import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

export class CompanyPageVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      companyName: {
        description: 'Name of the company or organization',
        type: VariableType.STRING,
        example: 'Tech Solutions Inc.',
      },
      companyAddress: {
        description: 'Company address',
        type: VariableType.ADDRESS,
        example: '789 Corporate Blvd, San Francisco, CA 94107',
      },
      companyPhone: {
        description: 'Company phone number',
        type: VariableType.PHONE,
        example: '(*************',
      },
      companyType: {
        description: 'Type of company (corporation, LLC, partnership, etc.)',
        type: VariableType.STRING,
        example: 'Corporation',
      },
      industry: {
        description: 'Company industry or sector',
        type: VariableType.STRING,
        example: 'Information Technology',
      },
    };
  }
}