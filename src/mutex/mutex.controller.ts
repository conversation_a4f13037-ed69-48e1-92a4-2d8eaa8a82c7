import {
  Controller,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
} from '@nestjs/common';

import { MutexService } from './mutex.service';
import { CreateMutexDto } from './dto/create-mutex.dto';
import { Roles, RolesGuard } from 'src/shared/guards/roles.guard';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { AuthGuard } from '@nestjs/passport';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';

@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'), RolesGuard)
@Controller('mutex')
export class MutexController {
  constructor(private readonly mutexService: MutexService) {}

  @Post()
  @Roles(RolesEnum.SuperAdmin)
  create(@Body() createMutexDto: CreateMutexDto) {
    return this.mutexService.create(createMutexDto);
  }

  @Delete(':serviceName')
  @Roles(RolesEnum.SuperAdmin)
  remove(@Param('serviceName') serviceName: string) {
    return this.mutexService.remove({ serviceName });
  }
}
