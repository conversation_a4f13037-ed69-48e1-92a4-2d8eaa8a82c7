# MongoDB Atlas Flex Cluster Module
# Designed to work with Flex clusters which have replaced M0/M2/M5 clusters

#
#  SSM Parameter Store Variables
#
data "aws_ssm_parameter" "MONGODB_ATLAS_ORG_ID" {
  name = var.mongodb_atlas_org_id_ssm
}

data "aws_ssm_parameter" "MONGODB_ATLAS_API_PUB_KEY" {
  name = var.mongodb_atlas_api_pub_key_ssm
}

data "aws_ssm_parameter" "MONGODB_ATLAS_API_PRI_KEY" {
  name = var.mongodb_atlas_api_pri_key_ssm
}

data "aws_ssm_parameter" "MONGODB_ATLAS_DB_USERNAME" {
  name = var.mongodb_atlas_database_user_username_ssm
}

data "aws_ssm_parameter" "MONGODB_ATLAS_DB_PASSWORD" {
  name = var.mongodb_atlas_database_user_password_ssm
}


#
#  Local Variables
#
locals {
  mongodb_atlas_api_pub_key = data.aws_ssm_parameter.MONGODB_ATLAS_API_PUB_KEY.value
  mongodb_atlas_api_pri_key = data.aws_ssm_parameter.MONGODB_ATLAS_API_PRI_KEY.value
  mongodb_atlas_org_id      = data.aws_ssm_parameter.MONGODB_ATLAS_ORG_ID.value

  # https://docs.atlas.mongodb.com/tutorial/create-mongodb-user-for-cluster/
  mongodb_atlas_database_user_username = data.aws_ssm_parameter.MONGODB_ATLAS_DB_USERNAME.value
  mongodb_atlas_database_user_password = data.aws_ssm_parameter.MONGODB_ATLAS_DB_PASSWORD.value
}

#
# Configure the MongoDB Atlas Provider
#
terraform {
  required_providers {
    mongodbatlas = {
      source  = "mongodb/mongodbatlas"
      version = "~> 1.29.0"
    }
  }
}

provider "mongodbatlas" {
  public_key  = local.mongodb_atlas_api_pub_key
  private_key = local.mongodb_atlas_api_pri_key
}

#
# Create a Project
#
resource "mongodbatlas_project" "onbord_db" {
  name   = "onbord-db-${var.environment}"
  org_id = local.mongodb_atlas_org_id
}

#
# Create a Cluster (configured for Flex in M0 tier)
#
resource "mongodbatlas_advanced_cluster" "cluster" {
  project_id   = mongodbatlas_project.onbord_db.id
  name         = var.environment
  cluster_type = "REPLICASET"
  
  replication_specs {
    num_shards = 1
    
    region_configs {
      electable_specs {
        instance_size = var.instance_size
        node_count    = 3
      }
      provider_name        = "FLEX"
      backing_provider_name = "AWS"
      priority             = 7
      region_name          = "US_EAST_1"
    }
  }

  # Match the existing configuration to prevent unwanted changes
  mongo_db_major_version = "8.0"
  backup_enabled = true
  termination_protection_enabled = false
  version_release_system = "LTS"
  
  # Use lifecycle to ignore certain attributes that we don't want to manage
  lifecycle {
    ignore_changes = [
      mongo_db_major_version,
      mongo_db_version,
      replication_specs[0].zone_name
    ]
  }
}

#
# Create an Atlas Admin Database User
#
resource "mongodbatlas_database_user" "db_user" {
  username           = local.mongodb_atlas_database_user_username
  password           = local.mongodb_atlas_database_user_password
  project_id         = mongodbatlas_project.onbord_db.id
  auth_database_name = "admin"

  roles {
    role_name     = "atlasAdmin"
    database_name = "admin"
  }
}

#
# Create an IP Accesslist
#
resource "mongodbatlas_project_ip_access_list" "atlas_ip_access" {
  project_id = mongodbatlas_project.onbord_db.id
  ip_address = var.nat_gateway_ip
  comment    = "NAT Gateway IP address"
}

resource "aws_ssm_parameter" "mongodb_connection_string" {
  name  = "/onbord/backend/${var.environment}/DB_URL"
  type  = "SecureString"
  value = mongodbatlas_advanced_cluster.cluster.connection_strings.0.standard_srv
}