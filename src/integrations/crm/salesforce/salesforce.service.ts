import { HttpException, Inject, Injectable, forwardRef } from '@nestjs/common';
import { Response } from 'express';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { OAuth } from 'src/auth/auth.types';
import { CRMEnum } from 'src/shared/types/integrations';
import { Logger } from 'winston';
import { PKCEUtil } from '../utils/pkce.util';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { OperationType } from 'src/super-http/enums';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
@Injectable()
export class SalesforceService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly httpService: SuperHttpService,
    @Inject(forwardRef(() => AdvisorsCrmService))
    private readonly advisorsCrmService: AdvisorsCrmService,
    private readonly advisorsCrudService: AdvisorsCrudService,
  ) {}

  /**
   * Handles the callback from the Salesforce OAuth authorization process.
   * Retrieves access and refresh tokens using the provided authorization code.
   */
  async handleCallback(code: string, state: string, res: Response) {
    try {
      // Use consistent state decoding
      const { advisorId, timestamp, codeVerifier } = PKCEUtil.decodeState(state);
      const advisor = await this.advisorsCrudService.findOne({ _id: advisorId });

      // Verify timestamp is not older than 5 minutes
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
      if (timestamp < fiveMinutesAgo) {
        throw new HttpException('Authorization request expired', 400);
      }

      const params = new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        client_id: process.env.SALESFORCE_CLIENT_ID,
        client_secret: process.env.SALESFORCE_CLIENT_SECRET,
        redirect_uri: SalesforceService.getRedirectUri(),
        code_verifier: codeVerifier
      });

      const url = `${process.env.SALESFORCE_TOKEN_URL}/services/oauth2/token`;
      const tokens = await this.httpService.post(
        url,
        advisor.organisation._id,
        params.toString(),
        { rateLimiter: 'salesforce', integrationName: 'salesforce', entityType: 'me', operation: OperationType.GET, forceFresh: true },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
          },
        },
      );

      const { access_token: accessToken, refresh_token: refreshToken, instance_url: instanceUrl } = tokens;

      await this.storeAccessToken(advisorId, { accessToken, refreshToken, instanceUrl });

      res.redirect(process.env.SALESFORCE_CALLBACK_REDIRECT_URI);

      return {
        accessToken,
        refreshToken,
        advisorId,
      };
    } catch (error) {
      this.logger.error('Salesforce OAuth error', {
        error: error.response?.data || error.message,
        stack: error.stack,
        state,
        code
      });
      res.redirect(process.env.SALESFORCE_CALLBACK_ERROR_REDIRECT_URI);
      return error;
    }
  }

  /**
   * Generates the authorization URL for Salesforce integration.
   */
  async getSalesforceAuthorisationUrl(advisorId: string) {
    if (!advisorId) {
      throw new HttpException('No advisor id provided', 400);
    }

    // Generate PKCE values with simpler charset
    const codeVerifier = PKCEUtil.generateCodeVerifier();
    const codeChallenge = PKCEUtil.generateCodeChallenge(codeVerifier);

    // Create and encode state with consistent handling
    const state = PKCEUtil.encodeState({
      advisorId,
      timestamp: Date.now(),
      codeVerifier,
    });

    const params = new URLSearchParams({
      response_type: 'code',
      client_id: process.env.SALESFORCE_CLIENT_ID,
      redirect_uri: SalesforceService.getRedirectUri(),
      state,
      code_challenge: codeChallenge,
      code_challenge_method: 'S256',
      scope: 'api refresh_token',  // Removed openid for now
      prompt: 'login',  // Simplified prompt
      display: 'page'
    });

    const authUrl = `${process.env.SALESFORCE_AUTH_URL}/services/oauth2/authorize?${params.toString()}`;

    this.logger.debug('Authorization URL generated', {
      codeVerifier,
      codeChallenge,
      state,
      url: authUrl
    });

    return authUrl;
  }

  /**
   * Stores the access token for a given advisor in the database.
   */
  async storeAccessToken(advisorId: string, oAuthData: OAuth) {
    await this.advisorsCrmService.linkCrm(
      {
        _id: advisorId,
      },
      {
        credentials: {
          instanceUrl: oAuthData.instanceUrl,
          accessToken: oAuthData.accessToken,
          refreshToken: oAuthData.refreshToken,
          lastRefresh: new Date(),
        },
        crmType: CRMEnum.Salesforce,
      },
      false,
    );
  }

  /**
   * Returns the redirect URI for Salesforce integration.
   */
  public static getRedirectUri() {
    return process.env.SALESFORCE_OAUTH_REDIRECT_URI;
  }
}
