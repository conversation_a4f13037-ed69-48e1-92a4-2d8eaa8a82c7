import { HttpException, HttpStatus, Inject, Injectable, forwardRef , NotFoundException, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import * as dataguardian from 'data-guardian';
import _, { isEmpty } from 'lodash';
import mongoose, { ClientSession, Model } from 'mongoose';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { CSVService } from 'src/csv/csv.service';
import { DocusignEnvelopeStatusEnum } from 'src/integrations/docusign/docusign.types';
import {
  DocusignJsonSimWebhookDto,
  LegacyDocuSignWebhookDto,
} from 'src/integrations/docusign/dto/webhook.dto';
import { MailService } from 'src/notifications/mail/mail.service';
import { SmsService } from 'src/notifications/sms/sms.service';
import { OrganisationsNotificationsService } from 'src/organisations/organisations.notifications.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { NotificationTemplate } from 'src/organisations/schemas/notification.template';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';
import { EmailTemplateNameEnum } from 'src/templates/mail/types';
import { TransitionStatementContext } from 'src/templates/sms/transition-statement/transition-statement.context';
import { TextMessageTemplateNameEnum } from 'src/templates/sms/types';
import { BrokerageStatementUploadQueryDto } from 'src/transitions/dto/public/brokerage-statement-upload.dto';
import { PublicTransitionInfoResponseDto } from 'src/transitions/dto/public/public-info.response.dto';
import { VerifyNonSolicitAgreementQueryDto } from 'src/transitions/dto/public/verify-non-solicit.dto';
import {
  RegisterEmailQueryDto,
  RegisterEmailRequestDto,
} from 'src/transitions/dto/register-email.dto';
import { ContactTypeEnum } from 'src/transitions/types/contact-type.enum';
import { getFrontendDomain } from 'src/utils/getFrontendDomain';
import { replaceTemplatePlaceholders } from 'src/utils/replaceTemplatePlaceholders';
import { Logger } from 'winston';
import {
  CreateTransitionsDto,
  TransitionDto,
} from './dto/create-transitions.dto';
import { UpdateSmsTemplateDto } from './dto/update-sms-template.dto';
import { UpdateTransitionDto } from './dto/update-transition.dto';
import { Transition } from './schemas/transitions.schema';
import { TransitionStatusEnum } from './types/transition-status.enum';
import { TransitionTypeEnum } from './types/transition-type.enum';
import { TransitionCsvRow } from './types/transitions-csv-row.entity';
import { Asset } from 'src/shared/schemas/asset.schema';
import { AssetTypeEnum } from 'src/shared/types/general/asset-types.enum';
import AWS from 'aws-sdk';
import { CreateEnvelopeDto } from 'src/integrations/docusign/dto/create-envelope.dto';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { computeContactFileSuffix } from 'src/utils/pdf-mappers/utils/getContactByFileName';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { EnrichedContact } from 'src/clients/dto/v1/get-clients.dto';
import parsePhoneNumber from 'libphonenumber-js';
import { AuditsService } from 'src/audits/audits.service';
import { AuditSource } from 'src/audits/types/audit';
import { AuditEntityEnum } from 'src/audits/types/entities.enum';
import { getDataFromEnvelope } from 'src/integrations/docusign/utils/envelope-utils';
import { CreateContactResponseDto } from 'src/integrations/crm/types/create-contact.dto';
import { TransitionUrl } from './types/transition-url.type';

@Injectable()
export class TransitionsService {
  private readonly baseUrl: string;

  constructor(
    @InjectModel(Transition.name)
    private readonly transitionModel: Model<Transition>,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private smsService: SmsService,
    private mailService: MailService,
    private organisationNotificationsService: OrganisationsNotificationsService,
    private configService: ConfigService,
    @Inject(forwardRef(() => OrganisationsService))
    private organisationsService: OrganisationsService,
    @Inject(forwardRef(() => AdvisorsCrudService))
    private advisorsCrudService: AdvisorsCrudService,
    @Inject(forwardRef(() => AdvisorsCrmService))
    private advisorsCrmService: AdvisorsCrmService,
    private readonly filesService: CSVService,
    @Inject(forwardRef(() => DocusignService))
    private readonly docusignService: DocusignService,
    private readonly auditService: AuditsService,
  ) {
    this.baseUrl = this.configService.get<string>('BASE_DOMAIN_NAME');
  }

  /**
   * Creates a batch of transitions from an array of transition DTOs.
   * @param organisationId - The ID of the organisation.
   * @param createTransitionsDto - DTO for creating multiple transitions.
   * @returns An array of the created Transition objects.
   */
  async createBatchFromArray(
    organisationId: string,
    createTransitionsDto: CreateTransitionsDto,
    session?: ClientSession,
  ) {
    const MAX_CONCURRENT_TRANSITIONS = 20;
    const { transitions } = createTransitionsDto;
    const transitionsToBeCreated = transitions.map((transition) => {
      return {
        ...transition,
        organisationId,
        status: TransitionStatusEnum.Draft,
        primaryContact: transition.primaryContact && {
          ...transition.primaryContact,
          mobile: parsePhoneNumber(transition.primaryContact.mobile, 'US')
            .nationalNumber,
          crmClientId: transition.primaryContact.crmId,
        },
        secondaryContact: transition.secondaryContact && {
          ...transition.secondaryContact,
          mobile: parsePhoneNumber(transition.secondaryContact.mobile, 'US')
            .nationalNumber,
          crmClientId: transition.secondaryContact.crmId,
        },
      };
    });
    const createdTransitions = await this.transitionModel.insertMany(
      transitionsToBeCreated,
      { session },
    );

    // Send SMS to clients if sendNow is true
    if (createTransitionsDto.sendNow) {
      await Promise.allLimitted(
        createdTransitions.map(async (transition) => {
          await this.upsertTransitionContactsInCrm(transition, session);
          return this.notifyClientsAboutTransition(
            transition,
            organisationId,
            createTransitionsDto.smsPayload,
            session,
          );
        }),
        MAX_CONCURRENT_TRANSITIONS,
      );
    }

    return createdTransitions;
  }

  public async upsertTransitionContactsInCrm(
    transition: Transition,
    session?: ClientSession,
  ) {
    const primaryAdvisorId = transition.primaryAdvisor._id.toString();
    const primaryAdvisor = await this.advisorsCrudService.findOne({
      _id: primaryAdvisorId,
    });
    const primaryCSA = await this.advisorsCrudService.findOne({
      _id: transition.primaryCSA._id?.toString(),
    });
    const crmData = {
      primaryAdvisor,
      primaryCSA,
      primaryContact: {
        firstName: transition.primaryContact.firstName,
        lastName: transition.primaryContact.lastName,
        email: transition.primaryContact.email,
        crmClientId: transition.primaryContact.crmClientId,
        accounts: [],
        mobile: transition.primaryContact?.mobile,
        skipContactInterview: true,
      },
      secondaryContact: transition.secondaryContact && {
        firstName: transition.secondaryContact?.firstName,
        lastName: transition.secondaryContact?.lastName,
        email: transition.secondaryContact?.email,
        crmClientId: transition.secondaryContact?.crmClientId,
        accounts: [],
        mobile: transition.secondaryContact?.mobile,
        skipContactInterview: true,
      },
    };

    // Update existing contacts in CRM
    const hasCrmClientId = transition.primaryContact?.crmClientId;
    if (hasCrmClientId) {
      return this.advisorsCrmService.updateContact(crmData, primaryAdvisorId);
    }

    // Insert new contacts in CRM
    const crmResult: CreateContactResponseDto =
      await this.advisorsCrmService.createCrmContact(crmData, primaryAdvisorId);
    transition.primaryContact.crmClientId = crmResult.primaryContactCrmId;

    if (transition.secondaryContact) {
      transition.secondaryContact.crmClientId = crmResult.secondaryContactCrmId;
    }
    return transition.save({ session });
  }

  /**
   * Creates a batch of transitions from a CSV file.
   *
   * @param organisationId - The ID of the organization.
   * @param transitionType - The type of transition.
   * @param file - The CSV file to parse.
   * @param session - Optional MongoDB session for transactional operations.
   * @returns A promise that resolves to an array of transition DTOs.
   * @throws HttpException if no file is provided or if there is an error during processing.
   */
  async createBatchFromCsv(
    transitionType: TransitionTypeEnum,
    file: Express.Multer.File,
  ) {
    if (!file)
      throw new HttpException('No file provided', HttpStatus.BAD_REQUEST);

    const isFileEmpty = await this.filesService.isCsvEmpty<TransitionCsvRow>(file.buffer, TransitionCsvRow);

    if (isFileEmpty) {
      throw new HttpException('Your file seems to have no data. Please check', HttpStatus.BAD_REQUEST);
    }

    try {
      const csvRows = await this.filesService.parseCsv<TransitionCsvRow>(
        file.buffer,
        TransitionCsvRow,
      );

      // Collect all unique advisor and CSA names
      const advisorEmails = new Set<string>();
      csvRows.forEach((row) => {
        if (row.primaryAdvisorEmail) advisorEmails.add(row.primaryAdvisorEmail);
        if (row.primaryCSAEmail) advisorEmails.add(row.primaryCSAEmail);
      });

      // Query the database for all advisors
      const advisors = await this.advisorsCrudService.findAll(
        { page: 1, limit: 1000 },
        {
          'personalInfo.email': { $in: Array.from(advisorEmails) },
        },
      );

      // Create a map of advisor names to IDs
      const advisorMap = new Map(
        advisors.result.map((advisor) => [
          advisor.personalInfo.email,
          advisor._id,
        ]),
      );

      // Map advisor and CSA names to their IDs
      csvRows.forEach((row) => {
        row.primaryAdvisor = advisorMap.get(row.primaryAdvisorEmail) || null;
        row.primaryCSA = advisorMap.get(row.primaryCSAEmail) || null;
      });

      const transitionDtos = csvRows.map((row) =>
        row.toCreateDto(transitionType),
      );

      return transitionDtos;
    } catch (error) {
      throw new HttpException(error.message, HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Finds all transitions for an organisation.
   * @param paginationOptions - Pagination options.
   * @param filters - Filters for the query.
   * @returns A promise of an array of Transition objects.
   */
  async findAll(
    paginationOptions: PaginationQueryDto,
    filters: mongoose.FilterQuery<Transition>,
  ): Promise<Transition[]> {
    const { limit = 5, page = 1 } = paginationOptions;
    const skip = limit * (page - 1);

    const transitions = await this.transitionModel
      .find(filters)
      .sort({ createdAt: -1 })
      .populate('primaryContact')
      .populate('primaryAdvisor')
      .exec();

    return transitions;
  }

  /**
   * Finds a transition by ID.
   * @param transitionId The ID of the transition.
   * @returns A promise of a Transition object.
   */
  async findOne(
    transitionId: string,
    session?: ClientSession,
  ): Promise<Transition> {
    const select = 'id _id assets personalInfo integrations.integrationType';

    return this.transitionModel
      .findOne({ _id: transitionId })
      .populate('primaryContact')
      .populate({
        path: 'primaryAdvisor',
        select,
      })
      .populate({
        path: 'primaryCSA',
        select,
      })
      .session(session)
      .exec();
  }

  /**
   * Finds a transition by ID.
   * @param transitionId The ID of the transition.
   * @returns A promise of a Transition object.
   */
  async findByIds(transitionIds: string[], session?: ClientSession): Promise<Transition[]> {
    const select = 'id _id assets personalInfo integrations.integrationType';

    return this.transitionModel
    .find({ _id: { $in: transitionIds } })
    .populate('primaryContact')
    .populate({
      path: 'primaryAdvisor',
      select,
    })
    .populate({
      path: 'primaryCSA',
      select,
    })
    .session(session)
    .exec();
  }

  /**
   * Updates a transition.
   * @param id The ID of the transition.
   * @param updateTransitionsDto DTO for updating a transition.
   * @returns A promise of the updated Transition object.
   */
  async update(
    id: string,
    updateTransitionsDto: UpdateTransitionDto,
  ): Promise<Transition> {
    return this.transitionModel
      .findByIdAndUpdate(id, updateTransitionsDto, { new: true })
      .exec();
  }

  /**
   * Updates a transition.
   * @param id The ID of the transition.
   * @param TransitionStatusEnum Enum for updating a transition.
   * @returns A promise of the updated Transition object.
   */
  async updateStatus(
    id: string,
    status: TransitionStatusEnum,
    session?: ClientSession,
  ): Promise<Transition> {
    return this.transitionModel.findByIdAndUpdate(id, { status }, {
      new: true
    }).session(session);
  }

  /**
   * Removes a transition.
   * @param id The ID of the transition.
   * @returns A promise of the deleted Transition object.
   */
  async remove(id: string): Promise<Transition> {
    return this.transitionModel.findByIdAndDelete(id).exec();
  }

  /**
   * Removes multiple transitions.
   * @param ids The IDs of the transitions to be removed.
   * @returns A promise of the deleted Transition object.
   */
  async batchRemove(ids: string[]): Promise<{ deletedCount: number }> {
    return this.transitionModel.deleteMany({ _id: { $in: ids } }).exec();
  }

  /**
   * Sends a text message to clients involved in a transition.
   * @param transitionId The ID of the transition.
   * @param organisationId The ID of the organisation.
   * @param smsPayload The payload for the SMS.
   * @returns A promise of the SMS sending operation.
   */
  async notifyClientsAboutTransition(
    transitionOrId: Transition | string,
    organisationId: string,
    smsPayload?: string,
    session?: ClientSession,
  ) {
    const organisation = await this.organisationsService.findOne(
      organisationId,
      session,
    );

    if (!organisation.allowSendTransitionNotifications) {
      throw new HttpException(
        `Organisation with ID ${organisationId} does not allow sending transition notifications.`,
        HttpStatus.BAD_REQUEST,
      );
    }

    let transition: Transition;

    if (typeof transitionOrId === 'string') {
      transition = await this.findOne(transitionOrId, session);
      if (!transition) {
        throw new HttpException(
          `Transition with ID ${transitionOrId} not found.`,
          HttpStatus.NOT_FOUND,
        );
      }
    } else {
      transition = transitionOrId;
    }

    const primaryAdvisor = await this.advisorsCrudService.findOne({
      _id:
        transition.primaryAdvisor?._id?.toString() ?? transition.primaryAdvisor,
    });

    if (isEmpty(smsPayload)) {
      smsPayload = this.getOrganisationDefaultTransitionSmsTemplate(
        transition.transitionType,
        organisation,
      ).template;
    }

    if (transition.status === TransitionStatusEnum.Draft) {
      this.validateSmsTemplate(smsPayload, transition.transitionType);
    }

    const promises = [transition.primaryContact, transition.secondaryContact]
      .filter((contacts) => !!contacts)
      .map(async (contact, index) => {
        const contactType =
          index === 0 ? ContactTypeEnum.Primary : ContactTypeEnum.Secondary;

        if (!contact.mobile) {
          throw new HttpException(
            `${contactType} contact's mobile number is not available.`,
            HttpStatus.BAD_REQUEST,
          );
        }

        // Prepare data for placeholder replacement
        const templateData: TransitionStatementContext = {
          clientFirstName: contact.firstName,
          clientLastName: contact.lastName,
          advisorFirstName: primaryAdvisor.personalInfo.firstName,
          advisorLastName: primaryAdvisor.personalInfo.lastName,
          organisationName: organisation.name,
          organisationPhone: organisation.phone,
          emailLink: `https://${getFrontendDomain()}/email-register/${
            transition.id
          }?relatesTo=${contactType}`,
          statementLink: `https://${getFrontendDomain()}/statement-upload/${
            transition.id
          }?relatesTo=${contactType}`,
        };

        // Replace placeholders in the template
        const templatePayload = replaceTemplatePlaceholders(
          smsPayload,
          templateData,
        );

        return this.smsService.sendText(
          contact.mobile,
          templatePayload,
          undefined,
          contact as EnrichedContact,
          primaryAdvisor._id.toString(),
        );
      });

    await Promise.all(promises);

    // Change the transition status to 'NonSolicitPending' or 'StatementPending' based on the transition type
    if (transition.status === TransitionStatusEnum.Draft) {
      transition.status =
        transition.transitionType === TransitionTypeEnum.NonProtocol
          ? TransitionStatusEnum.EmailPending
          : TransitionStatusEnum.StatementPending;
    }

    return transition.save({ session });
  }

  private getOrganisationDefaultTransitionSmsTemplate(
    transitionType: TransitionTypeEnum,
    organisation: Organisation,
  ) {
    const templateName =
      transitionType === TransitionTypeEnum.NonProtocol
        ? TextMessageTemplateNameEnum.NonProtocolTransitionRequestEmail
        : TextMessageTemplateNameEnum.TransitionStatement;

    const notificationTemplate: NotificationTemplate =
      organisation.notificationTemplates.find((template) => {
        return (
          template.templateName === templateName &&
          template.templateType === 'sms'
        );
      });

    if (!notificationTemplate || !notificationTemplate.template) {
      throw new HttpException(
        `No suitable SMS template found for Organisation with ID ${organisation._id.toString()}.`,
        HttpStatus.NOT_FOUND,
      );
    }

    return notificationTemplate;
  }

  async updateSmsTemplate(
    organisationId: string,
    updateSmsTemplateDto: UpdateSmsTemplateDto,
  ) {
    // Validate SMS template before saving
    this.validateSmsTemplate(
      updateSmsTemplateDto.template,
      updateSmsTemplateDto.transitionType,
    );

    const templateName =
      updateSmsTemplateDto.transitionType === TransitionTypeEnum.NonProtocol
        ? TextMessageTemplateNameEnum.NonProtocolTransitionRequestEmail
        : TextMessageTemplateNameEnum.TransitionStatement;

    const template =
      await this.organisationNotificationsService.upsertNotificationTemplate(
        organisationId,
        {
          templateName,
          templateType: 'sms',
          template: updateSmsTemplateDto.template,
          default: false,
        },
      );

    return template;
  }

  private validateSmsTemplate(
    smsTemplate: string,
    transitionType: TransitionTypeEnum,
  ): void {
    switch (transitionType) {
      case TransitionTypeEnum.NonProtocol: {
        if (!smsTemplate.includes('[emailLink]')) {
          throw new HttpException(
            `SMS template for Non Protocol transition must include the [emailLink] placeholder.`,
            HttpStatus.BAD_REQUEST,
          );
        }
        break;
      }
      case TransitionTypeEnum.Protocol: {
        if (!smsTemplate.includes('[statementLink]')) {
          throw new HttpException(
            `SMS template for Protocol transition must include the [statementLink] placeholder.`,
            HttpStatus.BAD_REQUEST,
          );
        }
        break;
      }
      default:
        throw new HttpException(
          `Invalid transition type: ${transitionType}`,
          HttpStatus.BAD_REQUEST,
        );
    }
  }

  /**
   * Sends a non-solicit agreement by creating a new DocusSign envelope.
   *
   * @param transitionId - The ID of the transition.
   * @returns A Promise that resolves when the non-solicit agreement is sent successfully.
   */
  async sendNonSolicitAgreement(transition: Transition) {
    // Fetch the transition and its primary advisor
    const { organisationId, primaryAdvisor, primaryContact, secondaryContact } =
      transition;
    const advisor = await this.advisorsCrudService.findOne({
      _id: primaryAdvisor._id.toString(),
    });
    // Fetch the non-solicit agreement from S3
    const organisation = await this.organisationsService.findOne(
      organisationId.toString(),
    );
    const nonSolicitAgreement: Asset = organisation.assets.find(
      (asset: Asset) => asset.assetType === AssetTypeEnum.NonSolicit,
    );

    const params = {
      Bucket: this.configService.get<string>('ORG_DOCS_BUCKET_NAME'),
      Key: nonSolicitAgreement.assetKey,
    };
    const s3 = new AWS.S3();
    const data = await s3.getObject(params).promise();

    // Prepare the DocuSign envelopes for both applicants
    const applicants = [primaryContact, secondaryContact];
    const promises = applicants
      .filter((applicant) => !!applicant)
      .map(async (applicant, index) => {
        // Prepare webhook url
        const contactType =
          index === 0 ? ContactTypeEnum.Primary : ContactTypeEnum.Secondary;

        const file: Express.Multer.File = {
          fieldname: 'files',
          originalname: `NonSolicitAgreement${computeContactFileSuffix(
            contactType,
          )}.pdf`,
          encoding: '7bit',
          mimetype: 'application/pdf',
          buffer: data.Body,
        } as Express.Multer.File;

        const webhookUrl = `https://${
          this.baseUrl
        }/transitions/${transition._id.toString()}/non-solicit-agreement/verify?relatesTo=${contactType}`;

        // Create a new DocusSign envelope for each applicant
        const createEnvelopeDto: CreateEnvelopeDto = {
          advisorId: advisor._id.toString(),
          organisationId: organisationId.toString(),
          applicant: {
            name: `${applicant.firstName} ${applicant.lastName}`,
            email: applicant.email,
            phone: applicant.mobile,
          },
          envelopeId: undefined,
          shouldSend: true,
          interviewData: {
            organisation,
            primaryContact,
            secondaryContact,
            primaryAdvisor: {
              firstName: advisor.personalInfo.firstName,
              lastName: advisor.personalInfo.lastName,
              mobile: advisor.personalInfo.phone,
            },
          } as any,
          files: [file],
          webhookUrl,
          addSchwabCC: false,
        };

        return this.docusignService.createEnvelope(createEnvelopeDto);
      });

    await Promise.all(promises);
  }

  /**
   * Private method to determine the transition status based on the provided DTO.
   * @param createTransitionDto DTO for creating a transition.
   * @returns The determined TransitionStatusEnum.
   */
  private determineTransitionStatus(
    createTransitionDto: TransitionDto,
  ): TransitionStatusEnum {
    return createTransitionDto.transitionType === TransitionTypeEnum.NonProtocol
      ? TransitionStatusEnum.NonSolicitPending
      : TransitionStatusEnum.StatementPending;
  }

  /**
   * Registers a contact email for a transition.
   *
   * @param transitionId - The ID of the transition.
   * @param body - The request body containing the email.
   * @param relativeTo - The relative contact type.
   * @param session - Optional MongoDB session for transactional operations.
   * @returns The updated transition object.
   */
  async registerContactEmail(
    transitionId: string,
    body: RegisterEmailRequestDto,
    { relativeTo }: RegisterEmailQueryDto,
    session?: ClientSession,
  ) {
    const { email } = body;
    const transition = await this.transitionModel.findOne({
      _id: transitionId.toString(),
    });

    if (!transition) {
      throw new HttpException(
        `Transition with ID ${transitionId} not found.`,
        HttpStatus.NOT_FOUND,
      );
    }

    if (
      transition.transitionType === TransitionTypeEnum.NonProtocol &&
      transition.status !== TransitionStatusEnum.EmailPending
    ) {
      throw new HttpException(
        'Transition is not in the correct state to register an email',
        HttpStatus.BAD_REQUEST,
      );
    }

    const contact =
      relativeTo === ContactTypeEnum.Primary
        ? transition.primaryContact
        : transition.secondaryContact;

    contact.email = email;

    try {
      await this.upsertTransitionContactsInCrm(transition, session);
    } catch (error) {
      this.logger.error(
        `Error updating contact in CRM for transition with ID ${transitionId}.`,
        error,
      );
      this.auditService.create({
        message: `Error updating contact in CRM for transition with ID ${transitionId}.
        Error: ${error}`,
        context: AuditSource.BACKEND,
        entityType: AuditEntityEnum.TRANSITION,
        entityId: transition._id,
        level: 'error',
        organisationId: transition.organisationId,
      });
    }

    if (transition.transitionType !== TransitionTypeEnum.NonProtocol) {
      await transition.save({ session });
      return transition;
    }

    if (isEmpty(transition.secondaryContact)) {
      await this.sendNonSolicitAgreement(transition);
      transition.status = TransitionStatusEnum.NonSolicitPending;
    }

    if (
      transition.primaryContact?.email &&
      transition.secondaryContact?.email
    ) {
      try {
        await this.sendNonSolicitAgreement(transition);
        transition.status = TransitionStatusEnum.NonSolicitPending;
      } catch (error) {
        this.logger.error(
          `Error sending statement upload SMS to client for transition with ID ${transitionId}.`,
          error,
        );
        this.auditService.create({
          message: `Error sending statement upload SMS to client for transition with ID ${transitionId}.`,
          context: AuditSource.BACKEND,
          entityType: AuditEntityEnum.TRANSITION,
          entityId: transition._id,
          level: 'error',
          organisationId: transition.organisationId,
        });
      }
    }

    await transition.save({ session });

    return transition;
  }

  /**
   * Webhook that verifies the non-solicitation agreement for a transition.
   *
   * @param orgId - The ID of the organization.
   * @param transitionId - The ID of the transition.
   */
  async verifyNonSolicitAgreement(
    transitionId: string,
    payload: LegacyDocuSignWebhookDto | DocusignJsonSimWebhookDto,
    query: VerifyNonSolicitAgreementQueryDto,
    session?: ClientSession,
  ) {
    if (!payload) {
      throw new HttpException(
        'Docusign webhook payload is required',
        HttpStatus.BAD_REQUEST,
      );
    }
    const { status } = getDataFromEnvelope(payload) || {};

    if (status !== DocusignEnvelopeStatusEnum.COMPLETED) {
      this.logger.info(
        `Non-solicit agreement callback executed for transition with ID ${transitionId} with status ${status}.`,
      );
      return;
    }

    const transition = await this.transitionModel.findOne({
      _id: transitionId,
    });

    if (transition.status !== TransitionStatusEnum.NonSolicitPending) {
      throw new HttpException(
        'Transition is not in the correct state to verify non-solicit agreement',
        HttpStatus.BAD_REQUEST,
      );
    }

    const contact =
      query.relatesTo === ContactTypeEnum.Primary
        ? transition.primaryContact
        : transition.secondaryContact;

    contact.nonSolicitAgreementVerifiedAt = new Date().toString();

    if (isEmpty(transition.secondaryContact)) {
      transition.status = TransitionStatusEnum.StatementPending;
    }

    if (
      !isEmpty(transition.secondaryContact) &&
      transition.primaryContact.nonSolicitAgreementVerifiedAt &&
      transition.secondaryContact.nonSolicitAgreementVerifiedAt
    ) {
      transition.status = TransitionStatusEnum.StatementPending;
    }

    this.logger.info(
      `Non-solicit agreement verified for transition with ID ${transitionId}. Docusign Status: ${status}. Transition Status: ${transition.status}.`,
    );

    if (transition.status === TransitionStatusEnum.StatementPending) {
      try {
        await this.sendStatementUploadSmsToClient(transition, session);
      } catch (error) {
        this.logger.error(
          `Error sending statement upload SMS to client for transition with ID ${transitionId}.`,
          error,
        );
        this.auditService.create({
          message: `Error sending statement upload SMS to client for transition with ID ${transitionId}.`,
          context: AuditSource.BACKEND,
          entityType: AuditEntityEnum.TRANSITION,
          entityId: transition._id,
          level: 'error',
          organisationId: transition.organisationId,
        });
      }
    }

    return transition.save({ session });
  }

  private async sendStatementUploadSmsToClient(
    transition: Transition,
    session?: ClientSession,
  ) {
    const primaryAdvisor = await this.advisorsCrudService.findOne(
      transition.primaryAdvisor._id,
    );

    const organisation = await this.organisationsService.findOne(
      transition.organisationId.toString(),
      session,
    );

    const promises = [transition.primaryContact, transition.secondaryContact]
      .filter((contacts) => !!contacts)
      .map(async (contact, index) => {
        const contactType =
          index === 0 ? ContactTypeEnum.Primary : ContactTypeEnum.Secondary;

        if (!contact.mobile) {
          throw new HttpException(
            `${contactType} contact's mobile number is not available.`,
            HttpStatus.BAD_REQUEST,
          );
        }

        // Prepare data for placeholder replacement
        const templateData: TransitionStatementContext = {
          clientFirstName: contact.firstName,
          clientLastName: contact.lastName,
          advisorFirstName: primaryAdvisor.personalInfo.firstName,
          advisorLastName: primaryAdvisor.personalInfo.lastName,
          organisationName: organisation.name,
          organisationPhone: organisation.phone,
          emailLink: `https://${getFrontendDomain()}/email-register/${
            transition.id
          }?relatesTo=${contactType}`,
          statementLink: `https://${getFrontendDomain()}/statement-upload/${
            transition.id
          }?relatesTo=${contactType}`,
        };

        // Replace placeholders in the template
        const templatePayload = replaceTemplatePlaceholders(
          `Hi [clientName].
          Thank you for singing the non-solicit agreement.
          Please upload your statement so we can start the account creation process:
          [statementLink]`,
          templateData,
        );

        return this.smsService.sendText(
          contact.mobile,
          templatePayload,
          undefined,
          contact as EnrichedContact,
          primaryAdvisor._id.toString(),
        );
      });

    return Promise.all(promises);
  }

  /**
   * Uploads a brokerage statement to a transition.
   *
   * @param transitionId - The ID of the transition.
   * @param file - The file to upload.
   * @param session - Optional MongoDB session for transactional operations.
   * @returns The updated transition object.
   */
  async uploadBrokerageStatement(
    transitionId: string,
    { relatesTo }: BrokerageStatementUploadQueryDto,
    file: Express.Multer.File,
    session?: ClientSession,
  ) {
    if (!relatesTo) {
      throw new HttpException('relatesTo is required', HttpStatus.BAD_REQUEST);
    }
    // Get the transition
    const transition = await this.transitionModel
      .findById({ _id: transitionId })
      .session(session);

    if (transition.status !== TransitionStatusEnum.StatementPending) {
      throw new HttpException(
        'Transition is not in the correct state to upload a statement',
        HttpStatus.BAD_REQUEST,
      );
    }
    const contact =
      relatesTo === ContactTypeEnum.Primary
        ? transition.primaryContact
        : transition.secondaryContact;

    const primaryAdvisor = await this.advisorsCrudService.findOne(
      transition.primaryAdvisor._id,
    );

    const primaryCSA = await this.advisorsCrudService.findOne(
      transition.primaryCSA._id,
    );

    if (!primaryAdvisor || !primaryCSA) {
      throw new HttpException(
        'Primary advisor or primary CSA not found',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    // Unique emails
    const emails = [
      ...new Set([
        primaryAdvisor.personalInfo.email,
        primaryCSA.personalInfo.email,
      ]),
    ];

    try {
      await Promise.all(
        emails.map(async (email) => {
          // Get the organization for the transition
          const organisation = await this.organisationsService.findOne(
            transition.organisationId.toString(),
          );

          return this.mailService.sendEmailWithFileAttachments({
            to: email,
            subject: 'Brokerage Statement Uploaded',
            templateName: EmailTemplateNameEnum.StatementUploaded,
            context: {
              clientFirstName: contact.firstName,
              clientLastName: contact.lastName,
              advisorFirstName: primaryAdvisor.personalInfo.firstName,
              advisorLastName: primaryAdvisor.personalInfo.lastName,
            },
            attachments: [file],
            organisation,
          });
        }),
      );
    } catch (error) {
      this.logger.error(
        `Error sending statement upload email to advisors for transition with ID ${transitionId}.`,
        error,
      );
      this.auditService.create({
        message: `Error sending statement upload email to advisors for transition with ID ${transitionId}. Error: ${error}`,
        context: AuditSource.BACKEND,
        entityType: AuditEntityEnum.TRANSITION,
        entityId: transition._id,
        level: 'error',
        organisationId: transition.organisationId,
      });
      throw error;
    }

    contact.statementUploadedAt = new Date().toString();

    transition.status = TransitionStatusEnum.StatementUploaded;

    // Save the transition
    const updatedTransition = await transition.save({ session });

    return updatedTransition;
  }

  /**
   * Retrieves public information about a transition.
   * @param transitionId - The ID of the transition.
   * @returns A Promise that resolves to the public information of the transition.
   * @throws HttpException if the transition is not found.
   */
  async getPublicTransitionInfo(
    transitionId: string,
  ): Promise<PublicTransitionInfoResponseDto> {
    const select = [
      'primaryContact.email',
      'secondaryContact.email',
      'status',
      'primaryContact.statementUploadedAt',
      'secondaryContact.statementUploadedAt',
      'organisationId',
    ];

    const transition = await this.transitionModel
      .findById(transitionId)
      .select(select)
      .lean()
      .exec();

    if (!transition) {
      throw new HttpException('Transition not found', HttpStatus.NOT_FOUND);
    }

    const organisationLogo = (
      await this.organisationsService.findOne(transition?.organisationId.toString())
    )?.assets?.filter((asset) => asset.assetType === 'logo')[0]?.assetLocation;

    // mask the emails
    const maskedTransition = await dataguardian.maskData(transition) as any;
    return { ...maskedTransition, organisationLogo }
  }

  // Update getTransitionUrl() to handle multiple transitions
  async getTransitionUrl(
    organisationId: string,
    transitionId: string | string[],
  ): Promise<TransitionUrl | TransitionUrl[]> {
    // Handle single transition case
    if (typeof transitionId === 'string') {
      return this.generateTransitionUrl(organisationId, transitionId);
    }

    // Handle multiple transitions case
    const transitions = await this.findByIds(transitionId);
    return Promise.all(
      transitions.map(transition =>
        this.generateTransitionUrl(organisationId, transition._id.toString())
      )
    );
  }

  // Private helper method to generate URLs for a single transition
  private async generateTransitionUrl(
    organisationId: string,
    transitionId: string,
  ): Promise<TransitionUrl> {
    const transition = await this.transitionModel.findOne({
      _id: transitionId,
      organisationId,
    });

    if (!transition) {
      throw new NotFoundException('Transition not found');
    }

    const baseUrl = getFrontendDomain();

    // Determine the correct path based on transition type and status
    let path: string;

    if (transition.transitionType === TransitionTypeEnum.Protocol) {
      // For Protocol transitions, always use statement-upload
      path = 'statement-upload';
    } else {
      // For NonProtocol transitions:
      // - If status is EmailPending, use email-register
      // - If status is StatementPending or later, use statement-upload
      path = transition.status === TransitionStatusEnum.EmailPending
        ? 'email-register'
        : 'statement-upload';
    }

    const response: TransitionUrl = {
      primaryUrl: `https://${baseUrl}/${path}/${transitionId}?relatesTo=Primary`,
    };

    // Add secondary URL only if there's a secondary contact
    if (transition.secondaryContact) {
      response.secondaryUrl = `https://${baseUrl}/${path}/${transitionId}?relatesTo=Secondary`;
    }

    return response;
  }
}

