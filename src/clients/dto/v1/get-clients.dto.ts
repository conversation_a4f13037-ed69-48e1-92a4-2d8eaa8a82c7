import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';
import { Client, ClientContact } from '../../schemas/clients.schema';
import { IsBoolean, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
export class GetClientsQueryDto extends PaginationQueryDto {
  include?: string;
  @ApiProperty()
  @Transform(({ value }) => value === 'true')
  @IsBoolean()
  @IsOptional()
  enrich?: boolean;
}
export interface EnrichedContact extends ClientContact {
  email: string;
  mobile: string;
}
export interface EnrichedContactWithInterviewStatus extends ClientContact {
  completionPercentage: number;
  belongsToHousehold: boolean;
  interviewComplete: boolean;
}

export interface EnrichedClient
  extends Omit<Client, 'primaryContact' | 'secondaryContact'> {
  primaryContact: EnrichedContact;
  secondaryContact?: EnrichedContact;
}

export interface EnrichedClientWithInterviewStatus
  extends Omit<Client, 'primaryContact' | 'secondaryContact'> {
  primaryContact: EnrichedContactWithInterviewStatus;
  secondaryContact?: EnrichedContactWithInterviewStatus;
}
