export enum RedtailNoteTypeEnum {
  'Note' = 1,
}
export interface RedtailNoteAssociation {
  id: number;
  note_id: number;
  noteable_id: number;
  noteable_type: string;
  deleted: boolean;
  created_at: string;
  updated_at: string;
}

export interface RedtailNote {
  id: number;
  category_id: number;
  category: string;
  note_type: number;
  note_type_description: string;
  pinned: boolean;
  draft: boolean;
  added_by: number;
  body: string;
  deleted: boolean;
  created_at: string;
  updated_at: string;
  note_associations: RedtailNoteAssociation[];
  comments: [];
  document_associations: [];
}
