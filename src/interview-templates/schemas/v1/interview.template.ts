import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, SchemaTypes } from 'mongoose';
import { DataFactory, Factory } from 'nestjs-seeder';
import { InterviewPageStatusEnum } from 'src/interview-templates/types/interview-page-status.enum';
import { InterviewTemplateTypeEnum } from 'src/interview-templates/types/interview-template-type.enum';

@Schema()
export class Element extends Document {
  @Factory('text')
  @Prop()
  elementType: string;

  @Factory('element label')
  @Prop()
  elementLabel: string;

  @Factory((faker) => faker.datatype.boolean())
  @Prop()
  required: boolean;
}

export const ElementSchema = SchemaFactory.createForClass(Element);

@Schema({ timestamps: true })
export class InterviewPage extends Document {
  @Factory((_, { page }) => page)
  @Prop()
  name: string;

  @Factory((faker) => faker.number.int({ min: 1, max: 10 }))
  @Prop()
  order: number;

  @Factory(null)
  @Prop({ type: <PERSON><PERSON>an, default: false })
  filled: boolean;

  @Factory({})
  @Prop({ type: Object, default: {} })
  data: any;

  @Factory(InterviewPageStatusEnum.UNANSWERED)
  @Prop({
    type: String,
    enum: InterviewPageStatusEnum,
    default: function (this: InterviewPage) {
      // If the page is filled, then it means that the user has answered the question
      // So we set the status to SYNCED
      return this.filled
        ? InterviewPageStatusEnum.SYNCED
        : InterviewPageStatusEnum.UNANSWERED;
    },
  })
  status: InterviewPageStatusEnum;
}

export const InterviewPageSchema = SchemaFactory.createForClass(InterviewPage);

@Schema({ timestamps: true })
export class InterviewTemplate extends Document {
  @Factory((_, { organisationId }) => organisationId)
  @Prop({
    type: SchemaTypes.ObjectId,
    required: false,
    ref: 'Organisation',
    index: true,
  })
  organisationId: mongoose.Types.ObjectId;

  @Factory((faker) => `Template-${faker.word.sample()}`)
  @Prop({ required: true })
  templateName: string;

  @Factory((_, { pages }) =>
    pages.map(
      (page) =>
        DataFactory.createForClass(InterviewPage).generate(1, { page })[0],
    ),
  )
  @Prop({ type: [InterviewPageSchema] })
  pages: InterviewPage[];

  @Prop({
    type: String,
    enum: InterviewTemplateTypeEnum,
    default: InterviewTemplateTypeEnum.DEFAULT,
  })
  type: InterviewTemplateTypeEnum;

  @Factory(true)
  @Prop({ type: Boolean, default: false })
  default: boolean;
}

export const InterviewTemplateSchema =
  SchemaFactory.createForClass(InterviewTemplate);
