import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ClientSession, FilterQuery, Model } from 'mongoose';
import { Role } from './schemas/role.schema';
import { CreateRoleDto } from './dto/create-role.dto';
import { AddPermissionDto } from './dto/add-permission.dto';
import { DeletePermissionDto } from './dto/delete-permission.dto';
import { Permissions } from './permissions/permissions';

@Injectable()
export class RbacService {
  constructor(@InjectModel(Role.name) private roleModel: Model<Role>) {}

  public async findAllRoles(): Promise<Role[]> {
    return this.roleModel.find().exec();
  }

  public async findOneRole(
    filter: FilterQuery<Role>,
    session?: ClientSession,
  ): Promise<Role> {
    return this.roleModel.findOne(filter).session(session);
  }

  public async createRole(role: CreateRoleDto): Promise<Role> {
    const found = await this.roleModel.findOne({ name: role.name });
    if (found) {
      throw new HttpException('Role already exists', HttpStatus.CONFLICT);
    }

    const permissions = role.permissions.map((permissionId) => {
      const permission = Permissions[permissionId];
      if (!permission) {
        throw new HttpException(
          `Permission ${permissionId} not found`,
          HttpStatus.NOT_FOUND,
        );
      }
      return {
        ...permission,
        name: permissionId,
      };
    });

    const permissionsSet = new Set(permissions);
    return this.roleModel.create({
      name: role.name,
      permissions: Array.from(permissionsSet),
    });
  }

  public async deleteRole(id: string): Promise<Role> {
    return this.roleModel.findByIdAndDelete(id).exec();
  }

  public async addPermission(
    roleId: string,
    addPermissionDto: AddPermissionDto,
  ): Promise<Role> {
    const role = await this.roleModel.findById(roleId);
    if (!role) {
      throw new HttpException(
        `Role with id ${roleId} not found`,
        HttpStatus.NOT_FOUND,
      );
    }

    const permission = Permissions[addPermissionDto.permissionId];
    if (!permission) {
      throw new HttpException(
        `Permission ${addPermissionDto.permissionId} not found`,
        HttpStatus.NOT_FOUND,
      );
    }

    role.permissions.push({
      ...permission,
      name: addPermissionDto.permissionId,
    });
    return role.save();
  }

  public async deletePermission(
    roleId: string,
    deletePermissionDto: DeletePermissionDto,
  ): Promise<Role> {
    const role = await this.roleModel.findById(roleId);

    const permission = role.permissions.find(
      (p) => p.id === deletePermissionDto.permissionId,
    );

    if (!permission) {
      throw new HttpException(
        `Permission ${deletePermissionDto.permissionId} not found in role ${role.name}`,
        HttpStatus.NOT_FOUND,
      );
    }

    role.permissions = role.permissions.filter(
      (p) => p.id !== deletePermissionDto.permissionId,
    );

    return role.save();
  }
}
