import {
  Injectable,
  HttpException,
  HttpStatus,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { ClientsV2Service } from 'src/clients/services/v2/clients-v2.service';
import { InjectModel, InjectConnection } from '@nestjs/mongoose';
import { Model, ClientSession, Connection } from 'mongoose';
import { Interview } from '../../../schemas/v1/interview.schema';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { UploadDocumentDto } from '../../../dto/v1/upload-document.dto';
import { RequiredDocumentDto } from '../../../dto/v1/required-document.dto';
import { Asset } from 'src/shared/schemas/asset.schema';
import { AssetTypeEnum } from 'src/shared/types/general/asset-types.enum';
import { Account } from 'src/clients/schemas/clients.schema';
import { isEmpty } from 'lodash';
import AWS from 'aws-sdk';
import { ConfigService } from '@nestjs/config';
import { AccountFeaturesEnum } from 'src/shared/types/accounts/account-features.enum';
import { AccountClientDocumentsEnum, AccountAdvisoryDocumentsEnum } from 'src/shared/types/accounts/account-documents.enum';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { CacheService } from 'src/shared/services/cache.service';
import { getFileCacheKey, DOWNLOAD_PATH } from 'src/utils/cache/cache.util';
import { createWriteStream, existsSync } from 'fs';
import { mkdirAsync, unlinkAsync } from 'src/utils/file/file.util';
import path from 'path';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { ClientQueueJobType } from 'src/clients/types/client-queue-job.enum';
import { CLIENT_QUEUE } from 'src/shared/constants/client.constant';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { computeAccountFileSuffix } from 'src/utils/accounts/accountFileNameSuffix';

@Injectable()
export class InterviewDocumentsService {
  constructor(
    private readonly configService: ConfigService,
    @InjectModel(Asset.name) private readonly assetModel: Model<Asset>,
    @InjectModel(Interview.name) private readonly interviewModel: Model<Interview>,
    @Inject(forwardRef(() => ClientsV2Service)) private readonly clientsService: ClientsV2Service,
    @Inject(forwardRef(() => DocusignService)) private readonly docusignService: DocusignService,
    @Inject(forwardRef(() => OrganisationsService)) private readonly organisationsService: OrganisationsService,
    @InjectConnection() private readonly connection: Connection,
    @InjectQueue(CLIENT_QUEUE.NAME) private readonly clientQueue: Queue,
    private readonly cacheService: CacheService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  async upload(
    interview: Interview,
    files: Express.Multer.File[],
    dto?: UploadDocumentDto,
    session?: ClientSession,
  ): Promise<string> {
    let transactionToUse = session;
    let shouldManageTransaction = false;

    if (!session) {
      transactionToUse = await this.connection.startSession();
      await transactionToUse.startTransaction();
      shouldManageTransaction = true;
    }

    try {
      const envelopeId = interview.envelopeId;
      const { client } = interview as any;
      const clientId = client._id.toString();

      const envelopeExists = !!envelopeId;
      if (!envelopeExists) {
        throw new HttpException('Cannot upload to non-existent envelope', HttpStatus.BAD_REQUEST);
      }

      const { organisationId, primaryAdvisor } = await this.clientsService.findOne({ _id: clientId }, transactionToUse);
      const entityIds = { organisationId: organisationId.toString(), advisorId: primaryAdvisor.id.toString() };

      await this.docusignService.addClientDocuments({ ...entityIds, envelopeId, files });

      const { documentName, accountId, feature } = dto || {};

      const query: any = { _id: interview._id, 'documents.name': documentName };
      const filter: any = { 'elem.name': documentName };

      if (!isEmpty(accountId)) {
        query['documents.accountId'] = accountId;
        filter['elem.accountId'] = accountId;
      }
      if (!isEmpty(feature)) {
        query['documents.feature'] = feature;
        filter['elem.feature'] = feature;
      }

      if (!!documentName && documentName.length > 0) {
        const { fileUploadsNo } = await this.clientsService.findOne({ _id: clientId }, transactionToUse);
        const now = new Date();
        const interviewUpdatePromise = this.interviewModel.updateOne(
          query,
          { $set: { 'documents.$[elem].updatedAt': now } },
          { arrayFilters: [filter], session: transactionToUse },
        );
        const clientUpdatePromise = this.clientsService.update(clientId, { 
          fileUploadsNo: fileUploadsNo + 1,
          status: ClientStatusEnum.Sent
        }, transactionToUse);
        await Promise.all([interviewUpdatePromise, clientUpdatePromise]);
      }

      this.clientQueue.add(ClientQueueJobType.UPDATE_LAST_CONTACT_ACTIVITY_TIMESTAMP, {
        clientId,
        isPrimary: interview.isPrimary,
      });

      if (shouldManageTransaction) await transactionToUse.commitTransaction();
      return 'Document successfully uploaded.';
    } catch (error) {
      if (shouldManageTransaction) await transactionToUse.abortTransaction();
      throw new HttpException(error.message, error?.status || HttpStatus.INTERNAL_SERVER_ERROR);
    } finally {
      if (shouldManageTransaction) await transactionToUse.endSession();
    }
  }

  selectClientFilesToFetch(accountFeatures: AccountFeaturesEnum[], accountId: string) {
    const fileTypes = new Set<{ documentType: AccountClientDocumentsEnum; accountId: string; feature: AccountFeaturesEnum }>();
    accountFeatures.forEach(feature => {
      switch (feature) {
        case AccountFeaturesEnum.MoneyLink:
          fileTypes.add({ documentType: AccountClientDocumentsEnum.CancelledCheck, accountId, feature });
          break;
        case AccountFeaturesEnum.Acat:
          fileTypes.add({ documentType: AccountClientDocumentsEnum.BrokerageStatement, accountId, feature });
          break;
        case AccountFeaturesEnum.IraDistribution:
          break;
        default:
          throw new HttpException(`Account opening files not found for account feature ${feature}`, HttpStatus.NOT_FOUND);
      }
    });
    return [...fileTypes];
  }

  getAccountAdvisoryFileTypes(accountType: string, accountFeatures: AccountFeaturesEnum[]): AccountAdvisoryDocumentsEnum[] {
    const advisoryFileTypes: Set<AccountAdvisoryDocumentsEnum> = new Set();
    const isIraAccount = [ 'Ira', 'RothIra' ].includes(accountType);
    const isBrokerageAccount = [ 'SingleNameBrokerage', 'JointNameBrokerage' ].includes(accountType);
    if (isIraAccount) advisoryFileTypes.add(AccountAdvisoryDocumentsEnum.SchwabIraAccountApplication);
    if (isBrokerageAccount) advisoryFileTypes.add(AccountAdvisoryDocumentsEnum.SchwabOnePersonalAccountApplication);
    accountFeatures.forEach(feature => {
      switch (feature) {
        case AccountFeaturesEnum.MoneyLink:
          advisoryFileTypes.add(AccountAdvisoryDocumentsEnum.SchwabMoneyLinkElectronicTransferForm);
          break;
        case AccountFeaturesEnum.Acat:
          advisoryFileTypes.add(AccountAdvisoryDocumentsEnum.SchwabTransferAccountForm);
          break;
        case AccountFeaturesEnum.IraDistribution:
          advisoryFileTypes.add(AccountAdvisoryDocumentsEnum.SchwabIraDistributionForm);
          break;
        default:
          throw new HttpException(`Account opening files not found for account feature ${feature}`, HttpStatus.NOT_FOUND);
      }
    });
    return [...advisoryFileTypes];
  }

  async addRequiredDocument(interviewId: string, dto: RequiredDocumentDto): Promise<any> {
    return this.interviewModel.updateOne(
      { _id: interviewId },
      {
        $push: {
          documents: {
            name: dto.document,
            updatedAt: null,
          },
        },
      },
    );
  }

  async removeRequiredDocument(interviewId: string, dto: RequiredDocumentDto): Promise<any> {
    const interview = await this.interviewModel.findById(interviewId);
    if (!interview) {
      throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);
    }
    
    return this.interviewModel.updateOne(
      { _id: interviewId },
      {
        $set: {
          documents: interview.documents.filter(
            (document) => document.name !== dto.document,
          ),
        },
      },
    );
  }

  async getAccountOpeningAdvisoryFiles(
    accounts: Account[],
    organisationId: string | undefined = undefined,
  ): Promise<Express.Multer.File[]> {
    const allRequiredFiles: Express.Multer.File[] = [];

    for (const account of accounts) {
      const { type, features, label } = account;
      const accountAdvisoryFileTypes = this.getAccountAdvisoryFileTypes(
        type,
        features,
      );

      if (!isEmpty(accountAdvisoryFileTypes)) {
        const accountOpeningFiles: Express.Multer.File[] =
          await this.getFilesFromS3(accountAdvisoryFileTypes, organisationId);

        accountOpeningFiles.forEach((accountOpeningFile) => {
          const filename = accountOpeningFile.originalname.replace('.pdf', '');
          const accountSuffix = computeAccountFileSuffix(label);

          accountOpeningFile.originalname = `${filename}${accountSuffix}.pdf`;
        });

        allRequiredFiles.push(...accountOpeningFiles);
      }
    }

    return allRequiredFiles;
  }

  async getFilesFromS3(
    fileNames: AccountAdvisoryDocumentsEnum[],
    organisationId: string | undefined = undefined,
  ): Promise<Express.Multer.File[]> {
    try {
      const s3 = new AWS.S3();

      const promises = fileNames.map<Promise<Express.Multer.File>>(
        async (document) => {
          let cacheKey: string;
          let advisoryAgreementAsset: Asset;
          let assetId: string;

          const isAdvisoryAgreement =
            document === AccountAdvisoryDocumentsEnum.AdvisoryAgreement;

          if (isAdvisoryAgreement && organisationId) {
            const organisation = await this.organisationsService.findOne(
              organisationId,
            );
            advisoryAgreementAsset = organisation.assets.find(
              (asset: Asset) =>
                asset.assetType === AssetTypeEnum.AdvisoryAgreement,
            );
            assetId = advisoryAgreementAsset?.assetId;
            cacheKey = getFileCacheKey(document, organisationId);
          } else {
            const asset = await this.assetModel
              .findOne({
                assetId: document,
                assetType: AssetTypeEnum.Document,
              })
              .exec();
            assetId = asset?.assetId;
            cacheKey = getFileCacheKey(document);
          }

          if (this.cacheService.has(cacheKey)) {
            let file: Express.Multer.File;

            try {
              file = await this.cacheService.get(cacheKey);
              this.logger.info(`File fetched from cache: ${cacheKey}`);
            } catch (error) {
              this.logger.error(
                `Error fetching file from cache: ${cacheKey}`,
                error,
              );
            }

            if (file) {
              return file;
            }
          } else {
            this.logger.info(`File not found in cache: ${cacheKey}`);
          }

          const params = {
            Bucket: this.configService.get<string>('ORG_DOCS_BUCKET_NAME'),
            Key: isAdvisoryAgreement
              ? advisoryAgreementAsset.assetKey
              : `global/${assetId}`,
          };

          const directoryPath = path.join(__dirname, DOWNLOAD_PATH);
          const filePath = path.join(directoryPath, `${cacheKey}.pdf`);

          if (!existsSync(directoryPath)) {
            await mkdirAsync(directoryPath, { recursive: true });
          }

          return new Promise((resolve, reject) => {
            const fileWriteStream = createWriteStream(filePath);

            fileWriteStream.on('finish', () => {
              const file: Express.Multer.File = {
                fieldname: 'files',
                originalname: `${document}.pdf`,
                encoding: '7bit',
                mimetype: 'application/pdf',
                path: filePath,
              } as Express.Multer.File;
              this.logger.info(`File downloaded: ${filePath} setting cache`);
              this.cacheService.set(cacheKey, file);
              resolve(file);
            });

            fileWriteStream.on('error', async (writeStreamError) => {
              this.logger.error(
                `Error writing file to disk: ${filePath}`,
                writeStreamError,
              );
              if (existsSync(filePath)) {
                try {
                  await unlinkAsync(filePath);
                } catch (cleanupError) {
                  this.logger.error(
                    `Error cleaning up file: ${filePath}`,
                    cleanupError,
                  );
                }
              }
              reject(writeStreamError);
            });

            s3.getObject(params)
              .createReadStream()
              .on('error', (readStreamError) => reject(readStreamError))
              .pipe(fileWriteStream);
          });
        },
      );
      return Promise.all(promises);
    } catch (error) {
      throw new HttpException(
        `Error fetching files from S3: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}