import { Logger } from '@nestjs/common';
import {
  CreateActivityRequest,
  CreateActivityRequestDto,
} from 'src/integrations/crm/redtail/dto/requests/activities/create-activity-request.dto';
import { UpdateActivityRequestDto } from 'src/integrations/crm/redtail/dto/requests/activities/update-activity-request.dto';
import {
  CreateActivityResponse,
  CreateActivityResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/activities/create-activity-response.dto';
import {
  GetActivitiesResponse,
  GetActivitiesResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/activities/get-activities-response.dto';
import { GetActivityCodeResponse } from 'src/integrations/crm/redtail/dto/responses/activities/get-activity-code-response.dto';
import { GetActivityCodesResponse } from 'src/integrations/crm/redtail/dto/responses/activities/get-activity-codes-response.dto';
import {
  GetActivityResponse,
  GetActivityResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/activities/get-activity-response.dto';
import {
  UpdateActivityResponse,
  UpdateActivityResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/activities/update-activity-response.dto';
import { OperationType } from 'src/super-http/enums';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { camelize } from 'src/utils';
import { snakeify } from 'src/utils/snakeify';

export class RedtailTaskActivityService {
  private readonly baseUrl: string;
  private logger: Logger;

  constructor(
    private httpService: SuperHttpService,
    private authHeader: object,
    private tenantId: string
  ) {
    this.baseUrl = process.env.REDTAIL_BASE_URL;
    this.logger = new Logger('RedtailTaskActivityService');
  }

  /**
   * Creates a new activity in Redtail CRM.
   */
  public async createActivity(
    activityData: CreateActivityRequest,
  ): Promise<CreateActivityResponse> {
    const url = `${this.baseUrl}/activities`;
    const response = await this.httpService.post<CreateActivityResponseDto>(
      url,
      this.tenantId,
      snakeify(activityData),
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.CREATE, entityType: 'activity' },
      { headers: {... this.authHeader} }
    );
    return camelize(response);
  }

  /**
   * Updates an existing activity in Redtail CRM.
   */
  public async updateActivity(
    activityId: number,
    activityData: UpdateActivityRequestDto,
  ): Promise<UpdateActivityResponse> {
    const url = `${this.baseUrl}/activities/${activityId}`;
    const response = await this.httpService.put<UpdateActivityResponseDto>(
      url,
      this.tenantId,
      snakeify(activityData),
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.UPDATE,
        entityType: 'activity',
        entityId: activityId.toString(),
        wideCacheInvalidation: true
      },
      { headers: {... this.authHeader} }
    );
    return camelize(response);
  }

  /**
   * Lists activities in Redtail CRM within a specified date range.
   */
  public async listActivities(
    startDate: string,
    endDate: string,
  ): Promise<GetActivitiesResponse> {
    const url = `${this.baseUrl}/activities?start_date=${startDate}&end_date=${endDate}`;
    const response = await this.httpService.get<GetActivitiesResponseDto>(
      url,
      this.tenantId,
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.LIST,
        entityType: 'activity',
        cacheKeysObject: { startDate, endDate }
      },
      { headers: {... this.authHeader} }
    );
    return camelize(response);
  }

  /**
   * Retrieves a specific activity from Redtail CRM.
   */
  public async getActivity(activityId: number): Promise<GetActivityResponse> {
    const url = `${this.baseUrl}/activities/${activityId}`;
    const response = await this.httpService.get<GetActivityResponseDto>(
      url,
      this.tenantId,
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.GET,
        entityType: 'activity',
        entityId: activityId.toString(),
        cacheKeysObject: { activityId }
      },
      { headers: {... this.authHeader} }
    );
    return camelize(response);
  }

  /**
   * Retrieves a list of activity codes from the Redtail CRM API.
   */
  async getActivityCodes(page: number = 1): Promise<GetActivityCodesResponse> {
    const url = `${this.baseUrl}/lists/activity_codes?page=${page}`;
    return this.httpService.get(
      url,
      this.tenantId,
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.LIST,
        entityType: 'activity-code',
        cacheKeysObject: { page }
      },
      { headers: {... this.authHeader} }
    );
  }

  /**
   * Retrieves an activity code by its ID.
   */
  async getActivityCodeById(codeId: number): Promise<GetActivityCodeResponse> {
    const url = `${this.baseUrl}/lists/activity_codes/${codeId}`;
    return this.httpService.get(
      url,
      this.tenantId,
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.GET,
        entityType: 'activity-code',
        entityId: codeId.toString(),
        cacheKeysObject: { codeId }
      },
      { headers: {... this.authHeader} }
    );
  }
}
