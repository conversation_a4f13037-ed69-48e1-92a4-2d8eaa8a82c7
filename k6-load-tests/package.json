{"name": "k6-load-tests", "version": "1.0.0", "description": "Load testing suite for OnBord API", "type": "module", "scripts": {"test:client-creation-basic": "dotenv -- k6 run tests/clients.js -e SCENARIO=basic", "test:client-creation-notification": "dotenv -- k6 run tests/clients.js -e SCENARIO=withNotification", "test:client-creation-failure": "dotenv -- k6 run tests/clients.js -e SCENARIO=failure", "test:client-all": "for scenario in basic withNotification failure; do dotenv -- k6 run tests/clients.js -e SCENARIO=$scenario; done", "test:interview-ira": "dotenv -- k6 run tests/interviews.js -e SCENARIO=iraRequirements", "test:interview-completion": "dotenv -- k6 run tests/interviews.js -e SCENARIO=interviewCompletion", "test:interview-all": "for scenario in iraRequirements interviewCompletion; do dotenv -- k6 run tests/interviews.js -e SCENARIO=$scenario; done"}, "keywords": ["k6", "load-testing", "performance", "<PERSON><PERSON><PERSON>"], "author": "<PERSON>", "license": "ISC", "devDependencies": {"dotenv": "^16.4.1", "dotenv-cli": "^8.0.0"}}