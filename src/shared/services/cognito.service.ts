import { HttpException, Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import {
  AdminResetUserPasswordRequest,
  AdminUserGlobalSignOutRequest,
  ConfirmForgotPasswordRequest,
  InitiateAuthRequest,
  RespondToAuthChallengeRequest,
  ChangePasswordRequest,
  AdminDisableUserRequest,
  AdminEnableUserRequest,
} from 'aws-sdk/clients/cognitoidentityserviceprovider';
import { InviteUserDto } from 'src/auth/dto/invite.dto';
import { UpdateUserDto } from 'src/auth/dto/update.dto';
import { XOR } from '../types/monads';
import { isEmpty } from 'lodash';

@Injectable()
export class CognitoService {
  private readonly cognito: AWS.CognitoIdentityServiceProvider;

  constructor() {
    this.cognito = new AWS.CognitoIdentityServiceProvider({
      region: process.env.AWS_REGION,
    });
  }

  public initiateAuth(params: InitiateAuthRequest) {
    return this.cognito.initiateAuth(params).promise();
  }

  public respondToAuthChallenge(params: RespondToAuthChallengeRequest) {
    return this.cognito.respondToAuthChallenge(params).promise();
  }

  public adminCreateUser(inviteUserDto: InviteUserDto) {
    const params = {
      UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
      Username: inviteUserDto.email,
      UserAttributes: this.getCognitoUserAttributes(inviteUserDto),
      DesiredDeliveryMediums: ['EMAIL', 'SMS'],
    };

    return this.cognito.adminCreateUser(params).promise();
  }

  public adminResendInvite(email: string) {
    const params = {
      UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
      MessageAction: 'RESEND',
      DesiredDeliveryMediums: ['EMAIL'],
      Username: email,
    };

    return this.cognito.adminCreateUser(params).promise();
  }

  public adminUpdateUser(dto: UpdateUserDto) {
    const userAttributes = this.getCognitoUserAttributes(dto);
    const params = {
      UserAttributes: userAttributes,
      UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
      Username: dto.email,
    };

    return this.cognito.adminUpdateUserAttributes(params).promise();
  }

  public async adminDisableUser(username: string) {
    const params: AdminDisableUserRequest = {
      UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
      Username: username,
    };
    return this.cognito.adminDisableUser(params).promise();
  }

  public adminEnableUser(username: string) {
    const params: AdminEnableUserRequest = {
      UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
      Username: username,
    };
    return this.cognito.adminEnableUser(params).promise();
  }

  public adminResetUserPassword(params: AdminResetUserPasswordRequest) {
    return this.cognito.adminResetUserPassword(params).promise();
  }

  public confirmForgotPassword(params: ConfirmForgotPasswordRequest) {
    return this.cognito.confirmForgotPassword(params).promise();
  }

  public adminUserGlobalSignOut(params: AdminUserGlobalSignOutRequest) {
    return this.cognito.adminUserGlobalSignOut(params).promise();
  }

  public adminDeleteUser(username: string) {
    const params = {
      UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
      Username: username,
    };
    return this.cognito.adminDeleteUser(params).promise();
  }

  public async getCognitoUser(username: string) {
    try {
      const user = await this.cognito
        .adminGetUser({
          UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
          Username: username,
        })
        .promise();

      return user;
    } catch (error) {
      return undefined;
    }
  }

  private getCognitoUserAttributes = (dto: InviteUserDto | UpdateUserDto) => {
    const { firstName, lastName } = dto || {};

    const obj = firstName
      ? {
          ...dto,
          name: `${firstName} ${lastName}`,
          // We consider the email to be verified by default
          emailVerified: 'true',
        }
      : dto;

    const attrs = Object.keys(obj)
      .map((key) => {
        return (
          {
            email: {
              Name: 'email',
              Value: obj[key],
            },
            phoneNumber: {
              Name: 'phone_number',
              Value: obj[key],
            },
            name: {
              Name: 'name',
              Value: obj[key],
            },
            emailVerified: {
              Name: 'email_verified',
              Value: obj[key],
            },
          }[key] || undefined
        );
      })
      .filter((attr) => attr !== undefined);

    return attrs;
  };

  public async upsertUser(
    params: XOR<InviteUserDto, UpdateUserDto>,
    username: string = '',
  ) {
    if (username !== '') {
      const existingUser = await this.getCognitoUser(params.email);

      if (existingUser) {
        throw new HttpException('User already exists', 400);
      }

      if (isEmpty(params.phoneNumber)) {
        throw new HttpException(
          'Phone number is required. Please assign a phone number first',
          400,
        );
      }
      await this.adminDeleteUser(username);
      return this.adminCreateUser(params as InviteUserDto);
    }

    return this.adminUpdateUser(params);
  }

  public async changePassword(params: ChangePasswordRequest) {
    return this.cognito.changePassword(params).promise();
  }
}
