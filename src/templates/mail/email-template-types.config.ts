import { EmailTemplateNameEnum } from './types';

export interface EmailTemplateType {
  emailType: EmailTemplateNameEnum;
  label: string;
  description: string;
  availablePlaceholders: string[];
}

export const emailTemplateTypes: EmailTemplateType[] = [
  {
    emailType: EmailTemplateNameEnum.ClientWelcome,
    label: "Welcome Email",
    description: "Sent to clients when they are first onboarded to welcome them to the platform and provide initial instructions.",
    availablePlaceholders: [
      'firstName',
      'csaFirstName',
      'csaLastName',
      'csaEmail',
      'interviewUrl',
      'organizationLogo',
    ],
  },
  {
    emailType: EmailTemplateNameEnum.FailedTextMessage,
    label: "Failed Text Message",
    description: "Notification sent when text messages to a client have failed to deliver, providing alternative contact methods.",
    availablePlaceholders: [
      'clientMobilePhone',
      'onbordMobilePhone',
      'failedTextBody',
    ],
  },
  // {
  //   emailType: EmailTemplateNameEnum.NonCitizen,
  //   label: "Non-Citizen Notification",
  //   description: "Sent to advisors when a client is a non-US citizen.",
  //   availablePlaceholders: [
  //     'clientFirstName',
  //     'clientLastName',
  //   ],
  // },
  // {
  //   emailType: EmailTemplateNameEnum.ReadyToSign,
  //   label: "Ready to Sign",
  //   description: "Notification sent when documents are ready for the client to review and sign, including a link to the signing portal.",
  //   availablePlaceholders: [
  //     'csaFirstName',
  //     'csaLastName',
  //     'clientFirstName',
  //     'clientLastName',
  //     'docusignAccounts',
  //     'offlineAccounts',
  //   ],
  // },
  // {
  //   emailType: EmailTemplateNameEnum.StatementUploaded,
  //   label: "Statement Uploaded",
  //   description: "Notification sent when a new brokerage statement has been uploaded to the client's account for their review.",
  //   availablePlaceholders: [
  //     'advisorFirstName',
  //     'advisorLastName',
  //     'clientFirstName',
  //     'clientLastName',
  //   ],
  // },
]; 