import sharp from 'sharp';
import { DocusignDocumentFileExtensionEnum } from 'src/integrations/docusign/docusign.types';
import { Readable } from 'stream';

function handleStreamEvents(
  stream: Readable,
  chunks: Buffer[],
  resolve: (value: string) => void,
  reject: (reason?: any) => void,
) {
  const logPrefix = '[Stream Event Handler]';

  stream.on('data', (chunk) => {
    console.log(`${logPrefix} Data chunk received, size: ${chunk.length}`);
    chunks.push(chunk);
  });

  stream.on('end', () => {
    console.log(`${logPrefix} Stream ended`);
    resolve(Buffer.concat(chunks).toString('base64'));
  });

  stream.on('error', (error) => {
    console.error(`${logPrefix} Stream error: ${error.message}`);
    reject(error);
  });
}

export async function compressFile(
  stream: Readable,
  extension: string,
): Promise<string> {
  return new Promise((resolve, reject) => {
    if (!extension) reject(new Error('No extension provided'));
    if (!stream) reject(new Error('No stream provided'));

    const chunks: Buffer[] = [];

    if (extension === DocusignDocumentFileExtensionEnum.PDF) {
      handleStreamEvents(stream, chunks, resolve, reject);
      return;
    }

    let processedStream;
    switch (extension) {
      case DocusignDocumentFileExtensionEnum.JPG:
      case DocusignDocumentFileExtensionEnum.JPEG:
        processedStream = sharp().jpeg({ quality: 80 });
        break;
      case DocusignDocumentFileExtensionEnum.PNG:
        processedStream = sharp().png({ quality: 80 });
        break;
      default:
        reject(new Error('Unsupported file extension'));
        return;
    }

    stream.pipe(processedStream);
    handleStreamEvents(processedStream, chunks, resolve, reject);
  });
}
