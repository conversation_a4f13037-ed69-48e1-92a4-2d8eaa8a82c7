import { snakeCase } from 'lodash';
import { SnakeCase } from 'src/shared/types/monads';

function snakeCaseObject<T>(obj: T, parentKey?: string): SnakeCase<T> {
  const result: any = {};
  if (Array.isArray(obj)) {
    return obj.map((item) => snakeCaseObject(item)) as unknown as SnakeCase<T>;
  }
  for (const key in obj) {
    const snakeKey = snakeCase(key);
    result[snakeKey] =
      typeof obj[key] === 'object' && obj[key] !== null
        ? snakeCaseObject(obj[key], snakeKey)
        : obj[key];
  }
  return result;
}

export function snakeify<T>(obj: T): SnakeCase<T> {
  return snakeCaseObject(obj) as SnakeCase<T>;
}
