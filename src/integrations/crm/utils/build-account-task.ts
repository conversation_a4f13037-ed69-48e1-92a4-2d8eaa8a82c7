import { ContactDto } from 'src/clients/dto/v1/create-client.dto';
import { AccountFullOwnershipEnum } from 'src/integrations/crm/types/accounts/account-ownership.enum';
import { DBAccountType } from 'src/integrations/crm/types/accounts/db-account-type.enum';
import { AccountDto } from 'src/shared/types/accounts/account.dto';

export function buildAccountNumber(
  nameOnAccount: string,
  accountType: DBAccountType,
  accountLabel: string,
  ownership: string,
) {
  let accountNumber = nameOnAccount;

  if (accountType === DBAccountType.joint) {
    accountNumber += ownership
      ? ` ${AccountFullOwnershipEnum[ownership]} Account`
      : ' Joint Account';
  } else {
    accountNumber += ` ${accountType}`;
  }

  if (accountLabel.trim()) {
    accountNumber += ` (${accountLabel})`;
  }

  return accountNumber;
}

export function buildAccountCreationTaskDetails(
  account: AccountDto,
  contact: ContactDto,
) {
  const nameOnAccount = `${contact.firstName} ${contact.lastName}`;
  const accountType = DBAccountType[account.type];
  const taskSubject = `${accountType}`.toLowerCase().includes('offline')
    ? `Create ${accountType} for ${nameOnAccount}`
    : `OPEN ${buildAccountNumber(
        nameOnAccount,
        accountType,
        account.label,
        account.ownership,
      )}`;
  const taskDescription = `Account Features: ${
    account.features ? account.features.join(', ') : 'None'
  }`;
  return {
    subject: taskSubject,
    description: taskDescription,
  };
}
