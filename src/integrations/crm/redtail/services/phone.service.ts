import { HttpService } from '@nestjs/axios';
import { Logger } from '@nestjs/common';
import { CreatePhoneResponseDto } from 'src/integrations/crm/redtail/dto/responses/phone/create-phone-response.dto';
import { GetMobileNumbersResponseDto } from 'src/integrations/crm/redtail/dto/responses/phone/get-mobile-response.dto';
import { UpdatePhoneResponseDto } from 'src/integrations/crm/redtail/dto/responses/update-phone-response.dto';
import {
  RedtailCRMEndpointsEnum,
  RedtailCountryCodeEnum,
  RedtailPhoneTypeEnum,
} from 'src/integrations/crm/redtail/types/enums';
import { RedtailPhone } from 'src/integrations/crm/redtail/types/phone.type';
import { HttpMethodsEnum } from 'src/shared/types/general/http-methods.enum';
import { Camelize } from 'src/shared/types/monads';
import { OperationType } from 'src/super-http/enums';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { camelize } from 'src/utils';

import { makeRequest } from 'src/utils/makeRequest';
import { snakeify } from 'src/utils/snakeify';

export class RedtailPhoneNumberService {
  private readonly baseUrl: string;
  private readonly apiKey: string;
  private logger: Logger;

  constructor(private httpService: SuperHttpService, private authHeader: object, private tenantId: string) {
    this.baseUrl = process.env.REDTAIL_BASE_URL;
    this.apiKey = process.env.REDTAIL_API_KEY;
    this.logger = new Logger(RedtailPhoneNumberService.name);
  }

  public async upsertPhone(
    crmClientId: string,
    phoneNumber: string,
    phoneType = RedtailPhoneTypeEnum.MOBILE,
    isPrimary = true,
    isPreferred = true,
  ): Promise<Camelize<RedtailPhone>> {
    const { phones: phoneNumbers } = await this.getContactPhoneNumbers(
      crmClientId,
    );
    const existingPhoneNumber = phoneNumbers.find(
      (phone) => phone.phoneType === phoneType,
    );

    if (!existingPhoneNumber) {
      return this.createPhoneNumber(
        crmClientId,
        phoneNumber,
        phoneType,
        isPrimary,
        isPreferred,
      );
    }

    return this.updatePhoneNumber(
      crmClientId,
      existingPhoneNumber.id.toString(),
      phoneNumber,
    );
  }

  public async createPhoneNumber(
    crmClientId: string,
    phoneNumber: string,
    phoneType = RedtailPhoneTypeEnum.MOBILE,
    isPrimary = true,
    isPreferred = true,
  ): Promise<Camelize<RedtailPhone>> {
    const phoneRequestBody = {
      country_code: RedtailCountryCodeEnum.US,
      number: phoneNumber,
      phone_type: phoneType,
      is_primary: isPrimary,
      is_preferred: isPreferred,
    };

    const response = await this.httpService.post<CreatePhoneResponseDto>(
      `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/phones`,
      this.tenantId,
      snakeify(phoneRequestBody),
      { 
        rateLimiter: 'redtail', 
        integrationName: 'redtail', 
        operation: OperationType.CREATE, 
        entityType: 'phone',
        entityId: crmClientId,
        wideCacheInvalidation: true
      },
      { headers: { ...this.authHeader } }
    );

    return camelize(response?.phone);
  }

  public async updatePhoneNumber(
    crmClientId: string,
    phoneId: string,
    phoneNumber: string,
  ): Promise<Camelize<RedtailPhone>> {
    const phoneRequestBody = {
      number: phoneNumber,
    };

    const response = await this.httpService.put<UpdatePhoneResponseDto>(
      `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/phones/${phoneId}`,
      this.tenantId,
      snakeify(phoneRequestBody),
      { 
        rateLimiter: 'redtail', 
        integrationName: 'redtail', 
        operation: OperationType.UPDATE, 
        entityType: 'phone',
        entityId: `${crmClientId}_${phoneId}`,
        wideCacheInvalidation: true
      },
      { headers: { ...this.authHeader } }
    );
    return camelize(response.phone);
  }

  public async getContactPhoneNumbers(
    crmClientId: string,
  ): Promise<Camelize<GetMobileNumbersResponseDto>> {
    const response = await this.httpService.get<GetMobileNumbersResponseDto>(
      `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/phones`,
      this.tenantId,
      { 
        rateLimiter: 'redtail', 
        integrationName: 'redtail', 
        operation: OperationType.LIST, 
        entityType: 'phone',
        entityId: crmClientId,
        cacheKeysObject: { crmClientId }
      },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }
}
