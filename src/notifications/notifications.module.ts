import { Module, forwardRef } from '@nestjs/common';
import { SmsModule } from './sms/sms.module';
import { MailService } from './mail/mail.service';
import { MailModule } from './mail/mail.module';
import { SmsService } from './sms/sms.service';
import { NotificationsService } from './notifications.service';
import { MutexModule } from 'src/mutex/mutex.module';
import { ScheduleModule } from '@nestjs/schedule';
import { ClientsModule } from 'src/clients/clients.module';
import { OrganisationsModule } from 'src/organisations/organisations.module';
import { InterviewsModule } from 'src/interviews/interviews.module';
import { AdvisorsModule } from 'src/advisors/advisors.module';

@Module({
  imports: [
    forwardRef(() => SmsModule),
    forwardRef(() => MailModule),
    MutexModule,
    forwardRef(() => ClientsModule),
    forwardRef(() => InterviewsModule),
    forwardRef(() => OrganisationsModule),
    forwardRef(() => AdvisorsModule),
    ScheduleModule.forRoot(),
  ],
  providers: [NotificationsService],
  exports: [NotificationsService],
})
export class NotificationsModule {}
