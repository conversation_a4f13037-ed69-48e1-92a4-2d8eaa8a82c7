# Interview V2 Architecture Guide

## Table of Contents
1. [Overview](#overview)
2. [Key Concepts](#key-concepts)
3. [Schema Architecture](#schema-architecture)
4. [Branching and Navigation](#branching-and-navigation)
5. [Improvements Over V1](#improvements-over-v1)
6. [Current Limitations](#current-limitations)
7. [Usage Examples](#usage-examples)

## Overview

Interview V2 is a complete redesign of the interview system that addresses the fundamental limitations of V1. The new architecture introduces:

- **Streaming architecture**: Direct CRM integration without local answer storage
- **Dynamic branching**: Conditional page flows based on user responses
- **Template versioning**: Immutable published templates with draft/publish lifecycle
- **Account-aware navigation**: Different flows for different account types
- **Audit trail**: Comprehensive tracking without storing PII

## Key Concepts

### 1. Streaming vs Storage
**V1 Problem**: Answers were stored locally in MongoDB, creating data synchronization issues and potential PII concerns.

**V2 Solution**: Answers stream directly to the CRM system. The interview system only tracks navigation state and completion status.

### 2. Branching
**Definition**: Branching allows interviews to take different paths based on user responses or context. For example:
- Skip employment questions for retirees
- Show beneficiary pages only for IRA accounts
- Route to different document requirements based on citizenship status

**Implementation**: Uses JEXL (JavaScript Expression Language) for condition evaluation, allowing complex logic without code changes.

### 3. Template Versioning
Templates now have a proper lifecycle:
- **Draft**: Editable, can add/remove/modify pages
- **Published**: Immutable, used by active interviews
- **Archived**: Retired templates kept for historical reference

## Schema Architecture

### Interview Template V2 Schema
```typescript
// Location: src/interview-templates/schemas/v2/interview.template.ts
{
  templateName: string,          // Unique within organization
  templateType: 'standard' | 'transition' | 'custom',
  status: 'draft' | 'published' | 'archived',
  version: number,               // Auto-incremented on publish
  
  pages: InterviewPageDefV2[],   // Array of page definitions
  startPageId: string,           // UUID of the first page
  
  organisationId: ObjectId,      // Multi-tenant isolation
  createdBy: string,
  lastModifiedBy: string,
  publishedAt?: Date,
  publishedBy?: string,
  
  metadata: {
    description?: string,
    tags?: string[],
    estimatedDuration?: number,  // minutes
  }
}
```

### Interview Page Definition V2 Schema
```typescript
// Location: src/interview-templates/schemas/v2/interview-page-def.schema.ts
{
  pageId: string,                // UUID for stable references
  pageName: string,              // Unique within template
  pageType: PageTypeEnum,        // Explicit categorization
  
  displayName: string,           // User-facing title
  description?: string,
  helpText?: string,
  
  isActive: boolean,             // Can be disabled
  isRequired: boolean,           // Cannot be skipped
  defaultOrder: number,          // Fallback ordering
  
  questions: any[],              // Page-specific questions
  validation: {                  // Page-level validation rules
    rules: any[],
    customMessages?: Record<string, string>
  },
  
  flow: {                        // Navigation configuration
    next?: NavigationRule[],     // Forward navigation rules
    previous?: NavigationRule[], // Back navigation rules
    isTerminal?: boolean,        // End of interview marker
  }
}
```

### Navigation Rule Schema
```typescript
{
  condition?: string,            // JEXL expression (optional)
  targetPageId: string,          // Where to navigate
  priority: number,              // Rule evaluation order
}
```

### Interview V2 Instance Schema
```typescript
// Location: src/interviews/schemas/v2/interview.schema.ts
{
  client: ObjectId,              // Reference to client
  template: ObjectId,            // Reference to template version
  contactType: 'primary' | 'secondary',
  
  status: 'pending' | 'in_progress' | 'completed' | 'abandoned',
  branch: string,                // Named path through interview
  
  // Navigation state (no answers stored!)
  currentPageId?: string,
  visitedPages: string[],        // Page IDs in visit order
  
  // Account context
  accountInstanceCount: number,  // Number of associated accounts
  
  // Metadata
  organisationId: ObjectId,      // Redundant for query performance
  advisor: ObjectId,             // Assigned advisor
  apiVersion: 'v2',              // API version marker
  
  // Timestamps
  startedAt?: Date,
  completedAt?: Date,
  lastActivityAt?: Date,
}
```

### Interview Page Instance V2 Schema
```typescript
// Location: src/interviews/schemas/v2/interview-page-instance.schema.ts
{
  interviewId: ObjectId,         // Parent interview
  pageId: string,                // UUID from template
  pageName: string,              // Cached for convenience
  
  visitOrder: number,            // When was this visited (-1 if not)
  status: 'pending' | 'in_progress' | 'completed' | 'skipped',
  syncStatus: 'pending' | 'syncing' | 'synced' | 'failed',
  
  accountContext?: {             // For account-specific pages
    accountId: string,
    accountType: string,         // 'beneficiary', 'spouse', etc.
    accountSubtype: string,      // 'primary', 'contingent', etc.
    accountName: string,         // Display name
  },
  
  // Cached flow decision
  evaluatedFlow?: {
    appliedRule?: string,        // Which rule was used
    targetPageId?: string,       // Where it went
    isTerminal?: boolean,
  }
}
```

### Interview Account Instance V2 Schema
```typescript
// Location: src/interviews/schemas/v2/interview-account-instance.schema.ts
{
  interviewId: ObjectId,         // Parent interview
  accountId: string,             // Client's account ID
  accountType: string,           // IRA, RothIRA, etc.
  accountLabel: string,          // Display name
  
  requiresBeneficiaries: boolean,
  beneficiaryPages?: {
    primary?: string,            // Page instance ID
    contingent?: string,         // Page instance ID
  }
}
```

## Branching and Navigation

### How Branching Works

1. **Page Definition**: Each page defines navigation rules in its `flow` property
2. **Rule Evaluation**: When a page is submitted, rules are evaluated in priority order
3. **Condition Matching**: First rule whose condition evaluates to `true` determines next page
4. **Default Behavior**: If no conditions match, follow `defaultOrder` or first unconditional rule

### JEXL Expression Examples

```javascript
// Skip employment page for retirees
{
  condition: "answers.employmentStatus === 'retired'",
  targetPageId: "page-uuid-for-income",
  priority: 1
}

// Different document requirements for non-citizens
{
  condition: "answers.citizenship !== 'us_citizen'",
  targetPageId: "page-uuid-for-visa-docs",
  priority: 1
}

// Complex conditions with multiple criteria
{
  condition: "answers.age >= 65 && answers.income < 50000",
  targetPageId: "page-uuid-for-benefits",
  priority: 1
}
```

### Navigation Flow Types

1. **Linear Flow**: Pages follow `defaultOrder` with no conditions
2. **Conditional Skip**: Skip pages based on previous answers
3. **Branch and Merge**: Different paths that converge later
4. **Early Termination**: End interview early based on disqualifying answers

### Terminal Pages

Pages marked with `isTerminal: true` end the interview. Common uses:
- Thank you page after completion
- Disqualification page (e.g., non-citizen when not supported)
- Error or support contact page

## Improvements Over V1

### 1. Transaction Safety
**V1 Issue**: Complex nested updates across multiple collections often failed partially
**V2 Solution**: 
- Atomic operations per page submission
- Separate collections prevent document bloat
- Streaming eliminates most transaction needs

### 2. Performance
**V1 Issue**: Large embedded documents (answers, pages, accounts) caused MongoDB document size limits
**V2 Solution**:
- Answers stream to CRM (not stored)
- Account instances in separate collection
- Page instances allow efficient querying

### 3. Flexibility
**V1 Issue**: Hard-coded page flows, no conditional logic
**V2 Solution**:
- JEXL-based branching rules
- Dynamic page ordering
- Account-aware navigation

### 4. Auditability
**V1 Issue**: Limited tracking, PII stored in audit logs
**V2 Solution**:
- Comprehensive audit schema
- No PII storage
- Event-based tracking (page visits, completions, navigation)

### 5. Template Management
**V1 Issue**: No versioning, changes affected active interviews
**V2 Solution**:
- Immutable published templates
- Draft/publish lifecycle
- Version history maintained

### 6. Multi-tenancy
**V1 Issue**: Weak organization isolation
**V2 Solution**:
- Organization ID on all documents
- Indexed for performance
- API version tracking

## Current Limitations

### 1. CRM Dependency
- Requires CRM to be available for every page submission
- No offline capability
- CRM API rate limits can affect user experience

### 2. Complex Navigation Rules
- JEXL expressions can become complex and hard to debug
- No visual flow editor (yet)
- Testing all paths requires careful planning

### 3. Migration Complexity
- V1 to V2 migration requires CRM data reconciliation
- Cannot run both versions for same client
- Historical data format differences

### 4. Real-time Collaboration
- No support for multiple users editing same interview
- No real-time sync between advisor and client views
- Lock mechanisms not implemented

## Usage Examples

### Creating a Template with Branching

```typescript
// POST /v2/organisations/:orgId/interview-templates
{
  "templateName": "standard-onboarding-v2",
  "templateType": "standard",
  "pages": [
    {
      "pageName": "personal-info",
      "pageType": "name",
      "displayName": "Personal Information",
      "flow": {
        "next": [{
          "targetPageId": "employment-page-id",
          "priority": 1
        }]
      }
    },
    {
      "pageName": "employment",
      "pageType": "employment",
      "displayName": "Employment Information",
      "flow": {
        "next": [
          {
            "condition": "answers.employmentStatus === 'retired'",
            "targetPageId": "benefits-page-id",
            "priority": 1
          },
          {
            "targetPageId": "income-page-id",
            "priority": 2
          }
        ]
      }
    }
  ]
}
```

### Starting an Interview

```typescript
// POST /v2/clients/:clientId/interviews
{
  "templateId": "template-uuid",  // Optional, uses org default if not provided
  "contactType": "primary"
}

// Response includes navigation context
{
  "id": "interview-uuid",
  "startPageId": "first-page-uuid",
  "accountInstances": [
    {
      "accountId": "acct-123",
      "accountType": "Ira",
      "requiresBeneficiaries": true
    }
  ]
}
```

### Submitting a Page with Navigation

```typescript
// POST /v2/interviews/:interviewId/pages/submit
{
  "pageId": "current-page-uuid",
  "answers": {
    "employmentStatus": "retired",
    "retirementDate": "2023-01-01"
  }
}

// Response includes next page based on branching rules
{
  "success": true,
  "navigation": {
    "nextPageId": "benefits-page-id",  // Skipped income page
    "isComplete": false,
    "progress": 0.4
  }
}
```

### Handling Account-Specific Pages

For accounts requiring beneficiaries, the system automatically creates page instances:

```typescript
// GET /v2/interviews/:interviewId/pages/next
{
  "pageId": "beneficiary-page-uuid",
  "accountContext": {
    "accountId": "ira-123",
    "accountType": "beneficiary",
    "accountSubtype": "primary",
    "accountName": "Traditional IRA - Primary Beneficiaries"
  }
}
```

## Best Practices

1. **Keep Conditions Simple**: Complex JEXL expressions are hard to maintain
2. **Test All Paths**: Ensure every possible branch is tested
3. **Use Meaningful Page Names**: Makes debugging and analytics easier
4. **Design for Failure**: Consider CRM unavailability in your flow
5. **Audit Everything**: Use the audit service for compliance tracking
6. **Version Carefully**: Published templates cannot be changed

## Future Enhancements

1. **Visual Flow Designer**: Drag-and-drop interface for template creation
2. **A/B Testing**: Multiple active versions with traffic splitting
3. **Smart Defaults**: ML-based answer predictions
4. **Offline Support**: Queue answers locally when CRM unavailable
5. **Real-time Sync**: WebSocket support for collaborative editing