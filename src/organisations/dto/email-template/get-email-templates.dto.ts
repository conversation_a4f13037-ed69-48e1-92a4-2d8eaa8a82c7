import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional, IsString } from 'class-validator';
import { EmailTemplateDto } from 'src/organisations/dto/email-template/email-template.dto';
import { PaginationQueryDto, PaginationResponseDto } from 'src/shared/dto/pagination.dto';
import { EmailTemplateNameEnum } from 'src/templates/mail/types';
import { Transform } from 'class-transformer';

export class GetEmailTemplatesQueryDto extends PaginationQueryDto {
  @IsOptional()
  @IsEnum(EmailTemplateNameEnum)
  @ApiProperty({ enum: EmailTemplateNameEnum, required: false })
  emailTemplateType?: EmailTemplateNameEnum;

  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value === 'true')
  @ApiProperty({ required: false, description: 'Filter by active status' })
  isActive?: boolean;
}

export class EmailTemplatesResponseDto extends PaginationResponseDto<EmailTemplateDto[]> {} 