import { HttpException, HttpStatus } from '@nestjs/common';
import { ArgumentsHost } from '@nestjs/common/interfaces/features/arguments-host.interface';
import { AllExceptionsFilter } from './exceptions.filter';

describe('AllExceptionsFilter', () => {
  let allExceptionsFilter: AllExceptionsFilter;

  beforeEach(() => {
    allExceptionsFilter = new AllExceptionsFilter({
      error: jest.fn(),
    } as any);
  });

  it('should catch general exceptions and return status 500', () => {
    const mockJson = jest.fn();
    const mockStatus = jest.fn().mockImplementation(() => ({ json: mockJson }));

    const exception = new Error('Test Error');
    const host: Partial<ArgumentsHost> = {
      switchToHttp: () =>
        ({
          getResponse: (): any => ({
            status: mockStatus,
          }),
          getRequest: (): any => ({
            url: 'test_url',
          }),
        } as any),
    };

    allExceptionsFilter.catch(exception, host as ArgumentsHost);

    expect(mockStatus).toBeCalledWith(HttpStatus.INTERNAL_SERVER_ERROR);
    expect(mockJson).toBeCalledWith({
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      timestamp: expect.any(String),
      path: 'test_url',
      message: 'Internal Server Error',
      exceptionMessage: expect.any(String),
    });
  });

  it('should catch HttpException and return its status and response', () => {
    const mockJson = jest.fn();
    const mockStatus = jest.fn().mockImplementation(() => ({ json: mockJson }));

    const exception = new HttpException(
      'HttpException Message',
      HttpStatus.NOT_FOUND,
    );
    const host: Partial<ArgumentsHost> = {
      switchToHttp: () =>
        ({
          getResponse: (): any => ({
            status: mockStatus,
          }),
          getRequest: () => ({
            url: 'test_url',
          }),
        } as any),
    };

    allExceptionsFilter.catch(exception, host as ArgumentsHost);

    expect(mockStatus).toBeCalledWith(HttpStatus.NOT_FOUND);
    expect(mockJson).toBeCalledWith({
      statusCode: HttpStatus.NOT_FOUND,
      timestamp: expect.any(String),
      path: 'test_url',
      message: 'HttpException Message',
      exceptionMessage: expect.any(String),
    });
  });
});
