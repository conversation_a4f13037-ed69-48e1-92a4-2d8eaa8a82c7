import { ApiProperty } from '@nestjs/swagger';
import { Exclude, Expose } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CredentialsDto {
  @IsOptional()
  @IsString()
  @ApiProperty()
  username?: string;

  @IsOptional()
  @IsString()
  @ApiProperty()
  password?: string;

  @IsOptional()
  @IsString()
  @ApiProperty()
  apiKey?: string;

  @IsOptional()
  @IsString()
  @ApiProperty()
  userKey?: string;
}

export class IntegrationConfigDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  @Expose()
  name: string;

  @IsNotEmpty()
  @Exclude()
  credentials: CredentialsDto | string;

  @ApiProperty()
  @Expose()
  accountId: string;

  @ApiProperty()
  @Expose()
  username: string;

  @ApiProperty()
  @Expose()
  accountOwnership: string;

  @ApiProperty()
  @Expose()
  accountName: string;

  @ApiProperty()
  @Expose()
  accounts: {
    accountId: string;
    accountName: string;
    baseUri: string;
    isDefault: boolean;
  }[];

  @ApiProperty()
  @Expose()
  enabled: boolean;
}
