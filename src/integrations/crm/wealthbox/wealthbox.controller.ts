import { Controller, Get, Post, Query, Res, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { WealthboxService } from 'src/integrations/crm/wealthbox/wealthbox.service';
import { Response } from 'express';
@Controller('integrations/wealthbox')
export class WealthboxController {
  constructor(private readonly wealthboxService: WealthboxService) {}

  @Get('callback')
  async handleCallback(
    @Query('code') code: string,
    @Query('advisorId') advisorId: string,
    @Res() res: Response,
  ) {
    return this.wealthboxService.handleCallback(code, advisorId, res);
  }

  @Post('link')
  @UseGuards(AuthGuard('jwt'))
  async getWealthboxAuthorisationUri(@Query('advisorId') advisorId: string): Promise<string> {
    return this.wealthboxService.getWealthboxAuthorisationUrl(advisorId);
  }
}
