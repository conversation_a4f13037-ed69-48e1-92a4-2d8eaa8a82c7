import { HttpService } from '@nestjs/axios';
import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as docusign from 'docusign-esign';
import {
  CarbonCopy,
  DateSigned,
  Document,
  EnvelopeDefinition,
  EnvelopeRecipientTabs,
  EventNotification,
  ReturnUrlRequest,
  SignHere,
  Signer,
  CompositeTemplate
} from 'docusign-esign';
import { isEmpty } from 'lodash';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { extname } from 'path';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { AdvisorsDocusignService } from 'src/advisors/services/advisors.docusign.service';
import { ClientsV1Service } from 'src/clients/services/v1/clients-v1.service';
import { AddAdvisorDocumentsDto } from 'src/integrations/docusign/dto/add-advisor-documents.dto';
import { AddClientDocumentsDto } from 'src/integrations/docusign/dto/add-client-documents.dto';
import { CreateEnvelopeDto } from 'src/integrations/docusign/dto/create-envelope.dto';
import { InterviewsService } from 'src/interviews/interviews.service';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';
import { MutexService } from 'src/mutex/mutex.service';
import { SmsService } from 'src/notifications/sms/sms.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { Integration } from 'src/shared/schemas/integration.schema';
import { AccountOwnerEnum, DocusignRoleNameEnum, DocusignTemplateRoleEnum } from 'src/shared/types/accounts/account-ownership.enum';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import {
  IntegrationConfig,
  IntegrationEnum,
} from 'src/shared/types/integrations';
import { Camelize } from 'src/shared/types/monads';
import { smsTemplateMapper } from 'src/templates/sms';
import { TextMessageTemplateNameEnum } from 'src/templates/sms/types';
import {
  camelize,
  compressFile,
  contains,
  decryptCredentials,
  isObjectOfType,
  prepareDocusignTabs,
} from 'src/utils';
import { getReadableStreamFromFile } from 'src/utils/streams/readable';
import { Logger } from 'winston';
import {
  ApplicantTabs,
  BATCH_SIZE,
  DocusignAccountOwnershipEnum,
  DocusignAuthScopesEnum,
  DocusignDocumentFileExtensionEnum,
  DocusignEnvelopeStatusEnum,
  DocusignGrantTypesEnum,
  DocusignOAuthData,
  DocusignOAuthResponse,
  DocusignRecipientIdEnum,
  END_PAGINATION,
  Entity,
  EnvelopePreparedTabs,
  RecipientInfo,
} from './docusign.types';
import { AccountInfoDto } from './dto/account-info.dto';
import { AuthorizationUriDto } from './dto/authorization-uri.dto';
import { AuthorizationDto } from './dto/authorization.dto';
import { RefreshAccessTokenResponseDto } from './dto/refresh-access-token.dto';
import { SendEnvelopeDto } from './dto/send-envelope.dto';
import {
  DocusignJsonSimWebhookDto,
  DocusignWebhookEvent,
  LegacyDocuSignWebhookDto,
} from './dto/webhook.dto';
import { getDataFromEnvelope } from 'src/integrations/docusign/utils/envelope-utils';
import {
  DOCUSIGN_MAX_TABS,
  MAX_TAB_PRUNE_THRESHOLD,
} from 'src/integrations/docusign/constants';
import { Cron, CronExpression } from '@nestjs/schedule';
import { GetEnvelopDocumentsDto } from './dto/get-envelop-documents.dto';
import { env } from 'process';
import { ClientSession } from 'mongoose';

/**
 * Docusign Service
 * @see {@link https://docusign.github.io/docusign-esign-node-client/}
 */
@Injectable()
export class DocusignService {
  private apiClient: docusign.ApiClient;
  private clientId: string;
  private clientSecret: string;
  private redirectUri: string;
  private serviceName: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
    private readonly mutexService: MutexService,
    private readonly smsService: SmsService,
    @Inject(forwardRef(() => AdvisorsCrudService))
    private readonly advisorsCrudService: AdvisorsCrudService,
    @Inject(forwardRef(() => AdvisorsDocusignService))
    private readonly advisorsDocusignService: AdvisorsDocusignService,
    @Inject(forwardRef(() => OrganisationsService))
    private readonly organisationsService: OrganisationsService,
    @Inject(forwardRef(() => InterviewsService))
    private readonly interviewsService: InterviewsService,
    @Inject(forwardRef(() => ClientsV1Service))
    private readonly clientsService: ClientsV1Service,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {
    this.apiClient = new docusign.ApiClient();
    this.apiClient.setBasePath(
      this.configService.get<string>('DOCUSIGN_API_BASE_URL'),
    );

    this.clientId = this.configService.get<string>('DOCUSIGN_CLIENT_ID');
    this.clientSecret = this.configService.get<string>(
      'DOCUSIGN_CLIENT_SECRET',
    );
    this.redirectUri = this.configService.get<string>(
      'DOCUSIGN_OAUTH_REDIRECT_URI',
    );

    this.serviceName = this.constructor.name;
  }

  async getSchwabEnvelopeTemplate(accountId, authorizationDto: AuthorizationDto) {
    try {
      const templatesApi = await this.getTemplatesApi(authorizationDto)
      const templates = await templatesApi.listTemplates(accountId, { searchText: 'Schwab Non-Template Envelope To Process' });
      const schwabTemplateId = templates?.envelopeTemplates?.[0]?.templateId || null;
      return schwabTemplateId;
    } catch (error) {
      this.logger.error(error?.response?.text?.message);
      this.logger.info('Error fetching Schwab templates');
    }
  }


  /**
   * Returns an instance of the DocuSign Template API using the provided authorization DTO.
   * @param dto - The authorization DTO containing the necessary credentials to authenticate with the DocuSign API.
   * @returns An instance of the DocuSign Envelopes API.
   */
  private async getTemplatesApi(
    dto: AuthorizationDto,
  ): Promise<docusign.TemplatesApi> {
    const client = await this.getAuthorizedApiClient(dto);
    const templatesApi = new docusign.TemplatesApi(client);

    return templatesApi;
  }

  /**
   * Retrieves an authorized DocuSign API client using the provided authorization DTO.
   * @param dto The authorization DTO containing the necessary credentials.
   * @returns The authorized DocuSign API client.
   */
  async getAuthorizedApiClient(dto: AuthorizationDto) {
    const { credentials } = await this.getEntityIntegrationConfig(dto);
    const { accessToken } = await decryptCredentials(credentials);

    const accountInfo = await this.getAccountInfo(dto);

    const { baseUri } = accountInfo;

    this.apiClient = new docusign.ApiClient();
    this.apiClient.setBasePath(`${baseUri}/restapi`);
    this.apiClient.addDefaultHeader('Authorization', `Bearer ${accessToken}`);
    (this.apiClient as any).timeout = 60000 * 10;

    docusign.Configuration.default.setDefaultApiClient(this.apiClient);

    return this.apiClient;
  }

  /**
   * Returns the authorization URI for the DocuSign API.
   * @param authorizationUriDto - The DTO containing the advisor and organization IDs.
   * @returns The authorization URI as a string.
   */
  async getAuthorizationUri(
    authorizationUriDto: AuthorizationUriDto,
  ): Promise<string> {
    const { advisorId, organisationId } = authorizationUriDto || {};

    const scopes = [
      DocusignAuthScopesEnum.SIGNATURE,
      DocusignAuthScopesEnum.EXTENDED,
    ];
    const state = JSON.stringify({ advisorId, organisationId });

    const authUri = this.apiClient.getAuthorizationUri(
      this.clientId,
      scopes,
      this.redirectUri,
      DocusignGrantTypesEnum.AUTHORIZATION_CODE,
      state,
    );

    return authUri;
  }

  /**
   * Generates an access token for DocuSign API using the provided authorization code.
   * @param authorizationCode The authorization code obtained from DocuSign OAuth flow.
   * @returns An object containing the access token, refresh token, expiration time and last refresh date.
   * @throws HttpException if there was an error generating the access token.
   */
  async getAccessToken(authorizationCode: string): Promise<DocusignOAuthData> {
    let tokenInfo: DocusignOAuthResponse;
    try {
      tokenInfo = await this.apiClient.generateAccessToken(
        this.clientId,
        this.clientSecret,
        authorizationCode,
      );
    } catch (error) {
      this.logger.error(error);
      throw new HttpException(
        'Error generating access token',
        HttpStatus.BAD_REQUEST,
      );
    }

    const { accessToken, refreshToken, expiresIn } = tokenInfo || {};

    return { accessToken, refreshToken, expiresIn, lastRefresh: new Date() };
  }

  /**
   * Stores the access token for a given advisor or organization in the database.
   * @param id - An object containing either the advisorId or the organisationId.
   * @param oAuthData - An object containing the OAuth data to be stored.
   * @param isRefresh - A boolean indicating whether the access token is being refreshed.
   * @returns A Promise that resolves to the result of linking the Docusign account to the advisor or organization.
   * @throws HttpException if neither advisorId nor organisationId is provided.
   */
  async storeAccessToken(
    id: { advisorId?: string; organisationId?: string },
    oAuthData: DocusignOAuthData,
    isRefresh = false,
    ownership = DocusignAccountOwnershipEnum.Firm,
  ) {
    const { advisorId, organisationId } = id || null;

    if (!advisorId && !organisationId) {
      throw new HttpException(
        'Advisor or Organisation id not found.',
        HttpStatus.BAD_REQUEST,
      );
    }

    const isOrganisation = !!organisationId;

    const entityId = isOrganisation ? organisationId : advisorId;
    const service = isOrganisation
      ? this.organisationsService
      : this.advisorsDocusignService;

    return service.linkDocusign(entityId, oAuthData, isRefresh, ownership);
  }

  /**
   * Verifies the access token for a given authorization DTO.
   *
   *  NOTE:  To be called before each request to the Docusign API
   *
   * @param dto The authorization DTO containing the necessary information to verify the access token.
   * @returns A boolean indicating whether the access token is valid or not.
   */
  async verifyAccessToken(dto: AuthorizationDto) {
    const { expiresIn, lastRefresh, advisorId, organisationId } =
      await this.getEntityIntegrationConfig(dto);

    try {
      const date = new Date(lastRefresh);
      const iat = Math.floor(date.getTime() / 1000);

      const now = new Date();
      const exp = new Date((iat + parseInt(expiresIn)) * 1000);

      // Refresh if token is expired
      if (now >= exp) {
        const entity = await this.getEntity({ advisorId, organisationId });
        await this.refreshTokensForEntity(entity, {
          httpService: this.httpService,
          logger: this.logger,
        });
      }
    } catch (error) {
      this.logger.error(error);
      return false;
    }

    return true;
  }

  /**
   * Refreshes access tokens for all entities that use DocuSign integration.
   * Uses a mutex to prevent multiple instances from running simultaneously.
   *
   *  NOTE:  Run worload every 15 days at midnight
   *
   * @returns A promise that resolves when all entities have been processed.
   */
  @Cron('0 0 */2 * *')
  async refreshTokens() {
    const pid = `${this.serviceName}.refreshTokens`;

    try {
      await this.mutexService.lock(pid, '0 0 */2 * *');

      let page = 1;
      while (page !== END_PAGINATION) {
        // Get batch of entities that need to be refreshed
        const entitiesToRefresh = (
          await Promise.all(
            [this.advisorsDocusignService, this.organisationsService].map(
              (service) => service.findDocusignRefreshCandidates(page),
            ),
          )
        ).flat();

        // Check if all entities have been processed
        const entitiesNo = entitiesToRefresh.length;
        if (entitiesNo === 0) {
          break;
        }

        this.logger.info(
          `Retrieved all tokens in this batch ${page} to be processed`
        );


        // Update pagination
        if (entitiesNo < BATCH_SIZE) {
          page = END_PAGINATION;
        } else {
          page++;
        }

        // Process each batch in parallel
        const options = {
          httpService: this.httpService,
          logger: this.logger,
        };

        await Promise.all(
          entitiesToRefresh.map((entity) =>
            this.refreshTokensForEntity(entity, options),
          ),
        );

        this.logger.info(
          `RefreshAccessTokens batch process updated successfully`
        );

      }
    } catch (error) {
      this.logger.error(
        `Error in refreshAccessTokens batch process: ${error.message}`,
      );
    } finally {
      await this.mutexService.unlock(pid);
      this.logger.info(`Unlocked ${pid}`);
    }
  }

  /**
   * @see {@link https://www.docusign.com/blog/developers/authorization-code-grant-refresh-tokens}
   */
  /**
   * Refreshes the access token for a given entity (either an Organisation or an Advisor) using their current refresh token.
   * @param entity - The entity for which to refresh the access token.
   * @param options - An object containing the HttpService and Logger instances.
   * @returns A Promise that resolves when the access token has been refreshed.
   * @throws An error if there was an issue refreshing the access token.
   */
  async refreshTokensForEntity(
    entity: Entity,
    options: { httpService: HttpService; logger: Logger },
  ): Promise<void> {
    const isOrganisation = isObjectOfType<Organisation>(
      entity,
      'externalAccounts',
    );

    try {
      // Get the current refresh token
      const integrations = isOrganisation
        ? entity.externalAccounts
        : entity.integrations;

      const docusignIntegration = integrations.find(
        ({ integrationType }: Integration) =>
          integrationType === IntegrationEnum.DocumentSigning,
      ).integrationConfig;

      if (!docusignIntegration) {
        this.logger.error(
          `No Docusign integration found for ${
            isOrganisation ? 'Organisation' : 'Advisor'
          } ${entity._id}`,
        );
        return;
      }

      const { credentials, accountOwnership } = docusignIntegration;

      if (!credentials || !accountOwnership) {
        this.logger.error(
          `No Docusign credentials found for ${
            isOrganisation ? 'Organisation' : 'Advisor'
          } ${entity._id}`,
        );
        return;
      }

      const { refreshToken: currentRefreshToken } = await decryptCredentials(
        credentials,
      );

      // Request a new access token
      const clientId = process.env.DOCUSIGN_CLIENT_ID;
      const clientSecret = process.env.DOCUSIGN_CLIENT_SECRET;

      const basicAuthToken = Buffer.from(
        `${clientId}:${clientSecret}`,
        'utf8',
      ).toString('base64');

      const url = process.env.DOCUSIGN_OAUTH_ACCESS_TOKEN_URI;

      const headers = {
        Authorization: `Basic ${basicAuthToken}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      };

      const data = new URLSearchParams({
        refresh_token: currentRefreshToken,
        grant_type: 'refresh_token',
      });

      const { accessToken, refreshToken, expiresIn } =
        await options.httpService.axiosRef
          .post(url, data, {
            headers,
          })
          .then(
            ({ data }: { data: RefreshAccessTokenResponseDto }) =>
              camelize(data) as Camelize<RefreshAccessTokenResponseDto>,
          )
          .catch((err) => {
            this.logger.error(
              `Error in refreshAccessTokens batch process: ${err.message}`
            );
            throw err;
          });

      // Store the new access token
      const isRefresh = true;
      const entityId = isOrganisation
        ? { organisationId: entity._id }
        : { advisorId: entity._id };

      await this.storeAccessToken(
        entityId,
        {
          accessToken,
          refreshToken,
          expiresIn,
          lastRefresh: new Date(),
        },
        isRefresh,
        accountOwnership,
      );
    } catch (error) {
      options.logger.error(
        `Error refreshing token for ${
          isOrganisation ? 'Organisation' : 'Advisor'
        } ${entity._id}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Retrieves the account information for the authorized user from DocuSign API.
   * @param dto - The authorization data transfer object.
   * @returns A promise that resolves to an AccountInfoDto object containing the user's account information.
   * @throws HttpException if the request is unauthorized.
   */
  async getAccountInfo(dto: AuthorizationDto): Promise<AccountInfoDto> {
    const isAuthorisedRequest = await this.verifyAccessToken(dto);

    if (!isAuthorisedRequest) {
      throw new HttpException(
        'Unauthorised request to DocuSign',
        HttpStatus.UNAUTHORIZED,
      );
    }

    const { credentials, accountOwnership, accountId, accountName } =
      await this.getEntityIntegrationConfig(dto);
    const { accessToken } = await decryptCredentials(credentials);

    const response = await this.apiClient.getUserInfo(accessToken);
    if (!response) {
      throw new HttpException(
        'Error retrieving account information',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    const { accounts, email, name: username } = response;

    // if account is already set, return the account info
    let account;
    if (accountId) {
      account = response.accounts.find(
        (account) => account.accountId === accountId,
      );
    } else {
      account = response.accounts.find(
        (account) => account.isDefault === 'true',
      );
    }

    return { username, email, ...account, accounts, accountOwnership };
  }

  async getAccountInfoById(
    id: string,
    dto: AuthorizationDto,
  ): Promise<AccountInfoDto> {
    const { credentials, accountOwnership, accountName } =
      await this.getEntityIntegrationConfig(dto);

    const { accessToken } = await decryptCredentials(credentials);

    const response = await this.apiClient.getUserInfo(accessToken);
    if (!response) {
      throw new HttpException(
        'Error retrieving account information',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    const account = response.accounts.find(
      (account) => account.accountId === id,
    );

    if (!account) {
      throw new HttpException('Account id not found.', HttpStatus.NOT_FOUND);
    }

    return account;
  }


  /**
   * Creates a new envelope for document signing.
   *
   * @param dto - The data transfer object containing the necessary information for creating the envelope.
   * @param session - Optional MongoDB session for transaction support.
   * @returns The ID of the created envelope.
   * @throws {HttpException} If there is an error creating the envelope.
   */
  async createEnvelope(dto: CreateEnvelopeDto, session?: ClientSession) {
    let envelopeDefinition: EnvelopeDefinition = {};
    let signers: Signer[] = [];
    let documents: Document[] = [];
    let documentTabs: EnvelopeRecipientTabs[] = [];

    let tabs: EnvelopePreparedTabs = {};

    // Prepare the documents if files are provided
    const { interviewData, files } = dto || {};
    if (!isEmpty(dto.files)) {
      const {
        documents: preparedDocuments,
        documentTabs: preparedDocusignTabs,
      } = await this.prepareEnvelopeDocuments(files, interviewData, 0);
      documents = preparedDocuments;
      documentTabs = preparedDocusignTabs;
      tabs = await this.prepareEnvelopeTabs(documentTabs);
    }

    // Prepare the signers
    const {
      isSchwabOwnedDocusignAccount,
      primaryAdvisorEmail,
      primaryAdvisorName,
      primaryAdvisorPhone,
      applicant,
      addSchwabCC,
      coapplicant,
      schwabTemplateId
    } = dto || {};

    const info = {
      primaryAdvisor: primaryAdvisorEmail && {
        email: primaryAdvisorEmail,
        name: primaryAdvisorName,
        phone: primaryAdvisorPhone,
      },
      applicant,
      coapplicant,
    };
    const { signers: preparedSigners, carbonCopies } =
      await this.prepareEnvelopeRecipients(
        isSchwabOwnedDocusignAccount,
        info,
        tabs,
        addSchwabCC,
      );
    signers = preparedSigners;

    // Prepare the WebHook configuration
    const { webhookUrl } = dto || {};
    const eventNotification: EventNotification =
      await this.prepareEnvelopeWebhook(webhookUrl);

    // Prepare the envelope
    const { organisationId, shouldSend } = dto || {};
    const { name: organisationName } = await this.organisationsService.findOne(
      organisationId,
      session
    );

    const compositeTemplates: CompositeTemplate[] = [];

    const inlineTemplates = [
      {
        sequence: "2",
        recipients: {
          signers: signers.map(signer => ({
            ...signer,
          })),
        },
      },
    ];


    if (isSchwabOwnedDocusignAccount && schwabTemplateId) {
      compositeTemplates.push({
        serverTemplates: [
          {
            sequence: "1",
            templateId: schwabTemplateId,
          },
        ],
        inlineTemplates,
      },)
    } else {
      compositeTemplates.push({
        inlineTemplates,
      },)
    }

    if (!isEmpty(files)) {
      documents.forEach((document, index) => {
        compositeTemplates.push({
          document: {
            documentId: (index + 1).toString(),
            name: document.name,
            documentBase64: document.documentBase64,
            fileExtension: document.fileExtension,
          },
          inlineTemplates: [
            {
              sequence: (index + 3).toString(),
              recipients: {
                signers: signers.map(signer => ({
                  ...signer,
                })),
              },
            },
          ],
        });
      });
    }

    envelopeDefinition = {
      emailSubject: `${organisationName} is ready for you to sign your documents!`,
      status: shouldSend
        ? DocusignEnvelopeStatusEnum.SENT
        : DocusignEnvelopeStatusEnum.CREATED,
      eventNotification,
      compositeTemplates,
    };

    // Create the envelope
    try {
      const { organisationId, advisorId } = dto || {};
      const { accountId } = await this.getAccountInfo({
        advisorId,
        organisationId,
      });
      const envelopesApi = await this.getEnvelopesApi({
        advisorId,
        organisationId,
      });
      const response = await envelopesApi.createEnvelope(accountId, {
        envelopeDefinition,
      });

      return response.envelopeId;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(
        error.response ? `DocuSign API Error: ${error.response.text}` : error,
      );

      throw new HttpException(
        'Error creating envelope',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Sends a DocuSign envelope with the provided data.
   * @param dto - The data needed to send the envelope.
   * @returns A Promise that resolves when the envelope is sent.
   * @throws HttpException if there is an error sending the envelope.
   */
  async sendEnvelope(dto: SendEnvelopeDto) {
    try {
      const { envelopeId, advisorId, organisationId } = dto || {};
      const args = { advisorId, organisationId };

      const { accountId } = await this.getAccountInfo(args);
      const envelopesApi = await this.getEnvelopesApi(args);

      await envelopesApi.update(accountId, envelopeId, {
        envelope: { status: DocusignEnvelopeStatusEnum.SENT },
      });
    } catch (error) {
      this.logger.error(
        error.response ? `DocuSign API Error: ${error.response.text}` : error,
      );

      throw new HttpException('Error sending envelope', HttpStatus.BAD_REQUEST);
    }
  }

  /**
   * Sends an envelope for review and returns the URL for the sender view.
   * @param dto - The data transfer object containing the envelope ID, advisor ID, and organization ID.
   * @returns The URL for the sender view.
   * @throws HttpException if there is an error generating the envelope sender view.
   */
  async reviewEnvelope(dto: SendEnvelopeDto) {
    try {
      const { envelopeId, advisorId, organisationId } = dto || {};
      const args = { advisorId, organisationId };

      const { accountId } = await this.getAccountInfo(args);
      const envelopesApi = await this.getEnvelopesApi(args);

      const senderViewRequest: ReturnUrlRequest = {
        returnUrl: this.configService.get<string>(
          'DOCUSIGN_CALLBACK_REDIRECT_URI',
        ),
      };

      const { url } = await envelopesApi.createSenderView(
        accountId,
        envelopeId,
        { senderViewRequest },
      );

      return url;
    } catch (error) {
      this.logger.error(
        error.response ? `DocuSign API Error: ${error.response.text}` : error,
      );

      throw new HttpException(
        'Error generating envelope sender view',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Adds advisory documents to the envelope.
   *
   * @param dto - The DTO containing the necessary information for adding advisory documents.
   * @returns The ID of the updated envelope.
   * @throws {HttpException} If there is an error upserting the envelope.
   */
  async addAdvisorDocuments(dto: AddAdvisorDocumentsDto) {
    try {
      const { organisationId, advisorId, envelopeId, interviewData, files } =
        dto || {};

      const { accountId } = await this.getAccountInfo({
        advisorId,
        organisationId,
      });
      const envelopesApi = await this.getEnvelopesApi({
        advisorId,
        organisationId,
      });

      // Prepare and update the envelope documents
      const { envelopeDocuments: existingDocuments } =
        await envelopesApi.listDocuments(accountId, envelopeId);
      const startingDocId = existingDocuments?.length || 0;
      
      this.logger.info(
        `Preparing envelope documents: ${files
          .map((file) => `${file.originalname}: ${file.path}`)
          .join(', ')}`,
      );

      const { documents, documentTabs } = await this.prepareEnvelopeDocuments(
        files,
        interviewData,
        startingDocId,
      );
      this.logger.info(`Successfully prepared envelope documents`);

      // this.logger.info(JSON.stringify({'documentTabs': documentTabs}))

      if (documents.length > 0) {
        await envelopesApi.updateDocuments(accountId, envelopeId, {
          envelopeDefinition: {
            documents,
          },
        });
        this.logger.info(`Successfully updated envelope documents`);
      }

      // Prepare and update the envelope recipient tabs
      const recipients = await envelopesApi.listRecipients(
        accountId,
        envelopeId,
      );

      // this.logger.info(JSON.stringify({'listed recipients': recipients}))

      const tabs: EnvelopePreparedTabs = await this.prepareEnvelopeTabs(
        documentTabs,
      );

      // this.logger.info(JSON.stringify(tabs))

      // Filter empty tabs to avoid hitting the 3000 limit
      if (this.getNumberOfTotalTabs(tabs) >= MAX_TAB_PRUNE_THRESHOLD) {
        this.logger.info(
          `Pruning empty tabs for envelope ${envelopeId} with ${this.getNumberOfTotalTabs(
            tabs,
          )} tabs`,
        );
        this.pruneEmptyTabs(tabs);
        this.logger.info(
          `Successfully pruned empty tabs for envelope ${envelopeId} with ${this.getNumberOfTotalTabs(
            tabs,
          )} tabs`,
        );
      }


      for (const signer of recipients.signers) {
        let tabsToUpdate: EnvelopeRecipientTabs;
        if (signer.name === "") {
          continue;
        }
        switch (signer.roleName) {
          case DocusignTemplateRoleEnum.PrimaryAdvisor:
            tabsToUpdate = tabs.advisorTabs;
            break;
          case DocusignTemplateRoleEnum.PrimaryContact:
            tabsToUpdate = tabs.primaryContactApplicantTabs;
            break;
          case DocusignTemplateRoleEnum.SecondaryContact:
            tabsToUpdate = this.aggregateTabs(
              tabs.secondaryContactApplicantTabs,
              tabs.coapplicantTabs,
            );
            break;
          default:
            break;
        }

        if (!isEmpty(tabsToUpdate) && Object.values(tabsToUpdate).some((member: any) => member.length)) {
          this.logger.info(`Updating tabs for recipient ${signer.recipientId}`);
          
          if (this.getNumberOfTotalTabs(tabs) >= DOCUSIGN_MAX_TABS) {
            this.logger.error(
              `Too many tabs for envelope ${envelopeId} with ${this.getNumberOfTotalTabs(
                tabs,
              )} tabs. Trying anyways...`,
            );
          }
          // this.logger.info(JSON.stringify({'tabsToUpdate': tabsToUpdate}))

          await envelopesApi.createTabs(
            accountId,
            envelopeId,
            signer.recipientId,
            {
              tabs: tabsToUpdate,
            },
          );
          this.logger.info(
            `Successfully updated tabs for recipient ${signer.recipientId}`,
          );
        }
      }

      return envelopeId;
    } catch (error) {
      this.logger.error(
        error.response
          ? `DocuSign API Error: ${error?.response?.text}`
          : error?.message,
      );


      
      throw new HttpException(
        {message: `Error adding advisor documents to envelope`, stack: error.stack},
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  private pruneEmptyTabs(tabs: EnvelopePreparedTabs) {
    tabs.advisorTabs = {
      ...tabs.advisorTabs,
      textTabs: tabs.advisorTabs.textTabs.filter(
        (tab) =>
          !isEmpty(tab.value) ||
          tab.required ||
          tab?.tooltip?.toLowerCase().includes('required'),
      ),
    };

    if (this.getNumberOfTotalTabs(tabs) >= MAX_TAB_PRUNE_THRESHOLD) {
      this.logger.info(
        `Pruning empty checkbox tabs for envelope with ${this.getNumberOfTotalTabs(
          tabs,
        )} tabs`,
      );
      tabs.advisorTabs = {
        ...tabs.advisorTabs,
        checkboxTabs: tabs.advisorTabs.checkboxTabs.filter(
          (tab) => tab.selected,
        ),
      };
    }
  }

  private getNumberOfTotalTabs(tabs: EnvelopePreparedTabs) {
    const {
      advisorTabs,
      primaryContactApplicantTabs,
      secondaryContactApplicantTabs,
      coapplicantTabs,
    } = tabs;

    return (
      advisorTabs?.textTabs.length +
      advisorTabs?.dateTabs.length +
      advisorTabs?.checkboxTabs.length +
      advisorTabs?.signHereTabs.length +
      primaryContactApplicantTabs?.signHereTabs?.length +
      secondaryContactApplicantTabs?.signHereTabs?.length +
      coapplicantTabs.signHereTabs?.length +
      advisorTabs?.dateSignedTabs?.length +
      primaryContactApplicantTabs?.dateSignedTabs?.length +
      secondaryContactApplicantTabs?.dateSignedTabs?.length +
      coapplicantTabs?.dateSignedTabs?.length
    );
  }

  /**
   * Adds client documents to a DocuSign envelope.
   *
   * @param dto - The DTO (Data Transfer Object) containing the necessary information for adding client documents.
   * @returns The ID of the added envelope.
   * @throws {HttpException} If there is an error upserting the envelope.
   */
  async addClientDocuments(dto: AddClientDocumentsDto) {
    try {
      const { organisationId, advisorId, envelopeId, files } = dto || {};
      const { accountId } = await this.getAccountInfo({
        advisorId,
        organisationId,
      });
      const envelopesApi = await this.getEnvelopesApi({
        advisorId,
        organisationId,
      });

      const { envelopeDocuments: existingDocuments } =
        await envelopesApi.listDocuments(accountId, envelopeId);

      const startingDocId = existingDocuments?.length || 0;
      const { documents } = await this.prepareEnvelopeDocuments(
        files,
        null,
        startingDocId,
        false,
      );

      await envelopesApi.updateDocuments(accountId, envelopeId, {
        envelopeDefinition: {
          documents,
        },
      });

      return envelopeId;
    } catch (error) {
      this.logger.error(error);
      this.logger.error(
        error.response ? `DocuSign API Error: ${error.response.text}` : error,
      );

      throw new HttpException(
        'Error adding client documents envelope',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Processes the webhook payload received from DocuSign.
   * @param payload The webhook payload received from DocuSign.
   * @returns Promise<void>
   */
  async processWebhook(
    payload: LegacyDocuSignWebhookDto | DocusignJsonSimWebhookDto,
  ) {
    if (!payload) {
      throw new HttpException(
        'Invalid DocuSign webhook payload',
        HttpStatus.BAD_REQUEST,
      );
    }

    const { envelopeId, status } = getDataFromEnvelope(payload) || {};

    // Lookup Interview by DocuSign envelopeId
    const interview = await this.interviewsService.findOne({ envelopeId });
    const clientId = interview.client._id.toString();
    const client = await this.clientsService.findOne({
      _id: interview.client._id.toString(),
    });
    const organisation = await this.organisationsService.findOne(
      client.organisationId.toString(),
    );

    // This support is for the legacy DocuSign webhook and the new JSON Sim webhook
    const clientStatus =
      {
        [DocusignWebhookEvent.ENVELOPE_SENT]: ClientStatusEnum.WaitingSignature,
        [DocusignWebhookEvent.ENVELOPE_COMPLETED]: ClientStatusEnum.Signed,
        [DocusignEnvelopeStatusEnum.SENT]: ClientStatusEnum.WaitingSignature,
        [DocusignEnvelopeStatusEnum.COMPLETED]: ClientStatusEnum.Signed,
      }[status?.toLowerCase()] || null;

    if (!clientStatus) {
      this.logger.info(
        `Invalid DocuSign envelope status: ${status} for envelopeId: ${envelopeId}`,
      );
      throw new HttpException(
        'Invalid DocuSign envelope status',
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.clientsService.update(clientId, {
      status: clientStatus,
    });

    // Use SMS Service to notify the client when the documents are ready to be signed
    if (clientStatus === ClientStatusEnum.WaitingSignature) {
      const contact = interview.isPrimary
        ? client.primaryContact
        : client.secondaryContact;

      const templateType = TextMessageTemplateNameEnum.ReadyToSign;
      const context = { organisationName: organisation.name };
      const body = smsTemplateMapper(templateType, context);

      await this.smsService.sendText(contact.mobile, body, undefined, contact);
    }
  }

  async getAccountOwnership(dto: AuthorizationDto) {
    const isAuthorisedRequest = await this.verifyAccessToken(dto);

    if (!isAuthorisedRequest) {
      throw new HttpException(
        'Unauthorised request to DocuSign',
        HttpStatus.UNAUTHORIZED,
      );
    }

    const { accountOwnership } = await this.getEntityIntegrationConfig(dto);

    return accountOwnership;
  }

  /**
   * Retrieves an entity based on the provided AuthorizationDto.
   * @param dto - The AuthorizationDto containing either an advisorId or an organisationId.
   * @returns - The entity found based on the provided dto.
   * @throws - If neither advisorId nor organisationId are provided in the dto.
   */
  private async getEntity(dto: AuthorizationDto) {
    const { advisorId, organisationId } = dto || {};

    if (!advisorId && !organisationId) {
      throw new HttpException(
        'Advisor or Organisation id not found.',
        HttpStatus.BAD_REQUEST,
      );
    }

    const service = organisationId
      ? this.organisationsService
      : this.advisorsCrudService;

    const arg = organisationId || { _id: advisorId };
    const entity = await service.findOne(arg as any);

    return entity as Entity;
  }

  /**
   * Returns an instance of the DocuSign Envelopes API using the provided authorization DTO.
   * @param dto - The authorization DTO containing the necessary credentials to authenticate with the DocuSign API.
   * @returns An instance of the DocuSign Envelopes API.
   */
  private async getEnvelopesApi(
    dto: AuthorizationDto,
  ): Promise<docusign.EnvelopesApi> {
    const client = await this.getAuthorizedApiClient(dto);
    const envelopesApi = new docusign.EnvelopesApi(client);

    return envelopesApi;
  }

  /**
   * Retrieves the integration configuration for a given entity (advisor or organization) in DocuSign.
   * @param dto - The authorization data transfer object containing the advisor or organization ID.
   * @returns A promise that resolves to an IntegrationConfig object containing the DocuSign integration configuration for the entity.
   * @throws HttpException with a BAD_REQUEST status if neither advisorId nor organisationId is provided in the dto parameter.
   */
  private async getEntityIntegrationConfig(
    dto: AuthorizationDto,
  ): Promise<IntegrationConfig> {
    const { advisorId, organisationId } = dto || null;

    if (!advisorId && !organisationId) {
      throw new HttpException(
        'Advisor or Organisation id not found.',
        HttpStatus.BAD_REQUEST,
      );
    }

    const advisorIntegrationConfig =
      await this.advisorsDocusignService.findDocusignIntegration(advisorId);

    const organisationIntegrationConfig =
      await this.organisationsService.findDocusignIntegration(organisationId);

    return advisorIntegrationConfig
      ? { ...advisorIntegrationConfig, advisorId }
      : {
          ...organisationIntegrationConfig,
          organisationId,
        };
  }

  /**
   * Prepares the envelope documents for the Docusign service.
   *
   * @param files - The array of files to be included in the envelope.
   * @param interviewData - The interview data with CRM information.
   * @returns An object containing the prepared documents and document tabs.
   * @throws HttpException if an invalid file extension is encountered.
   */
  private async prepareEnvelopeDocuments(
    files: Express.Multer.File[],
    interviewData?: InterviewDataWithCrmInfo,
    startingDocId = 0,
    parseTabs = true,
  ) {
    const documentTabs: EnvelopeRecipientTabs[] = [];
    const documents: Document[] = [];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const documentId = `${i + 1 + startingDocId}`;
      const name = file.originalname;
      const fileExtension = extname(name).slice(1);

      if (file.buffer) {
        file.stream = getReadableStreamFromFile(file);
        file.buffer = null;
      }

      let documentBase64: string;
      try {
        this.logger.info(`Compressing file ${name}`);
        documentBase64 = await compressFile(
          getReadableStreamFromFile(file),
          fileExtension,
        );
        this.logger.info(`Successfully compressed file ${name}`);
      } catch (error) {
        throw new HttpException(
          `Error processing file ${name}: ${error.message}`,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      const invalidFileExtension = !Object.values(
        DocusignDocumentFileExtensionEnum,
      ).includes(
        fileExtension?.toLowerCase() as DocusignDocumentFileExtensionEnum,
      );

      if (invalidFileExtension) {
        throw new HttpException(
          `Invalid file extension: ${fileExtension}`,
          HttpStatus.BAD_REQUEST,
        );
      }
      let tabs = null;
      if (
        fileExtension === DocusignDocumentFileExtensionEnum.PDF &&
        parseTabs
      ) {
        try {
          this.logger.info(`Preparing DocuSign tabs for file ${name}`);
          tabs = await prepareDocusignTabs(file, documentId, interviewData);
          this.logger.info(
            `Successfully prepared DocuSign tabs for file ${name}`,
          );
          documentTabs.push(tabs);
        } catch (error) {
          throw new HttpException(
            `Error preparing DocuSign tabs for file ${name}: ${error.message}`,
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        }
      }

      documents.push({
        documentId,
        documentBase64,
        name,
        fileExtension,
      });
    }

    return { documents, documentTabs };
  }

  /**
   * Prepares the envelope tabs for different recipients.
   *
   * @param documentTabs - An array of EnvelopeRecipientTabs containing the tabs for each recipient.
   * @returns An object containing the prepared tabs for advisor, applicant, and coapplicant.
   */
  private async prepareEnvelopeTabs(
    documentTabs: EnvelopeRecipientTabs[],
  ): Promise<EnvelopePreparedTabs> {
    // Reduce the tabs for each recipient into a single object
    const tabs = documentTabs.reduce<EnvelopeRecipientTabs>(
      (acc, curr) => ({
        textTabs: [...acc.textTabs, ...curr.textTabs],
        dateTabs: [...acc.dateTabs, ...curr.dateTabs],
        checkboxTabs: [...acc.checkboxTabs, ...curr.checkboxTabs],
        signHereTabs: [...acc.signHereTabs, ...curr.signHereTabs],
        dateSignedTabs: [...acc.dateSignedTabs, ...curr.dateSignedTabs],
      }),
      {
        textTabs: [],
        dateTabs: [],
        checkboxTabs: [],
        signHereTabs: [],
        dateSignedTabs: [],
      },
    );

    // Filter the tabs for each recipient
    const advisorKeyWords = ['advisor', 'Advisor'];
    const applicantKeyWords = ['applicant', 'Applicant'];
    const coapplicantKeyWords = ['coapplicant', 'Coapplicant'];

    type PredicateTab = SignHere | DateSigned;
    const predicates = {
      advisor: (tab: PredicateTab) => contains(tab.tooltip, advisorKeyWords),
      applicant: {
        all: (tab: PredicateTab) =>
          contains(tab.tooltip, applicantKeyWords) &&
          !contains(tab.tooltip, [...coapplicantKeyWords, ...advisorKeyWords]),
        secondaryContact: (tab: PredicateTab) =>
          contains(tab.tooltip, [AccountOwnerEnum.SecondaryContact]),
      },
      coapplicant: (tab: PredicateTab) =>
        contains(tab.tooltip, coapplicantKeyWords),
    };

    // Parse advisor tabs
    const advisorTabs = {
      ...tabs,
      signHereTabs: tabs.signHereTabs.filter(predicates.advisor),
      dateSignedTabs: tabs.dateSignedTabs.filter(predicates.advisor),
    };

    // Parse applicant Tabs
    const allApplicantTabs = {
      signHereTabs: tabs.signHereTabs.filter(predicates.applicant.all),
      dateSignedTabs: tabs.dateSignedTabs.filter(predicates.applicant.all),
    };

    const secondaryContactApplicantTabs = {
      signHereTabs: allApplicantTabs.signHereTabs.filter(
        predicates.applicant.secondaryContact,
      ),
      dateSignedTabs: allApplicantTabs.dateSignedTabs.filter(
        predicates.applicant.secondaryContact,
      ),
    };

    const secondaryContactSignHereTabs = new Set(
      secondaryContactApplicantTabs.signHereTabs,
    );
    const secondaryContactDateSignedTabs = new Set(
      secondaryContactApplicantTabs.dateSignedTabs,
    );
    const primaryContactApplicantTabs = {
      signHereTabs: allApplicantTabs.signHereTabs.filter(
        (tab) => !secondaryContactSignHereTabs.has(tab),
      ),
      dateSignedTabs: allApplicantTabs.dateSignedTabs.filter(
        (tab) => !secondaryContactDateSignedTabs.has(tab),
      ),
    };

    // Parse coapplicant tabs
    const coapplicantTabs = {
      signHereTabs: tabs.signHereTabs.filter(predicates.coapplicant),
      dateSignedTabs: tabs.dateSignedTabs.filter(predicates.coapplicant),
    };

    return {
      primaryContactApplicantTabs,
      secondaryContactApplicantTabs,
      coapplicantTabs,
      advisorTabs,
    };
  }

  /**
   * Prepares the recipients for the envelope.
   *
   * @param isSchwabOwnedDocusignAccount - Indicates if the Docusign account is owned by Schwab.
   * @param info - The information about the recipients.
   * @param tabs - The tabs for each recipient.
   * @returns An object containing the signers and carbon copies for the envelope.
   */
  private async prepareEnvelopeRecipients(
    isSchwabOwnedDocusignAccount: boolean,
    info: RecipientInfo,
    tabs: EnvelopePreparedTabs,
    addSchwabCC: boolean = true,
  ) {
    const { primaryAdvisor, applicant, coapplicant } = info;
    const {
      advisorTabs,
      primaryContactApplicantTabs,
      secondaryContactApplicantTabs,
      coapplicantTabs,
    } = tabs;

    const signersInfo = [
      [
        applicant.email,
        applicant.name,
        applicant.phone,
        DocusignRecipientIdEnum.PrimaryContact,
        DocusignTemplateRoleEnum.PrimaryContact
      ],
    ];

    if (coapplicant) {
      signersInfo.push([
        coapplicant.email,
        coapplicant.name,
        coapplicant.phone,
        DocusignRecipientIdEnum.SecondaryContact,
        DocusignTemplateRoleEnum.SecondaryContact
      ]);
    }

    if (primaryAdvisor) {
      signersInfo.push([
        primaryAdvisor.email,
        primaryAdvisor.name,
        primaryAdvisor.phone,
        DocusignRecipientIdEnum.PrimaryAdvisor,
        DocusignTemplateRoleEnum.PrimaryAdvisor
      ]);
    }

    const signers: Signer[] = signersInfo.map(
      ([email, name, phone, recipientId, roleName], index) => {
        const signerTabs = {
          [DocusignRecipientIdEnum.PrimaryContact]: primaryAdvisor
            ? primaryContactApplicantTabs
            : {
                ...advisorTabs,
                ...primaryContactApplicantTabs,
              },
          [DocusignRecipientIdEnum.SecondaryContact]:
            secondaryContactApplicantTabs,
          [DocusignRecipientIdEnum.PrimaryAdvisor]: advisorTabs,
        }[recipientId];

        return {
          email,
          name,
          recipientId,
          roleName,
          routingOrder: (index + 1).toString(),
          tabs:
            recipientId === DocusignRecipientIdEnum.SecondaryContact
              ? this.aggregateTabs(
                  secondaryContactApplicantTabs,
                  coapplicantTabs,
                )
              : signerTabs,
          smsAuthentication: {
            senderProvidedNumbers: [phone],
            recipMayProvideNumber: 'true',
          },
          idCheckConfigurationName: isSchwabOwnedDocusignAccount
            ? 'Knowledge Based and SMS'
            : 'SMS Auth $',
        };
      },
    );

    // If Schwab owned account, then create carbon copy recipient
    const j = (signers.length + 1).toString();
    const firmOwnedCC = {
      name: 'Please Disregard: Internal Use Only: Schwab Internal',
      email: '<EMAIL>',
      routingOrder: j,
      recipientId: j,
    };

    const schwabOwnedCC = {
      name: 'Please Disregard: Internal Use Only: Schwab Internal',
      email: '<EMAIL>',
      routingOrder: j,
      recipientId: j,
    };

    const carbonCopies: CarbonCopy[] = isSchwabOwnedDocusignAccount
      ? [schwabOwnedCC]
      : [firmOwnedCC];

    return { signers, carbonCopies: addSchwabCC ? carbonCopies : [] };
  }

  /**
   * Prepares the webhook configuration for the Docusign envelope events.
   * @returns The configured event notification object.
   */
  private async prepareEnvelopeWebhook(url: string) {
    const statusCodes: string[] = [
      DocusignEnvelopeStatusEnum.SENT,
      DocusignEnvelopeStatusEnum.COMPLETED,
    ].map((status) => status.charAt(0).toUpperCase() + status.slice(1));

    const envelopeEvents: docusign.EnvelopeEvent[] = statusCodes.map(
      (statusCode) => ({
        envelopeEventStatusCode: statusCode,
      }),
    );

    const eventNotification: EventNotification = {
      url,
      loggingEnabled: 'true',
      requireAcknowledgment: 'true',
      envelopeEvents,
      eventData: {
        version: 'restv2.1',
        format: 'json',
      },
    };

    return eventNotification;
  }

  /**
   * Aggregates the signHereTabs and dateSignedTabs from secondaryContactTabs and coapplicantTabs.
   *
   * @param secondaryContactTabs - The tabs for the secondary contact.
   * @param coapplicantTabs - The tabs for the coapplicant.
   * @returns An object containing the merged signHereTabs and dateSignedTabs.
   */
  private aggregateTabs(
    secondaryContactTabs: ApplicantTabs,
    coapplicantTabs: ApplicantTabs,
  ) {
    const result = {
      signHereTabs: [],
      dateSignedTabs: [],
    };

    // Merge signHereTabs
    if (secondaryContactTabs?.signHereTabs) {
      for (const tab of secondaryContactTabs?.signHereTabs) {
        result.signHereTabs.push(tab);
      }
    }
    if (coapplicantTabs?.signHereTabs) {
      for (const tab of coapplicantTabs?.signHereTabs) {
        result.signHereTabs.push(tab);
      }
    }

    // Merge dateSignedTabs
    if (secondaryContactTabs?.dateSignedTabs) {
      for (const tab of secondaryContactTabs.dateSignedTabs) {
        result.dateSignedTabs.push(tab);
      }
    }
    if (coapplicantTabs?.dateSignedTabs) {
      for (const tab of coapplicantTabs.dateSignedTabs) {
        result.dateSignedTabs.push(tab);
      }
    }

    return result;
  }

  /**
   * Gets existing documents uploaded to an envelope.
   *
   * @param organisationId - The organisationId.
   * @param envelopeId - The envelope ID.
   * @param advisorId - The advisor ID.
   * @returns The documents uploaded to the envelope..
   * @throws {HttpException} If there is an error upserting the envelope.
   */
  async getEnvelopDocuments(payload: GetEnvelopDocumentsDto) {
    const { envelopeId, organisationId, advisorId } = payload;

    const { accountId } = await this.getAccountInfo({
        advisorId,
        organisationId,
      });
   
    const envelopesApi = await this.getEnvelopesApi({
      organisationId,
      advisorId,
    });

    return envelopesApi.listDocuments(accountId, envelopeId);
  }
}
