import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { FileInterceptor } from '@nestjs/platform-express';
import mongoose, { ClientSession } from 'mongoose';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { Transaction } from 'src/shared/decorators/transaction.decorator';
import { OrganisationGuard } from 'src/shared/guards/organisation.guard';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { MAX_FILE_SIZE } from 'src/shared/types/general/asset-types.enum';
import { fileFilter } from 'src/utils/filters/files.filter';
import { CreateTransitionsDto } from './dto/create-transitions.dto';
import { GetTransitionsQueryDto } from './dto/get-transitions.dto';
import { UpdateSmsTemplateDto } from './dto/update-sms-template.dto';
import { UpdateTransitionDto } from './dto/update-transition.dto';
import { Transition } from './schemas/transitions.schema';
import { TransitionsService } from './transitions.service';
import { TransitionTypeEnum } from './types/transition-type.enum';
import { TransitionUrl } from './types/transition-url.type';

@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'), OrganisationGuard)
@Controller('/organisations/:organisationId/transition-clients')
export class TransitionsController {
  constructor(
    private readonly transitionsService: TransitionsService,
    private transactionManager: TransactionManager,
  ) {}

  @Post('/batch')
  @Transaction()
  createBatchFromArray(
    @Param('organisationId') organisationId: string,
    @Body() createTransitionsDto: CreateTransitionsDto,
    session?: ClientSession,
  ) {
    return this.transitionsService.createBatchFromArray(
      organisationId,
      createTransitionsDto,
      session,
    );
  }

  @Post('/batch/csv')
  @UseInterceptors(
    FileInterceptor('file', {
      limits: { fileSize: MAX_FILE_SIZE },
      fileFilter,
    }),
  )
  createBatchFromCsv(
    @Param('organisationId') organisationId: string,
    @Query('transitionType') transitionType: TransitionTypeEnum,
    @UploadedFile() file: Express.Multer.File,
  ) {
    return this.transitionsService.createBatchFromCsv(transitionType, file);
  }

  @Get()
  findAll(
    @Param('organisationId') organisationId: string,
    @Query() query: GetTransitionsQueryDto,
  ) {
    const { limit, page } = query;
    return this.transitionsService.findAll(
      { limit, page },
      {
        organisationId: new mongoose.Types.ObjectId(organisationId),
      },
    );
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<Transition> {
    return this.transitionsService.findOne(id);
  }

  @Put('/sms-template')
  async updateSmsTemplateForOrganisation(
    @Param('organisationId') organisationId: string,
    @Body() updateSmsTemplateDto: UpdateSmsTemplateDto,
  ): Promise<any> {
    return this.transitionsService.updateSmsTemplate(
      organisationId,
      updateSmsTemplateDto,
    );
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateTransitionsDto: UpdateTransitionDto,
  ): Promise<Transition> {
    return this.transitionsService.update(id, updateTransitionsDto);
  }

  @Patch('transition-batch-delete')
  async batchRemove(@Body() { transitionIds }: { transitionIds: string[] }) {
    await this.transitionsService.batchRemove(transitionIds);

    return {
      success: true,
      message: 'Batch SMS notifications sent successfully',
    };
  }

  @Delete(':id')
  async remove(@Param('id') id: string): Promise<Transition> {
    return this.transitionsService.remove(id);
  }

  @Post('/:transitionId/send-text')
  @Transaction()
  async notifyClientsAboutTransition(
    @Param('organisationId') organisationId: string,
    @Param('transitionId') transitionId: string,
    @Body() { smsPayload }: { smsPayload: string },
  ) {
    const transition: Transition = await this.transitionsService.findOne(
      transitionId,
    );

    if (!transition.primaryContact?.crmClientId) {
      await this.transitionsService.upsertTransitionContactsInCrm(transition);
    }

    return this.transitionsService.notifyClientsAboutTransition(
      transition,
      organisationId,
      smsPayload,
    );
  }


  @Post('/send-batch-text')
  @Transaction()
  async notifyClientsAboutTransitions(
    @Param('organisationId') organisationId: string,
    @Body() { transitionIds, smsPayload }: { transitionIds: string[], smsPayload: string },
  ) {

    const transitions = await this.transitionsService.findByIds(transitionIds);

    const notifyPromises = transitions.map(async (transition) => {
      if (!transition.primaryContact?.crmClientId) {
        await this.transitionsService.upsertTransitionContactsInCrm(transition);
      }
  
      return this.transitionsService.notifyClientsAboutTransition(
        transition,
        organisationId,
        smsPayload,
      );
    });
  
    await Promise.all(notifyPromises);
  
    return {
      success: true,
      message: 'Batch SMS notifications sent successfully',
    };
  }

  @Get(':transitionId/url')
  async getTransitionUrl(
    @Param('organisationId') organisationId: string,
    @Param('transitionId') transitionId: string,
  ): Promise<TransitionUrl | TransitionUrl[]> {
    return this.transitionsService.getTransitionUrl(organisationId, transitionId);
  }
}
