/**
 * Memory leak detection and prevention utilities for testing
 */

// Flag to enable comprehensive memory debugging
const DEBUG_MEMORY = false;

// Track global variables and objects
const initialGlobals = new Map<string, any>();
const capturedObjects = new WeakMap<object, string>();
const objectReferences = new Set<object>();

/**
 * Capture the initial global objects state
 */
export function captureGlobalState(): void {
  if (!DEBUG_MEMORY) return;
  
  initialGlobals.clear();
  Object.keys(global).forEach(key => {
    initialGlobals.set(key, global[key]);
  });
  
  // Log initial heap usage
  if (global.gc) {
    global.gc();
    const memUsage = process.memoryUsage();
    console.log(`[Memory] Initial heap: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
  }
}

/**
 * Detects global leaks by comparing current globals with initial state
 */
export function detectGlobalLeaks(): string[] {
  if (!DEBUG_MEMORY) return [];
  
  const leaks: string[] = [];
  Object.keys(global).forEach(key => {
    if (!initialGlobals.has(key)) {
      leaks.push(key);
    }
  });
  
  return leaks;
}

/**
 * Tracks an object to help identify leaks
 */
export function trackObject(obj: object, identifier: string): void {
  if (!DEBUG_MEMORY) return;
  
  capturedObjects.set(obj, identifier);
  objectReferences.add(obj);
}

/**
 * Removes tracking for an object
 */
export function untrackObject(obj: object): void {
  if (!DEBUG_MEMORY) return;
  
  objectReferences.delete(obj);
}

/**
 * Detects module leaks - mock modules not properly restored
 */
export function detectModuleLeaks(): void {
  if (!DEBUG_MEMORY) return;
  
  // Jest doesn't expose getMockRegistry publicly, use a safer approach
  const mockModules = Object.keys(jest).filter(key => 
    key.startsWith('mock') && typeof jest[key] === 'function'
  );
  
  if (mockModules.length > 0) {
    console.warn('[Memory] Possible module mocks detected:', mockModules);
  }
}

/**
 * Checks for event listeners potentially leaking in the EventEmitter
 */
export function detectEventEmitterLeaks(): void {
  if (!DEBUG_MEMORY) return;
  
  const EventEmitter = require('events');
  
  if (EventEmitter.listenerCount && EventEmitter.eventNames) {
    console.log('[Memory] Checking EventEmitter leaks:');
    const eventNames = EventEmitter.eventNames();
    eventNames.forEach(eventName => {
      const count = EventEmitter.listenerCount(eventName);
      if (count > 0) {
        console.warn(`- Event '${eventName}' has ${count} listeners`);
      }
    });
  }
}

/**
 * Cleans up any active handles from the event loop
 */
export function cleanupEventLoopHandles(): void {
  if (!process._getActiveHandles || typeof process._getActiveHandles !== 'function') {
    return;
  }
  
  // @ts-ignore - _getActiveHandles is a Node.js internal method
  const activeHandles = process._getActiveHandles();
  const ignoredTypes = ['WriteStream', 'ReadStream', 'Socket', 'Server'];
  
  if (DEBUG_MEMORY) {
    console.log('[Memory] Active handles before cleanup:');
    activeHandles.forEach(handle => {
      if (handle && handle.constructor) {
        const type = handle.constructor.name;
        if (!ignoredTypes.includes(type)) {
          console.log(`- ${type}`);
        }
      }
    });
  }
  
  // Close handles that we can safely close
  activeHandles.forEach(handle => {
    if (handle && 
        typeof handle === 'object' && 
        'close' in handle && 
        typeof handle.close === 'function' && 
        handle.constructor && 
        !ignoredTypes.includes(handle.constructor.name)) {
      try {
        handle.close();
      } catch (e) {
        // Ignore errors during cleanup
      }
    }
  });
}

/**
 * Performs a comprehensive cleanup to prevent memory leaks
 */
export function performMemoryCleanup(): void {
  // Clear all mocks
  jest.clearAllMocks();
  jest.restoreAllMocks();
  
  // Reset modules
  jest.resetModules();
  
  // Use real timers in case any fake timers are running
  jest.useRealTimers();
  
  // Clean up event loop handles
  cleanupEventLoopHandles();
  
  // Check for module leaks (for debugging)
  if (DEBUG_MEMORY) {
    detectModuleLeaks();
    detectEventEmitterLeaks();
    
    const leaks = detectGlobalLeaks();
    if (leaks.length > 0) {
      console.warn('[Memory] Possible global leaks:', leaks);
    }
    
  }
  
  // Force garbage collection
  if (global.gc) {
    global.gc();
    if (DEBUG_MEMORY) {
      const memUsage = process.memoryUsage();
      console.log(`[Memory] Heap after cleanup: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
    }
  }
} 