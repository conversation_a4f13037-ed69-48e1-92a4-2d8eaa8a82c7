import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsEnum,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { AccountFeaturesEnum } from './account-features.enum';

export class AccountDto {
  @ApiProperty()
  @IsEnum(AccountTypeEnum)
  type: AccountTypeEnum;

  @ApiProperty()
  @IsString()
  label: string;

  @ApiProperty()
  @IsString()
  ownership: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  masterAccountNumber: string;

  @ApiProperty()
  features: AccountFeaturesEnum[];

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  advisoryRate: number;
}
