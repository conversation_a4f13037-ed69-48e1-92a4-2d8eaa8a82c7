import { ApiProperty } from '@nestjs/swagger';
import { Expose, Transform } from 'class-transformer';
import { EmailTemplateNameEnum } from 'src/templates/mail/types';

export class EmailTemplateDto {
  @Expose()
  @Transform(({ obj }) => obj._id.toString())
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  emailTemplateName: string;

  @Expose()
  @ApiProperty({ enum: EmailTemplateNameEnum })
  emailTemplateType: EmailTemplateNameEnum;

  @Expose()
  @ApiProperty()
  emailTemplateContent: string;

  @Expose()
  @Transform(({ obj }) => obj.organisationId.toString())
  @ApiProperty()
  organisationId: string;

  @Expose()
  @Transform(({ obj }) => obj.advisorId?.toString())
  @ApiProperty({ required: false })
  advisorId?: string;

  @Expose()
  @ApiProperty({ description: 'Whether this email template is active' })
  isActive: boolean;

  @Expose()
  @ApiProperty()
  createdAt: Date;

  @Expose()
  @ApiProperty()
  updatedAt: Date;
} 