import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { AssetViewDto } from 'src/shared/dto/asset.dto';
import { IntegrationDto } from 'src/shared/dto/integration.dto';

export class AddressDTO {
  @ApiProperty()
  @Expose()
  zip: string;

  @ApiProperty()
  @Expose()
  state: string;

  @ApiProperty()
  @Expose()
  city: string;

  @ApiProperty()
  @Expose()
  street: string;
}

export class PersonalInfoDTO {
  @ApiProperty()
  @Expose()
  email: string;

  @ApiProperty()
  @Expose()
  phone: string;

  @ApiProperty({ type: AddressDTO })
  @Type(() => AddressDTO)
  @Expose()
  address: AddressDTO;

  @ApiProperty()
  @Expose()
  lastName: string;

  @ApiProperty()
  @Expose()
  firstName: string;

  @ApiProperty()
  @Expose()
  role: string;
}

class OrganisationDTO {
  @ApiProperty()
  @Expose()
  @Type(() => String)
  _id: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  phone: string;

  @ApiProperty({ type: AddressDTO })
  @Type(() => AddressDTO)
  @Expose()
  address: AddressDTO;

  @ApiProperty()
  @Expose()
  status: string;
}

class PermissionDTO {
  @ApiProperty()
  @Expose()
  @Type(() => String)
  id: string;

  @ApiProperty()
  @Expose()
  description: string;
}

class RoleDTO {
  @ApiProperty()
  @Expose()
  @Type(() => String)
  _id: string;

  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty({ type: [PermissionDTO] })
  @Type(() => PermissionDTO)
  @Expose()
  permissions: PermissionDTO[];

  @ApiProperty()
  @Expose()
  createdAt: Date;

  @ApiProperty()
  @Expose()
  updatedAt: Date;
}

export class AdvisorViewDTO {
  @ApiProperty()
  @Expose()
  @Type(() => String)
  _id: string;

  @ApiProperty({ type: OrganisationDTO })
  @Type(() => OrganisationDTO)
  @Expose()
  organisation: OrganisationDTO;

  @ApiProperty({ type: PersonalInfoDTO })
  @Type(() => PersonalInfoDTO)
  @Expose()
  personalInfo: PersonalInfoDTO;

  @ApiProperty()
  @Expose()
  status: string;

  @ApiProperty({ type: RoleDTO })
  @Type(() => RoleDTO)
  @Expose()
  role: RoleDTO;

  @ApiProperty({ type: [IntegrationDto] })
  @Type(() => IntegrationDto)
  @Expose()
  integrations: IntegrationDto[];

  @ApiProperty({ type: [AssetViewDto] })
  @Type(() => AssetViewDto)
  @Expose()
  assets: AssetViewDto[];

  @ApiProperty()
  @Expose()
  crmId: number;

  @ApiProperty()
  @Expose()
  createdAt: Date;

  @ApiProperty()
  @Expose()
  updatedAt: Date;
}

export class AdvisorViewListItemDTO {
  @ApiProperty()
  @Expose()
  @Type(() => String)
  _id: string;

  @ApiProperty()
  @Expose()
  @Type(() => OrganisationDTO)
  organisation: OrganisationDTO;

  @ApiProperty({ type: PersonalInfoDTO })
  @Type(() => PersonalInfoDTO)
  @Expose()
  personalInfo: PersonalInfoDTO;

  @ApiProperty()
  @Expose()
  status: string;

  @ApiProperty()
  @Expose()
  role: string;

  @ApiProperty({ type: [IntegrationDto] })
  @Type(() => IntegrationDto)
  @Expose()
  integrations: IntegrationDto[];

  @ApiProperty({ type: [AssetViewDto] })
  @Type(() => AssetViewDto)
  @Expose()
  assets: AssetViewDto[];

  @ApiProperty()
  @Expose()
  crmId: number;

  @ApiProperty()
  @Expose()
  createdAt: Date;

  @ApiProperty()
  @Expose()
  updatedAt: Date;

  @ApiProperty()
  @Expose()
  clientsCount: number;
}

export class AdvisorViewListDTO {
  @ApiProperty()
  @Expose()
  @Type(() => AdvisorViewListItemDTO)
  result: AdvisorViewListItemDTO[];

  @ApiProperty()
  @Expose()
  total: number;
}
