import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

export class PrimaryBeneficiariesPageVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      beneficiaryCount: {
        description: 'Number of primary beneficiaries',
        type: VariableType.NUMBER,
        example: 2,
      },
      beneficiaryNames: {
        description: 'Names of all primary beneficiaries',
        type: VariableType.ARRAY,
        example: ['<PERSON>', '<PERSON>'],
      },
      beneficiaryRelationships: {
        description: 'Relationships to the client',
        type: VariableType.ARRAY,
        example: ['Spouse', 'Child'],
      },
      beneficiaryPercentages: {
        description: 'Percentage allocations for each beneficiary',
        type: VariableType.ARRAY,
        example: [60, 40],
      },
      totalAllocation: {
        description: 'Total percentage allocated (should equal 100%)',
        type: VariableType.PERCENTAGE,
        example: 100,
      },
    };
  }
}