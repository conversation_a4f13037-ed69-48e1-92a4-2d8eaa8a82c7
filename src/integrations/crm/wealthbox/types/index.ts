import { WealthboxEmailAddressKind } from 'src/integrations/crm/wealthbox/dto/email.dto';
import { WealthboxPhoneNumberKind } from 'src/integrations/crm/wealthbox/dto/phone.dto';

export type DateTime = string;

export enum WealthboxContactType {
  Person = 'Person',
  Household = 'Household',
  Organization = 'Organization',
  Trust = 'Trust',
}

export enum WealthboxGender {
  Female = 'Female',
  Male = 'Male',
  NonBinary = 'Non-binary',
  Unknown = 'Unknown',
}

export enum WealthboxContactSource {
  Referral = 'Referral',
  Conference = 'Conference',
  DirectMail = 'Direct Mail',
  ColdCall = 'Cold Call',
  Other = 'Other',
}

export enum WealthboxContactStatus {
  Active = 'Active',
  Inactive = 'Inactive',
}

export enum WealthboxMaritalStatus {
  Married = 'Married',
  Single = 'Single',
  Divorced = 'Divorced',
  Widowed = 'Widowed',
  LifePartner = 'Life Partner',
  Separated = 'Separated',
  Unknown = 'Unknown',
}

export enum WealthboxInvestmentObjective {
  AggressiveGrowth = 'Aggressive Growth',
  Growth = 'Growth',
  Income = 'Income',
  SafetyOfPrincipal = 'Safety of Principal',
}

export enum WealthboxTimeHorizon {
  ShortTerm = 'Short Term',
  Intermediate = 'Intermediate',
  LongTerm = 'Long Term',
}

export enum WealthboxRiskTolerance {
  Low = 'Low',
  Moderate = 'Moderate',
  HighRisk = 'High Risk',
}

export enum WealthboxAddressKind {
  Work = 'Work',
  Home = 'Home',
  Mobile = 'Mobile',
  Vacation = 'Vacation',
  Fax = 'Fax',
  Other = 'Other',
}

export interface WealthboxOccupation {
  name: string;
  start_date: DateTime;
}

export interface WealthboxDriversLicense {
  number: string;
  state: string;
  issued_date: DateTime;
  expires_date: DateTime;
}

export interface WealthboxHouseholdMember {
  id: number;
  first_name: string;
  last_name: string;
  title: string; // This can be an export enum if you have specific titles
  type: WealthboxContactType;
}

export interface WealthboxHousehold {
  name: string;
  title: string; // This can be an export enum if you have specific titles
  id: number;
  members: WealthboxHouseholdMember[];
}

export interface WealthboxTag {
  id: number;
  name: string;
}

export interface WealthboxMailingAddress {
  street_line_1: string;
  street_line_2: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
  principal: boolean;
  kind: WealthboxAddressKind;
  id?: number;
  address: string;
  destroy?: boolean;
}

export interface WealthboxEmailAddress {
  id?: number;
  address: string;
  principal: boolean;
  kind: WealthboxEmailAddressKind;
  destroy?: boolean;
}

export interface WealthboxPhoneNumber {
  id?: number;
  address: string;
  principal: boolean;
  extension?: string;
  kind: WealthboxPhoneNumberKind;
  destroy?: boolean;
}

export interface WealthboxWebsite {
  id?: number;
  address: string;
  principal: boolean;
  kind: string; // This can be an export enum if you have specific website types
}

export interface CustomField {
  id: number;
  name: string;
  value: string;
  document_type: string; // This can be an export enum if you have specific document types
  field_type: string; // This can be an export enum if you have specific field types
}

export interface ContactRoleAssignment {
  id: number;
  type: string;
  name: string;
}

export interface ContactRoleValue {
  id: number;
  name?: string;
  value: number;
  assigned_to?: ContactRoleAssignment;
}
