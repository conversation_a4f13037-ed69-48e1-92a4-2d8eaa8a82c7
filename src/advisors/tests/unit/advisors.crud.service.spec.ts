import { Test } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { HttpException, HttpStatus, Scope } from '@nestjs/common';
import mongoose, { FilterQuery, Model } from 'mongoose';
import { CRMService } from 'src/integrations/crm/crm.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { CognitoService } from 'src/shared/services/cognito.service';
import { mockRbacService } from 'src/rbac/mocks/rbac.service.mock';
import { RbacService } from 'src/rbac/rbac.service';
import { AuthService } from 'src/auth/auth.service';
import { HttpService } from '@nestjs/axios';
import { ClsService } from 'nestjs-cls';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { Advisor } from 'src/advisors/schemas/advisors.schema';
import { UpdateAdvisorDto } from 'src/advisors/dto/update-advisor.dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { organisationMock } from 'src/organisations/tests/mocks/organisation.mock';
import { createAdvisorDtoMock } from 'src/advisors/tests/mocks/create-advisor.dto.mock';
import { mockCognitoService } from 'src/shared/services/tests/mocks/cognito.service.mock';
import {
  advisorMockObj,
  expectedDetails,
} from 'src/advisors/tests/mocks/advisor.mock';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';

jest.mock('aws-sdk', () => {
  const mKMS = {
    encrypt: jest.fn().mockReturnValue({
      promise: jest.fn().mockReturnValue({ CiphertextBlob: 'encrypted' }),
    }),
    decrypt: jest.fn().mockReturnValue({
      promise: jest.fn().mockReturnValue({ Plaintext: 'decrypted' }),
    }),
    promise: jest.fn(),
  };
  return { KMS: jest.fn(() => mKMS) };
});

describe.skip('AdvisorsCrudService', () => {
  const mockExec = jest.fn();
  const today = new Date();

  let model: jest.Mocked<Model<Advisor>>;

  let service: AdvisorsCrudService;
  let orgService: OrganisationsService;
  let cognitoService: CognitoService;
  let transactionManager: TransactionManager;

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        AdvisorsCrudService,
        {
          provide: CognitoService,
          useValue: mockCognitoService,
        },
        {
          provide: getModelToken(Advisor.name),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn().mockImplementation(() => {
              return {
                skip: jest.fn().mockReturnThis(),
                limit: jest.fn().mockReturnThis(),
                exec: mockExec,
              };
            }),
            create: jest.fn().mockResolvedValue(advisorMockObj),
            updateOne: jest.fn(),
            deleteOne: jest.fn(),
            countDocuments: jest.fn(),
          },
        },
        {
          provide: OrganisationsService,
          useValue: {
            findOne: jest.fn().mockResolvedValue({} as Organisation),
          },
        },
        {
          provide: AuthService,
          useValue: {
            inviteUser: jest
              .fn()
              .mockResolvedValue({ User: { Username: '<EMAIL>' } } as any),
            deleteUser: jest.fn().mockResolvedValue({}),
          },
        },
        {
          provide: RbacService,
          useValue: mockRbacService,
        },
        {
          provide: CRMService,
          useValue: {},
        },
        {
          provide: HttpService,
          useValue: {
            axiosRef: {
              get: jest.fn(),
              post: jest.fn(),
              put: jest.fn(),
            },
          },
        },
        {
          provide: ClsService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: TransactionManager,
          useValue: {
            currentSession: jest.fn().mockReturnValue({}),
          },
        },
      ],
    }).compile();

    model = moduleRef.get<jest.Mocked<Model<Advisor>>>(
      getModelToken(Advisor.name),
    );

    service = moduleRef.get<AdvisorsCrudService>(AdvisorsCrudService);
    orgService = moduleRef.get<OrganisationsService>(OrganisationsService);
    cognitoService = moduleRef.get<CognitoService>(CognitoService);
    transactionManager = moduleRef.get<TransactionManager>(TransactionManager);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should fail if no organisation found', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce(null);
      jest.spyOn(orgService, 'findOne').mockResolvedValueOnce(null);

      await expect(service.create(createAdvisorDtoMock)).rejects.toEqual(
        new HttpException('Organisation not found.', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw error if auth invite user fails', async () => {
      jest
        .spyOn(orgService, 'findOne')
        .mockResolvedValueOnce(organisationMock as any);

      jest.spyOn(mockCognitoService, 'adminCreateUser').mockResolvedValueOnce({
        User: { Username: '<EMAIL>' },
      } as any);

      jest
        .spyOn(model, 'create')
        .mockRejectedValueOnce(new Error('Mock Error'));

      await expect(() =>
        service.create(createAdvisorDtoMock),
      ).rejects.toThrow();
    });

    it('should throw error if atlas advisor creation fails', async () => {
      jest
        .spyOn(orgService, 'findOne')
        .mockResolvedValueOnce(organisationMock as any);
      jest
        .spyOn(model, 'create')
        .mockRejectedValueOnce(
          new HttpException(
            'Atlas advisor creation failed.',
            HttpStatus.INTERNAL_SERVER_ERROR,
          ),
        );

      await expect(service.create(createAdvisorDtoMock)).rejects.toEqual(
        new HttpException(
          'Atlas advisor creation failed.',
          HttpStatus.INTERNAL_SERVER_ERROR,
        ),
      );
    });

    it('should successfully invite a user and create an advisor', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce(null);
      jest.spyOn(orgService, 'findOne').mockResolvedValueOnce({} as any);
      jest
        .spyOn(mockRbacService, 'findOneRole')
        .mockResolvedValueOnce({ name: 'admin' });
      jest.spyOn(mockCognitoService, 'adminCreateUser').mockResolvedValueOnce({
        User: { Username: '<EMAIL>' },
      } as any);
      jest.spyOn(model, 'create').mockResolvedValueOnce(advisorMockObj as any);

      const result = await service.create(createAdvisorDtoMock);

      expect(mockCognitoService.adminCreateUser).toHaveBeenCalled();
      expect(model.create).toHaveBeenCalled();
      expect(result.advisor._id).toBe(advisorMockObj._id);
      expect(result.message).toBe('New advisor created!');
    });

    it('should delete a user if advisor creation fails', async () => {
      jest
        .spyOn(orgService, 'findOne')
        .mockResolvedValueOnce(organisationMock as any);
      jest
        .spyOn(mockCognitoService, 'adminCreateUser')
        .mockResolvedValueOnce({ User: { Username: '<EMAIL>' } } as any);
      jest
        .spyOn(mockCognitoService, 'adminDeleteUser')
        .mockResolvedValueOnce({} as any);

      jest
        .spyOn(model, 'create')
        .mockRejectedValueOnce(
          new HttpException(
            'Atlas advisor creation failed.',
            HttpStatus.INTERNAL_SERVER_ERROR,
          ),
        );

      try {
        await service.create(createAdvisorDtoMock);
      } catch (e) {
        expect(mockCognitoService.adminDeleteUser).toHaveBeenCalled();
        expect(e).toBeInstanceOf(HttpException);
        expect(e.status).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      }
    });
    it('should throw error if advisor creation fails due to existing email', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce(advisorMockObj as any);

      await expect(service.create(createAdvisorDtoMock)).rejects.toEqual(
        new HttpException('Advisor already exists.', HttpStatus.CONFLICT),
      );
    });

    it('should create a new advisor', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce(null);

      const result = await service.create(createAdvisorDtoMock);

      expect(result.advisor._id).toBe(advisorMockObj._id);
      expect(result.message).toBe('New advisor created!');
      expect(result).toStrictEqual({
        message: 'New advisor created!',
        advisor: advisorMockObj,
      });
    });
  });

  describe('findAll', () => {
    it('should return all advisors', async () => {
      const result = [...Array(3).keys()].map((key: number) => ({
        ...advisorMockObj,
        _id: key.toString(),
      }));
      const totalResults = result.length;

      mockExec.mockResolvedValueOnce(result);

      const orgId = advisorMockObj.organisation.organisationId;
      const query = {
        limit: 10,
        page: 1,
      };

      jest.spyOn(model, 'countDocuments').mockResolvedValueOnce(totalResults);

      await expect(
        service.findAll(query, {
          'organisation.organisationId': new mongoose.Types.ObjectId(orgId),
        }),
      ).resolves.toStrictEqual({
        result,
        totalResults,
      });
    });
  });

  describe('findOne', () => {
    it('should throw an error if advisor not found', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce(null);

      try {
        await service.findOne({ _id: advisorMockObj._id });
      } catch (e) {
        expect(e).toBeInstanceOf(HttpException);
        expect(e.status).toBe(HttpStatus.NOT_FOUND);
      }
    });

    it('should return an advisor', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce(advisorMockObj);
      jest.spyOn(mockRbacService, 'findOneRole').mockResolvedValueOnce({
        name: 'CompanyAdmin',
      });

      const result = await service.findOne({ _id: advisorMockObj._id });

      expect(result).toEqual({
        ...advisorMockObj,
        role: expect.any(Object),
        toObject: expect.any(Function),
      });
    });
  });

  describe('update', () => {
    const dto: UpdateAdvisorDto = {
      personalInfo: {
        ...advisorMockObj.personalInfo,
        email: expectedDetails.newEmail,
        firstName: expectedDetails.firstName,
        lastName: expectedDetails.lastName,
        phone: expectedDetails.phone,
      },
    };

    const filter: FilterQuery<Advisor> = {
      _id: advisorMockObj._id,
    };

    const updatedAdvisor = {
      ...advisorMockObj,
      personalInfo: {
        ...advisorMockObj.personalInfo,
        email: expectedDetails.newEmail,
        firstName: expectedDetails.firstName,
        lastName: expectedDetails.lastName,
        phone: expectedDetails.phone,
      },
      organisationId: expectedDetails.organisationId,
      role: expectedDetails.role,
      updatedAt: today,
    };

    it('should throw an error if advisor not found', async () => {
      jest.spyOn(model, 'updateOne').mockResolvedValueOnce(null);
      await expect(service.update(filter, dto)).rejects.toThrow(
        new HttpException('Advisor not found.', HttpStatus.NOT_FOUND),
      );

      try {
        await service.update({ _id: advisorMockObj._id }, dto);
      } catch (e) {
        expect(e).toBeInstanceOf(HttpException);
        expect(e.status).toBe(HttpStatus.NOT_FOUND);
      }
    });

    it('should update email in Cognito if the email has changed', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce(advisorMockObj);

      const updateEmailInCognitoSpy = jest.spyOn(
        service as any,
        'updateEmailInCognito',
      );

      const upsertUserInCognitoSpy = jest.spyOn(cognitoService, 'upsertUser');

      await service.update(filter, dto);

      expect(updateEmailInCognitoSpy).toHaveBeenCalledWith(expectedDetails);
      expect(upsertUserInCognitoSpy).toHaveBeenCalledWith(
        {
          email: expectedDetails.newEmail,
          firstName: expectedDetails.firstName,
          lastName: expectedDetails.lastName,
          phoneNumber: expectedDetails.phone,
          organisationId: expectedDetails.organisationId,
          role: expectedDetails.role,
        },
        expectedDetails.oldEmail,
      );
    });

    it('should validate and update the role if provided', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce(advisorMockObj);

      const validateRoleSpy = jest
        .spyOn(service as any, 'validateRole')
        .mockImplementationOnce(() => RolesEnum.CompanyAdmin);

      await service.update(filter, {
        ...dto,
        personalInfo: {
          ...dto.personalInfo,
          email: advisorMockObj.personalInfo.email,
        },
        role: RolesEnum.CompanyAdmin,
      });

      expect(validateRoleSpy).toHaveBeenCalledWith(advisorMockObj.role);
      expect(validateRoleSpy).toHaveReturnedWith(RolesEnum.CompanyAdmin);
    });

    it('should throw error if role not valid', async () => {
      const invalidRole: any = 'invalid role';

      jest.spyOn(model, 'findOne').mockResolvedValueOnce(advisorMockObj);

      const validateRoleSpy = jest
        .spyOn(service as any, 'validateRole')
        .mockImplementationOnce(() => {
          throw new HttpException('Role not found.', HttpStatus.NOT_FOUND);
        });

      await expect(
        service.update(filter, {
          ...dto,
          personalInfo: {
            ...dto.personalInfo,
            email: advisorMockObj.personalInfo.email,
          },
          role: invalidRole,
        }),
      ).rejects.toEqual(
        new HttpException('Role not found.', HttpStatus.NOT_FOUND),
      );

      expect(validateRoleSpy).toHaveBeenCalledWith(invalidRole);
    });

    it('should update an advisor', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce(advisorMockObj);

      jest
        .spyOn(model, 'updateOne')
        .mockResolvedValueOnce(updatedAdvisor as any);

      await expect(
        service.update({ _id: advisorMockObj._id }, dto),
      ).resolves.toStrictEqual({
        message: 'Advisor updated.',
        query: {
          _id: advisorMockObj._id,
        },
      });
    });
  });

  describe('remove', () => {
    it('should throw an error if advisor not found', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce(null);

      try {
        await service.remove({ _id: advisorMockObj._id });
      } catch (e) {
        expect(e).toBeInstanceOf(HttpException);
        expect(e.status).toBe(HttpStatus.NOT_FOUND);
      }
    });

    it('should remove an advisor', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce(advisorMockObj);

      const result = await service.remove({ _id: advisorMockObj._id });

      expect(result).toStrictEqual({
        message: 'Advisor removed.',
        query: {
          _id: advisorMockObj._id,
        },
      });
    });
  });
});
