import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

export class VipPageVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      vipStatus: {
        description: 'VIP status indicator (true/false)',
        type: VariableType.BOOLEAN,
        example: true,
      },
      vipLevel: {
        description: 'VIP level or tier',
        type: VariableType.STRING,
        example: 'Gold',
        isConditional: true,
      },
      specialInstructions: {
        description: 'Special handling instructions for VIP clients',
        type: VariableType.STRING,
        example: 'Priority processing required',
        isConditional: true,
      },
    };
  }
}