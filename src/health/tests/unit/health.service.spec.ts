import { Test, TestingModule } from '@nestjs/testing';
import {
  HealthCheckService,
  HttpHealthIndicator,
  TerminusModule,
} from '@nestjs/terminus';
import { HttpModule } from '@nestjs/axios';
import { HealthService } from 'src/health/health.service';

describe.skip('HealthService', () => {
  let healthService: HealthService;
  let healthCheckService: HealthCheckService;
  let checkFunction: () => Promise<unknown>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [HttpModule, TerminusModule],
      providers: [
        HealthService,
        HttpHealthIndicator,
        {
          provide: HealthCheckService,
          useFactory: () => ({
            check: jest.fn().mockImplementation((checks) => {
              checkFunction = checks[0];
              return Promise.resolve({
                status: 'ok',
                details: {
                  google: { status: 'up' },
                },
              });
            }),
          }),
        },
        {
          provide: 'TERMINUS_LOGGER', // Replace 'TERMINUS_LOGGER' with the appropriate logger token
          useValue: null, // Replace null with the actual logger implementation if necessary
        },
      ],
    }).compile();

    healthService = module.get<HealthService>(HealthService);
    healthCheckService = module.get<HealthCheckService>(HealthCheckService);
  });

  it('should be defined', () => {
    expect(healthService).toBeDefined();
  });

  it('should perform a health check', async () => {
    jest.spyOn(healthCheckService, 'check');
    const res = await healthService.checkHealth();
    expect(res).toEqual({
      status: 'ok',
      details: {
        google: { status: 'up' },
      },
    });
    await checkFunction();
  });
});
