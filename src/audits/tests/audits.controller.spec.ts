import { Test, TestingModule } from '@nestjs/testing';
import { AuditsController } from '../audits.controller';
import { AuditsService } from '../audits.service';
import { CreateAuditDto } from '../dto/create-audit.dto';
import mongoose from 'mongoose';
import { AuditSource } from 'src/audits/types/audit';

describe('AuditsController', () => {
  let auditsController: AuditsController;
  let auditsService: jest.Mocked<AuditsService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuditsController],
      providers: [
        {
          provide: AuditsService,
          useValue: {
            create: jest.fn(),
            findAll: jest.fn(),
            findOne: jest.fn(),
          },
        },
      ],
    }).compile();

    auditsController = module.get<AuditsController>(AuditsController);
    auditsService = module.get<AuditsService>(
      AuditsService,
    ) as jest.Mocked<AuditsService>;
  });

  describe('create', () => {
    it('should call AuditsService create method', async () => {
      const dto: CreateAuditDto = {
        message: 'Test message',
        context: AuditSource.BACKEND,
        level: 'error',
        organisationId: new mongoose.Types.ObjectId(),
      };
      await auditsController.create(dto);
      expect(auditsService.create).toHaveBeenCalledWith(dto);
    });

    it('should handle service exception', async () => {
      const dto: CreateAuditDto = {
        message: 'Test message',
        context: AuditSource.BACKEND,
        level: 'error',
        organisationId: new mongoose.Types.ObjectId(),
      };
      auditsService.create.mockRejectedValue(
        new Error('Failed to create audit'),
      );
      await expect(auditsController.create(dto)).rejects.toThrow(
        'Failed to create audit',
      );
    });
  });

  describe('findAll', () => {
    it('should call AuditsService findAll method', async () => {
      await auditsController.findAll(null);
      expect(auditsService.findAll).toHaveBeenCalled();
    });

    it('should handle service exception', async () => {
      auditsService.findAll.mockRejectedValue(
        new Error('Failed to fetch audits'),
      );
      await expect(auditsController.findAll(null)).rejects.toThrow(
        'Failed to fetch audits',
      );
    });
  });

  describe('findOne', () => {
    it('should call AuditsService findOne method', async () => {
      const id = 'test id';
      await auditsController.findOne(id);
      expect(auditsService.findOne).toHaveBeenCalledWith(id);
    });

    it('should handle service exception', async () => {
      const id = 'test id';
      auditsService.findOne.mockRejectedValue(
        new Error('Failed to fetch audit'),
      );
      await expect(auditsController.findOne(id)).rejects.toThrow(
        'Failed to fetch audit',
      );
    });
  });
});
