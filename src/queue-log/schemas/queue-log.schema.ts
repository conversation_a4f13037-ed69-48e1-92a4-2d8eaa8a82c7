import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { QueueLogStatusEnum } from '../enums/queue-log-status.enum';
import { ResourceOptionsEnum } from 'src/shared/types/queue-log/queue';
import { Factory } from 'nestjs-seeder';

@Schema({
  // Automatically manage createdAt and updatedAt
  timestamps: true,
})
export class QueueLog extends Document {
  @Prop({ required: true })
  jobId: string;

  @Factory(ResourceOptionsEnum.CLIENT)
  @Prop({ required: true, enum: ResourceOptionsEnum })
  resource: ResourceOptionsEnum;

  @Prop({ required: true })
  queueName: string;

  @Prop({ type: String, enum: QueueLogStatusEnum, required: true })
  status: QueueLogStatusEnum;

  @Prop({ type: String, index: true, sparse: true })
  clientId?: string;

  @Prop()
  result?: string;

  @Prop()
  error?: string;

  @Prop({ type: Date })
  timestamp: Date;

  @Prop({ default: 0 })
  retryCount: number;

  @Prop()
  lastRetryTimestamp: Date;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;

  @Prop({ type: String, required: true })
  organisationId: string;
}

export const QueueLogSchema = SchemaFactory.createForClass(QueueLog);

// Fast queries on jobId since it's used frequently for lookups
QueueLogSchema.index({ jobId: 1 });

// Fast queries on organisationId since it's used in most queries
QueueLogSchema.index({ organisationId: 1 });

// Compound index for pagination and filtering
QueueLogSchema.index({ organisationId: 1, timestamp: -1 });

// TTL Indexes for data retention
// Failed jobs - keep for 60 days
QueueLogSchema.index(
  { timestamp: 1 },
  {
    expireAfterSeconds: 60 * 24 * 60 * 60,
    partialFilterExpression: { status: QueueLogStatusEnum.FAILED }
  }
);

// Completed jobs - keep for 15 days
QueueLogSchema.index(
  { timestamp: 1 },
  {
    expireAfterSeconds: 15 * 24 * 60 * 60,
    partialFilterExpression: { status: QueueLogStatusEnum.COMPLETED }
  }
);

// Retrying jobs - keep for 30 days
QueueLogSchema.index(
  { timestamp: 1 },
  {
    expireAfterSeconds: 30 * 24 * 60 * 60,
    partialFilterExpression: { status: QueueLogStatusEnum.RETRYING }
  }
);

// Default TTL for any other status - 30 days
QueueLogSchema.index(
  { timestamp: 1 },
  {
    expireAfterSeconds: 30 * 24 * 60 * 60
  }
);