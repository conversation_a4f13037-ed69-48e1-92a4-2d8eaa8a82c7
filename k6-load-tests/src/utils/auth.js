import http from 'k6/http';
import { BASE_URL, USER_EMAIL, USER_PASSWORD } from "../config/constants/shared.js";

export function getAuthToken() {
  const loginPayload = {
    email: USER_EMAIL,
    password: USER_PASSWORD
  };

  console.log('Attempting login with:', loginPayload.email);

  const loginResponse = http.post(
    `${BASE_URL}/auth/login`,
    JSON.stringify(loginPayload),
    {
      headers: { 'Content-Type': 'application/json' }
    }
  );

  if (loginResponse.status !== 200) {
    console.error('Login failed:', loginResponse.body);
    return null;
  }

  const setCookieHeader = loginResponse.headers['Set-Cookie'];
  const accessTokenMatch = setCookieHeader.match(/access_token=([^;]+)/);
  const body = JSON.parse(loginResponse.body);
  if (!accessTokenMatch) {
    console.error('No access token found in cookies');
    return null;
  }

  return {
    token: accessTokenMatch[1],
    organisationId: body.organisationId,
    advisorId: body.advisorId
  };
}