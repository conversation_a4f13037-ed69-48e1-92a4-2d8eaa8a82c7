files:
  "/etc/datadog-agent/datadog.yaml": 
    mode: "000640"
    owner: root # will be changed to dd-agent after the installation
    group: root
    content: |
      # Placeholder for the script to replace
      api_key: placeholder_key
      site: us5.datadoghq.com
      logs_enabled: true
      logs_config:
        container_collect_all: true
      process_config:
        process_collection:
          enabled: true
  "/etc/datadog-agent/conf.d/nginx.d/conf.yaml":
    mode: "000640"
    owner: root # will be changed to dd-agent after the installation
    group: root
    content: |
      logs:
        - type: file
          service: nginx-access
          path: /var/log/nginx/access.log
          source: nginx
          sourcecategory: http_web_access
        - type: file
          service: nginx-error
          path: /var/log/nginx/error.log
          source: nginx
          sourcecategory: http_web_access
  "/datadog_install_script.sh": 
    mode: "000700"
    owner: root
    group: root
    source: https://s3.amazonaws.com/dd-agent/scripts/install_script_agent7.sh
  "/scripts/update_datadog_key.sh":
    mode: "000700"
    owner: root
    group: root
    content: |
      #!/bin/bash
      export DD_API_KEY=$(aws ssm get-parameter --name "/onbord/backend/DD_API_KEY" --with-decryption --region us-east-1 --output text --query Parameter.Value)
      sed -i "s/placeholder_key/${DD_API_KEY}/" /etc/datadog-agent/datadog.yaml

container_commands:
  05update_datadog_key:
    command: "/scripts/update_datadog_key.sh"

  06setup_datadog:
    command: "DD_API_KEY=unused /datadog_install_script.sh; sed -i 's/ install_script/ ebs_install_script/' /etc/datadog-agent/install_info"
  
  