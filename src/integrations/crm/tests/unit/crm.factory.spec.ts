import { Test, TestingModule } from '@nestjs/testing';
import { CRMFactory } from 'src/integrations/crm/crm.factory';
import { CRMEnum, CRMClasses } from 'src/shared/types/integrations';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { Credentials } from 'src/auth/auth.types';
import mongoose from 'mongoose';
import { Logger } from 'winston';

describe('CRMFactory', () => {
  describe('createCRM', () => {
    it('should create a new instance of Redtail CRM', () => {
      const credentials: Credentials = {
        username: 'test',
        password: 'test',
      };
      
      // Create a proper organisation object with _id
      const organisationId = new mongoose.Types.ObjectId();
      const organisation = {
        _id: organisationId,
      };
      
      const CRMInstance = CRMFactory.createCRM(
        {
          _id: new mongoose.Types.ObjectId(),
        } as any,
        organisation as any, // organisation with proper _id
        'Redtail',
        credentials,
        {} as any,
        {} as any,
        {} as any,
        {} as Logger,
      );
      expect(CRMInstance).toBeInstanceOf(RedtailCRM);
    });

    it('should throw an error when an unsupported CRM type is provided', () => {
      const credentials: Credentials = {
        username: 'test',
        password: 'test',
      };
      
      // Create a proper organisation object with _id
      const organisationId = new mongoose.Types.ObjectId();
      const organisation = {
        _id: organisationId,
      };
      
      expect(() =>
        CRMFactory.createCRM(
          {
            _id: new mongoose.Types.ObjectId(),
          } as any,
          organisation as any, // organisation with proper _id
          'UnsupportedCRMType' as keyof typeof CRMEnum,
          credentials,
          {} as any,
          {} as any,
          {} as any,
          {} as Logger,
        ),
      ).toThrow('Unsupported CRM type: UnsupportedCRMType');
    });
  });
});
