# Interview V2 Integration Plan

## Executive Summary

This document outlines the integration plan for the new Interview V2 schema that introduces branching workflows, streaming architecture, and template versioning while maintaining complete isolation from the existing V1 system. The V2 APIs will be consumed by a new frontend application, with clients clearly separated between V1 and V2 systems.

## Core Principles

1. **Complete Isolation**: V2 schemas are entirely separate from V1, using different collections and models
2. **Zero Impact**: V1 functionality remains untouched and continues to operate normally
3. **Frontend-Based Routing**: New frontend uses V2 APIs exclusively, old frontend continues with V1
4. **Client Version Tracking**: Clients are marked as V1 or V2 based on which system created them
5. **Account Data Integrity**: Careful handling of account relationships, beneficiaries, and CRM integrations

## Architecture Overview

### Schema Separation

```
V1 Collections:                    V2 Collections:
- interviews                       - interviews_v2
- interviewtemplates              - interviewtemplates_v2
                                  - interviewpageinstances_v2
```

### Service Architecture

```
V1 Flow:
InterviewsV1Controller → InterviewsService → InterviewsV1Service → MongoDB

V2 Flow:
InterviewsV2Controller → InterviewV2Facade → V2 Services → MongoDB/CRM
                                              ├── InterviewV2CoreService
                                              ├── InterviewV2StreamingService
                                              ├── InterviewV2BranchingService
                                              └── InterviewV2AccountService
```

## Phase 1: Schema Implementation (Week 1)

### 1.1 Directory Structure

```
src/
├── interviews/
│   ├── schemas/
│   │   ├── interview.schema.ts (v1 - untouched)
│   │   └── v2/
│   │       ├── interview-v2.schema.ts
│   │       ├── interview-page-instance-v2.schema.ts
│   │       └── interview-account-instance-v2.schema.ts
├── interview-templates/
│   ├── schemas/
│   │   ├── interview.template.ts (v1 - untouched)
│   │   └── v2/
│   │       ├── interview-template-v2.schema.ts
│   │       ├── interview-page-def-v2.schema.ts
│   │       ├── interview-account-instance-v2.schema.ts
│   │       └── enums/
│   │           ├── template-status.enum.ts
│   │           ├── page-type.enum.ts
│   │           └── branch-rule.interface.ts
```

### 1.2 Page Type Enum

```typescript
// src/interview-templates/schemas/v2/enums/page-type.enum.ts
export enum PageTypeEnum {
  QUESTION = 'QUESTION',
  NAME = 'NAME',
  ADDRESS = 'ADDRESS',
  EMPLOYMENT = 'EMPLOYMENT',
  BENEFICIARY_PRIMARY = 'BENEFICIARY_PRIMARY',
  BENEFICIARY_CONTINGENT = 'BENEFICIARY_CONTINGENT',
  DOCUMENT_UPLOAD = 'DOCUMENT_UPLOAD',
  CUSTOM_QUESTION = 'CUSTOM_QUESTION',
}
```

### 1.3 Client Version Tracking

```typescript
// src/clients/schemas/clients.schema.ts (extended)
@Schema()
export class Client extends Document {
  // ... existing fields ...
  
  @Prop({ type: String, enum: ['v1', 'v2'], default: 'v1', index: true })
  apiVersion!: 'v1' | 'v2';
}
```

### 1.4 V2 Interview Schema

```typescript
// src/interviews/schemas/v2/interview-v2.schema.ts
@Schema({ timestamps: true, collection: 'interviews_v2' })
export class InterviewV2 extends Document {
  @Prop({ type: SchemaTypes.ObjectId, ref: 'Client', index: true })
  client!: string;

  @Prop({ type: SchemaTypes.ObjectId, ref: 'InterviewV2', default: null })
  rootInterview!: string | null;

  @Prop({ type: String, default: 'main', index: true })
  branch!: string;

  @Prop({ type: Boolean, default: false })
  sealed!: boolean;

  @Prop({ type: SchemaTypes.ObjectId, ref: 'InterviewTemplateV2', required: true })
  template!: string;

  @Prop({ type: String, required: true, unique: true, index: true })
  crmObjectId!: string;

  @Prop({ type: Boolean, default: false })
  isComplete!: boolean;

  @Prop({ type: Boolean, default: true })
  isPrimary!: boolean;
}

export const InterviewV2Schema = SchemaFactory.createForClass(InterviewV2);
InterviewV2Schema.index({ rootInterview: 1, branch: 1 }, { unique: true, sparse: true });
InterviewV2Schema.index({ crmObjectId: 1 }, { unique: true });
```

### 1.5 Interview Page Instance Schema

```typescript
// src/interviews/schemas/v2/interview-page-instance-v2.schema.ts
@Schema({ timestamps: true, versionKey: 'revision', collection: 'interviewpageinstances_v2' })
export class InterviewPageInstanceV2 extends Document {
  @Prop({ type: SchemaTypes.ObjectId, ref: 'InterviewV2', index: true })
  interviewId!: string;

  @Prop({ type: SchemaTypes.ObjectId, ref: 'InterviewTemplateV2.pages', index: true })
  pageTemplateId!: string;

  @Prop({ type: String, enum: PageTypeEnum, required: true })
  pageType!: PageTypeEnum;

  @Prop({ type: Number })
  order!: number;

  @Prop({ type: String, enum: InterviewPageStatusEnum, default: InterviewPageStatusEnum.UNANSWERED })
  status!: InterviewPageStatusEnum;

  @Prop({ type: Date, default: null })
  crmSyncedAt!: Date | null;

  // Account association for beneficiary pages
  @Prop({ type: String, sparse: true })
  accountId?: string;

  // Validation state
  @Prop({ type: Object })
  validationErrors?: Record<string, string>;
}

export const InterviewPageInstanceV2Schema = SchemaFactory.createForClass(InterviewPageInstanceV2);
InterviewPageInstanceV2Schema.index({ interviewId: 1, pageTemplateId: 1 }, { unique: true });
InterviewPageInstanceV2Schema.index({ pageTemplateId: 1 });
```

### 1.6 Interview Account Instance Schema

```typescript
// src/interviews/schemas/v2/interview-account-instance-v2.schema.ts
@Schema({ timestamps: true, collection: 'interview_account_instances_v2' })
export class InterviewAccountInstanceV2 extends Document {
  @Prop({ type: SchemaTypes.ObjectId, ref: 'InterviewV2', index: true })
  interviewId!: string;
  
  @Prop({ required: true })
  accountId!: string;
  
  @Prop({ required: true })
  accountType!: string;
  
  @Prop({ type: Boolean, default: false })
  hasBeneficiaries!: boolean;
  
  @Prop({ type: SchemaTypes.ObjectId, ref: 'InterviewPageInstanceV2' })
  primaryBeneficiaryPageId?: string;
  
  @Prop({ type: SchemaTypes.ObjectId, ref: 'InterviewPageInstanceV2' })
  contingentBeneficiaryPageId?: string;
}

export const InterviewAccountInstanceV2Schema = SchemaFactory.createForClass(InterviewAccountInstanceV2);
InterviewAccountInstanceV2Schema.index({ interviewId: 1, accountId: 1 }, { unique: true });
```

## Phase 2: Client Creation and Routing (Week 2)

### 2.1 V2 Client Service

```typescript
// src/clients/services/v2/clients-v2.service.ts
@Injectable()
export class ClientsV2Service {
  async create(dto: CreateClientV2Dto): Promise<Client> {
    const client = await this.clientModel.create({
      ...dto,
      apiVersion: 'v2',  // Mark as V2 client
    });
    
    // V2 clients always use streaming architecture
    await this.initializeV2Interview(client);
    
    return client;
  }
  
  async findV2Clients(filter: FilterQuery<Client>): Promise<Client[]> {
    return this.clientModel.find({
      ...filter,
      apiVersion: 'v2',  // Only return V2 clients
    });
  }
}
```

### 2.3 Account Handling Service

```typescript
// src/interviews/services/v2/interview-v2-account.service.ts
@Injectable()
export class InterviewV2AccountService {
  async initializeAccountInstances(
    interviewId: string,
    clientId: string,
    isPrimary: boolean
  ): Promise<void> {
    const client = await this.clientService.findById(clientId);
    const contact = isPrimary ? client.primaryContact : client.secondaryContact;
    
    const accountInstances = [];
    
    for (const account of contact.accounts || []) {
      const instance = {
        accountId: account._id,
        accountType: account.type,
        hasBeneficiaries: this.requiresBeneficiaries(account.type),
      };
      
      if (instance.hasBeneficiaries) {
        // Create beneficiary page instances
        const primaryPage = await this.createBeneficiaryPage(
          interviewId,
          account._id,
          'PRIMARY'
        );
        const contingentPage = await this.createBeneficiaryPage(
          interviewId,
          account._id,
          'CONTINGENT'
        );
        
        instance.primaryBeneficiaryPageId = primaryPage._id;
        instance.contingentBeneficiaryPageId = contingentPage._id;
      }
      
      accountInstances.push(instance);
    }
    
    await this.interviewV2Model.findByIdAndUpdate(
      interviewId,
      { accountInstances }
    );
  }
  
  private requiresBeneficiaries(accountType: string): boolean {
    return [AccountTypeEnum.Ira, AccountTypeEnum.RothIra].includes(accountType);
  }
}
```

### 2.4 CRM Streaming Adapter

```typescript
// src/integrations/crm/v2/crm-streaming-adapter.interface.ts
export interface CrmV2StreamingAdapter {
  // Stream answers directly to CRM
  streamAnswers(crmObjectId: string, pageId: string, answers: any): Promise<void>;
  
  // Create interview tracking object in CRM
  createInterviewObject(clientId: string): Promise<string>;
  
  // Handle account-specific data
  streamAccountData(
    crmObjectId: string,
    accountId: string,
    data: any
  ): Promise<void>;
  
  // Stream beneficiary data per account
  streamBeneficiaryData(
    crmObjectId: string,
    accountId: string,
    beneficiaryType: 'PRIMARY' | 'CONTINGENT',
    beneficiaries: any[]
  ): Promise<void>;
}
```

### 2.5 V2 Form Mappers

```typescript
// src/integrations/crm/v2/form-mappers/beneficiary-v2.mapper.ts
export class BeneficiaryV2Mapper {
  static mapToGeneric(
    answers: any,
    accountId: string,
    beneficiaryType: 'PRIMARY' | 'CONTINGENT'
  ): GenericBeneficiary[] {
    const beneficiaries = answers.beneficiaries || [];
    
    return beneficiaries.map((ben, index) => ({
      accountId,
      beneficiaryType,
      order: index + 1,
      firstName: ben.firstName,
      lastName: ben.lastName,
      percentage: ben.percentage,
      dateOfBirth: ben.dateOfBirth,
      relationship: ben.relationship,
      ssn: ben.ssn,
    }));
  }
}
```

### 2.2 V2 Interview Controller Updates

```typescript
// src/interviews/controllers/v2/interviews-v2.controller.ts
@Controller({ path: 'interviews', version: '2' })
@ApiTags('Interviews V2')
export class InterviewsV2Controller {
  constructor(
    private readonly interviewV2Service: InterviewV2Service,
    private readonly clientService: ClientsV1Service,
  ) {}

  @Post()
  async create(@Body() dto: CreateInterviewDto) {
    // Verify client is V2
    const client = await this.clientService.findById(dto.clientId);
    if (client.apiVersion !== 'v2') {
      throw new BadRequestException('V2 interviews can only be created for V2 clients');
    }
    
    return this.interviewV2Service.create(dto);
  }

  @Get('/client/:clientId')
  async findByClientId(@Param('clientId') clientId: string) {
    // Only returns V2 interviews for V2 clients
    const client = await this.clientService.findById(clientId);
    if (client.apiVersion !== 'v2') {
      return []; // V1 clients have no V2 interviews
    }
    
    return this.interviewV2Service.findByClient(clientId);
  }
}
```

## Phase 3: Branching and Template Versioning (Week 3)

### 3.1 Page Definition V2 Schema

```typescript
// src/interview-templates/schemas/v2/interview-page-def-v2.schema.ts
@Schema()
export class InterviewPageDefV2 extends Document {
  @Prop({ required: true })
  name!: string;

  @Prop({ type: String, enum: PageTypeEnum, required: true })
  pageType!: PageTypeEnum;

  @Prop({ required: true })
  order!: number;

  @Prop({ type: [Object], default: [] })
  elements!: unknown[];

  @Prop({ type: [Object], default: [] })
  branches!: BranchRule[];
}

export const InterviewPageDefV2Schema = SchemaFactory.createForClass(InterviewPageDefV2);
```

### 3.2 Template V2 Schema

```typescript
// src/interview-templates/schemas/v2/interview-template-v2.schema.ts
@Schema({ timestamps: true, collection: 'interviewtemplates_v2' })
export class InterviewTemplateV2 extends Document {
  @Prop({ required: true })
  templateName!: string;

  @Prop({ type: [InterviewPageDefV2Schema], default: [] })
  pages!: InterviewPageDefV2[];

  @Prop({
    type: String,
    enum: TemplateStatusEnum,
    default: TemplateStatusEnum.DRAFT,
    index: true,
  })
  status!: TemplateStatusEnum;

  @Prop({ type: Number, default: 1 })
  version!: number;

  @Prop({ type: Boolean, default: false })
  default!: boolean;

  @Prop({ type: SchemaTypes.ObjectId, ref: 'Organisation' })
  organisationId!: string;

  // Account configuration
  @Prop({ type: Object })
  accountConfig?: {
    supportedTypes: string[];
    requireBeneficiaries: boolean;
    documentRequirements: Record<string, string[]>;
  };
}

export const InterviewTemplateV2Schema = SchemaFactory.createForClass(InterviewTemplateV2);
InterviewTemplateV2Schema.index({ templateName: 1, version: 1 }, { unique: true });

// Immutable Publishing Service
InterviewTemplateV2Schema.statics.publish = async function (templateId: string) {
  const draft = await this.findById(templateId);
  if (!draft) throw new Error('Template not found');
  if (draft.status !== TemplateStatusEnum.DRAFT) {
    throw new Error('Only drafts can be published');
  }

  // Clone to new document with new stable IDs
  const published = new this({
    ...draft.toObject(),
    _id: new ObjectId(),
    status: TemplateStatusEnum.PUBLISHED,
    version: draft.version + 1,
    pages: draft.pages.map(page => ({
      ...page.toObject(),
      _id: new ObjectId(), // New stable page IDs
    }))
  });

  await published.save();
  
  // Archive the draft
  draft.status = TemplateStatusEnum.ARCHIVED;
  await draft.save();
  
  return published;
};
```

### 3.3 Branching Service

```typescript
// src/interviews/services/v2/interview-v2-branching.service.ts
@Injectable()
export class InterviewV2BranchingService {
  private jexl: any;
  
  constructor() {
    this.jexl = new Jexl();
    this.registerCustomFunctions();
  }
  
  private registerCustomFunctions() {
    // Account-specific functions
    this.jexl.addFunction('hasIRAAccount', (accounts: any[]) => {
      return accounts.some(a => [AccountTypeEnum.Ira, AccountTypeEnum.RothIra].includes(a.type));
    });
    
    this.jexl.addFunction('totalAccountValue', (accounts: any[]) => {
      return accounts.reduce((sum, acc) => sum + (acc.value || 0), 0);
    });
    
    this.jexl.addFunction('hasJointAccount', (accounts: any[]) => {
      return accounts.some(a => a.ownership === AccountOwnershipEnum.JointTenants);
    });
  }
  
  async evaluateNext(
    interview: InterviewV2,
    currentPageId: string,
    answers: any
  ): Promise<string | null> {
    const template = await this.templateService.findById(interview.template);
    const currentPage = template.pages.find(p => p._id.toString() === currentPageId);
    
    if (!currentPage?.branches?.length) {
      return this.getNextPageByOrder(template, currentPage);
    }
    
    // Enrich context with account data
    const client = await this.clientService.findById(interview.client);
    const contact = interview.isPrimary ? client.primaryContact : client.secondaryContact;
    
    const context = {
      answers,
      accounts: contact.accounts || [],
      organisationId: client.organisationId,
      isPrimary: interview.isPrimary,
    };
    
    for (const rule of currentPage.branches) {
      try {
        const result = await this.jexl.eval(rule.when, context);
        if (result) return rule.goto;
      } catch (error) {
        this.logger.error(`Branch evaluation error: ${error.message}`, {
          rule: rule.when,
          pageId: currentPageId,
        });
      }
    }
    
    return this.getNextPageByOrder(template, currentPage);
  }
}
```

## Phase 4: PDF Generation Support (Week 4)

### 4.1 V2 PDF Service

```typescript
// src/interviews/services/v2/interview-v2-pdf.service.ts
@Injectable()
export class InterviewV2PdfService {
  async generatePdfData(interviewId: string): Promise<any> {
    const interview = await this.interviewV2Model.findById(interviewId)
      .populate('client')
      .populate('template');
    
    // Fetch all data from CRM
    const crmData = await this.crmService.fetchInterviewData(interview.crmObjectId);
    
    // Map account data
    const accountData = await this.mapAccountData(interview, crmData);
    
    // Map beneficiary data per account
    const beneficiaryData = await this.mapBeneficiaryData(interview, crmData);
    
    return {
      ...crmData,
      accounts: accountData,
      beneficiaries: beneficiaryData,
    };
  }
  
  private async mapAccountData(interview: InterviewV2, crmData: any): Promise<any[]> {
    const client = await this.clientService.findById(interview.client);
    const contact = interview.isPrimary ? client.primaryContact : client.secondaryContact;
    
    return contact.accounts.map(account => ({
      ...account.toObject(),
      // Add CRM-specific account data
      crmAccountId: crmData.accounts?.[account._id],
      documents: this.getAccountDocuments(interview, account._id),
    }));
  }
}
```

## Phase 5: Queue Processing (Week 5)

### 5.1 V2 Queue Configuration

```typescript
// src/interviews/constants/interview-v2-queue.constant.ts
export const INTERVIEW_V2_QUEUE = {
  NAME: 'interview-v2-queue',
  JOBS: {
    STREAM_PAGE: 'STREAM_PAGE_V2',
    STREAM_BENEFICIARIES: 'STREAM_BENEFICIARIES_V2',
    EVALUATE_BRANCH: 'EVALUATE_BRANCH_V2',
    SYNC_ACCOUNT_DATA: 'SYNC_ACCOUNT_DATA_V2',
    COMPLETE_INTERVIEW: 'COMPLETE_INTERVIEW_V2',
  },
};
```

### 5.2 V2 Queue Processor

```typescript
// src/interviews/processors/interview-v2.processor.ts
@Processor(INTERVIEW_V2_QUEUE.NAME)
export class InterviewV2Processor {
  @Process(INTERVIEW_V2_QUEUE.JOBS.STREAM_PAGE)
  async streamPage(job: Job<StreamPageJobData>) {
    const { interviewId, pageId, answers } = job.data;
    
    // Handle beneficiary pages specially
    const pageInstance = await this.pageInstanceModel.findById(pageId);
    if (pageInstance.accountId) {
      await this.handleAccountSpecificPage(interviewId, pageInstance, answers);
    } else {
      await this.streamingService.streamAnswers(interviewId, pageId, answers);
    }
    
    // Evaluate branching
    await this.queue.add(INTERVIEW_V2_QUEUE.JOBS.EVALUATE_BRANCH, {
      interviewId,
      pageId,
      answers,
    });
  }
  
  private async handleAccountSpecificPage(
    interviewId: string,
    pageInstance: InterviewPageInstanceV2,
    answers: any
  ): Promise<void> {
    const interview = await this.interviewV2Model.findById(interviewId);
    
    // Stream to account-specific CRM endpoint
    await this.streamingService.streamAccountData(
      interview.crmObjectId,
      pageInstance.accountId!,
      answers
    );
  }
}
```

## Phase 6: Migration Strategy (Week 6)

### 6.1 Migration Service

```typescript
// src/migrations/services/v1-to-v2-migration.service.ts
@Injectable()
export class V1ToV2MigrationService {
  async migrateInterview(v1InterviewId: string): Promise<string> {
    const v1 = await this.interviewModel.findById(v1InterviewId)
      .populate('client');
    
    // Create CRM object
    const crmObjectId = await this.crmService.createInterviewObject(v1.client._id);
    
    // Create V2 interview
    const v2 = await this.interviewV2Model.create({
      client: v1.client._id,
      template: await this.findOrCreateV2Template(v1.template),
      crmObjectId,
      branch: 'main',
      isPrimary: v1.isPrimary,
    });
    
    // Initialize account instances
    await this.accountService.initializeAccountInstances(
      v2._id,
      v1.client._id,
      v1.isPrimary
    );
    
    // Stream existing data to CRM
    await this.migrateAnswersToCRM(v1, v2);
    
    // Migrate documents
    await this.migrateDocuments(v1, v2);
    
    return v2._id;
  }
  
  private async migrateAnswersToCRM(v1: Interview, v2: InterviewV2): Promise<void> {
    // Rate limiting to prevent CRM hammering
    const rateLimiter = new Bottleneck({
      maxConcurrent: 2,
      minTime: 500, // 500ms between calls
    });

    const migrationTasks = v1.template.pages
      .filter(page => page.data && Object.keys(page.data).length > 0)
      .map(page => 
        rateLimiter.schedule(async () => {
          // Use pageType instead of name matching
          const pageType = this.mapV1PageToType(page.name);
          
          if (pageType === PageTypeEnum.BENEFICIARY_PRIMARY || 
              pageType === PageTypeEnum.BENEFICIARY_CONTINGENT) {
            await this.migrateBeneficiaryData(v1, v2, page);
          } else {
            await this.streamingService.streamAnswers(
              v2.crmObjectId,
              page._id,
              page.data
            );
          }
        })
      );

    await Promise.all(migrationTasks);
  }

  private mapV1PageToType(pageName: string): PageTypeEnum {
    if (pageName.includes('primary-beneficiaries')) return PageTypeEnum.BENEFICIARY_PRIMARY;
    if (pageName.includes('contingent-beneficiaries')) return PageTypeEnum.BENEFICIARY_CONTINGENT;
    if (pageName.includes('name')) return PageTypeEnum.NAME;
    if (pageName.includes('address')) return PageTypeEnum.ADDRESS;
    if (pageName.includes('employment')) return PageTypeEnum.EMPLOYMENT;
    return PageTypeEnum.QUESTION;
  }

  private async migrateBeneficiaryData(v1: Interview, v2: InterviewV2, page: any): Promise<void> {
    const beneficiaries = page.data.beneficiaries || [];
    
    // Validate percentages sum to 100
    const totalPercentage = beneficiaries.reduce((sum, ben) => sum + (ben.percentage || 0), 0);
    if (Math.abs(totalPercentage - 100) > 0.01) {
      throw new Error(`Beneficiary percentages must sum to 100%, got ${totalPercentage}%`);
    }

    // Stream to CRM with account association
    await this.streamingService.streamBeneficiaryData(
      v2.crmObjectId,
      page.accountId,
      page.name.includes('primary') ? 'PRIMARY' : 'CONTINGENT',
      beneficiaries
    );
  }
}
```

## Phase 7: Testing Strategy (Week 7)

### 7.1 Integration Tests

```typescript
// src/interviews/services/v2/__tests__/v2-account-integration.spec.ts
describe('V2 Account Integration', () => {
  it('should handle IRA accounts with beneficiaries', async () => {
    const client = await createTestClient({
      primaryContact: {
        accounts: [{
          type: AccountTypeEnum.Ira,
          label: 'Test IRA',
        }]
      }
    });
    
    const interview = await interviewV2Service.create({
      clientId: client._id,
      templateId: template._id,
    });
    
    // Should create beneficiary page instances
    const pageInstances = await pageInstanceModel.find({ interviewId: interview._id });
    const beneficiaryPages = pageInstances.filter(p => p.accountId);
    
    expect(beneficiaryPages).toHaveLength(2); // Primary and contingent
  });
});
```

## Phase 8: Monitoring and Rollout (Week 8)

### 8.1 Monitoring Configuration

```typescript
// src/shared/middleware/v2-monitoring.middleware.ts
@Injectable()
export class V2MonitoringMiddleware {
  private metrics = {
    v2Requests: new Counter({ name: 'interview_v2_requests_total' }),
    streamingLatency: new Histogram({ name: 'crm_streaming_latency_seconds' }),
    branchingEvaluations: new Counter({ name: 'branching_evaluations_total' }),
    queueRetries: new Counter({ name: 'interview_v2_queue_retries_total' }),
    deadLetterQueue: new Counter({ name: 'interview_v2_dlq_total' }),
    queueLength: new Gauge({ name: 'interview_v2_queue_length' }),
  };
  
  async trackV2Usage(req: Request, res: Response, next: NextFunction) {
    if (req.path.includes('/v2/')) {
      const start = Date.now();
      
      res.on('finish', () => {
        this.metrics.v2Requests.inc({
          method: req.method,
          path: req.path,
          status: res.statusCode,
        });
        
        const duration = (Date.now() - start) / 1000;
        this.metrics.streamingLatency.observe(duration);
      });
    }
    next();
  }
}
```

## Critical Considerations

### Account Data Integrity
1. Account instances track IRA accounts for beneficiary pages
2. Beneficiary data streams directly to CRM per account
3. Document associations maintained per account
4. Joint account handling preserves ownership model

### CRM Integration Safety
1. All CRM adapters implement V2 streaming interface
2. Existing form mappers work alongside V2 mappers
3. Account-level data properly segregated
4. Beneficiary data maintains account association

### PDF Generation Compatibility
1. V2 fetches all data from CRM for PDF generation
2. Account mappers handle both V1 and V2 data formats
3. Beneficiary data correctly associated with accounts
4. Document references preserved

### Data Migration Path
1. One-way sync from V1 to V2
2. Account structures preserved
3. Beneficiary data migrated per account
4. Document associations maintained

## Critical Fixes Implemented

### 1. Page Type Identification
- Added `PageTypeEnum` for explicit page categorization
- Eliminates fuzzy name matching in migration and branching
- Clear identification of beneficiary pages vs general questions

### 2. Template Immutability
- Publishing creates new documents with stable IDs
- Running interviews maintain hard references to frozen page IDs
- Draft templates remain editable without side effects

### 3. Separated Account Instances
- Moved to `interview_account_instances_v2` collection
- Prevents document size bloat for clients with many accounts
- Indexed on `(interviewId, accountId)` for fast lookups

### 4. Critical Indexes Added
- `crmObjectId` unique index prevents duplicates
- `pageTemplateId` standalone index for branch evaluation
- Proper composite indexes for performance

### 5. Migration Hardening
- Rate limiting prevents CRM API hammering
- Beneficiary percentage validation (must sum to 100%)
- PageType-based routing instead of name matching

### 6. Enhanced Monitoring
- Queue retry and DLQ counters
- Queue length gauge for bottleneck detection
- Streaming failure tracking

### 7. PDF Data Mapping Validation

**Pre-Week 4 Checklist** - Verify all PDF fields have CRM equivalents:

- [ ] Account data: labels, ownership, types, features
- [ ] Contact information: names, addresses, phones, emails
- [ ] Beneficiary data: names, percentages, relationships, DOBs
- [ ] Employment information: status, employer, income
- [ ] Document metadata: upload dates, types, account associations
- [ ] Advisor information: names, roles, contact details
- [ ] Organization branding: logos, colors, contact info

**Validation Process**:
```typescript
// src/interviews/services/v2/pdf-data-validator.service.ts
async validatePdfDataCompleteness(interviewId: string): Promise<ValidationResult> {
  const crmData = await this.crmService.fetchInterviewData(interviewId);
  const requiredFields = this.pdfMapper.getRequiredFields();
  
  const missingFields = requiredFields.filter(field => 
    !this.hasNestedValue(crmData, field)
  );
  
  return {
    isValid: missingFields.length === 0,
    missingFields,
    warnings: this.checkDataQuality(crmData)
  };
}
```

## Revised Rollout Timeline

- **Week 1-2**: Schema implementation and core services
- **Week 3-6**: Account handling, CRM integration, and streaming (extended)
- **Week 7-8**: Branching logic and template versioning
- **Week 9-10**: Migration tools and comprehensive testing
- **Week 11**: Monitoring setup and V2 API readiness
- **Month 3**: New frontend beta launch with V2 APIs
- **Month 4**: Progressive rollout of new frontend
- **Month 5**: Full new frontend availability
- **Month 7+**: V1 frontend sunset planning

## Success Metrics

1. **Zero V1 Impact**: No degradation in V1 performance or functionality
2. **CRM Streaming Reliability**: 99.9% successful streaming operations
3. **Account Data Accuracy**: 100% accurate beneficiary associations
4. **Migration Success**: 100% successful V1 to V2 migrations
5. **Performance**: <100ms latency for branch evaluations

## Risk Mitigation

1. **Rollback Strategy**: Simple feature flag disable reverts to V1
2. **Data Recovery**: Event sourcing for all CRM operations
3. **Account Validation**: Automated validation of account structures
4. **CRM Fallback**: Queue-based retry with exponential backoff
5. **Monitoring**: Real-time alerts for streaming failures