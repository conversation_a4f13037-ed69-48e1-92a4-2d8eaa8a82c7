import { RedtailDBAccountType } from 'src/integrations/crm/redtail/types/enums';
import { AccountFeaturesEnum } from 'src/shared/types/accounts/account-features.enum';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';

export class UpdateAccountRequestDto {
  nameOnAccount: string;
  accountType: RedtailDBAccountType;
  accountLabel: string;
  ownership: string;
  advisoryRate?: number;
  features?: AccountFeaturesEnum[];
  advisoryRateAccountUdfFieldId: number;
  accountFeaturesUdfId: number;
}
