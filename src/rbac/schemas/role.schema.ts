import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { Permission, PermissionSchema } from './permission.schema';

@Schema()
export class Role extends Document {
  @Prop({ type: String, enum: RolesEnum, index: true, unique: true })
  name: RolesEnum;

  @Prop({ type: [PermissionSchema] })
  permissions: Permission[];

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const RoleSchema = SchemaFactory.createForClass(Role);
