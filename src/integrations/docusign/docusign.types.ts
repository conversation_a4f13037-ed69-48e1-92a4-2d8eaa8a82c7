import { DateSigned, EnvelopeRecipientTabs, SignHere } from 'docusign-esign';
import { Advisor } from 'src/advisors/schemas/advisors.schema';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { XOR } from 'src/shared/types/monads';

/**
 * Docusign Auth Scopes
 * @see {@link https://developers.docusign.com/platform/auth/reference/scopes/}
 **/
export enum DocusignAuthScopesEnum {
  SIGNATURE = 'signature',
  EXTENDED = 'extended',
  IMPERSONATION = 'impersonation',
  OPENID = 'openid',
  //  NOTE:  Add more scopes if needed
}

export enum DocusignEnvelopeStatusEnum {
  SENT = 'sent', // The envelope is sent to the recipients.
  CREATED = 'created', // The envelope is created as a draft. It can be modified and sent later.
  SIGNED = 'signed', //  The envelope has been signed by the recipients.
  COMPLETED = 'completed', //  The recipients have finished working with the envelope: the documents are signed and all required tabs are filled in.
  DECLINED = 'declined', //  The envelope has been declined by the recipients.
  VOIDED = 'voided', //  The envelope is no longer valid and recipients cannot access or sign the envelope.
}

export enum DocusignDocumentFileExtensionEnum {
  PDF = 'pdf',
  PNG = 'png',
  JPG = 'jpg',
  JPEG = 'jpeg',
}

export enum DocusignGrantTypesEnum {
  AUTHORIZATION_CODE = 'code',
}

export enum DocusignAccountOwnershipEnum {
  Firm = 'Firm',
  Schwab = 'Schwab',
}

export enum DocusignRecipientIdEnum {
  PrimaryContact = '5',
  SecondaryContact = '6',
  PrimaryAdvisor = '7',
}

export type DocusignOAuthResponse = {
  accessToken: string; // Typical access token lifetime is 8 days
  refreshToken: string; // Typical refresh token lifetime is 30 days
  expiresIn: string; // Typically 28800 seconds (8 hours)
  tokenType?: string; // Typically "Bearer" for OAuth 2.0 access tokens
};

export type DocusignOAuthData = DocusignOAuthResponse & {
  lastRefresh: Date;
};

export type RecipientData = {
  name: string;
  email: string;
  phone: string;
};

export type RecipientInfo = {
  primaryAdvisor?: RecipientData;
  applicant: RecipientData;
  coapplicant?: RecipientData;
};

export type ApplicantTabs = {
  dateSignedTabs?: DateSigned[];
  signHereTabs?: SignHere[];
};

export type EnvelopePreparedTabs = {
  advisorTabs?: EnvelopeRecipientTabs;
  primaryContactApplicantTabs?: ApplicantTabs;
  secondaryContactApplicantTabs?: ApplicantTabs;
  coapplicantTabs?: ApplicantTabs;
};

export type Entity = XOR<Advisor, Organisation>;

export const BATCH_SIZE = 100; // 100 entities per batch
export const END_PAGINATION = -1; // -1 means no more pages
export const REFRESH_THRESHOLD = 20; // 20 days
