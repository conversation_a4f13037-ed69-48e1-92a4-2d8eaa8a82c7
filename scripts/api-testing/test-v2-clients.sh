#!/bin/bash

# Source the base API functions
source "$(dirname "$0")/api-base.sh"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}Testing V2 Clients API with Template Integration${NC}\n"

# Authenticate first
authenticate

# Get organization ID
ORG_ID=$(get_org_id)
echo -e "${BLUE}Organization ID: ${ORG_ID}${NC}\n"

# Test 1: Create a V2 client with template-based interviews
echo -e "${YELLOW}Test 1: Create V2 client with accounts for template composition${NC}"
CLIENT_PAYLOAD=$(cat <<EOF
{
  "primaryContact": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "luis+$<EMAIL>",
    "phone": "+**********",
    "skipContactInterview": false,
    "accounts": [
      {
        "type": "ira",
        "label": "Traditional IRA Account",
        "accountLabel": "John's IRA",
        "ownership": "individual"
      },
      {
        "type": "brokerage", 
        "label": "Brokerage Account",
        "accountLabel": "John's Brokerage",
        "ownership": "individual"
      }
    ]
  },
  "primaryAdvisor": {
    "id": "$(get_advisor_id)"
  },
  "primaryCSA": {
    "id": "$(get_advisor_id)"
  },
  "notificationMethods": ["email"],
  "doClientProfiling": true,
  "readyToSend": true,
  "sendNow": true
}
EOF
)

RESPONSE=$(api_request "POST" "/v2/organisations/${ORG_ID}/clients" "$CLIENT_PAYLOAD")
echo -e "Response: $RESPONSE\n"

# Extract client ID from response (get the main _id, not account _ids)
CLIENT_ID=$(echo "$RESPONSE" | grep -o '"_id":"[^"]*"' | tail -1 | cut -d'"' -f4)

if [ ! -z "$CLIENT_ID" ]; then
    echo -e "${GREEN}✅ Client created successfully with ID: $CLIENT_ID${NC}"
    
    # Check if apiVersion is set to v2
    API_VERSION=$(echo "$RESPONSE" | grep -o '"apiVersion":"[^"]*"' | cut -d'"' -f4)
    echo -e "${BLUE}API Version: ${API_VERSION}${NC}"
    
    if [ "$API_VERSION" = "v2" ]; then
        echo -e "${GREEN}✅ Client correctly marked as V2${NC}\n"
    else
        echo -e "${RED}❌ Client not marked as V2${NC}\n"
    fi
    
    # Test 2: Get the created client
    echo -e "${YELLOW}Test 2: Get V2 client details${NC}"
    RESPONSE=$(api_request "GET" "/v2/organisations/${ORG_ID}/clients/${CLIENT_ID}")
    echo -e "Response: $RESPONSE\n"
    
    # Check initial status
    INITIAL_STATUS=$(echo "$RESPONSE" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    echo -e "${BLUE}Initial client status: ${INITIAL_STATUS}${NC}"
    
    # Test 3: Wait for BullMQ to process and check if interviews were created automatically
    echo -e "${YELLOW}Test 3: Wait for BullMQ processing and check V2 interviews were created automatically${NC}"
    echo "Waiting 5 seconds for BullMQ to process client creation..."
    sleep 5
    
    # Test 4: Check final client status after BullMQ processing
    echo -e "${YELLOW}Test 4: Check final client status${NC}"
    RESPONSE=$(api_request "GET" "/v2/organisations/${ORG_ID}/clients/${CLIENT_ID}")
    FINAL_STATUS=$(echo "$RESPONSE" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    echo -e "${BLUE}Final client status: ${FINAL_STATUS}${NC}\n"
    
    # Test 5: Check if interviews were created using V2 system
    echo -e "${YELLOW}Test 5: Check V2 interviews were created automatically${NC}"
    RESPONSE=$(api_request "GET" "/v2/interviews/client/${CLIENT_ID}")
    echo -e "Response: $RESPONSE\n"
    
else
    echo -e "${RED}❌ Failed to create V2 client${NC}"
fi

# Test 6: Get all V2 clients
echo -e "${YELLOW}Test 6: Get all V2 clients${NC}"
RESPONSE=$(api_request "GET" "/v2/organisations/${ORG_ID}/clients")
echo -e "Response: $RESPONSE\n"

echo -e "${GREEN}V2 Client API Testing Complete${NC}"