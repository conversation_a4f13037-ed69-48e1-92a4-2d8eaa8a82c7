import { IsUUID, <PERSON>NotEmpty, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class SubmitPageV2Dto {
  @ApiProperty({ description: 'Page UUID' })
  @IsUUID('4')
  @IsNotEmpty()
  pageId!: string;

  @ApiProperty({ description: 'Page answers (will be sent to CRM, not stored)' })
  @IsNotEmpty()
  answers!: Record<string, any>;

  @ApiPropertyOptional({ description: 'Page metadata' })
  @IsOptional()
  metadata?: {
    timeSpent?: number;
    deviceType?: string;
    timestamp?: Date;
  };
}