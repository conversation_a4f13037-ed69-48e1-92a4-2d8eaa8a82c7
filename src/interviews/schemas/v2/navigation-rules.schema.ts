import { Prop, Schema } from "@nestjs/mongoose";
import { Schema as MongooseSchema } from 'mongoose';

// Base condition types
@Schema({ _id: false })
export class FieldCondition {
  @Prop({ 
    type: String, 
    required: true 
  })
  field!: string;  // "employmentStatus"

  @Prop({ 
    type: String,
    required: true,
    enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than', 'in', 'not_in', 'exists', 'not_exists']
  })
  operator!: string;

  @Prop({ 
    type: MongooseSchema.Types.Mixed,
    required: function() { return this.operator !== 'exists' && this.operator !== 'not_exists'; }
  })
  value?: any;  // "retired", ["retired", "unemployed"], 65, etc.

  @Prop({ 
    type: String,
    default: null
  })
  label?: string;  // Human-readable: "User is retired"
}

// Compound conditions
@Schema({ _id: false })
export class ConditionGroup {
  @Prop({ 
    type: String,
    enum: ['AND', 'OR'],
    default: 'AND'
  })
  logic!: 'AND' | 'OR';

  @Prop({ 
    type: [FieldCondition],
    default: []
  })
  conditions!: FieldCondition[];
}

// Navigation flow rule
@Schema({ _id: false })
export class NavigationFlowRule {
  @Prop({ 
    type: String,
    required: true
  })
  ruleId!: string;  // Unique identifier

  @Prop({ 
    type: String,
    required: true
  })
  ruleName!: string;  // "Route to retirement section"

  @Prop({ 
    type: Number,
    default: 0
  })
  priority!: number;  // Evaluation order (lower = higher priority)

  @Prop({ 
    type: ConditionGroup,
    required: true
  })
  when!: ConditionGroup;  // Structured conditions

  @Prop({ 
    type: String,
    required: true
  })
  goToPageId!: string;

  @Prop({ 
    type: Boolean,
    default: true
  })
  isActive!: boolean;

  @Prop({ 
    type: String,
    default: null
  })
  description?: string;  // Additional context for maintainers
}