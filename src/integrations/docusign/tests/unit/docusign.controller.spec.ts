import { Test, TestingModule } from '@nestjs/testing';
import { DocusignController } from 'src/integrations/docusign/docusign.controller';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { ConfigService } from '@nestjs/config';
import { Response } from 'express';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';

describe('DocusignController', () => {
  let docusignController: DocusignController;
  let docusignService: DocusignService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DocusignController],
      providers: [
        { provide: DocusignService, useValue: {} },
        { provide: ConfigService, useValue: {} },
        {
          provide: WINSTON_MODULE_PROVIDER,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
            info: jest.fn(),
          },
        },
      ],
    }).compile();

    docusignController = module.get<DocusignController>(DocusignController);
    docusignService = module.get<DocusignService>(DocusignService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should get authorization-uri', async () => {
    const dto = {
      advisorId: 'advisorId',
    };

    docusignService.getAuthorizationUri = jest
      .fn()
      .mockResolvedValue('Test Uri');

    expect(await docusignController.getDocusignAuthorizationUri(dto)).toBe(
      'Test Uri',
    );
  });

  it('should handle callback', async () => {
    const res = {} as Response;
    res.redirect = jest.fn();

    const state = JSON.stringify({
      advisorId: 'Test advisorId',
      organisationId: 'Test organisationId',
    });

    const code = 'Test code';

    docusignService.getAccessToken = jest.fn().mockResolvedValue('Test Token');

    docusignService.storeAccessToken = jest.fn().mockResolvedValue(undefined);

    configService.get = jest.fn().mockReturnValue('Test Redirect');

    await docusignController.handleDocusignCallback(code, state, res);

    expect(docusignService.getAccessToken).toBeCalledWith(code);

    expect(docusignService.storeAccessToken).toBeCalledWith(
      JSON.parse(state),
      'Test Token',
    );

    expect(res.redirect).toBeCalledWith('Test Redirect');
  });
});
