import { test, expect } from '@playwright/test';
import { login } from './utils/auth';
import { 
    navigateToClientCreation, 
    fillClientInfo, 
    fillJointClientInfo, 
    addAccount, 
    addJointAccounts, 
    completeClientSetup 
} from './utils/client';
import { TEST_CONFIG } from './config/environment';

test.describe('Client Creation', () => {
    test.beforeEach(async ({ page }) => {
        await login(page);
    });

    test('should create a single client with IRA account', async ({ page }) => {
        // Initial setup
        await navigateToClientCreation(page);
        await page.getByText('New Contact').first().click();
        await page.getByText('No', { exact: true }).click();
        await page.getByRole('button', { name: 'Continue' }).click();
        await page.getByText('Open Accounts').click();
        await page.getByRole('button', { name: 'Continue' }).click();
        
        // Fill client information - using defaults
        const clientInfo = {
            ...TEST_CONFIG.clientDefaults.single,
            email: TEST_CONFIG.clientDefaults.generateEmail('single')
        };
        await fillClientInfo(page, clientInfo);

        // Add IRA account
        await addAccount(page, {
            type: 'IRA',
            nickname: 'Test IRA',
            owner: `${clientInfo.firstName} ${clientInfo.lastName}`
        });

        // Complete setup
        await page.getByRole('button', { name: 'Continue' }).click();
        await completeClientSetup(page);

        // Verify client creation status
        await expect(page.locator('tbody')).toContainText('Creating', {
            timeout: TEST_CONFIG.defaultTimeout / 6
        });

        // Wait for client creation to complete and verify
        await expect(page.locator('tbody')).not.toContainText('Creating', {
            timeout: TEST_CONFIG.defaultTimeout / 2
        });
    });

    test('should create a joint client with multiple accounts', async ({ page }) => {
        // Initial setup
        await navigateToClientCreation(page);
        await page.getByText('Joint Account').first().click();
        await page.getByText('No', { exact: true }).click();
        await page.getByRole('button', { name: 'Continue' }).click();
        await page.getByText('Open Accounts').click();
        await page.getByRole('button', { name: 'Continue' }).click();
        
        // Fill joint client information - using defaults
        const clientsInfo = {
            clientOne: {
                ...TEST_CONFIG.clientDefaults.joint.clientOne,
                email: TEST_CONFIG.clientDefaults.generateEmail('joint1')
            },
            clientTwo: {
                ...TEST_CONFIG.clientDefaults.joint.clientTwo,
                email: TEST_CONFIG.clientDefaults.generateEmail('joint2')
            }
        };
        await fillJointClientInfo(page, clientsInfo);

        // Add accounts for both clients
        await addJointAccounts(page, clientsInfo, {
            clientOne: [
                {
                    type: 'IRA',
                    nickname: 'Primary IRA'
                },
                {
                    type: 'Individual',
                    nickname: 'Primary Individual'
                }
            ],
            clientTwo: [
                {
                    type: 'Roth IRA',
                    nickname: 'Secondary Roth IRA'
                }
            ]
        });

        // Complete setup
        await page.getByRole('button', { name: 'Continue' }).click();
        await completeClientSetup(page);

        // Verify client creation status
        await expect(page.locator('tbody')).toContainText('Creating', {
            timeout: TEST_CONFIG.defaultTimeout / 6
        });

        // Wait for client creation to complete and verify
        await expect(page.locator('tbody')).not.toContainText('Creating', {
            timeout: TEST_CONFIG.defaultTimeout / 2
        });
    });
});