import { Injectable, Inject, forwardRef } from '@nestjs/common';
import { CreateNotificationTemplateDto } from './dto/create-notification-template.dto';
import { NotificationTemplate } from './schemas/notification.template';
import { OrganisationsService } from './organisations.service';

@Injectable()
export class OrganisationsNotificationsService {
  constructor(
    @Inject(forwardRef(() => OrganisationsService))
    private readonly organisationService: OrganisationsService
  ) {}

  async upsertNotificationTemplate(
    organisationId: string,
    createNotificationTemplateDto: CreateNotificationTemplateDto,
  ): Promise<NotificationTemplate> {
    // Fetch the current organisation along with its notification templates
    const organisation = await this.organisationService.findOne(organisationId);

    // Check if a similar template already exists
    const existingTemplate = organisation.notificationTemplates.find(
      (template) => {
        return (
          template?.templateName ===
            createNotificationTemplateDto.templateName &&
          template?.templateType === createNotificationTemplateDto.templateType
        );
      },
    );

    if (existingTemplate) {
      // Update the existing template
      existingTemplate.template = createNotificationTemplateDto.template;
      existingTemplate.default = createNotificationTemplateDto.default;
      await organisation.save();
      return existingTemplate;
    }

    organisation.notificationTemplates.push(
      createNotificationTemplateDto as NotificationTemplate,
    );
    await organisation.save();
    return organisation.notificationTemplates[0];
  }
}
