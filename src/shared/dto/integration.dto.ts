import { IsNotEmpty, IsEnum, ValidateNested } from 'class-validator';
import { Expose, Type } from 'class-transformer';
import { IntegrationConfigDto } from './integration-config.dto';
import { IntegrationEnum } from 'src/shared/types/integrations';
import { ApiProperty } from '@nestjs/swagger';

export class IntegrationDto {
  @IsNotEmpty()
  @IsEnum(IntegrationEnum)
  @ApiProperty()
  @Expose()
  integrationType: IntegrationEnum;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => IntegrationConfigDto)
  @Expose()
  @ApiProperty()
  integrationConfig: IntegrationConfigDto;
}
