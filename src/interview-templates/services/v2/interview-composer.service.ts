import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { InterviewTemplateV2 } from '../../schemas/v2/interview.template';
import { InterviewPageDefV2 } from '../../schemas/v2/interview-page-def.schema';
import { ComposeInterviewDto } from 'src/interviews/dto/v2/compose-interview.dto';
import { TemplateTypeEnum } from '../../types/template-type.enum';

interface ComposedPage extends InterviewPageDefV2 {
  visitOrder: number;
  source: 'base_template' | 'account_template';
  sourceTemplateId: string;
  accountContext?: {
    accountId: string;
    accountType: string;
    accountLabel: string;
  };
}

@Injectable()
export class InterviewComposerService {
  private readonly logger = new Logger(InterviewComposerService.name);

  constructor(
    @InjectModel(InterviewTemplateV2.name)
    private templateModel: Model<InterviewTemplateV2>,
  ) {}

  /**
   * Compose an interview by combining base template with account-specific templates
   */
  async composeInterview(
    dto: ComposeInterviewDto,
    session?: ClientSession
  ): Promise<{
    composedPages: ComposedPage[];
    compositionInfo: any;
    baseTemplate: InterviewTemplateV2;
  }> {
    // 1. Get base template
    const baseTemplate = await this.getBaseTemplate(
      dto.organisationId,
      dto.baseTemplateId,
      session
    );
    
    // 2. Start with base pages
    const composedPages: ComposedPage[] = baseTemplate.pages.map((page, index) => {
      // Ensure base pages have required fields
      const pageObj = (page as any).toObject ? (page as any).toObject() : page;
      return {
        ...pageObj,
        pageId: pageObj.pageId || uuidv4(), // Ensure pageId is present
        pageName: pageObj.pageName || `base_page_${index}`, // Ensure pageName is present
        visitOrder: index,
        source: 'base_template',
        sourceTemplateId: baseTemplate._id.toString()
      };
    });

    // 3. Track account templates used
    const accountTemplatesUsed: any[] = [];

    // 4. Add account-specific pages
    for (const account of dto.accounts) {
      const accountTemplate = await this.getAccountTemplate(
        dto.organisationId,
        account.accountType,
        account.templateId,
        session
      );

      if (!accountTemplate) {
        this.logger.warn(`No template found for account type ${account.accountType}`);
        continue;
      }

      accountTemplatesUsed.push({
        accountId: account.accountId,
        templateId: accountTemplate._id.toString(),
        accountType: account.accountType
      });

      // Add pages with account context
      const startOrder = composedPages.length;
      const accountPages: ComposedPage[] = accountTemplate.pages.map((page, index) => {
        // Ensure account pages have required fields
        const pageObj = (page as any).toObject ? (page as any).toObject() : page;
        return {
          ...pageObj,
          pageId: uuidv4(), // Generate new page IDs for account pages
          pageName: `${pageObj.pageName || `account_page_${index}`}_${account.accountId}`,
          pageTitle: `${account.accountLabel} - ${pageObj.pageTitle || 'Account Page'}`,
          visitOrder: startOrder + index,
          defaultOrder: 1000 + (startOrder + index) * 10, // Place account pages after base pages
          accountContext: {
            accountId: account.accountId,
            accountType: account.accountType,
            accountLabel: account.accountLabel
          },
          source: 'account_template',
          sourceTemplateId: accountTemplate._id.toString()
        };
      });

      composedPages.push(...accountPages);
    }

    // 5. Update page flows to maintain navigation integrity
    const finalPages = this.updatePageFlows(composedPages);

    // 6. Create composition info
    const compositionInfo = {
      baseTemplateId: baseTemplate._id.toString(),
      accountTemplates: accountTemplatesUsed,
      composedAt: new Date()
    };

    return {
      composedPages: finalPages,
      compositionInfo,
      baseTemplate
    };
  }

  /**
   * Get the base template (client onboarding template)
   */
  private async getBaseTemplate(
    organisationId: string,
    templateId?: string,
    session?: ClientSession
  ): Promise<InterviewTemplateV2> {
    const query = templateId
      ? { _id: templateId, organisationId }
      : {
          organisationId,
          templateType: TemplateTypeEnum.CLIENT_ONBOARDING,
          isDefaultForType: true,
          status: 'published'
        };

    const template = await this.templateModel
      .findOne(query)
      .session(session);
      
    if (!template) {
      throw new NotFoundException(
        templateId 
          ? `Template ${templateId} not found`
          : 'No default client onboarding template found for organization'
      );
    }

    return template;
  }

  /**
   * Get account-specific template
   */
  private async getAccountTemplate(
    organisationId: string,
    accountType: string,
    templateId?: string,
    session?: ClientSession
  ): Promise<InterviewTemplateV2 | null> {
    const query = templateId
      ? { _id: templateId, organisationId }
      : {
          organisationId,
          templateType: TemplateTypeEnum.ACCOUNT,
          accountType,
          isDefaultForType: true,
          status: 'published'
        };

    return this.templateModel
      .findOne(query)
      .session(session);
  }

  /**
   * Update page flows to maintain proper navigation
   * This ensures that:
   * 1. Base template pages flow into account pages
   * 2. Account pages flow back to the next base page or completion
   * 3. All page references are updated with new UUIDs
   */
  private updatePageFlows(pages: ComposedPage[]): ComposedPage[] {
    const pageMap = new Map(pages.map(p => [p.pageId, p]));
    const oldToNewPageIdMap = new Map<string, string>();
    
    // Build mapping of old page names/IDs to new page IDs
    pages.forEach(page => {
      if (page.pageName) {
        oldToNewPageIdMap.set(page.pageName, page.pageId);
      }
    });
    
    return pages.map((page, index) => {
      const nextPage = pages[index + 1];
      const updatedPage = { ...page };
      
      // Update flow if it exists
      if (updatedPage.flow) {
        // Update rules
        if (updatedPage.flow.rules && Array.isArray(updatedPage.flow.rules)) {
          updatedPage.flow.rules = updatedPage.flow.rules.map(rule => {
            // Try to resolve page references
            const targetPageId = this.resolvePageReference(
              rule.goToPageId,
              oldToNewPageIdMap,
              pages
            );
            
            return {
              ...rule,
              goToPageId: targetPageId || rule.goToPageId
            };
          });
        }
        
        // Update defaultNext
        if (updatedPage.flow.defaultNext) {
          // If this is the last base template page and we have account pages
          if (page.source === 'base_template' && 
              nextPage && 
              nextPage.source === 'account_template') {
            updatedPage.flow.defaultNext.pageId = nextPage.pageId;
          } else if (nextPage) {
            // Otherwise just point to the next page in sequence
            updatedPage.flow.defaultNext.pageId = nextPage.pageId;
          } else {
            // No next page - this should be terminal
            updatedPage.isTerminal = true;
          }
        }
        
        // If this is the last page and not terminal, mark it as terminal
        if (index === pages.length - 1 && !updatedPage.isTerminal) {
          updatedPage.isTerminal = true;
        }
      }
      
      return updatedPage;
    });
  }

  /**
   * Resolve page reference by name or ID
   */
  private resolvePageReference(
    reference: string,
    oldToNewMap: Map<string, string>,
    pages: ComposedPage[]
  ): string | null {
    // First try direct mapping
    if (oldToNewMap.has(reference)) {
      return oldToNewMap.get(reference)!;
    }
    
    // Then try to find by page name
    const targetPage = pages.find(p => 
      p.pageName === reference || p.pageId === reference
    );
    
    return targetPage?.pageId || null;
  }

  /**
   * Get all account templates for an organization
   */
  async getAccountTemplates(
    organisationId: string,
    session?: ClientSession
  ): Promise<InterviewTemplateV2[]> {
    return this.templateModel
      .find({
        organisationId,
        templateType: TemplateTypeEnum.ACCOUNT,
        status: 'published'
      })
      .session(session)
      .sort({ accountType: 1, createdAt: -1 });
  }

  /**
   * Set a template as default for its type and account type
   */
  async setAsDefaultForType(
    templateId: string,
    session?: ClientSession
  ): Promise<InterviewTemplateV2> {
    const template = await this.templateModel
      .findById(templateId)
      .session(session);
      
    if (!template) {
      throw new NotFoundException(`Template ${templateId} not found`);
    }
    
    // Unset other defaults of the same type
    const query: any = {
      organisationId: template.organisationId,
      templateType: template.templateType,
      isDefaultForType: true,
      _id: { $ne: templateId }
    };
    
    if (template.templateType === TemplateTypeEnum.ACCOUNT) {
      query.accountType = template.accountType;
    }
    
    await this.templateModel.updateMany(
      query,
      { $set: { isDefaultForType: false } },
      { session }
    );
    
    // Set this template as default
    template.isDefaultForType = true;
    await template.save({ session });
    
    this.logger.log(
      `Set template ${templateId} as default for type ${template.templateType}` +
      (template.accountType ? ` and account type ${template.accountType}` : '')
    );
    
    return template;
  }
}