import { LogLevel } from '@nestjs/common';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { AuditSource } from 'src/audits/types/audit';

@Schema({
  autoCreate: true,
  autoIndex: true,
  timestamps: true,
})
export class Audit extends Document {
  @Prop({ required: true })
  message: string;

  @Prop({ required: true, enum: AuditSource, default: AuditSource.BACKEND })
  context: AuditSource; // Identifies the application part where the log was generated

  @Prop({ required: true })
  level: LogLevel; // e.g., 'error', 'warn', 'info'

  @Prop({ type: Types.ObjectId, ref: 'Organisation', required: true })
  organisationId: Types.ObjectId;

  @Prop()
  entityId: Types.ObjectId; // Generic reference to any entity (client, transition, etc.)

  @Prop()
  entityType: string; // Type of the entity (Client, Transition, etc.)
}

export const AuditSchema = SchemaFactory.createForClass(Audit);
