# First step is to create a MongoDB Atlas account
# https://docs.atlas.mongodb.com/tutorial/create-atlas-account/
#
# Then create an organization and programmatic API key
# https://docs.atlas.mongodb.com/tutorial/manage-organizations
# https://docs.atlas.mongodb.com/tutorial/manage-programmatic-access
#
# Terraform MongoDB Atlas Provider Documentation
# https://www.terraform.io/docs/providers/mongodbatlas/index.html
# Terraform 0.14+, MongoDB Atlas Provider 0.9.1+

#
#  SSM Parameter Store Variables
#
data "aws_ssm_parameter" "MONGODB_ATLAS_ORG_ID" {
  name = var.mongodb_atlas_org_id_ssm
}

data "aws_ssm_parameter" "MONGODB_ATLAS_API_PUB_KEY" {
  name = var.mongodb_atlas_api_pub_key_ssm
}

data "aws_ssm_parameter" "MONGODB_ATLAS_API_PRI_KEY" {
  name = var.mongodb_atlas_api_pri_key_ssm
}

data "aws_ssm_parameter" "MONGODB_ATLAS_DB_USERNAME" {
  name = var.mongodb_atlas_database_user_username_ssm
}

data "aws_ssm_parameter" "MONGODB_ATLAS_DB_PASSWORD" {
  name = var.mongodb_atlas_database_user_password_ssm
}


#
#  Local Variables
#
locals {
  mongodb_atlas_api_pub_key = data.aws_ssm_parameter.MONGODB_ATLAS_API_PUB_KEY.value
  mongodb_atlas_api_pri_key = data.aws_ssm_parameter.MONGODB_ATLAS_API_PRI_KEY.value
  mongodb_atlas_org_id      = data.aws_ssm_parameter.MONGODB_ATLAS_ORG_ID.value

  # https://docs.atlas.mongodb.com/tutorial/create-mongodb-user-for-cluster/
  mongodb_atlas_database_user_username = data.aws_ssm_parameter.MONGODB_ATLAS_DB_USERNAME.value
  mongodb_atlas_database_user_password = data.aws_ssm_parameter.MONGODB_ATLAS_DB_PASSWORD.value
}

#
# Configure the MongoDB Atlas Provider
#
terraform {
  required_providers {
    mongodbatlas = {
      source = "mongodb/mongodbatlas"
    }
  }
}

provider "mongodbatlas" {
  public_key  = local.mongodb_atlas_api_pub_key
  private_key = local.mongodb_atlas_api_pri_key
}

#
# Create a Project
#
resource "mongodbatlas_project" "onbord_db" {
  name   = "onbord-db-${var.environment}"
  org_id = local.mongodb_atlas_org_id
}

#
# Create a Shared Tier Cluster
#
resource "mongodbatlas_cluster" "cluster" {
  project_id = mongodbatlas_project.onbord_db.id
  name       = var.environment

  # Provider Settings "block"
  provider_name               = var.environment == "production" ? "AWS" : "TENANT"
  backing_provider_name       = "AWS"
  provider_instance_size_name = var.instance_size
  provider_region_name        = "US_EAST_1"
  cloud_backup                = var.environment == "production"

  mongo_db_major_version       = var.environment == "staging" ? "8.0" : "6.0"
  auto_scaling_disk_gb_enabled = "true"
}

#
# Create an Atlas Admin Database User
#
resource "mongodbatlas_database_user" "db_user" {
  username           = local.mongodb_atlas_database_user_username
  password           = local.mongodb_atlas_database_user_password
  project_id         = mongodbatlas_project.onbord_db.id
  auth_database_name = "admin"

  # TODO: Add a role for the database user using 'aws_iam_type' = 'role'
  roles {
    role_name     = "atlasAdmin"
    database_name = "admin"
  }
}

resource "mongodbatlas_cloud_backup_schedule" "db_backup_schedule" {
  count = var.environment == "production" ? 1 : 0

  project_id   = mongodbatlas_cluster.cluster.project_id
  cluster_name = mongodbatlas_cluster.cluster.name

  reference_hour_of_day    = 3
  reference_minute_of_hour = 45
  restore_window_days      = 4

  policy_item_daily {
    frequency_interval = 1
    retention_unit     = "days"
    retention_value    = 7
  }
}

#
# Create an IP Accesslist
#
resource "mongodbatlas_project_ip_access_list" "atlas_ip_access" {
  project_id = mongodbatlas_project.onbord_db.id
  ip_address = var.nat_gateway_ip
  comment    = "NAT Gateway IP address"
}

resource "aws_ssm_parameter" "mongodb_connection_string" {
  name  = "/onbord/backend/${var.environment}/DB_URL"
  type  = "SecureString"
  value = mongodbatlas_cluster.cluster.connection_strings[0].standard_srv
}
