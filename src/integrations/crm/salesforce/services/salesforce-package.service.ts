import { Injectable } from '@nestjs/common';
import { Connection } from 'jsforce';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { OperationType } from 'src/super-http/enums';
import { generateRequestConfigs } from 'src/integrations/crm/salesforce/utils/generate-request-configs.util';

@Injectable()
export class SalesforcePackageService {
    private connection: Connection;

    constructor(
      private readonly httpService: SuperHttpService,
      private tenantId: string
    ) {}

    setConnection(connection: Connection) {
      this.connection = connection;
    }

    async isPackageInstalled() {
      try {
        const salesforceBaseUrl = `${this.connection.instanceUrl}/services/data/v${this.connection.version}`;
        const salesforceAcessToken = this.connection.accessToken;
        const requestConfigs = generateRequestConfigs(salesforceAcessToken);
        const { withInterceptors, ...configsWithoutInterceptors } = requestConfigs;

        await this.httpService.get(
          `${salesforceBaseUrl}/sobjects/Onbord_Account__c/`,
          this.tenantId,
          {
            rateLimiter: 'salesforce',
            integrationName: 'salesforce',
            operation: OperationType.GET,
            entityType: 'account',
            forceFresh: true,
          },
          configsWithoutInterceptors
        );
        return true;
      } catch (error) {
        return false;
      }
    }
}
