import { CrmAdapter } from 'src/integrations/crm/crm.adapter.interface';
import { Record as SalesforceRecord } from 'jsforce';
import { CrmContact } from '../types/contacts/crm-contact.type';
import { RedtailCitizenshipEnum } from '../redtail/types/enums';
import { GenericPhone, PhoneTypeEnum } from '../types/generic/phone.type';
import { GenericEmail, EmailTypeEnum } from '../types/generic/email.type';
import { GenericAddress, AddressTypeEnum } from '../types/generic/address.type';
import { GenericCrmAccount } from '../types/accounts/crm-account.type';

export class SalesforceAdapter implements CrmAdapter {
  public mapContact(contact: SalesforceRecord): CrmContact {
    if (!contact) throw new Error('Contact is undefined');
    
    return {
      id: contact.Id,
      firstName: contact.FirstName || '',
      middleName: contact.MiddleName || '', // Salesforce doesn't have middle name by default
      lastName: contact.LastName || '',
      suffix: contact.Suffix || '', // Salesforce doesn't have suffix by default
      taxId: contact.Onbord_SSN__c || '',
      dob: contact.Birthdate || '',
      citizenship: contact.Onbord_US_Citizen__c ? RedtailCitizenshipEnum.US : contact.Country_of_Citizenship__c || '',
      residence: contact.MailingCountry || '',
      accounts: [],
      additionalInfo: {
        employmentStatus: contact.Onbord_Employment_Status__c || '',
        phones: this.extractPhones(contact),
        addresses: this.extractAddresses(contact),
        emails: this.extractEmails(contact),
        jobDescription: contact.Onbord_Title__c || '',
        companyAssociation: contact.Onbord_Affiliated_Company__c || '',
        industryAffiliation: contact.Onbord_Industry_Affiliation__c || '',
      },
    };
  }

  public mapAccount(account: GenericCrmAccount): GenericCrmAccount {
    throw new Error('Method not implemented.');
  }

  public mapPhone(phone: { type: string; number: string; ext?: string; isPrimary?: boolean }): GenericPhone {
    return {
      number: phone.number,
      type: this.mapPhoneType(phone.type),
      ext: phone.ext,
      isPrimary: phone.isPrimary,
    };
  }

  public mapEmail(email: { address: string; type?: string; isPrimary?: boolean }): GenericEmail {
    return {
      address: email.address,
      type: EmailTypeEnum.HOME,
      isPrimary: email.isPrimary || false,
    };
  }

  public mapAddress(address: { 
    street?: string; 
    city?: string; 
    state?: string; 
    zipCode?: string; 
    country?: string;
    customTypeTitle?: string;
  }): GenericAddress {
    return {
      street: address.street || '',
      city: address.city || '',
      zip: address.zipCode || '',
      state: address.state || '',
      country: address.country || '',
      type: address.customTypeTitle ? AddressTypeEnum.WORK : AddressTypeEnum.MAILING,
      ...(address.customTypeTitle && { title: address.customTypeTitle}),
    };
  }

  private extractPhones(contact: SalesforceRecord): GenericPhone[] {
    const phones: GenericPhone[] = [];

    if (contact.Phone) {
      phones.push(this.mapPhone({ type: 'work', number: contact.Phone }));
    }
    if (contact.MobilePhone) {
      phones.push(this.mapPhone({ type: 'mobile', number: contact.MobilePhone }));
    }
    if (contact.HomePhone) {
      phones.push(this.mapPhone({ type: 'home', number: contact.HomePhone }));
    }
    if (contact.OtherPhone) {
      phones.push(this.mapPhone({ type: 'other', number: contact.OtherPhone }));
    }

    return phones;
  }

  private extractEmails(contact: SalesforceRecord): GenericEmail[] {
    const emails: GenericEmail[] = [];

    if (contact.Email) {
      emails.push(this.mapEmail({ address: contact.Email, isPrimary: true }));
    }

    return emails;
  }

  private extractAddresses(contact: SalesforceRecord): GenericAddress[] {
    const addresses: GenericAddress[] = [];

    if (contact.MailingStreet || contact.MailingCity || contact.MailingState || contact.MailingPostalCode || contact.MailingCountry) {
      addresses.push(this.mapAddress({
        street: contact.MailingStreet,
        city: contact.MailingCity,
        state: contact.MailingState,
        zipCode: contact.MailingPostalCode,
        country: contact.MailingCountry,
      }));
    }
    
    if (contact.Onbord_Company__c) {
      const companyAdress = contact.Onbord_Company_Address__c.split(', ');
      addresses.push(this.mapAddress({
        street: companyAdress[0] || '',
        city: companyAdress[1] || '',
        state: companyAdress[2] || '',
        zipCode: companyAdress[3] || '',
        country: companyAdress[4] || '',
        customTypeTitle: contact.Onbord_Company__c
      }));
    }

    return addresses;
  }

  private mapPhoneType(type: string): PhoneTypeEnum {
    switch (type.toLowerCase()) {
      case 'work':
        return PhoneTypeEnum.WORK;
      case 'mobile':
        return PhoneTypeEnum.MOBILE;
      case 'home':
        return PhoneTypeEnum.HOME;
      case 'other':
        return PhoneTypeEnum.OTHER;
      default:
        return PhoneTypeEnum.OTHER;
    }
  }

  toGenericEmail(contact: any): GenericEmail {
    if (!contact.Email) {
      return null;
    }
    
    return {
      address: contact.Email,
      type: EmailTypeEnum.HOME,
      isPrimary: true
    };
  }

  toGenericPhones(contact: any): GenericPhone[] {
    const phones: GenericPhone[] = [];

    if (contact.Phone) {
      phones.push({
        number: contact.Phone,
        type: PhoneTypeEnum.HOME,
        isPrimary: true,
        ext: ''
      });
    }

    if (contact.MobilePhone) {
      phones.push({
        number: contact.MobilePhone,
        type: PhoneTypeEnum.MOBILE,
        isPrimary: false,
        ext: ''
      });
    }

    if (contact.OtherPhone) {
      phones.push({
        number: contact.OtherPhone,
        type: PhoneTypeEnum.OTHER,
        isPrimary: false,
        ext: ''
      });
    }

    return phones;
  }

  toMainMobilePhone(contact: any): string {
    return contact.MobilePhone || contact.Phone || contact.OtherPhone || null;
  }
} 