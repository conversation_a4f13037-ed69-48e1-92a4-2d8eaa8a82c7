import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { RedtailUdfService } from 'src/integrations/crm/redtail/services/udf.service';
import {
  REDTAIL_OWNERSHIP_FIELD,
  RedtailAccountOwnershipEnum,
  RedtailAccountTaxQualifiedTypeEnum,
  RedtailAccountTypeEnum,
  RedtailBeneficiaryEnum,
  RedtailCRMEndpointsEnum,
  RedtailDBAccountType,
} from 'src/integrations/crm/redtail/types/enums';
import { makeRequest } from 'src/utils/makeRequest';
import { HttpMethodsEnum } from 'src/shared/types/general/http-methods.enum';
import {
  CreateAccountRequestDto,
  RedtailCreateAccountRequestDto,
} from 'src/integrations/crm/redtail/dto/requests/accounts/create-account-request.dto';
import { CreateAccountResponseDto } from 'src/integrations/crm/redtail/dto/responses/accounts/create-account-response.dto';
import { camelize } from 'src/utils';
import { AccountOwnershipEnum } from 'src/shared/types/accounts/account-ownership.enum';
import { UpdateAccountRequestDto } from 'src/integrations/crm/redtail/dto/requests/accounts/update-account-request.dto';
import { ContactDto } from 'src/clients/dto/v1/create-client.dto';
import { GetRedtailAccountsResponseDto } from 'src/integrations/crm/redtail/dto/responses/accounts/get-accounts-response.dto';
import { AddBeneficiaryRequest } from 'src/integrations/crm/redtail/dto/requests/accounts/add-beneficiary-request.dto';
import { snakeify } from 'src/utils/snakeify';
import {
  AddBeneficiaryResponse,
  AddBeneficiaryResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/accounts/add-beneficiary-response.dto';
import {
  GetBeneficiariesResponse,
  GetBeneficiariesResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/accounts/get-beneficiaries-response.dto';
import { RedtailBeneficiary } from 'src/integrations/crm/redtail/types/account-beneficiary.type';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { OperationType } from 'src/super-http/enums';

@Injectable()
export class RedtailAccountService {
  private readonly ownershipField = 'registration';
  private readonly baseUrl: string;
  private logger: Logger;

  constructor(
    private httpService: SuperHttpService,
    private udfService: RedtailUdfService,
    private authHeader: object,
    private tenantId: string
  ) {
    this.baseUrl = process.env.REDTAIL_BASE_URL;
    this.logger = new Logger('RedtailAccountService');
  }

  /**
   * Creates a new account for a Redtail contact.
   * @param redtailContactId The ID of the Redtail contact to create the account for.
   * @param nameOnAccount The name to use on the account.
   * @param accountType The type of account to create.
   * @param accountLabel The label to use for the account.
   * @param ownership The ownership type of the account.
   * @param advisoryRate The advisory rate for the account, if applicable.
   * @param features The features for the account, if applicable.
   * @param advisoryRateAccountUdfFieldId The ID of the advisory rate UDF field.
   * @param accountFeaturesUdfId The ID of the account features UDF field.
   * @returns The ID of the newly created Redtail account, or null if the creation failed.
   */
  public async createAccount(
    redtailContactId: string,
    createAccountDto: CreateAccountRequestDto,
    advisoryRateAccountUdfFieldId: number,
    accountFeaturesUdfId: number,
  ): Promise<number | null> {
    const {
      nameOnAccount,
      accountType,
      accountLabel,
      ownership,
      advisoryRate,
      features,
    } = createAccountDto;

    const url = `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${redtailContactId}/accounts`;
    const accountRequestBody: RedtailCreateAccountRequestDto = {
      account_type_id: RedtailAccountTypeEnum.BROKERAGE_ACCOUNT,
      number: this.buildAccountNumber(
        nameOnAccount,
        accountType,
        accountLabel,
        ownership,
      ),
      taxqualified_type: null,
    };

    if (accountType === RedtailDBAccountType.roth) {
      accountRequestBody.taxqualified_type =
        RedtailAccountTaxQualifiedTypeEnum.ROTH_IRA;
    } else if (accountType === RedtailDBAccountType.ira) {
      accountRequestBody.taxqualified_type =
        RedtailAccountTaxQualifiedTypeEnum.TRADITIONAL_IRA;
    }

    const { account } = camelize(
      await this.httpService.post<CreateAccountResponseDto>(
        url,
        this.tenantId,
        accountRequestBody,
        {
          rateLimiter: 'redtail',
          integrationName: 'redtail',
          operation: OperationType.CREATE,
          entityType: 'account',
          entityId: redtailContactId,
          wideCacheInvalidation: true
        },
        { headers: { ...this.authHeader } }
      ),
    );

    const redtailAccountId = account.id;
    if (advisoryRate) {
      await this.udfService.addAccountUdf(redtailAccountId, {
        accountUdfFieldId: advisoryRateAccountUdfFieldId,
        fieldValue: Number(advisoryRate) ?? 0,
      });
    }

    if (features) {
      await this.udfService.addAccountUdf(redtailAccountId, {
        accountUdfFieldId: accountFeaturesUdfId,
        fieldValue: this.formatFeatures(features),
      });
    }

    return redtailAccountId;
  }

  /**
   * Updates an account in Redtail CRM.
   * @param redtailContactId The ID of the contact associated with the account.
   * @param redtailAccountId The ID of the account to update.
   * @param nameOnAccount The name on the account.
   * @param accountType The type of account.
   * @param accountLabel The label of the account.
   * @param ownership The ownership of the account.
   * @param advisoryRate The advisory rate of the account.
   * @param features The features of the account.
   * @returns A Promise that resolves to a boolean indicating whether the update was successful.
   */
  public async updateAccount(
    redtailContactId: string,
    redtailAccountId: number,
    updateAccountDto: UpdateAccountRequestDto,
  ): Promise<boolean> {
    const {
      nameOnAccount,
      accountType,
      accountLabel,
      ownership,
      advisoryRate,
      features,
      advisoryRateAccountUdfFieldId,
      accountFeaturesUdfId,
    } = updateAccountDto;
    const url = `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${redtailContactId}/accounts/${redtailAccountId}`;

    const accountRequestBody = {
      number: this.buildAccountNumber(
        nameOnAccount,
        accountType,
        accountLabel,
        ownership,
      ),
      [REDTAIL_OWNERSHIP_FIELD]: AccountOwnershipEnum[ownership],
    };

    // Update account
    const accountResponse = await this.httpService.put(
      url,
      this.tenantId,
      accountRequestBody,
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.UPDATE,
        entityType: 'account',
        entityId: redtailAccountId.toString(),
        wideCacheInvalidation: true
      },
      { headers: { ...this.authHeader } }
    );

    if (advisoryRate) {
      await this.udfService.upsertAccountUdf(redtailAccountId, {
        accountUdfFieldId: advisoryRateAccountUdfFieldId,
        fieldValue: Number(advisoryRate),
      });
    }

    await this.udfService.upsertAccountUdf(redtailAccountId, {
      accountUdfFieldId: accountFeaturesUdfId,
      fieldValue: this.formatFeatures(features),
    });

    return true;
  }

  /**
   * Syncs the contact accounts with Redtail CRM.
   * @param contact The contact to sync the accounts for.
   * @param crmClientId The ID of the client in Redtail CRM.
   * @param advisoryRateAccountUdfFieldId The ID of the advisory rate account UDF field.
   * @param accountFeaturesUdfId The ID of the account features UDF field.
   * @returns A Promise that resolves when the sync is complete.
   */
  public async syncContactAccounts(
    contact: ContactDto,
    crmClientId: string,
    advisoryRateAccountUdfFieldId: number,
    accountFeaturesUdfId: number,
  ): Promise<void> {
    if (!contact.accounts) {
      return;
    }

    await Promise.all(
      contact.accounts?.map((account) => {
        const { type, label, ownership, advisoryRate, features } = account;
        const nameOnAccount = `${contact.firstName} ${contact.lastName}`;

        return this.createAccount(
          crmClientId,
          {
            nameOnAccount,
            accountType: RedtailDBAccountType[type],
            accountLabel: label,
            ownership,
            advisoryRate,
            features,
          },
          advisoryRateAccountUdfFieldId,
          accountFeaturesUdfId,
        );
      }),
    );
  }

  /**
   * Retrieves accounts for a given CRM client ID and account type from Redtail CRM.
   * @param crmClientId The ID of the CRM client.
   * @param type The type of account to retrieve.
   * @returns A Promise that resolves to an array of accounts.
   */
  public async getAccounts(crmClientId: string, type: RedtailAccountTypeEnum) {
    const url = `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/accounts`;
    const { accounts } = camelize(
      await this.httpService.get<GetRedtailAccountsResponseDto>(
        url,
        this.tenantId,
        {
          rateLimiter: 'redtail',
          integrationName: 'redtail',
          operation: OperationType.LIST,
          entityType: 'account',
          entityId: crmClientId,
          ttl: 3600, // Cache for 1 hour
          forceFresh: false,
          cacheKeysObject: { crmClientId, type }
        },
        { headers: { ...this.authHeader } }
      ),
    );
    return accounts;
  }

  /**
   * Deletes an account from Redtail CRM.
   * @param crmClientId The ID of the CRM client.
   * @param accountId The ID of the account to be deleted.
   * @returns The deleted account.
   */
  public async deleteAccount(crmClientId: string, accountId: number) {
    const url = `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/accounts/${accountId}`;
    const { accounts } = camelize(
      await this.httpService.delete<GetRedtailAccountsResponseDto>(
        url,
        this.tenantId,
        {
          rateLimiter: 'redtail',
          integrationName: 'redtail',
          operation: OperationType.DELETE,
          entityType: 'account',
          entityId: accountId.toString(),
          wideCacheInvalidation: true
        },
        { headers: { ...this.authHeader } }
      ),
    );
    return accounts;
  }

  /**
   * Builds an account number based on the provided parameters.
   * @param nameOnAccount The name on the account.
   * @param accountType The type of account.
   * @param accountLabel The label for the account.
   * @param ownership The ownership type for the account.
   * @returns The generated account number.
   */
  public buildAccountNumber(
    nameOnAccount: string,
    accountType: RedtailDBAccountType,
    accountLabel: string,
    ownership: string | null,
  ): string {
    let accountNumber = nameOnAccount;

    if (accountType === RedtailDBAccountType.joint) {
      accountNumber += ownership
        ? ` ${RedtailAccountOwnershipEnum[ownership]} Account`
        : ' Joint Account';
    } else {
      accountNumber += ` ${accountType}`;
    }

    if (accountLabel.trim()) {
      accountNumber += ` (${accountLabel})`;
    }

    return accountNumber;
  }

  /**
   * Returns the label from an account number.
   * @param accountNumber The account number to extract the label from.
   * @returns The label extracted from the account number, or null if no label is found.
   */
  public getLabelFromAccountNumber(accountNumber: string): string | null {
    const labelMatch = accountNumber.match(/\(([^)]+)\)/);

    return labelMatch ? labelMatch[1] : '';
  }

  /**
   * Retrieves the beneficiaries associated with a Redtail account.
   * @param accountId - The ID of the account to retrieve beneficiaries for.
   * @returns A Promise that resolves to a GetBeneficiariesResponse object.
   */
  public async getAccountBeneficiaries(
    accountId: number,
  ): Promise<GetBeneficiariesResponse> {
    const url = `${this.baseUrl}/accounts/${accountId}/beneficiaries`;

    const response = await this.httpService.get<GetBeneficiariesResponseDto>(
      url,
      this.tenantId,
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.LIST,
        entityType: 'beneficiary',
        entityId: accountId.toString(),
      },
      { headers: { ...this.authHeader } }
    );

    return camelize(response);
  }

  /**
   * Updates the beneficiaries of an account by deleting all existing beneficiaries and adding new ones.
   * @param accountId - The ID of the account to update the beneficiaries for.
   * @param beneficiaries - An array of `AddBeneficiaryRequest` objects representing the new beneficiaries to add.
   * @returns A promise that resolves with an array of promises that resolve with the added beneficiaries.
   */
  public async updateAccountBeneficiaries(
    accountId: number,
    beneficiaries: AddBeneficiaryRequest[],
  ) {
    await this.deleteAccountBeneficiaries(accountId);

    return Promise.all(
      beneficiaries.map((beneficiary) => {
        return this.addAccountBeneficiary(accountId, beneficiary);
      }),
    );
  }

  /**
   * Adds a beneficiary to an account in Redtail CRM.
   * @param clientCrmId The ID of the client in Redtail CRM.
   * @param accountId The ID of the account to add the beneficiary to.
   * @param data The data of the beneficiary to add.
   * @returns A Promise that resolves to the response of the API call.
   */
  public async addAccountBeneficiary(
    accountId: number,
    data: AddBeneficiaryRequest,
  ): Promise<AddBeneficiaryResponse> {
    const url = `${this.baseUrl}/accounts/${accountId}/beneficiaries`;

    const response = await this.httpService.post<AddBeneficiaryResponseDto>(
      url,
      this.tenantId,
      snakeify(data),
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.CREATE,
        entityType: 'beneficiary',
        entityId: accountId.toString(),
        wideCacheInvalidation: true
      },
      { headers: { ...this.authHeader } }
    );

    return camelize(response);
  }

  /**
   * Deletes a beneficiary from an account.
   * @param accountId - The ID of the account to delete the beneficiary from.
   * @param beneficiaryId - The ID of the beneficiary to delete.
   * @returns A Promise that resolves when the beneficiary has been deleted.
   */
  public async deleteAccountBeneficiary(
    accountId: number,
    beneficiaryId: number,
  ): Promise<void> {
    const url = `${this.baseUrl}/accounts/${accountId}/beneficiaries/${beneficiaryId}`;

    await this.httpService.delete(
      url,
      this.tenantId,
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.DELETE,
        entityType: 'beneficiary',
        entityId: `${accountId}_${beneficiaryId}`,
        wideCacheInvalidation: true
      },
      { headers: { ...this.authHeader } }
    );
  }

  /**
   * Deletes all beneficiaries associated with the given account ID.
   * @param accountId The ID of the account to delete beneficiaries from.
   * @returns A Promise that resolves when all beneficiaries have been deleted.
   */
  public async deleteAccountBeneficiaries(accountId: number) {
    const { accountBeneficiaries } = await this.getAccountBeneficiaries(
      accountId,
    );

    await Promise.all(
      accountBeneficiaries.map((beneficiary) => {
        return this.deleteAccountBeneficiary(accountId, beneficiary.id);
      }),
    );
  }

  /**
   * Deletes all beneficiaries associated with the given account ID and type.
   * @param accountId The ID of the account to delete beneficiaries from.
   * @returns A Promise that resolves when all beneficiaries have been deleted.
   */
  public async deleteAccountBeneficiariesByType(
    accountId: number,
    type: RedtailBeneficiaryEnum,
  ) {
    const { accountBeneficiaries } = await this.getAccountBeneficiaries(
      accountId,
    );

    const beneficiariesToDelete = accountBeneficiaries.filter(
      (beneficiary) => beneficiary.beneficiaryType === type,
    );

    await Promise.all(
      beneficiariesToDelete.map((beneficiary) => {
        return this.deleteAccountBeneficiary(accountId, beneficiary.id);
      }),
    );
  }

  private formatFeatures(features: string[]): string {
    return features ? features.join(',') : '';
  }

}
