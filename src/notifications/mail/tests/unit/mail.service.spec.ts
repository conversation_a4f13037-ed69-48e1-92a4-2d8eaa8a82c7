import { Test, TestingModule } from '@nestjs/testing';
import { MailService } from 'src/notifications/mail/mail.service';
import { MailerService } from '@nestjs-modules/mailer';
import { ConfigService } from '@nestjs/config';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { ClsService } from 'nestjs-cls';
import { EmailTemplatesService } from 'src/organisations/services/email-templates.service';

describe('MailService', () => {
  let service: MailService;
  let configService: ConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        MailService,
        {
          provide: AdvisorsCrmService,
          useValue: {
            logCommunication: jest.fn(),
          },
        },
        {
          provide: MailerService,
          useValue: {
            sendMail: jest.fn().mockResolvedValue({}),
            addTransporter: jest.fn(),
            transporter: {
              verify: jest.fn().mockResolvedValue(true)
            },
          },
        },
        {
          provide: 'MAILER_OPTIONS',
          useValue: {
            transport: {
              host: 'smtp.mailtrap.io',
              port: 2525,
              auth: {
                user: 'test',
                pass: 'test',
              },
            },
            defaults: {
              from: '"No Reply" <<EMAIL>>',
            },
          },
        },
        {
          provide: 'MAILER_TRANSPORT_FACTORY',
          useValue: {
            createTransport: jest.fn().mockReturnValue({
              verify: jest.fn().mockResolvedValue(true)
            }),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('test'),
          },
        },
        {
          provide: WINSTON_MODULE_PROVIDER,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
            info: jest.fn(),
          },
        },
        {
          provide: ClsService,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            run: jest.fn(callback => callback()),
          },
        },
        {
          provide: EmailTemplatesService,
          useValue: {
            findByType: jest.fn().mockResolvedValue(null),
          },
        },
      ],
    }).compile();

    service = module.get<MailService>(MailService);
    configService = module.get<ConfigService>(ConfigService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
