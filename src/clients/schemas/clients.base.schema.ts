import { Factory, DataFactory } from 'nestjs-seeder';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { SchemaObjectID } from 'src/shared/types/mongoose';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { AccountFeaturesEnum } from 'src/shared/types/accounts/account-features.enum';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { Contact } from 'src/shared/schemas/contact.schema';
/**
 * This file provides the base schemas that ClientSchema and DraftClientSchema can inherit.
 */
@Schema()
/**
 * Required fields are present here because if an account is added in the array,
 * it would have these at least, otherwise according to current logic, an account
 * will not even be added to the accounts array.
 */
export class BaseAccount extends Document {
  @Factory((faker) => faker.finance.accountName())
  @Prop({ required: true })
  label: string;

  @Factory((faker) => faker.finance.accountName())
  @Prop({ required: true })
  ownership: string;

  @Factory((faker) =>
    faker.helpers.arrayElement(Object.values(AccountTypeEnum)),
  )
  @Prop({ required: true })
  type: AccountTypeEnum;

  @Prop()
  masterAccountNumber?: string;

  @Prop({ type: Array, enum: AccountFeaturesEnum, default: [] })
  features?: AccountFeaturesEnum[];

  @Prop()
  advisoryRate?: number;
}

export const BaseAccountSchema = SchemaFactory.createForClass(BaseAccount);

@Schema({ _id: false })
/**
 * Apart from firstName and lastName all other can be undefined
 * but first and last name are the minimum requirements here
 */
export class BaseClientContact extends Contact {
  @Prop()
  skipContactInterview?: boolean;

  @Prop({ type: [BaseAccountSchema], default: [] })
  accounts: BaseAccount[];

  @Prop()
  crmClientId?: string;

  @Factory(new Date())
  @Prop({ type: Date })
  lastActivityAt?: Date;
}

/**
 * All properties are required because if a primary CSA is selected then we will always have all these.
 * Even for draft ones.
*/
@Schema({ _id: false })
export class PrimaryAdvisor extends Document {
  @Factory((_, { advisor }) => advisor._id)
  @Prop({ type: SchemaObjectID, ref: 'Advisor' })
  id: mongoose.Types.ObjectId;

  @Factory((faker) => faker.person.firstName())
  @Prop({ required: true })
  firstName: string;

  @Factory((faker) => faker.person.lastName())
  @Prop({ required: true })
  lastName: string;

  @Factory((faker) => faker.internet.email())
  @Prop({ required: true })
  email: string;

  @Factory((faker) => faker.phone.number())
  @Prop({ required: true })
  mobile: string;

  @Factory(181)
  @Prop({ type: mongoose.Schema.Types.Mixed, default: null })
  crmId: number | string;
}

@Schema({ _id: false })
/**
 * All properties are required because if a primary CSA is selected then we will always have all these.
 * Even for draft ones.
 */
export class PrimaryCSA extends Document {
  @Prop({ type: SchemaObjectID, required: true, ref: 'Advisor' })
  id: mongoose.Types.ObjectId;

  @Factory((faker) => faker.person.firstName())
  @Prop({ required: true })
  firstName: string;

  @Factory((faker) => faker.person.lastName())
  @Prop({ required: true })
  lastName: string;

  @Factory((faker) => faker.internet.email())
  @Prop({ required: true })
  email: string;
}

@Schema({ timestamps: true })
export class BaseClient extends Document {
  @Factory((_, { organisation }) => organisation.id)
  @Prop({
    type: SchemaObjectID,
    required: true,
    ref: 'Organisation',
    index: true,
  })
  organisationId: mongoose.Types.ObjectId;

  @Factory((_, { transition }) => transition.id)
  @Prop({
    type: SchemaObjectID,
    required: false,
    ref: 'Transition',
    index: true,
  })
  transitionId: mongoose.Types.ObjectId;

  // Required: true because this is always set
  @Factory(true)
  @Prop({ required: true })
  readyToSend: boolean;

  // Required: true because this is decided before the user can save a draft or create a client
  @Factory(true)
  @Prop({ required: false })
  featuresSelected: boolean;

  // Required: true because this is decided before the user can save a draft or create a client
  @Factory(false)
  @Prop({ required: true })
  docusignSelected: boolean;

  // Whether to send ADV2B attachment in welcome emails
  @Factory(true)
  @Prop({ required: false, default: true })
  sendAdv2b: boolean;

  // required: true because firstName and lastName is must to save a draft even
  @Prop({ type: BaseClientContact, required: true })
  primaryContact: BaseClientContact;

  @Prop({ type: BaseClientContact })
  secondaryContact?: BaseClientContact;

  @Factory(
    (_, context) =>
      DataFactory.createForClass(PrimaryAdvisor).generate(1, context)[0],
  )
  @Prop({ type: PrimaryAdvisor })
  primaryAdvisor?: PrimaryAdvisor;

  @Factory(
    (_, context) =>
      DataFactory.createForClass(PrimaryCSA).generate(1, context)[0],
  )
  @Prop({ type: PrimaryCSA })
  primaryCSA?: PrimaryCSA;

  @Factory([])
  @Prop({ type: Array<{ id: string }> })
  secondaryAdvisor?: { id: string }[];

  @Factory([])
  @Prop({ type: Array<{ id: string }> })
  secondaryCSA?: { id: string }[];

  @Prop({ type: Date, default: null })
  archivedAt?: Date;

  // Required: true because every type of client even draft would have the status
  @Factory(ClientStatusEnum.Complete)
  @Prop({ required: true })
  status: ClientStatusEnum;

  @Prop({ type: Date })
  completedAt?: Date;

  @Factory(0)
  @Prop()
  fileUploadsNo: number;

  @Factory(
    (_, context) =>
      DataFactory.createForClass(PrimaryAdvisor).generate(1, context)[0].id,
  )
  @Prop({
    type: SchemaObjectID,
    required: false,
    ref: 'Advisor',
    index: true,
  })
  createdBy?: mongoose.Types.ObjectId;
}

export const BaseClientSchema = SchemaFactory.createForClass(BaseClient);
