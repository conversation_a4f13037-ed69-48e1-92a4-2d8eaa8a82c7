import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';
import { AddressMapperRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/address-request.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import {
  WealthboxAddressKind,
  WealthboxMailingAddress,
} from 'src/integrations/crm/wealthbox/types';
import { Country } from 'src/shared/types/general/country.enum';
export class WealthboxAddressMapper implements FormUpdateMapper {
  constructor(private WealthboxCrm: WealthboxCRM) {}

  /**
   * Maps page data to Wealthbox CRM address fields for a given client.
   * @param clientCrmId The ID of the client in Wealthbox CRM.
   * @param organisation The organisation associated with the client.
   * @param pageData The page data to map to Wealthbox CRM address fields.
   * @returns A Promise that resolves when the mapping is complete.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: AddressMapperRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    const addressUpdates: WealthboxMailingAddress[] = [];

    const crmContact = await this.WealthboxCrm.contactService.getContact(
      contact.crmClientId,
    );

    const existingWorkAddress = crmContact.street_addresses.find(
      (x) => x.kind === WealthboxAddressKind.Home,
    );
    const legalAddress: WealthboxMailingAddress = {
      id: existingWorkAddress?.id,
      street_line_1: pageData.legalAddress.line1,
      street_line_2: pageData.legalAddress.line2,
      city: pageData.legalAddress.city,
      state: pageData.legalAddress.state,
      zip_code: pageData.legalAddress.zip,
      address: pageData.legalAddress.line1,
      country: Country.US,
      kind: WealthboxAddressKind.Home,
      principal: false,
    };
    addressUpdates.push(legalAddress);

    if (pageData.mailingAddress) {
      const existingMailingAddress = crmContact.street_addresses.find(
        (x) => x.kind === WealthboxAddressKind.Other,
      );
      const mailingAddress: WealthboxMailingAddress = {
        id: existingMailingAddress?.id,
        street_line_1: pageData.mailingAddress.line1,
        street_line_2: pageData.mailingAddress.line2,
        city: pageData.mailingAddress.city,
        state: pageData.mailingAddress.state,
        zip_code: pageData.mailingAddress.zip,
        address: pageData.mailingAddress.line1,
        country: Country.US,
        kind: WealthboxAddressKind.Other,
        principal: false,
      };
      addressUpdates.push(mailingAddress);
    }

    await this.WealthboxCrm.contactService.updateContact(contact.crmClientId, {
      street_addresses: [...addressUpdates],
    });
  }
}
