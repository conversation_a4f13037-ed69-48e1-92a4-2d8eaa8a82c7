import { IsString, IsDate, IsObject, IsOptional } from 'class-validator';

class SenderDto {
  @IsString()
  @IsOptional()
  userName?: string;

  @IsString()
  @IsOptional()
  userId?: string;

  @IsString()
  @IsOptional()
  accountId?: string;

  @IsString()
  @IsOptional()
  email?: string;

  @IsString()
  @IsOptional()
  ipAddress?: string;
}

class EnvelopeMetadataDto {
  @IsString()
  allowAdvancedCorrect?: string;

  @IsString()
  enableSignWithNotary?: string;

  @IsString()
  allowCorrect?: string;
}

export enum DocusignWebhookEvent {
  ENVELOPE_SENT = 'envelope-sent',
  ENVELOPE_COMPLETED = 'envelope-completed',
  ENVELOPE_DECLINED = 'envelope-declined',
  ENVELOPE_VOIDED = 'envelope-voided',
  ENVELOPE_EXPIRED = 'envelope-expired',
  ENVELOPE_DELETED = 'envelope-deleted',
  ENVELOPE_SENT_WITH_DOCUMENTS = 'envelope-sent-with-documents',
  ENVELOPE_PARTIALLY_SIGNED = 'envelope-partially-signed',
  ENVELOPE_PURGED = 'envelope-purged',
  ENVELOPE_AUTOMATIC_DATA_EXTRACTION = 'envelope-automatic-data-extraction',
  ENVELOPE_ATTACHMENT_REMOVED = 'envelope-attachment-removed',
  ENVELOPE_ATTACHMENT_RENAMED = 'envelope-attachment-renamed',
  ENVELOPE_ATTACHMENT_REPLACED = 'envelope-attachment-replaced',
  ENVELOPE_ATTACHMENT_UPDATED = 'envelope-attachment-updated',
  ENVELOPE_ATTACHMENT_UPLOADED = 'envelope-attachment-uploaded',
  ENVELOPE_AUTO_NAVIGATION = 'envelope-auto-navigation',
  ENVELOPE_AUTO_NAVIGATION_RECIPIENT = 'envelope-auto-navigation-recipient',
  ENVELOPE_AUTO_NAVIGATION_RECIPIENT_COMPLETED = 'envelope-auto-navigation-recipient-completed',
  ENVELOPE_AUTO_NAVIGATION_RECIPIENT_DECLINED = 'envelope-auto-navigation-recipient-declined',
  ENVELOPE_AUTO_NAVIGATION_RECIPIENT_FAILED = 'envelope-auto-navigation-recipient-failed',
  ENVELOPE_AUTO_NAVIGATION_RECIPIENT_VIEWED = 'envelope-auto-navigation-recipient-viewed',
  ENVELOPE_AUTO_NAVIGATION_RECIPIENT_VISITED = 'envelope-auto-navigation-recipient-visited',
  ENVELOPE_AUTO_NAVIGATION_RECIPIENT_SENT = 'envelope-auto-navigation-recipient-sent',
  ENVELOPE_AUTO_NAVIGATION_RECIPIENT_SIGNED = 'envelope-auto-navigation-recipient-signed',
}

export class LegacyDocuSignWebhookDto {
  @IsString()
  @IsOptional()
  status?: string;

  @IsString()
  @IsOptional()
  documentsUri?: string;

  @IsString()
  @IsOptional()
  recipientsUri?: string;

  @IsString()
  @IsOptional()
  attachmentsUri?: string;

  @IsString()
  @IsOptional()
  envelopeUri?: string;

  @IsString()
  @IsOptional()
  emailSubject?: string;

  @IsString()
  @IsOptional()
  envelopeId?: string;

  @IsString()
  @IsOptional()
  signingLocation?: string;

  @IsString()
  @IsOptional()
  customFieldsUri?: string;

  @IsString()
  @IsOptional()
  notificationUri?: string;

  @IsString()
  @IsOptional()
  enableWetSign?: string;

  @IsString()
  @IsOptional()
  allowMarkup?: string;

  @IsString()
  @IsOptional()
  allowReassign?: string;

  @IsString()
  @IsOptional()
  createdDateTime?: string;

  @IsString()
  @IsOptional()
  lastModifiedDateTime?: string;

  @IsString()
  @IsOptional()
  initialSentDateTime?: string;

  @IsString()
  @IsOptional()
  sentDateTime?: string;

  @IsString()
  @IsOptional()
  statusChangedDateTime?: string;

  @IsString()
  @IsOptional()
  documentsCombinedUri?: string;

  @IsString()
  @IsOptional()
  certificateUri?: string;

  @IsString()
  @IsOptional()
  templatesUri?: string;

  @IsString()
  @IsOptional()
  brandId?: string;

  @IsString()
  @IsOptional()
  expireEnabled?: string;

  @IsString()
  @IsOptional()
  expireDateTime?: string;

  @IsString()
  @IsOptional()
  expireAfter?: string;

  @IsObject()
  @IsOptional()
  sender?: SenderDto;

  @IsString()
  @IsOptional()
  purgeState?: string;

  @IsString()
  @IsOptional()
  envelopeIdStamping?: string;

  @IsString()
  @IsOptional()
  is21CFRPart11?: string;

  @IsString()
  @IsOptional()
  signerCanSignOnMobile?: string;

  @IsString()
  @IsOptional()
  autoNavigation?: string;

  @IsString()
  @IsOptional()
  isSignatureProviderEnvelope?: string;

  @IsString()
  @IsOptional()
  hasFormDataChanged?: string;

  @IsString()
  @IsOptional()
  allowComments?: string;

  @IsString()
  @IsOptional()
  hasComments?: string;

  @IsString()
  @IsOptional()
  allowViewHistory?: string;

  @IsObject()
  @IsOptional()
  envelopeMetadata?: EnvelopeMetadataDto;

  @IsString()
  @IsOptional()
  anySigner?: string;

  @IsString()
  @IsOptional()
  envelopeLocation?: string;

  @IsString()
  @IsOptional()
  isDynamicEnvelope?: string;

  @IsString()
  @IsOptional()
  burnDefaultTabData?: string;
}

export class DocusignJsonSimWebhookDto {
  event: DocusignWebhookEvent;
  uri: string;
  retryCount: string;
  configurationId: string;
  apiVersion: string;
  generatedDateTime: string;
  data: {
    accountId: string;
    userId: string;
    recipientId?: string;
    envelopeId: string;
    envelopeSummary: string;
  };
}
