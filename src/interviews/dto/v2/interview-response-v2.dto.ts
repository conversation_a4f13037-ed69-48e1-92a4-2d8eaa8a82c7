import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class InterviewResponseV2Dto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  clientId!: string;

  @ApiProperty()
  templateId!: string;

  @ApiProperty({ enum: ['primary', 'secondary'] })
  contactType!: 'primary' | 'secondary';

  @ApiProperty()
  status!: 'pending' | 'in_progress' | 'completed' | 'abandoned';

  @ApiProperty()
  startPageId!: string;

  @ApiProperty({ type: [Object] })
  accountInstances!: Array<{
    accountId: string;
    accountType: string;
    accountLabel: string;
    requiresBeneficiaries: boolean;
    beneficiaryPageIds?: {
      primary?: string;
      contingent?: string;
    };
  }>;

  @ApiProperty()
  createdAt!: Date;

  @ApiPropertyOptional()
  startedAt?: Date;

  @ApiPropertyOptional()
  completedAt?: Date;
}