import { ContactDto } from 'src/clients/dto/v1/create-client.dto';
import { CrmAdapter } from 'src/integrations/crm/crm.adapter.interface';
import { GenericCrmAccount } from 'src/integrations/crm/types/accounts/crm-account.type';
import { CrmContact } from 'src/integrations/crm/types/contacts/crm-contact.type';
import { GenericAddress } from 'src/integrations/crm/types/generic/address.type';
import { GenericEmail } from 'src/integrations/crm/types/generic/email.type';
import { GenericPhone } from 'src/integrations/crm/types/generic/phone.type';
import { GenericUserDefinedFieldsEnum } from 'src/integrations/crm/types/generic/user-defined-fields.enum';
import { WealthboxEmailAddressKind } from 'src/integrations/crm/wealthbox/dto/email.dto';
import { HouseholdTitle } from 'src/integrations/crm/wealthbox/dto/household/household.dto';
import { WealthboxPhoneNumberKind } from 'src/integrations/crm/wealthbox/dto/phone.dto';
import {
  ContactRoleValue,
  WealthboxAddressKind,
  WealthboxContactType,
  WealthboxEmailAddress,
  WealthboxMailingAddress,
  WealthboxPhoneNumber,
} from 'src/integrations/crm/wealthbox/types';
import { WealthboxContact } from 'src/integrations/crm/wealthbox/types/contact.type';
import { addressKindToAddressType } from 'src/integrations/crm/wealthbox/utils/address-kind-mapper';
import { getFromUserContentField } from 'src/integrations/crm/wealthbox/utils/data-parsing/general-text-field';
import { emailAddressKindToEmailAddressType } from 'src/integrations/crm/wealthbox/utils/email-address-kind-mapper';
import { phoneNumberKindToPhoneType } from 'src/integrations/crm/wealthbox/utils/phone-kind-mapper';
import { CustomizableCategoryDto } from './dto/contacts/customizable-category.dto';
import { parsePhoneNumber, CountryCode } from 'libphonenumber-js';

export class WealthboxAdapter implements CrmAdapter {
  public mapContact(
    contact: WealthboxContact,
    accounts: GenericCrmAccount[],
    advisorId: string,
  ): CrmContact {
    if (!contact) throw new Error('Contact is undefined');

    const { background_info: backgroundInfo } = contact;
    const industryAffiliation = getFromUserContentField(
      backgroundInfo,
      GenericUserDefinedFieldsEnum.INDUSTRY_AFFILIATION,
    );
    const employmentStatus = getFromUserContentField(
      backgroundInfo,
      GenericUserDefinedFieldsEnum.EMPLOYMENT_STATUS,
    );
    const jobDescription = contact?.occupation?.name;
    const companyAssociation = getFromUserContentField(
      backgroundInfo,
      GenericUserDefinedFieldsEnum.COMPANY_ASSOCIATION,
    );
    const ssn = getFromUserContentField(
      backgroundInfo,
      GenericUserDefinedFieldsEnum.SSN,
    );
    const employer = getFromUserContentField(
      backgroundInfo,
      GenericUserDefinedFieldsEnum.COMPANY,
    );

    return {
      id: contact.id?.toString(),
      firstName: contact.first_name,
      lastName: contact.last_name,
      citizenship: 'US',
      accounts,
      dob: contact.birth_date,
      middleName: contact.middle_name,
      residence: contact.street_addresses?.find((address) => address.principal)
        ?.address,
      suffix: contact.suffix,
      taxId: ssn,
      profileUrl: `${
        process.env.WEALTHBOX_FRONTEND_URL
      }${advisorId}/contacts/${contact.id?.toString()}`,
      additionalInfo: {
        addresses: contact.street_addresses?.map((address) =>
          this.mapAddress(address, employer),
        ),
        companyAssociation,
        employmentStatus,
        emails: contact.email_addresses?.map((email) => this.mapEmail(email)),
        industryAffiliation,
        jobDescription,
        phones: contact.phone_numbers?.map((phone) => this.mapPhone(phone)),
      },
    };
  }

  public mapAddress(
    address: WealthboxMailingAddress,
    employer?: string,
  ): GenericAddress {
    if (!address) throw new Error('Address is undefined');
    return {
      id: address.id?.toString(),
      street: address.street_line_1,
      city: address.city,
      state: address.state,
      zip: address.zip_code,
      type: addressKindToAddressType(address.kind),
      country: address.country,
      title: address.kind === WealthboxAddressKind.Work ? employer : '',
    };
  }

  public mapAccount(account: GenericCrmAccount): GenericCrmAccount {
    throw new Error('Method not implemented.');
  }

  public mapPhone(phone: WealthboxPhoneNumber): GenericPhone {
    if (!phone || !phone.address) {
      return null;
    }
    
    let formattedNumber = phone.address;

    try {
      // Remove any 'x+1' or similar extensions from the number
      const cleanNumber = formattedNumber.split(/\s*x\s*\+?\d*$/)[0];
      
      // Try parsing as US number first, then fallback to GB if that fails
      let parsedNumber = parsePhoneNumber(cleanNumber, 'US');
      
      if (!parsedNumber?.isValid()) {
        parsedNumber = parsePhoneNumber(cleanNumber, 'GB');
      }

      if (parsedNumber?.isValid()) {
        formattedNumber = parsedNumber.format('E.164');
      } else {
        // If still invalid, return cleaned original number
        formattedNumber = cleanNumber.trim();
      }
    } catch (error) {
      // If parsing fails, return original number but cleaned
      formattedNumber = phone.address.trim();
    }

    return {
      id: phone.id.toString(),
      number: formattedNumber,
      type: phoneNumberKindToPhoneType(phone.kind),
      ext: phone.extension,
      isPrimary: phone.principal,
    };
  }

  public mapEmail(email: WealthboxEmailAddress): GenericEmail {
    return {
      address: email.address,
      isPrimary: email.principal,
      type: emailAddressKindToEmailAddressType(email.kind),
    };
  }

  public mapCreateContactDtoToCrmFormat(
    contact: ContactDto,
    householdName: string,
    isPrimary: boolean,
    advisorCrmId: number,
    csaAdvisorCrmId: number,
    advisorRole?: CustomizableCategoryDto,
    csaRole?: CustomizableCategoryDto,
  ) {
    const roles: ContactRoleValue[] = [];

    if (advisorRole && advisorCrmId) {
      const roleAssignment = advisorRole.available_options.find(
        (option) => option.assigned_to.id === advisorCrmId,
      );
      if (roleAssignment) {
        roles.push({
          id: advisorRole.id,
          value: roleAssignment?.id,
        });
      }
    }

    if (csaRole && csaAdvisorCrmId) {
      const roleAssignment = csaRole.available_options.find(
        (option) => option.assigned_to.id === csaAdvisorCrmId,
      );
      if (roleAssignment) {
        roles.push({
          id: csaRole.id,
          value: roleAssignment?.id,
        });
      }
    }
    return {
      first_name: contact.firstName,
      last_name: contact.lastName,
      contact_roles: roles,
      email_addresses: contact.email && [
        {
          address: contact.email,
          principal: true,
          kind: WealthboxEmailAddressKind.Mobile,
          destroy: false,
        },
      ],
      phone_numbers: contact?.mobile
        ? [
            {
              address: contact?.mobile?.replace('+1', ''),
              destroy: false,
              kind: WealthboxPhoneNumberKind.Mobile,
              principal: true,
              extension: '+1',
            },
          ]
        : [],
      type: WealthboxContactType.Person,
      household: {
        name: householdName,
        title: isPrimary ? HouseholdTitle.Head : HouseholdTitle.Spouse,
      },
    };
  }
}
