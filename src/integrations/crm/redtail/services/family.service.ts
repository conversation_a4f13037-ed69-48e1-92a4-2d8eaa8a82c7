import { HttpStatus, Logger } from '@nestjs/common';

import { ContactDto } from 'src/clients/dto/v1/create-client.dto';
import { OperationType } from 'src/super-http/enums';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { UpdateContactDto } from '../../types/create-contact.dto';
import { AddMemberResponseDto } from '../dto/responses/email/add-member-response.dto';
import { CreateFamilyResponseDto } from '../dto/responses/family/create-family-response.dto';
import { GetFamilyByContactDto } from '../dto/responses/family/get-family-by-contact.dto';
import {
  RedtailCRMEndpointsEnum,
  RedtailRelationshipEnum,
} from '../types/enums';
import { RedtailFamily, RedtailFamilyMember } from '../types/family.type';

export class RedtailFamilyService {
  private logger: Logger;
  private baseUrl: string;

  constructor(
    private httpService: SuperHttpService,
    private authHeader: object,
    private tenantId: string
  ) {
    this.logger = new Logger('RedtailCRM');
    this.baseUrl = process.env.REDTAIL_BASE_URL;
  }

  public async getFamily(contactId: string): Promise<RedtailFamily> {
    const url = `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${contactId}/family`;
    try {
      const { contact_family: family } = await this.httpService.get<GetFamilyByContactDto>(
        url,
        this.tenantId,
        { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.GET, entityType: 'family' },
        {
          params: {
            family_members: true,
          },
          headers: { ...this.authHeader },
        },
      );
      return family;
    } catch (error) {
      if (
        error.response?.status === HttpStatus.NOT_FOUND ||
        error.status === HttpStatus.NOT_FOUND
      ) {
        return undefined;
      }
      throw error;
    }
  }

  public async upsertFamily(
    contactInfo: UpdateContactDto,
  ): Promise<RedtailFamily | null> {
    const family = await this.getFamily(contactInfo.primaryContact.crmClientId);

    if (!family) {
      return this.createFamily(
        contactInfo.primaryContact,
        contactInfo.secondaryContact,
      );
    }

    await this.deleteFamily(contactInfo.primaryContact.crmClientId);
    return this.createFamily(
      contactInfo.primaryContact,
      contactInfo.secondaryContact,
    );
  }

  public async createFamily(
    primaryContact: ContactDto,
    secondaryContact?: ContactDto,
  ): Promise<RedtailFamily> {
    const familyName = `${primaryContact.firstName} ${primaryContact.lastName} Family`;
    const familyRequestBody = {
      name: familyName,
      members: [
        {
          contact_id: Number(primaryContact.crmClientId),
          hoh: true,
          relationship: null,
        },
      ],
    };

    if (secondaryContact) {
      await this.removeContactFamily(secondaryContact.crmClientId);
      familyRequestBody.members.push({
        contact_id: Number(secondaryContact.crmClientId),
        relationship: RedtailRelationshipEnum.SPOUSE,
        hoh: false,
      });
    }
    const response = await this.httpService.post<CreateFamilyResponseDto>(
      `${this.baseUrl}${RedtailCRMEndpointsEnum.Families}?family_members=true`,
      this.tenantId,
      familyRequestBody,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.CREATE, entityType: 'family' },
      {
        headers: { ...this.authHeader },
      },
    );
    return response.family;
  }

  public async addMemberToFamily(
    familyId: number,
    contactId: string,
    relationship: RedtailRelationshipEnum,
    hoh: boolean,
  ): Promise<RedtailFamilyMember> {
    const familyRequestBody = {
      contact_id: Number(contactId),
      relationship,
      hoh,
    };

    await this.removeContactFamily(contactId);

    const response = await this.httpService.post<AddMemberResponseDto>(
      `${this.baseUrl}${RedtailCRMEndpointsEnum.Families}/${familyId}/members`,
      this.tenantId,
      familyRequestBody,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.CREATE, entityType: 'family' },
      {
        headers: { ...this.authHeader },
      },
    );

    if (!response.family_member) {
      throw new Error('Error adding member to family');
    }
    return response.family_member;
  }

  public async getContactFamily(contactId: string): Promise<RedtailFamily> {
    const response = await this.httpService.post<GetFamilyByContactDto>(
      `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${contactId}/family`,
      this.tenantId,
      undefined,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.GET, entityType: 'family' },
      {
        params: {
          family_members: true,
        },
        headers: { ...this.authHeader },
      },
    );
    return response.contact_family;
  }

  async deleteFamily(contactId: string) {
    const family = await this.getFamily(contactId);
    if (!family) {
      return;
    }
    await this.httpService.delete(
      `${this.baseUrl}${RedtailCRMEndpointsEnum.Families}/${family.id}`,
      this.tenantId,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.DELETE, entityType: 'family' },
      {
        headers: { ...this.authHeader },
      },
    );
  }

  async removeContactFamily(contactId: string) {
    const family = await this.getFamily(contactId);
    if (!family) return;

    const member = family.members.find(
      (member) => member.contact_id.toString() === contactId,
    );
    if (!member) return;

    if (member.hoh) {
      return this.deleteFamily(contactId);
    }

    await this.httpService.delete(
      `${this.baseUrl}${RedtailCRMEndpointsEnum.Families}/${family.id}/members/${member.id}`,
      this.tenantId,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.DELETE, entityType: 'family' },
      {
        headers: { ...this.authHeader },
      },
    );
  }
}
