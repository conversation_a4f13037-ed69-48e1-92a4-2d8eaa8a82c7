export enum HouseholdTitle {
  Head = 'Head',
  Spouse = 'Spouse',
  Partner = 'Partner',
  Child = 'Child',
  <PERSON><PERSON> = 'Grandchild',
  Parent = 'Parent',
  Grandparent = 'Grandparent',
  Sibling = 'Sibling',
  OtherDependent = 'Other Dependent',
}

export interface HouseholdRequest {
  name: string;
  title: HouseholdTitle;
}

export class HouseholdMemberResponseDto {
  id: number;
  first_name: string;
  last_name: string;
  title: string;
  type: string;
}
