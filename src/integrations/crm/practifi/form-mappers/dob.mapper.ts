import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PractifiCRM } from '../crm.practifi';

interface DOBPageData {
  dob: string;
}

export class PractifiDOBMapper implements FormUpdateMapper {
  constructor(private practifiCRM: PractifiCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: DOBPageData,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
 
    const newDate = new Date(pageData.dob);
    const formattedDate = newDate.toISOString().split('T')[0];

    await this.practifiCRM.contactService.updateContact(contact.crmClientId, {
      Birthdate: formattedDate,
    });
  }
} 
