import {
  InterviewDataWithCrmInfo,
  MergedContact,
} from 'src/interviews/types/interview-data.type';
import { ContactTypeEnum } from 'src/transitions/types/contact-type.enum';

export function isContactFile(fileName: string) {
  return fileName.includes('#Contact_');
}

export function computeContactFileSuffix(type: ContactTypeEnum) {
  return `#Contact_${type}`;
}

export function parseContactEnumFromFileName(fileName: string) {
  if (!fileName.includes('#Contact_')) {
    throw new Error('Invalid file name, does not match #Contact_ pattern.');
  }
  return fileName.split('#')[1].replace('Contact_', '').replace('.pdf', '');
}

export function getContactByFileName(
  interviewData: InterviewDataWithCrmInfo,
  fileName: string,
): MergedContact {
  const contactType = parseContactEnumFromFileName(fileName);

  let contact;
  if (contactType === ContactTypeEnum.Primary) {
    contact = interviewData.primaryContact;
  }

  if (contactType === ContactTypeEnum.Secondary) {
    contact = interviewData.secondaryContact;
  }

  if (!contact) {
    throw new Error(`Contact with type ${contactType} not found`);
  }

  return contact;
}
