import { HttpService } from '@nestjs/axios';
import {
  HttpException,
  HttpStatus,
  InternalServerErrorException,
} from '@nestjs/common';
import { AxiosRequestConfig } from '@nestjs/terminus/dist/health-indicator/http/axios.interfaces';
import { isAxiosError } from '@nestjs/terminus/dist/utils';
import { HttpMethodsEnum } from 'src/shared/types/general/http-methods.enum';

const MAX_RETRY_COUNT = 3;

const BASE_DELAY_MS = 1000; // Base delay for exponential backoff
const MAX_DELAY_MS = 30000; // Maximum delay to cap the backoff
const JITTER_MS = 1000; // Maximum jitter to add randomness

// Function to introduce a delay, used in exponential backoff
function delay(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
export async function makeRequest<T>(
  httpService: HttpService,
  method: HttpMethodsEnum,
  url: string,
  data?: unknown,
  options?: AxiosRequestConfig,
  retry: number = MAX_RETRY_COUNT,
): Promise<T> {
  let lastError;
  while (retry > 0) {
    try {
      if (method === HttpMethodsEnum.GET || method === HttpMethodsEnum.DELETE) {
        const response = await httpService.axiosRef[method]<T>(url, options);
        return response?.data;
      }

      const response = await httpService.axiosRef[method]<T>(
        url,
        data,
        options,
      );

      return response?.data;
    } catch (error) {
      const status = error.response?.status;
      lastError = error;
      const shouldRetry =
        error.response &&
        [
          HttpStatus.GATEWAY_TIMEOUT,
          HttpStatus.SERVICE_UNAVAILABLE,
          HttpStatus.BAD_GATEWAY,
        ].some((code) => code === status) &&
        retry > 0;

      if (shouldRetry) {
        retry--;
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } else {
        if (isAxiosError(error)) {
          throw new HttpException(
            (error.response?.data && JSON.stringify(error.response?.data)) ||
              error.message,
            error.response?.status || error.code,
          );
        }
        throw error;
      }
    }
  }
  throw lastError;
}
