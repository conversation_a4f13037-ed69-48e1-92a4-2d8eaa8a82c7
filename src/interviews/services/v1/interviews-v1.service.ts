import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import { isEmpty } from 'lodash';
import { ClientSession, Connection, FilterQuery, Model } from 'mongoose';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { ClientsV1Service } from 'src/clients/services/v1/clients-v1.service';
import {
  RedtailAccountOwnershipEnum,
  RedtailDBAccountType,
} from 'src/integrations/crm/redtail/types/enums';
import { GenericCrmAccount } from 'src/integrations/crm/types/accounts/crm-account.type';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { DocusignAccountOwnershipEnum } from 'src/integrations/docusign/docusign.types';
import { InterviewTemplatesService } from 'src/interview-templates/interview-templates.service';
import {
  InterviewPage,
  InterviewTemplate,
} from 'src/interview-templates/schemas/v1/interview.template';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { Asset } from 'src/shared/schemas/asset.schema';
import {
  AccountAdvisoryDocumentsEnum,
  AccountClientDocumentsEnum,
} from 'src/shared/types/accounts/account-documents.enum';
import { AccountFeaturesEnum } from 'src/shared/types/accounts/account-features.enum';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { AccountDto } from 'src/shared/types/accounts/account.dto';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { CreateInterviewDto } from '../../dto/v1/create-interview.dto';
import { RequiredDocumentDto } from '../../dto/v1/required-document.dto';
import { UpdateInterviewDto } from '../../dto/v1/update-interview.dto';
import { UploadDocumentDto } from '../../dto/v1/upload-document.dto';
import { Interview } from '../../schemas/v1/interview.schema';
import { AssetTypeEnum } from 'src/shared/types/general/asset-types.enum';
import { computeAccountFileSuffix } from 'src/utils/accounts/accountFileNameSuffix';
import AWS from 'aws-sdk';
import { createWriteStream, existsSync } from 'fs';
import path from 'path';
import { DOWNLOAD_PATH, getFileCacheKey } from 'src/utils/cache/cache.util';
import { mkdirAsync, unlinkAsync } from 'src/utils/file/file.util';
import { MailService } from 'src/notifications/mail/mail.service';
import { ReadyToSignEmailContext } from 'src/templates/mail/ready-to-sign/ready-to-sign.context';
import { DesktopInterviewEmailContext } from 'src/templates/mail/desktop-interview/desktop-interview.context';
import { getFrontendDomain } from 'src/utils/getFrontendDomain';
import { NonCitizenEmailContext } from 'src/templates/mail/non-citizen/non-citizen.context';
import { EmailTemplateNameEnum } from 'src/templates/mail/types';
import { InterviewViewDto } from '../../dto/v1/extended-interview.dto';
import { ClsService } from 'nestjs-cls';
import { PageUpdateContext } from 'src/interviews/types/page-update-context.type';
import { getPageHandler } from 'src/interviews/utils/page-updates/handlers.mapper';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { EnrichedInterview } from '../../dto/v1/enriched-interview-dto';
import {
  EnrichedClient,
  EnrichedContact,
} from 'src/clients/dto/v1/get-clients.dto';
import { Account } from 'src/clients/schemas/clients.schema';
import { AccountDocument } from 'src/shared/types/accounts/account-documents.type';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { CreateEnvelopeDto } from 'src/integrations/docusign/dto/create-envelope.dto';
import { AddClientDocumentsDto } from 'src/integrations/docusign/dto/add-client-documents.dto';
import { AddAdvisorDocumentsDto } from 'src/integrations/docusign/dto/add-advisor-documents.dto';
import {
  InterviewCompletionParams,
  InterviewData,
  InterviewDataWithCrmInfo,
} from 'src/interviews/types/interview-data.type';
import { CacheService } from 'src/shared/services/cache.service';
import { Logger } from 'winston';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { fdob } from 'src/utils/formate-dob';
import { AdvisorsDocusignService } from 'src/advisors/services/advisors.docusign.service';
import { FlowProducer, Queue } from 'bullmq';
import { InjectQueue } from '@nestjs/bullmq';
import { INTERVIEW_QUEUE } from 'src/interviews/constants/interview-queue.constant';
import { InterviewPageStatusEnum } from 'src/interview-templates/types/interview-page-status.enum';
import { CRM_JOBS, CRM_QUEUE } from 'src/integrations/crm/constants/crm.constants';
import { CRMEnum, CRMType } from 'src/shared/types/integrations';
import { generateClientHash } from 'src/clients/utils/queue.utils';
import { CLIENT_QUEUE } from 'src/shared/constants/client.constant';
import { InterviewStatusEnum } from 'src/clients/types/client-interview-status';
import { ClientQueueJobType } from 'src/clients/types/client-queue-job.enum';
import { InterviewQueueJobType } from '../../types/interview-queue-job.enum';


@Injectable()
export class InterviewsV1Service {
  private readonly flowProducer: FlowProducer;

  constructor(
    @InjectModel(Asset.name) private assetModel: Model<Asset>,
    @InjectModel(Interview.name) private interviewModel: Model<Interview>,
    private readonly interviewTemplateService: InterviewTemplatesService,
    @Inject(forwardRef(() => ClientsV1Service))
    private readonly clientsService: ClientsV1Service,
    @Inject(forwardRef(() => AdvisorsCrmService))
    private readonly advisorsCrmService: AdvisorsCrmService,
    @Inject(forwardRef(() => AdvisorsCrudService))
    private readonly advisorsCrudService: AdvisorsCrudService,
    @Inject(forwardRef(() => AdvisorsDocusignService))
    private readonly advisorsDocusignService: AdvisorsDocusignService,
    @Inject(forwardRef(() => DocusignService))
    private readonly docusignService: DocusignService,
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => OrganisationsService))
    private readonly organisationsService: OrganisationsService,
    private readonly mailService: MailService,
    private readonly clsService: ClsService,
    private readonly transactionManager: TransactionManager,
    private readonly cacheService: CacheService,
    @InjectQueue(INTERVIEW_QUEUE.NAME) private readonly interviewQueue: Queue,
    @InjectQueue(CLIENT_QUEUE.NAME) private readonly clientQueue: Queue,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    @InjectConnection() private readonly connection: Connection,

  ) {

    this.flowProducer = new FlowProducer({ connection: this.interviewQueue.opts.connection });

  }

  /**
   * Creates a new interview.
   *
   * @param createInterviewDto - The data for creating the interview.
   * @param session - Optional MongoDB session for transactional support.
   * @returns A Promise that resolves to the created interview.
   */
  async create(
    createInterviewDto: CreateInterviewDto,
    session?: ClientSession,
  ): Promise<Interview> {
    const { customTemplates, clientId, accounts, isPrimary, docusignSelected } =
      createInterviewDto;

    let interviewTemplate: InterviewTemplate;
    const defaultTemplate =
      await this.interviewTemplateService.getDefaultTemplate();

    if (createInterviewDto.isAlreadyOnbord) {
      defaultTemplate.pages.forEach((page) => {
        page.filled = true;
      });
    }

    if (customTemplates && customTemplates.length > 0) {
      const templatesToMerge = [...customTemplates];
      if (createInterviewDto.doClientProfiling) {
        templatesToMerge.push(defaultTemplate._id);
      }
      interviewTemplate = await this.interviewTemplateService.getMergedTemplate(
        templatesToMerge,
        session,
      );
    } else {
      interviewTemplate = defaultTemplate;
    }

    const client = await this.clientsService.findOne(
      {
        _id: clientId,
      },
      session,
    );

    const clientAccounts: Account[] = isPrimary
      ? client.primaryContact?.accounts || []
      : client.secondaryContact?.accounts || [];

    interviewTemplate = this.setUpAdditionalPagesInInterviewTemplate(
      interviewTemplate,
      clientAccounts,
    );

    const documents: AccountDocument[] = [];
    if (docusignSelected)
      await Promise.all(
        clientAccounts.map(async ({ features, _id, label, type }) => {
          const clientFiles = await this.selectClientFilesToFetch(
            features,
            _id.toString(),
          );
          documents.push(
            ...clientFiles.map((document) => ({
              label,
              type,
              name: document.documentType,
              feature: document.feature,
              accountId: document.accountId,
            })),
          );
        }),
      );

    const template = createInterviewDto.isAlreadyOnbord
      ? {
        ...interviewTemplate,
        pages: interviewTemplate.pages.map((page) => ({
          ...page,
          filled: true,
        })),
      }
      : interviewTemplate;

    const d = new Date();
    const created = await this.interviewModel.create(
      [
        {
          ...createInterviewDto,
          template,
          client: client._id,
          documents,
          docusignSelected,
          createdAt: d,
          updatedAt: d,
        },
      ],
      { session },
    );

    return created[0];
  }

  /**
   * Sets up additional pages in the interview template.
   *
   * @param template - The original interview template.
   * @param accountInstances - The account instances to be added as pages.
   * @param customQuestions - The custom questions to be added as pages.
   * @returns The updated interview template with additional pages.
   */
  private setUpAdditionalPagesInInterviewTemplate(
    template: InterviewTemplate,
    accountInstances: AccountDto[],
  ): InterviewTemplate {
    const plainTemplate = template.toObject();

    // Filter for only IRA accounts with explicit type checking
    const iraAccounts = accountInstances.filter(account => {
      const isIra = account.type === AccountTypeEnum.Ira;
      const isRothIra = account.type === AccountTypeEnum.RothIra;
      return isIra || isRothIra;
    });

    // If no IRA accounts, filter out all beneficiary pages
    const newPages = plainTemplate.pages.filter((page: InterviewPage) => {
      if (iraAccounts.length === 0 && (
        page.name === PagesEnum.PRIMARY_BENEFICIARIES ||
        page.name === PagesEnum.CONTINGENT_BENEFICIARIES
      )) {
        return false; // Remove these pages
      }
      return true;
    }).flatMap((page: InterviewPage) => {
      // If we have IRA accounts and this is a beneficiary page, create one for each IRA account
      if (iraAccounts.length > 0 && (
        page.name === PagesEnum.PRIMARY_BENEFICIARIES ||
        page.name === PagesEnum.CONTINGENT_BENEFICIARIES
      )) {
        return iraAccounts.map((account) => ({
          ...page,
          data: {
            instance: account.type,
            label: account.label,
          },
        }));
      }
      return [page];
    });

    return {
      ...plainTemplate,
      pages: newPages,
    };
  }

  /**
   * Finds interviews based on the provided filter.
   * @param filter The filter to apply when searching for interviews.
   * @returns A promise that resolves to an array of interviews matching the filter.
   */
  async find(filter: FilterQuery<Interview>) {
    return this.interviewModel.find(filter);
  }

  /**
   * Retrieves all interviews.
   * @returns A promise that resolves to an array of Interview objects.
   */
  async findAll(): Promise<Interview[]> {
    return this.interviewModel.find();
  }

  /**
   * Finds a single interview based on the provided filter.
   * @param filter - The filter to apply when searching for the interview.
   * @param session - Optional MongoDB session to use for the operation.
   * @returns A promise that resolves to the found interview.
   */
  async findOne(
    filter: FilterQuery<Interview>,
    session?: ClientSession,
    skipEnrich: boolean = false,
  ): Promise<EnrichedInterview | null> {
    const interview = await this.interviewModel
      .findOne(filter)
      .sort({ createdAt: 'desc' })
      .session(session);

    if (!interview) return;

    const client = await this.clientsService.findOne(
      {
        _id: interview.client,
      },
      session,
      false,
      skipEnrich,
    );

    return { ...interview.toObject(), client };
  }

  /**
   * Finds an interview by its ID.
   * @param id - The ID of the interview.
   * @returns A promise that resolves to an InterviewViewDto object representing the found interview.
   */
  async findById(
    id: string,
    session?: ClientSession,
    skipEnrich: boolean = false,
  ): Promise<InterviewViewDto> {
    const start = performance.now();
    const interview = await this.interviewModel.findById(id);

    if (isEmpty(interview)) {
      throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);
    }

    const client = await this.clientsService.findOne(
      {
        _id: interview.client,
      },
      session,
      false,
      skipEnrich,
    );

    // Find the first logo type asset found for organisation
    const organisationLogo = (
      await this.organisationsService.findOne(client?.organisationId.toString())
    )?.assets?.filter((asset) => asset.assetType === 'logo')[0]?.assetLocation;

    // Return interview with organisation logo
    return { ...interview.toObject(), client, organisationLogo };
  }

  async enqueueFlow(interviewId: string, updateInterviewDto: UpdateInterviewDto) {
    const interview = await this.findOne({
      _id: interviewId,
    }, undefined, true);

    if (!interview) {
      throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);
    }

    const organisation = await this.organisationsService.findOne(
      interview.client.organisationId.toString(),
    );
    const organisationId = organisation._id;

    try {
      // Update page status synchronously first
      await this.updateInterviewPages(interviewId, updateInterviewDto, InterviewPageStatusEnum.IN_PROGRESS);

      // Then enqueue the flow for CRM sync and final status update
      await this.flowProducer.add({
        name: InterviewQueueJobType.UPDATE_PAGE.toString(),
        data: {
          interviewId,
          organisationId: organisation._id.toString(),
          updateInterviewDto,
          pageStatus: InterviewPageStatusEnum.SYNCED,
        },
        queueName: INTERVIEW_QUEUE.NAME,
        children: [
          {
            name: CRM_JOBS.UPDATE_PAGE_ANSWER.toString(),
            data: {
              interviewId,
              updateInterviewDto,
              organisationId,
            },
            queueName: this.getCrmQueueName(organisation.selectedCRM),
            opts: {
              failParentOnFailure: true,
              attempts: 3,
              backoff: {
                type: 'exponential',
                delay: 1000,
              }
            }
          }
        ],
        opts: {
          failParentOnFailure: true,
          attempts: 3,
          delay: 300,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        }
      });

      return interview;
    } catch (error) {
      this.logger.error('Failed to enqueue interview update flow', {
        error: error.message,
        interviewId
      });

      // If we fail to enqueue, update the status to FAILED
      try {
        await this.updateInterviewPages(interviewId, updateInterviewDto, InterviewPageStatusEnum.FAILED);
      } catch (updateError) {
        this.logger.error('Failed to update page status to FAILED after queue error', {
          error: updateError.message,
          interviewId
        });
      }

      throw error;
    }
  }

  private getCrmQueueName(selectedCrm: CRMEnum): string {

    // warning refactor this
    if (selectedCrm === CRMEnum.Redtail) {
      return CRM_QUEUE.REDTAIL.NAME;
    } else if (selectedCrm === CRMEnum.Wealthbox) {
      return CRM_QUEUE.WEALTHBOX.NAME;
    } else if (selectedCrm === CRMEnum.Salesforce) {
      return CRM_QUEUE.SALESFORCE.NAME;
    } else if (selectedCrm === CRMEnum.Practifi) {
      return CRM_QUEUE.PRACTIFI.NAME;
    }

    throw new Error('Unsupported CRM type');
  }

  public async updateInterviewPages(id: string, updateInterviewDto: UpdateInterviewDto, pageStatus: InterviewPageStatusEnum, session?: ClientSession) {

    const interview = await this.findOne({
      _id: id,
    }, session, true);

    const client = <EnrichedClient>interview.client;

    const updatedPages = this.updatePages(
      interview.template.pages,
      updateInterviewDto,
      pageStatus,
    );

    const percentageCompleted = this.calculateCompletionPercentage(
      interview,
      client,
    );

    // Update the client with the new completion percentage and current timestamp
    const now = new Date();
    await this.clientsService.updateClientCompletionPercentage(
      interview.client._id.toString(),
      percentageCompleted,
      now,
      session,
    );

    await this.clientsService.updateLastContactActivityTimestamp(
      interview.client._id,
      interview.isPrimary,
      session,
    );

    return this.interviewModel.findByIdAndUpdate(
      interview._id,
      {
        $set: { 'template.pages': updatedPages },
      },
      { new: true, session },
    );
  }

  private parseCrmAuthError(message) {
    const error = message.toLowerCase();

    if (error.includes('locked')) {
      return 'Your CRM account has been locked. Please contact your advisor for assistance.'
    }

    if (error.includes('invalid')) {
      return 'There was an issue connecting to the CRM. Please contact your advisor for assistance.'
    }

    return 'An unknown error occurred during CRM authentication. Please try again later.';

  }

  async removePageFromInterview(id: string, pageName: string): Promise<void> {
    const interview = await this.findOne({ _id: id });

    if (!interview) {
      throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);
    }

    if (!interview.template.pages.find((page) => page.name === pageName)) {
      return;
    }

    const result = await this.interviewModel.updateOne(
      { _id: id },
      { $pull: { 'template.pages': { name: pageName } } },
    );

    if (result.modifiedCount === 0) {
      throw new HttpException(
        'Page not found or Interview not found',
        HttpStatus.NOT_FOUND,
      );
    }
  }

  async addPageToInterview(id: string, pageName: PagesEnum): Promise<void> {
    const interview = await this.interviewModel.findOne({
      _id: id,
      'template.pages.name': { $ne: pageName },
    });
    if (!interview) {
      return;
    }

    await this.interviewModel.updateOne(
      { _id: id },
      {
        $push: {
          'template.pages': {
            // Assuming the structure of the page object to be added
            name: pageName,
            order: 0,
            elements: [],
            filled: false,
            data: {},
          },
        },
      },
      {
        runValidators: false,
        new: true,
      },
    );
  }

  /**
   * Removes an interview by its ID.
   * @param id The ID of the interview to be removed.
   * @returns A Promise that resolves to the removed interview.
   */
  async remove(id: string, session?: ClientSession): Promise<Interview> {
    return this.interviewModel.findByIdAndDelete(id, { session });
  }

  /**
   * Adds a required document to an interview.
   * @param id - The ID of the interview.
   * @param dto - The RequiredDocumentDto containing the document name.
   * @returns A promise that resolves to the result of the update operation.
   */
  async addRequiredDocument(id: string, dto: RequiredDocumentDto) {
    return this.interviewModel.updateOne(
      { _id: id },
      {
        $push: {
          documents: {
            name: dto.document,
            updatedAt: null,
          },
        },
      },
    );
  }

  /**
   * Removes a required document from an interview.
   * @param id - The ID of the interview.
   * @param dto - The RequiredDocumentDto containing the document to be removed.
   * @returns A promise that resolves to the result of the update operation.
   */
  async removeRequiredDocument(id: string, dto: RequiredDocumentDto) {
    const interview = await this.findOne({ _id: id });
    return this.interviewModel.updateOne(
      { _id: id },
      {
        $set: {
          documents: interview.documents.filter(
            (document) => document.name !== dto.document,
          ),
        },
      },
    );
  }

  /**
   * Uploads documents to a Docusign envelope and updates the interview document information.
   * @param id - The ID of the interview.
   * @param files - An array of files to be uploaded.
   * @param dto - The DTO containing the document info.
   * @returns A string indicating that the document was successfully uploaded.
   */
  async upload(
    id: string,
    files: Express.Multer.File[],
    dto?: UploadDocumentDto,
    session?: ClientSession,
  ): Promise<string> {
    // Remove the session creation if one is provided
    let transactionToUse = session;
    let shouldManageTransaction = false;

    if (!session) {
      transactionToUse = await this.connection.startSession();
      await transactionToUse.startTransaction();
      shouldManageTransaction = true;
    }

    try {
      const interview = await this.findOne({ _id: id }, transactionToUse);

      if (!interview) {
        throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);
      }

      let envelopeId = interview.envelopeId;
      const { client } = interview;
      const clientId = client._id.toString();

      const envelopeExists = !!envelopeId;
      if (!envelopeExists) {
        envelopeId = await this.createDocusignEnvelope(id, transactionToUse);
      }

      // Upload the files to the envelope
      const { organisationId, primaryAdvisor } =
        await this.clientsService.findOne({ _id: clientId }, transactionToUse);

      const entityIds = {
        organisationId: organisationId.toString(),
        advisorId: primaryAdvisor.id.toString(),
      };

      const addClientDocumentsDto: AddClientDocumentsDto = {
        ...entityIds,
        envelopeId,
        files,
      };

      await this.docusignService.addClientDocuments(addClientDocumentsDto);

      // Update the database after documents are uploaded
      const { documentName, accountId, feature } = dto || {};

      const query = { _id: id, 'documents.name': documentName };
      const filter = { 'elem.name': documentName };

      if (!isEmpty(accountId)) {
        query['documents.accountId'] = accountId;
        filter['elem.accountId'] = accountId;
      }

      if (!isEmpty(feature)) {
        query['documents.feature'] = feature;
        filter['elem.feature'] = feature;
      }

      if (!!documentName && documentName.length > 0) {
        const { fileUploadsNo } = await this.clientsService.findOne(
          { _id: clientId },
          transactionToUse,
        );

        const now = new Date();
        const interviewUpdatePromise = this.interviewModel.updateOne(
          query,
          {
            $set: {
              'documents.$[elem].updatedAt': now,
            },
          },
          {
            arrayFilters: [filter],
            session: transactionToUse,
          },
        );

        const clientUpdatePromise = this.clientsService.update(
          clientId,
          {
            fileUploadsNo: fileUploadsNo + 1,
            status: ClientStatusEnum.Sent,
          },
          transactionToUse,
        );

        await Promise.all([interviewUpdatePromise, clientUpdatePromise]);
      }

      this.clientQueue.add(ClientQueueJobType.UPDATE_LAST_CONTACT_ACTIVITY_TIMESTAMP, {
        clientId,
        isPrimary: interview.isPrimary,
      });

      if (shouldManageTransaction) {
        await transactionToUse.commitTransaction();
      }

      return 'Document successfully uploaded.';
    } catch (error) {
      if (shouldManageTransaction) {
        await transactionToUse.abortTransaction();
      }
      throw new HttpException(
        error.message,
        error?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    } finally {
      if (shouldManageTransaction) {
        await transactionToUse.endSession();
      }
    }
  }

  /**
   * @deprecated Use complete() instead which provides better validation and reliable queue-based processing
   * This method is kept for backwards compatibility and delegates to complete()
   */
  async finish(id: string, session?: ClientSession): Promise<EnrichedInterview> {
    return this.complete(id, session);
  }

  /**
   * Marks an interview as complete and performs necessary updates.
   * @param id - The ID of the interview to finish.
   * @param session - Optional MongoDB session for transaction support.
   * @returns The finished interview.
   */
  async complete(id: string, session?: ClientSession): Promise<EnrichedInterview> {
    const isAllPageSynced = await this.validateAllPagesAreSynced(id);
    if (!isAllPageSynced) {
      throw new HttpException(
        'Cannot finish interview: Some pages are not synced',
        HttpStatus.BAD_REQUEST
      );
    }

    const interview = await this.findOne({ _id: id }, session, true);
    if (!interview) {
      throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);
    }

    const { client } = interview;
    const { primaryContact, secondaryContact, primaryAdvisor } = client;

    const completionFlowParams = {
      interview,
      envelopeId: interview.envelopeId,
      organisationId: client.organisationId.toString(),
      advisorId: primaryAdvisor.id.toString(),
      clientId: client._id.toString(),
      primaryContact,
      secondaryContact,
      primaryAdvisor,
      docusignSelected: interview.docusignSelected,
      isPrimary: interview.isPrimary,
    };

    try {
      await this.enqueueInterviewCompletionFlow(completionFlowParams);
      return interview;
    } catch (error) {
      this.logger.error(`Failed to finish interview: ${error.message}`);
      throw error;
    }
  }

  async enqueueInterviewCompletionFlow(params: InterviewCompletionParams) {
    const {
      interview,
      clientId,
      organisationId,
      advisorId,
      primaryContact,
      secondaryContact,
      primaryAdvisor,
      envelopeId,
      docusignSelected,
      isPrimary
    } = params;

    const organisation = await this.organisationsService.findOne(organisationId);
    if (!organisation || !organisation.selectedCRM) {
      throw new HttpException('Organization or CRM not found', HttpStatus.BAD_REQUEST);
    }

    const flowId = generateClientHash(organisationId, {
      organisationId,
      primaryContact: isPrimary ? primaryContact : secondaryContact,
      primaryAdvisor: { id: primaryAdvisor._id },
    });

    const existingFlow = await this.flowProducer.getFlow({
      id: flowId,
      queueName: INTERVIEW_QUEUE.NAME,
    });

    if (existingFlow) {
      throw new HttpException('Interview already in progress', HttpStatus.BAD_REQUEST);
    }

    const interviewId = interview._id.toString();
    const baseJobConfig = {
      queueName: CLIENT_QUEUE.NAME,
      data: {
        clientId,
        organisationId,
        tenantId: organisationId,
        integrationName: organisation.selectedCRM,
      },
    };

    const makeInterviewAsComplete = {
      name: InterviewQueueJobType.MARK_COMPLETE.toString(),
      queueName: INTERVIEW_QUEUE.NAME,
      data: {
        interviewId,
        organisationId,
        message: 'Interview marked as complete'
      },
      opts: {
        jobId: `${flowId}-complete-interview-flow`,
      },
    }

    const updateClientInterviewStatusInProgressJob = {
      name: ClientQueueJobType.UPDATE_INTERVIEW_STATUS.toString(),
      queueName: CLIENT_QUEUE.NAME,
      data: {
        ...baseJobConfig.data,
        interviewStatus: InterviewStatusEnum.InProgress,
        message: 'Client interview status in progress',
      },
      opts: {
        jobId: `${flowId}-update-client-interview-status-in-progress`,
      },
      children: [makeInterviewAsComplete]
    }

    const prepareDocusignEnvelopJob = {
      name: InterviewQueueJobType.PREPARE_DOCUSIGN_ENVELOPE.toString(),
      queueName: INTERVIEW_QUEUE.NAME,
      data: {
        ...baseJobConfig.data,
        interviewId,
        message: 'Successfully prepared docusign envelop',
      },
      opts: {
        jobId: `${flowId}-prepare-docusign-envelop`,
      },
      children: [updateClientInterviewStatusInProgressJob]
    };

    const updateClientStatusAfterEnvelopeJob = {
      name: ClientQueueJobType.UPDATE_CLIENT_STATUS_AFTER_ENVELOPE.toString(),
      queueName: CLIENT_QUEUE.NAME,
      data: {
        ...baseJobConfig.data,
        docusignSelected,
      },
      opts: {
        jobId: `${flowId}-update-client-status-after-envelope`,
      },
      children: [prepareDocusignEnvelopJob]
    };


    const updateClientInterviewStatusEnvelopCreatedJob = {
      name: ClientQueueJobType.UPDATE_INTERVIEW_STATUS.toString(),
      queueName: CLIENT_QUEUE.NAME,
      data: {
        ...baseJobConfig.data,
        interviewStatus: InterviewStatusEnum.EnvelopCreated,
        message: 'Client interview status envelope created'
      },
      opts: {
        jobId: `${flowId}-update-client-interview-status-envelop-created`,
      },
      children: [updateClientStatusAfterEnvelopeJob]
    };

    const notificationJob = {
      name: InterviewQueueJobType.SEND_NOTIFICATION.toString(),
      data: {
        ...baseJobConfig.data,
        interviewId,
        message: 'Notification for signature request sent'
      },
      queueName: INTERVIEW_QUEUE.NAME,
      opts: {
        jobId: `${flowId}-send-notifications`,
      },
      children: [updateClientInterviewStatusEnvelopCreatedJob]
    };

    return this.flowProducer.add({
      queueName: CLIENT_QUEUE.NAME,
      name: ClientQueueJobType.UPDATE_CLIENT_INTERVIEW_COMPLETION.toString(),
      data: {
        ...baseJobConfig.data,
        interviewId,
        isPrimary,
        docusignSelected,
        percentage: 100,
        message: 'Client updated: status, completion percentage and timestamp'
      },
      opts: {
        jobId: `${flowId}-update-client`,
      },
      children: [notificationJob]
      // children: [updateClientInterviewStatusCompleted]
    });
  }

  /**
   * Validates that all pages in the interview have a SYNCED status
   * @param interviewId - The ID of the interview to validate
   * @returns A promise that resolves to a boolean indicating if all pages are synced
   * @throws HttpException if interview is not found
   */
  async validateAllPagesAreSynced(interviewId: string): Promise<boolean> {
    const interview = await this.findOne({ _id: interviewId });

    if (!interview) {
      throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);
    }

    // Check if all pages have SYNCED status
    return interview.template.pages.every(
      (page) => page.status === InterviewPageStatusEnum.SYNCED
    );
  }

  async markInterviewAsComplete(interviewId: string, session: ClientSession) {
    await this.interviewModel.findByIdAndUpdate(
      interviewId,
      {
        $set: {
          isComplete: true,
          updatedAt: new Date(),
        },
      },
      { session },
    );
  }

  async sendNotification(interviewId: string, session: ClientSession) {
    const interview = await this.findOne({
      _id: interviewId,
    }, session);
    ;
    const client = await this.clientsService.findOne({ _id: interview.client }, session);

    if (!client) {
      throw new HttpException('Client not found', HttpStatus.NOT_FOUND);
    }


    if (client.status === ClientStatusEnum.PendingReview) {
      this.sendReadyToSignEmail(interview)
    }
  }

  async prepareDocusignEnvelope(interviewId: string, session: ClientSession) {
    try {
      const interview = await this.findOne({
        _id: interviewId,
      }, session);

      if (!interview) {
        throw new Error(`Interview ${interviewId} not found`);
      }

      const { client } = interview;
      if (!client) {
        throw new Error(`Client not found for interview ${interviewId}`);
      }

      const { primaryContact, secondaryContact, primaryAdvisor } = client;


      const clientId = interview.client._id.toString();
      const organisationId = client.organisationId.toString();
      const advisorId = primaryAdvisor.id.toString();

      let envelopeId = interview.envelopeId;

      if (interview.docusignSelected) {
        const envelopeExists = !!envelopeId;
        if (!envelopeExists) {
          try {
            envelopeId = await this.createDocusignEnvelope(interviewId);
          } catch (error) {
            this.logger.error(`Failed to create DocuSign envelope: ${error.message}`);
            throw error;
          }
        }

        // Update the client status based on the interview completion
        const interviews = await this.interviewModel.find(
          { client: clientId },
          null,
          { session },
        );

        const bothInterviewsFinished = interviews.every(
          (interview) => interview.isComplete,
        );

        if (bothInterviewsFinished) {
          // Check the DocuSign account ownership
          const { accountOwnership } = await this.docusignService.getAccountInfo({
            organisationId,
            advisorId,
          });

          // Get the interview data enriched with CRM information
          const interviewData = await this.getInterviewDataWithCrmInfo(
            {
              interview,
              primaryContact,
              secondaryContact,
              primaryAdvisor,
            },
            advisorId,
          );

          // Get existing envelope documents
          const existingEnvelopFiles = await this.docusignService.getEnvelopDocuments({
            envelopeId,
            organisationId,
            advisorId
          }) || { envelopeDocuments: [] };

          // Safely extract filenames
          const existingFilenames = existingEnvelopFiles.envelopeDocuments?.map(doc =>
            typeof doc === 'string' ? doc : doc?.name
          ).filter(Boolean) || [];

          // Get files to be uploaded
          const files: Express.Multer.File[] = await this.getEnvelopeFiles(
            accountOwnership,
            organisationId,
            primaryContact,
            secondaryContact,
          ) || [];

          // Filter out already existing files
          const filesToUpload = files.filter(file =>
            file?.originalname && !existingFilenames.includes(file.originalname)
          );

          // Update the DocuSign envelope with the account opening advisory files
          const addAdvisorDocumentsDto: AddAdvisorDocumentsDto = {
            advisorId,
            organisationId,
            envelopeId,
            files: filesToUpload,
            interviewData,
            previousDocumentUploadNo: client?.fileUploadsNo || 0,
          };
          await this.docusignService.addAdvisorDocuments(addAdvisorDocumentsDto);
          this.logger.info(
            `Added advisor documents to envelope for client ${clientId}`,
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error in prepareDocusignEnvelope: ${error.message}`);
      throw error; // Let the processor handle the transaction abort
    }
  }

  async getEnvelopeFiles(
    accountOwnership: DocusignAccountOwnershipEnum,
    organisationId: string,
    primaryContact: EnrichedContact,
    secondaryContact: EnrichedContact,
  ): Promise<Express.Multer.File[]> {
    const advisoryFiles: Express.Multer.File[] = [];

    // Get the account opening advisory files for the applicant and co-applicant
    const applicantAccountFiles = await this.getAccountOpeningAdvisoryFiles(
      primaryContact?.accounts || [],
      organisationId,
    );

    const coApplicantAccountFiles = await this.getAccountOpeningAdvisoryFiles(
      secondaryContact?.accounts || [],
      organisationId,
    );

    advisoryFiles.push(
      ...(await this.getFilesFromS3(
        [AccountAdvisoryDocumentsEnum.AdvisoryAgreement],
        organisationId,
      )),
    );

    return [
      ...applicantAccountFiles,
      ...coApplicantAccountFiles,
      ...advisoryFiles,
    ];
  }

  /**
   * Updates the pages of an interview based on the provided updateInterviewDto.
   *
   * @param pages - The array of pages to be updated.
   * @param updateInterviewDto - The DTO containing the updated interview data.
   * @returns An array of updated pages.
   */
  public updatePages(pages, updateInterviewDto, pageStatus = InterviewPageStatusEnum.SYNCED) {
    const context: PageUpdateContext = {
      fillContingentBeneficiaries: false,
    };

    return pages.map((page) => {
      const pageHandler = getPageHandler(
        page.name,
        context,
        updateInterviewDto,
        pageStatus,
      );
      return pageHandler.handle(page);
    });
  }

  /**
   * Sends a desktop interview email.
   *
   * @param id - The ID of the interview.
   * @returns A promise that resolves when the email is sent.
   */
  async sendDesktopInterviewEmail(id: string) {
    const interview = await this.findOne({ _id: id });
    const client = interview.client;
    const organisation = await this.organisationsService.findOne(
      client.organisationId.toString(),
    );

    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const to: string = contact.email;
    const templateName = EmailTemplateNameEnum.DesktopInterview;
    const context: DesktopInterviewEmailContext = {
      organisationName: organisation.name,
      interviewUrl: `https://${getFrontendDomain()}/interview/${interview._id}`,
    };

    return this.mailService.sendEmail(
      { to, templateName, context, organisation },
      contact,
      client.primaryAdvisor.id?.toString(),
    );
  }

  /**
   * Sends a non-citizen email for the specified interview.
   * @param id - The ID of the interview.
   * @returns A promise that resolves when the email is sent.
   */
  async sendNonCitizenEmail(id: string) {
    const interview = await this.findOne({ _id: id });
    const client = interview.client;
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const to: string = contact.email;
    const templateName = EmailTemplateNameEnum.NonCitizen;
    const context: NonCitizenEmailContext = {
      clientFirstName: contact.firstName,
      clientLastName: contact.lastName,
    };

    await this.interviewModel.findByIdAndUpdate(id, {
      $set: {
        isComplete: true,
        updatedAt: new Date(),
      },
    });

    // Get the organization for the client
    const organisation = await this.organisationsService.findOne(
      client.organisationId.toString(),
    );

    return this.mailService.sendEmail(
      { to, templateName, context, organisation },
      contact,
      client.primaryAdvisor.id?.toString(),
    );
  }

  /**
   * Sends a ready-to-sign email to the contact associated with the interview.
   * @param interview - The interview object.
   * @returns A promise that resolves when the email is sent.
   */
  private async sendReadyToSignEmail(interview: EnrichedInterview) {
    const client = interview.client as EnrichedClient;

    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const to: string = client.primaryCSA.email;
    const templateName = EmailTemplateNameEnum.ReadyToSign;
    const context: ReadyToSignEmailContext = {
      csaFirstName: client.primaryCSA.firstName,
      csaLastName: client.primaryCSA.lastName,
      clientFirstName: contact.firstName,
      clientLastName: contact.lastName,
      docusignAccounts: contact.accounts
        .filter((account) => !account.type.toLowerCase().includes('offline'))
        .map((account) => `<li>${account.label}</li>`)
        .join(' '),
      offlineAccounts: contact.accounts
        .filter((account) => account.type.toLowerCase().includes('offline'))
        .map((account) => `<li>${account.label}</li>`)
        .join(' '),
    };

    // Get the organization for the client
    const organisation = await this.organisationsService.findOne(
      client.organisationId.toString(),
    );

    return this.mailService.sendEmail(
      { to, templateName, context, organisation },
      contact,
      client.primaryAdvisor.id?.toString()
    );
  }

  /**
   * Retrieves the all account opening advisory files types based on the provided parameters.
   * @param accounts - Accounts array for client.
   * @param isSchwabOwnedDocusignAccount - Indicates whether the Docusign account is owned by Schwab.
   * @param organisationId - The organisation id of the advisor whose client accounts are in question.
   * @returns An Array containing the account opening advisory files.
   */
  private async getAccountOpeningAdvisoryFiles(
    accounts: Account[],
    organisationId: string | undefined = undefined,
  ): Promise<Express.Multer.File[]> {
    const allRequiredFiles: Express.Multer.File[] = [];

    for (const account of accounts) {
      const { type, features, label } = account;
      const accountAdvisoryFileTypes = await this.getAccountAdvisoryFileTypes(
        type,
        features,
      );

      if (!isEmpty(accountAdvisoryFileTypes)) {
        // Fetch all S3 files sequentially for each account
        const accountOpeningFiles: Express.Multer.File[] =
          await this.getFilesFromS3(accountAdvisoryFileTypes, organisationId);

        // Parse file names to include account and owner suffixes
        accountOpeningFiles.forEach((accountOpeningFile) => {
          const filename = accountOpeningFile.originalname.replace('.pdf', '');
          const accountSuffix = computeAccountFileSuffix(label);

          accountOpeningFile.originalname = `${filename}${accountSuffix}.pdf`;
        });

        allRequiredFiles.push(...accountOpeningFiles);
      }
    }

    return allRequiredFiles;
  }

  /**
   * Selects the account documents to fetch based on the account type, account features, fetched file types, and whether the Docusign account is owned by Schwab.
   * @param accountType The type of account.
   * @param accountFeatures The features of the account.
   * @returns A promise that resolves to an array of account documents to fetch.
   */
  private async getAccountAdvisoryFileTypes(
    accountType: AccountTypeEnum,
    accountFeatures: AccountFeaturesEnum[],
  ): Promise<AccountAdvisoryDocumentsEnum[]> {
    const advisoryFileTypes: Set<AccountAdvisoryDocumentsEnum> = new Set();

    const isIraAccount = [
      AccountTypeEnum.Ira,
      AccountTypeEnum.RothIra,
    ].includes(accountType);

    const isBrokerageAccount = [
      AccountTypeEnum.SingleNameBrokerage,
      AccountTypeEnum.JointNameBrokerage,
    ].includes(accountType);

    if (isIraAccount) {
      advisoryFileTypes.add(
        AccountAdvisoryDocumentsEnum.SchwabIraAccountApplication,
      );
    }

    if (isBrokerageAccount) {
      advisoryFileTypes.add(
        AccountAdvisoryDocumentsEnum.SchwabOnePersonalAccountApplication,
      );
    }

    accountFeatures.forEach((feature) => {
      switch (feature) {
        case AccountFeaturesEnum.MoneyLink:
          advisoryFileTypes.add(
            AccountAdvisoryDocumentsEnum.SchwabMoneyLinkElectronicTransferForm,
          );
          break;
        case AccountFeaturesEnum.Acat:
          advisoryFileTypes.add(
            AccountAdvisoryDocumentsEnum.SchwabTransferAccountForm,
          );
          break;
        case AccountFeaturesEnum.IraDistribution:
          advisoryFileTypes.add(
            AccountAdvisoryDocumentsEnum.SchwabIraDistributionForm,
          );
          break;
        default:
          throw new HttpException(
            `Account opening files not found for account feature ${feature}`,
            HttpStatus.NOT_FOUND,
          );
      }
    });

    return [...advisoryFileTypes];
  }

  /**
   * Retrieves files from AWS S3 based on their file names.
   * @param fileNames - An array of file names to retrieve from S3.
   * @returns A Promise that resolves to an array of Express.Multer.File objects containing the retrieved files.
   * @throws HttpException if there is an error fetching the files from S3.
   */
  private async getFilesFromS3(
    fileNames: AccountAdvisoryDocumentsEnum[],
    organisationId: string | undefined = undefined,
  ): Promise<Express.Multer.File[]> {
    try {
      const s3 = new AWS.S3();

      const promises = fileNames.map<Promise<Express.Multer.File>>(
        async (document) => {
          let cacheKey: string;
          let advisoryAgreementAsset: Asset;
          let assetId: string;

          const isAdvisoryAgreement =
            document === AccountAdvisoryDocumentsEnum.AdvisoryAgreement;

          if (isAdvisoryAgreement && organisationId) {
            const organisation = await this.organisationsService.findOne(
              organisationId,
            );
            advisoryAgreementAsset = organisation.assets.find(
              (asset: Asset) =>
                asset.assetType === AssetTypeEnum.AdvisoryAgreement,
            );
            assetId = advisoryAgreementAsset?.assetId;
            cacheKey = getFileCacheKey(document, organisationId);
          } else {
            const asset = await this.assetModel
              .findOne({
                assetId: document,
                assetType: AssetTypeEnum.Document,
              })
              .exec();
            assetId = asset?.assetId;
            cacheKey = getFileCacheKey(document);
          }

          if (this.cacheService.has(cacheKey)) {
            let file: Express.Multer.File;

            try {
              file = await this.cacheService.get(cacheKey);
              this.logger.info(`File fetched from cache: ${cacheKey}`);
            } catch (error) {
              this.logger.error(
                `Error fetching file from cache: ${cacheKey}`,
                error,
              );
            }

            if (file) {
              return file;
            }
          } else {
            this.logger.info(`File not found in cache: ${cacheKey}`);
          }

          const params = {
            Bucket: this.configService.get<string>('ORG_DOCS_BUCKET_NAME'),
            Key: isAdvisoryAgreement
              ? advisoryAgreementAsset.assetKey
              : `global/${assetId}`,
          };

          const directoryPath = path.join(__dirname, DOWNLOAD_PATH);
          const filePath = path.join(directoryPath, `${cacheKey}.pdf`);

          // If folder doesn't exist
          if (!existsSync(directoryPath)) {
            await mkdirAsync(directoryPath, { recursive: true });
          }

          return new Promise((resolve, reject) => {
            const fileWriteStream = createWriteStream(filePath);

            fileWriteStream.on('finish', () => {
              const file: Express.Multer.File = {
                fieldname: 'files',
                originalname: `${document}.pdf`,
                encoding: '7bit',
                mimetype: 'application/pdf',
                path: filePath,
              } as Express.Multer.File;
              this.logger.info(`File downloaded: ${filePath} setting cache`);
              this.cacheService.set(cacheKey, file);
              resolve(file);
            });

            fileWriteStream.on('error', async (writeStreamError) => {
              this.logger.error(
                `Error writing file to disk: ${filePath}`,
                writeStreamError,
              );
              if (existsSync(filePath)) {
                try {
                  await unlinkAsync(filePath);
                } catch (cleanupError) {
                  this.logger.error(
                    `Error cleaning up file: ${filePath}`,
                    cleanupError,
                  );
                }
              }
              reject(writeStreamError);
            });

            s3.getObject(params)
              .createReadStream()
              .on('error', (readStreamError) => reject(readStreamError))
              .pipe(fileWriteStream);
          });
        },
      );
      return Promise.all(promises);
    } catch (error) {
      throw new HttpException(
        `Error fetching files from S3: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Retrieves the interview data with CRM information.
   *
   * @param interviewData - The interview data.
   * @param advisorId - The advisor ID.
   * @returns A promise that resolves to the interview data with CRM information.
   */
  private async getInterviewDataWithCrmInfo(
    interviewData: InterviewData,
    advisorId: string,
    session?: ClientSession,
  ): Promise<InterviewDataWithCrmInfo> {
    let organisation = this.clsService.get(ClsDataEnum.Organisation);
    const advisor = await this.advisorsCrudService.findOne({
      _id: advisorId,
    }, session);

    if (!organisation) {
      organisation = await this.organisationsService.findOne(
        advisor.organisation?._id?.toString(),
        session,
      );
    }

    const crmInstance = await this.advisorsCrmService.getCrmInstance(advisorId);
    const crmType = await crmInstance.getType();

    const primaryContactCrmInfo = await crmInstance.getContact(
      interviewData.primaryContact.crmClientId.toString(),
      true,
      true,
    );
    primaryContactCrmInfo.dob = fdob(primaryContactCrmInfo?.dob);

    const secondaryContactCrmInfo = !isEmpty(interviewData.secondaryContact)
      ? await crmInstance.getContact(
        interviewData.secondaryContact.crmClientId.toString(),
        true,
        true,
      )
      : null;
    secondaryContactCrmInfo &&
      (secondaryContactCrmInfo.dob = fdob(secondaryContactCrmInfo?.dob));

    const accounts = await Promise.all([
      this.mergeAccounts(
        primaryContactCrmInfo.accounts,
        interviewData.primaryContact.accounts,
        crmType
      ),
      !isEmpty(interviewData.secondaryContact)
        ? this.mergeAccounts(
          secondaryContactCrmInfo.accounts,
          interviewData.secondaryContact.accounts,
          crmType
        )
        : Promise.resolve([]),
    ]);
    return {
      ...interviewData,
      organisation,
      primaryAdvisor: interviewData.primaryAdvisor,
      primaryContact: {
        ...interviewData.primaryContact,
        ...primaryContactCrmInfo,
        accounts: accounts[0],
      },
      secondaryContact: !isEmpty(interviewData.secondaryContact)
        ? {
          ...interviewData.secondaryContact,
          ...secondaryContactCrmInfo,
          accounts: accounts[1],
        }
        : null,
    };
  }

  /**
   * Merges CRM accounts with database accounts.
   *
   * @param crmAccounts - The CRM accounts to merge.
   * @param dbAccounts - The database accounts to merge.
   * @returns A promise that resolves to an array of merged accounts.
   */
  public async mergeAccounts(
    crmAccounts: GenericCrmAccount[],
    dbAccounts: AccountDto[],
    crmType: CRMType
  ): Promise<((AccountDto & GenericCrmAccount) | null)[]> {
    return dbAccounts.map((dbAccount): AccountDto & GenericCrmAccount => {
      const crmAccount = crmAccounts.find((acc) => {
        if (dbAccount.type === AccountTypeEnum.JointNameBrokerage) {
          return acc.name.includes(
            RedtailAccountOwnershipEnum[dbAccount.ownership],
          );
        }

        if (crmType === CRMEnum.Practifi || crmType === CRMEnum.Salesforce) {
          return acc.name === dbAccount.label;
        }

        return acc.name.includes(RedtailDBAccountType[dbAccount.type]);
      });

      return { ...dbAccount, ...(crmAccount || {}) } as AccountDto & GenericCrmAccount;
    });
  }

  private async selectClientFilesToFetch(
    accountFeatures: AccountFeaturesEnum[],
    accountId: string,
  ) {
    const clientFileTypes: Set<{
      documentType: AccountClientDocumentsEnum;
      accountId: string;
      feature: AccountFeaturesEnum;
    }> = new Set();

    accountFeatures.forEach((feature) => {
      switch (feature) {
        case AccountFeaturesEnum.MoneyLink:
          clientFileTypes.add({
            documentType: AccountClientDocumentsEnum.CancelledCheck,
            accountId,
            feature: AccountFeaturesEnum.MoneyLink,
          });
          break;
        case AccountFeaturesEnum.Acat:
          clientFileTypes.add({
            documentType: AccountClientDocumentsEnum.BrokerageStatement,
            accountId,
            feature: AccountFeaturesEnum.Acat,
          });
          break;
        case AccountFeaturesEnum.IraDistribution:
          break;
        default:
          throw new HttpException(
            `Account opening files not found for account feature ${feature}`,
            HttpStatus.NOT_FOUND,
          );
      }
    });
    return [...clientFileTypes];
  }



  /**
   * Calculates the completion percentage of an interview based on the provided interview and client information.
   * If the interview is already complete, returns 100.
   * Otherwise, calculates the percentage of filled pages in the interview template.
   * If the interview is primary, checks if the client's primary contact has any accounts.
   * If the interview is not primary, checks if the client's secondary contact has any accounts.
   * Pages related to primary beneficiaries and contingent beneficiaries are excluded if there are no accounts.
   * @param interview - The interview object.
   * @param client - The client object.
   * @returns The completion percentage as a number.
   */
  calculateCompletionPercentage(
    interview: EnrichedInterview,
    client: EnrichedClient,
  ): number {
    if (interview.isComplete) {
      return 100;
    }

    let hasAccounts = false;
    if (interview.isPrimary) {
      hasAccounts = !isEmpty(client.primaryContact.accounts);
    } else {
      hasAccounts = !isEmpty(client.secondaryContact?.accounts);
    }

    const pagesToFill = hasAccounts
      ? interview.template.pages
      : interview.template.pages.filter(
        (page) =>
          page.name !== PagesEnum.PRIMARY_BENEFICIARIES &&
          page.name !== PagesEnum.CONTINGENT_BENEFICIARIES,
      );

    const totalPages = pagesToFill.length;
    const filledPages = interview.template.pages.filter(
      (page) => page.status === InterviewPageStatusEnum.SYNCED,
    ).length;
    return Math.ceil((filledPages / totalPages) * 100);
  }

  /**
   * Creates a new Docusign envelope for the specified interview.
   *
   * @param id - The ID of the interview.
   * @param session - Optional MongoDB session for transaction support.
   * @returns A Promise that resolves to the created envelope ID.
   */
  async createDocusignEnvelope(id: string, session?: ClientSession) {
    const interview = await this.findOne({ _id: id }, session);
    const { client, envelopeId } = interview;
    const clientId = client._id.toString();

    const { organisationId, primaryAdvisor, primaryContact, secondaryContact } =
      await this.clientsService.findOne({ _id: clientId });

    // Create a new docusign draft envelope
    const entityIds = {
      advisorId: primaryAdvisor.id.toString(),
      organisationId: organisationId.toString(),
    };

    const { accountOwnership } = await this.docusignService.getAccountInfo(
      entityIds,
    );

    const isSchwabOwnedDocusignAccount =
      accountOwnership === DocusignAccountOwnershipEnum.Schwab;

    const schwabAdvisoryFiles = [];

    const advisorIntegrationConfig = await this.advisorsDocusignService.findDocusignIntegration(entityIds.advisorId);
    const organisationIntegrationConfig = await this.organisationsService.findDocusignIntegration(entityIds.organisationId);

    const integrationConfig = advisorIntegrationConfig ?? organisationIntegrationConfig;


    const createEnvelopeDto: CreateEnvelopeDto = {
      ...entityIds,
      primaryAdvisorName: `${primaryAdvisor.firstName} ${primaryAdvisor.lastName}`,
      primaryAdvisorEmail: primaryAdvisor.email,
      primaryAdvisorPhone: primaryAdvisor.mobile,
      applicant: {
        name: `${primaryContact.firstName} ${primaryContact.lastName}`,
        email: primaryContact.email,
        phone: primaryContact.mobile,
      },
      coapplicant: !isEmpty(secondaryContact) && {
        name: `${secondaryContact?.firstName} ${secondaryContact?.lastName}`,
        email: secondaryContact?.email,
        phone: secondaryContact?.mobile,
      },
      envelopeId,
      isSchwabOwnedDocusignAccount,
      shouldSend: false,
      webhookUrl: this.configService.get<string>('DOCUSIGN_STATUS_WEBHOOK_URI'),
      files: schwabAdvisoryFiles,
      schwabTemplateId: integrationConfig?.schwabTemplateId
    };

    const createdEnvelopeId = await this.docusignService.createEnvelope(
      createEnvelopeDto,
      session
    );

    // Update all the interviews for the same client household with the envelope ID
    const now = new Date();
    await this.interviewModel.updateMany(
      { client },
      {
        $set: {
          envelopeId: createdEnvelopeId,
          updatedAt: now,
        },
      },
      { session }
    );

    return createdEnvelopeId;
  }
}
