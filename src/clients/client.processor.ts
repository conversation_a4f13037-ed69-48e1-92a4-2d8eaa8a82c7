import {
  OnWorkerE<PERSON>,
  Processor,
  WorkerHost
} from '@nestjs/bullmq';
import { Logger, Inject, forwardRef } from '@nestjs/common';
import { Job } from 'bullmq';
import { SaveJobsDto } from 'src/queue-log/dto/save-jobs.dto';
import { QueueLogStatusEnum } from 'src/queue-log/enums/queue-log-status.enum';
import { QueueLogService } from 'src/queue-log/queue-log.service';
import { CLIENT_QUEUE } from 'src/shared/constants/client.constant';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { ResourceOptionsEnum } from 'src/shared/types/queue-log/queue';
import { ClientsV1Service } from './services/v1/clients-v1.service';
import { ClientsV2Service } from './services/v2/clients-v2.service';
import {
  ClientQueueJobData,
  CreateInterviewJobData,
  ResendTextJobData,
  SendNotificationJobData,
  UpdateClientInterviewCompletionJobData,
  UpdateClientInterviewStatusJobData,
  UpdateClientStatusAfterEnvelopeJobData,
  UpdateClientStatusJobData,
  UpdateLastContactActivityTimestampJobData
} from './interfaces/client-queue.interface';
import { ClientQueueJobType } from './types/client-queue-job.enum';

function isResendTextJob(data: ClientQueueJobData): data is ResendTextJobData {
  return 'clientId' in data && 'organisationId' in data;
}

@Processor(CLIENT_QUEUE.NAME, {
  concurrency: 2
})
export class ClientProcessor extends WorkerHost {
  private readonly logger = new Logger(ClientProcessor.name);

  constructor(
    @Inject(forwardRef(() => ClientsV1Service))
    private readonly clientsService: ClientsV1Service,
    @Inject(forwardRef(() => ClientsV2Service))
    private readonly clientsV2Service: ClientsV2Service,
    private readonly queueLogService: QueueLogService,
  ) {
    super();
  }

  /**
   * Determines which client service to use based on the client's API version
   */
  private async getClientService(clientId: string) {
    // Try to get client from V2 service first (it can handle both V1 and V2)
    try {
      const client = await this.clientsV2Service.findOne({ _id: clientId }, undefined, false, true);
      
      // If client has apiVersion 'v2', use V2 service, otherwise use V1 service
      if (client?.apiVersion === 'v2') {
        return this.clientsV2Service;
      }
    } catch (error) {
      // If client not found in V2 or any error, fall back to V1
      this.logger.warn(`Could not determine client version for ${clientId}, using V1 service: ${error.message}`);
    }
    
    return this.clientsService;
  }

  async process(job: Job<ClientQueueJobData>): Promise<any> {
    const { name, data } = job;

    try {
      if (!name || typeof name !== 'string') {
        throw new Error(`Invalid job name: ${name}`);
      }

      // Process job based on type
      switch (name.toLowerCase()) {
          case ClientQueueJobType.CREATE_INTERVIEW: {
            const jobData = data as CreateInterviewJobData;
            const clientService = await this.getClientService(jobData.clientId);
            return await clientService.createInterview(jobData.clientId, jobData.createClientDto);
          }

          case ClientQueueJobType.SEND_NOTIFICATION: {
            const jobData = data as SendNotificationJobData;
            const clientService = await this.getClientService(jobData.clientId);
            return await clientService.sendWelcomeNotification(jobData.clientId, jobData.notificationMethods);
          }

          case ClientQueueJobType.UPDATE_STATUS: {
            const jobData = data as UpdateClientStatusJobData;
            return await this.clientsService.updateClientStatus(jobData.clientId, jobData.status);
          }

          case ClientQueueJobType.UPDATE_INTERVIEW_STATUS: {
            const jobData = data as UpdateClientInterviewStatusJobData;
            return await this.clientsService.updateInterviewStatus(jobData.clientId, jobData.interviewStatus);
          }

          case ClientQueueJobType.UPDATE_CLIENT_INTERVIEW_COMPLETION: {
            const jobData = data as UpdateClientInterviewCompletionJobData;
            return await this.clientsService.updateClientInterviewCompletion(jobData);
          }

          case ClientQueueJobType.UPDATE_CLIENT_STATUS_AFTER_ENVELOPE: {
            const jobData = data as UpdateClientStatusAfterEnvelopeJobData;
            return await this.clientsService.updateClientStatusAfterEnvelope(jobData.clientId, jobData.docusignSelected);
          }

          case ClientQueueJobType.UPDATE_LAST_CONTACT_ACTIVITY_TIMESTAMP: {
            const jobData = data as UpdateLastContactActivityTimestampJobData;
            return await this.clientsService.updateLastContactActivityTimestamp(jobData.clientId, jobData.isPrimary);
          }

          case ClientQueueJobType.RESEND_TEXT:
            if (!isResendTextJob(data)) throw new Error('Invalid job data for RESEND_TEXT');
            return await this.clientsService.processResendText(
              data.clientId, 
              data.primaryInterviewId, 
              data.secondaryInterviewId
            );

          default:
            throw new Error(`Unhandled job type: ${name}`);
        }
      return;
    } catch (error) {
      this.logger.error(`Job ${job.id} failed with error: ${error.message}`, error.stack);
      throw error;
    }
  }

  @OnWorkerEvent('completed')
  async onCompleted(job: Job) {
    this.logger.log(`Job ${job.id} completed`);
    const { organisationId } = job.data;

    const payload: SaveJobsDto = {
      jobId: job.id,
      resource: ResourceOptionsEnum.CLIENT,
      queueName: CLIENT_QUEUE.NAME,
      status: QueueLogStatusEnum.COMPLETED,
      result: 'Client created successfully',
      organisationId
    }
    await this.queueLogService.save(payload);
  }

  @OnWorkerEvent('failed')
  async onFailed(job: Job, error: Error) {
    this.logger.error(`Job ${job.id} failed with error: ${error.message}`, error.stack);
    const { organisationId } = job.data;

    const payload: SaveJobsDto = {
      jobId: job.id,
      resource: ResourceOptionsEnum.CLIENT,
      queueName: CLIENT_QUEUE.NAME,
      status: QueueLogStatusEnum.FAILED,
      error: error.message,
      organisationId
    }
    await this.queueLogService.save(payload);
    if(job.name === ClientQueueJobType.RESEND_TEXT) return; // Don't update client status for resend text
    await this.clientsService.updateClientStatus(job.data.clientId, ClientStatusEnum.Failed);
    
  }
}
