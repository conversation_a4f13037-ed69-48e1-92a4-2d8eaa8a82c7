import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { ConflictsOfInterestRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/conflicts-of-interest-request.dto';
import {
  ActivityAttendeeTypeEnum,
  ActivityRepeatEnum,
} from 'src/integrations/crm/redtail/types/activity.type';
import {
  RedtailActivityTypeEnum,
  RedtailContactUdfNameEnum,
} from 'src/integrations/crm/redtail/types/enums';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { AccountClientDocumentsEnum } from 'src/shared/types/accounts/account-documents.enum';

export class RedtailConflictOfInterestMapper implements FormUpdateMapper {
  constructor(private redtailService: RedtailCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: ConflictsOfInterestRequestDto,
  ): Promise<void> {
    const subject = 'Obtain BD/FINRA approval letter for account opening';
    const description =
      'Client employed by or associated with B/D, FINRA or Stock Exchange.  Email for approval letter and submit to Schwab.';
    const { _id: interviewId } = interview;
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    const crmClientId = contact.crmClientId;

    // Remove FINRA Document from required documents in case the client changes their answer
    if (!pageData.companyName) {
      await this.redtailService.interviewsService.removeRequiredDocument(
        interviewId,
        {
          document: AccountClientDocumentsEnum.FinraDocument,
        },
      );
      await this.redtailService.redtailUdfService.deleteContactUdf(
        crmClientId,
        organisation.configuration[
          RedtailContactUdfNameEnum.INDUSTRY_AFFILIATION
        ],
      );
      return;
    }

    // Add FINRA Document to required documents in the interview
    if (interview.docusignSelected) {
      await this.redtailService.interviewsService.addRequiredDocument(
        interviewId,
        {
          document: AccountClientDocumentsEnum.FinraDocument,
        },
      );
    }

    const updateContactUdf =
      this.redtailService.redtailUdfService.upsertContactUdf(crmClientId, {
        contactUdfFieldId:
          organisation.configuration[
            RedtailContactUdfNameEnum.INDUSTRY_AFFILIATION
          ],
        fieldValue: pageData.companyName,
      });

    const createActivity =
      this.redtailService.redtailActivityService.createActivity({
        activityCodeId: RedtailActivityTypeEnum.TASK,
        categoryId: RedtailActivityTypeEnum.CATEGORY_GENERAL_INFORMATION,
        allDay: true,
        startDate: new Date().toISOString(),
        endDate: new Date().toISOString(),
        repeats: ActivityRepeatEnum.NEVER,
        subject,
        description,
        linkedContacts: [{ contactId: Number(crmClientId) }],
        attendees: [
          {
            userId: Number(client.primaryAdvisor.crmId),
            type: ActivityAttendeeTypeEnum.USER,
          },
        ],
      });

    await Promise.all([createActivity, updateContactUdf]);
  }
}
