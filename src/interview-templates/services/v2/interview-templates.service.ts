// src/interview-templates/services/v2/interview-templates-v2.service.ts
import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession, FilterQuery, Types } from 'mongoose';
import { 
  CreateTemplateV2Dto, 
  UpdateTemplateV2Dto, 
  PublishTemplateV2Dto,
  TemplateQueryV2Dto,
  CreatePageDefDto,
} from '../../dto/v2';
import { v4 as uuidv4 } from 'uuid';
import { InterviewTemplateV2 } from 'src/interview-templates/schemas/v2/interview.template';
import { InterviewPageDefV2 } from 'src/interview-templates/schemas/v2/interview-page-def.schema';

@Injectable()
export class InterviewTemplatesV2Service {
  private readonly logger = new Logger(InterviewTemplatesV2Service.name);

  constructor(
    @InjectModel(InterviewTemplateV2.name)
    private readonly templateModel: Model<InterviewTemplateV2>,
  ) {}

  // ===== CRUD Operations =====

  /**
   * Create a new template in draft status
   */
  async create(
    organisationId: string,
    dto: CreateTemplateV2Dto,
    userId: string,
    session?: ClientSession,
  ): Promise<InterviewTemplateV2> {
    // Validate unique template name within organization
    await this.validateUniqueTemplateName(organisationId, dto.templateName, undefined, session);
    
    // Create pages with UUIDs
    const pages = this.createPageDefinitions(dto.pages);
    
    // Validate page flow references
    this.validatePageFlowReferences(pages);
    
    const templateData = {
      ...dto,
      organisationId,
      pages,
      status: 'draft' as const,
      version: 1,
      lastModifiedBy: userId,
      isDefault: false,
    };
    
    const [template] = await this.templateModel.create([templateData], { session });
    
    this.logger.log(`Created template ${template._id} for organization ${organisationId}`);
    
    return template;
  }

  /**
   * Find template by ID with validation
   */
  async findById(
    id: string,
    session?: ClientSession,
  ): Promise<InterviewTemplateV2> {
    const template = await this.templateModel
      .findById(id)
      .session(session);
      
    if (!template) {
      throw new NotFoundException(`Template ${id} not found`);
    }
    
    return template;
  }

  /**
   * Find templates with filtering and pagination
   */
  async find(
    organisationId: string,
    query: TemplateQueryV2Dto,
  ): Promise<{
    templates: InterviewTemplateV2[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { status, tags, isDefault, templateType, accountType, page = 1, limit = 10 } = query;
    
    const filter: FilterQuery<InterviewTemplateV2> = { organisationId };
    
    if (status) filter.status = status;
    if (isDefault !== undefined) filter.isDefault = isDefault;
    if (tags?.length) filter.tags = { $in: tags };
    if (templateType) filter.templateType = templateType;
    if (accountType) filter.accountType = accountType;
    
    const skip = (page - 1) * limit;
    
    const [templates, total] = await Promise.all([
      this.templateModel
        .find(filter)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.templateModel.countDocuments(filter),
    ]);
    
    return { templates, total, page, limit };
  }

  /**
   * Update template metadata (not pages)
   */
  async update(
    id: string,
    dto: UpdateTemplateV2Dto,
    userId: string,
    session?: ClientSession,
  ): Promise<InterviewTemplateV2> {
    const template = await this.findById(id, session);
    
    // Can only update draft templates
    if (template.status !== 'draft') {
      throw new ForbiddenException('Can only update draft templates');
    }
    
    // Validate unique name if changing
    if (dto.templateName && dto.templateName !== template.templateName) {
      await this.validateUniqueTemplateName(
        template.organisationId.toString(),
        dto.templateName,
        id,
        session
      );
    }
    
    const updates = {
      ...dto,
      lastModifiedBy: userId,
      updatedAt: new Date(),
    };
    
    const updated = await this.templateModel.findByIdAndUpdate(
      id,
      { $set: updates },
      { new: true, session }
    );
    
    return updated!;
  }

  /**
   * Delete template (only draft or archived)
   */
  async remove(
    id: string,
    session?: ClientSession,
  ): Promise<void> {
    const template = await this.findById(id, session);
    
    if (!['draft', 'archived'].includes(template.status)) {
      throw new ForbiddenException('Can only delete draft or archived templates');
    }
    
    // TODO: Check if any interviews use this template
    // This check should be done at a higher level (controller or facade)
    // to avoid circular dependencies between modules
    // For now, we'll skip this check in the service layer
    
    await this.templateModel.findByIdAndDelete(id, { session });
    
    this.logger.log(`Deleted template ${id}`);
  }

  // ===== Page Management =====


  /**
   * Add page to draft template
   */
  async addPage(
    templateId: string,
    dto: CreatePageDefDto,
    userId: string,
    session?: ClientSession,
  ): Promise<InterviewTemplateV2> {
    const template = await this.findById(templateId, session);
    
    if (template.status !== 'draft') {
      throw new ForbiddenException('Can only add pages to draft templates');
    }
    
    // Create page with UUID
    const newPage = this.createPageDefinition(dto);
    
    // Get pages as plain objects for manipulation
    const pages = template.toObject().pages;
    
    // Validate unique page name
    if (pages.some(p => p.pageName === newPage.pageName)) {
      throw new ConflictException(`Page name ${newPage.pageName} already exists`);
    }
    
    // Add new page
    pages.push(newPage);
    
    // Validate all flow references
    this.validatePageFlowReferences(pages);
    
    // Update template
    template.pages = pages as any;
    template.lastModifiedBy = userId;
    
    await template.save({ session });
    
    return template;
  }

  /**
   * Update page in draft template
   */
  async updatePage(
    templateId: string,
    pageId: string,
    dto: Partial<CreatePageDefDto>,
    userId: string,
    session?: ClientSession,
  ): Promise<InterviewTemplateV2> {
    const template = await this.findById(templateId, session);
    
    if (template.status !== 'draft') {
      throw new ForbiddenException('Can only update pages in draft templates');
    }
    
    // Get pages as plain objects
    const pages = template.toObject().pages;
    const pageIndex = pages.findIndex(p => p.pageId === pageId);
    
    if (pageIndex === -1) {
      throw new NotFoundException(`Page ${pageId} not found in template`);
    }
    
    // Update page, preserving existing flow properties
    if (dto.flow) {
      pages[pageIndex].flow = {
        ...pages[pageIndex].flow,
        ...dto.flow,
        isTerminal: dto.flow.isTerminal ?? pages[pageIndex].flow.isTerminal ?? false,
      };
      delete dto.flow;
    }
    
    Object.assign(pages[pageIndex], dto);
    
    // Validate flow references
    this.validatePageFlowReferences(pages);
    
    // Update template
    template.pages = pages as any;
    template.lastModifiedBy = userId;
    
    await template.save({ session });
    
    return template;
  }

  /**
   * Remove page from draft template
   */
  async removePage(
    templateId: string,
    pageId: string,
    userId: string,
    session?: ClientSession,
  ): Promise<InterviewTemplateV2> {
    const template = await this.findById(templateId, session);
    
    if (template.status !== 'draft') {
      throw new ForbiddenException('Can only remove pages from draft templates');
    }
    
    const pages = template.toObject().pages;
    
    // Check if page is referenced by other pages
    const isReferenced = pages.some(page => 
      page.flow.rules.some(rule => rule.goToPageId === pageId) ||
      page.flow.defaultNext?.pageId === pageId
    );
    
    if (isReferenced) {
      throw new ConflictException('Cannot remove page that is referenced by other pages');
    }
    
    // Remove page
    const filteredPages = pages.filter(p => p.pageId !== pageId);
    template.pages = filteredPages as any;
    template.lastModifiedBy = userId;
    
    await template.save({ session });
    
    return template;
  }

  /**
   * Reorder pages in draft template
   */
  async reorderPages(
    templateId: string,
    pageOrder: { pageId: string; order: number }[],
    userId: string,
    session?: ClientSession,
  ): Promise<InterviewTemplateV2> {
    const template = await this.findById(templateId, session);
    
    if (template.status !== 'draft') {
      throw new ForbiddenException('Can only reorder pages in draft templates');
    }
    
    const pages = template.toObject().pages;
    const templatePageIds = pages.map(p => p.pageId);
    const orderPageIds = pageOrder.map(o => o.pageId);
    
    if (!orderPageIds.every(id => templatePageIds.includes(id))) {
      throw new BadRequestException('Invalid page IDs in order list');
    }
    
    // Apply new order
    pageOrder.forEach(({ pageId, order }) => {
      const pageIndex = pages.findIndex(p => p.pageId === pageId);
      if (pageIndex !== -1) {
        pages[pageIndex].defaultOrder = order;
      }
    });
    
    template.pages = pages as any;
    template.lastModifiedBy = userId;
    await template.save({ session });
    
    return template;
  }

  // ===== Template Lifecycle =====

  /**
   * Publish a draft template
   */
  async publish(
    templateId: string,
    dto: PublishTemplateV2Dto,
    userId: string,
    session?: ClientSession,
  ): Promise<InterviewTemplateV2> {
    const template = await this.findById(templateId, session);
    
    if (template.status !== 'draft') {
      throw new ForbiddenException('Can only publish draft templates');
    }
    
    // Validate template is complete
    this.validateTemplateCompleteness(template);
    
    // If making default, unset current default
    if (dto.makeDefault) {
      await this.templateModel.updateMany(
        {
          organisationId: template.organisationId,
          isDefault: true,
          _id: { $ne: templateId }
        },
        { $set: { isDefault: false } },
        { session }
      );
    }
    
    // Publish template
    template.status = 'published';
    template.publishedAt = new Date();
    template.publishedBy = userId;
    template.lastModifiedBy = userId;
    
    if (dto.makeDefault) {
      template.isDefault = true;
    }
    
    await template.save({ session });
    
    this.logger.log(`Published template ${templateId}`);
    
    return template;
  }

  /**
   * Create new version of published template
   */
  async createVersion(
    templateId: string,
    userId: string,
    session?: ClientSession,
  ): Promise<InterviewTemplateV2> {
    const sourceTemplate = await this.findById(templateId, session);
    
    if (sourceTemplate.status !== 'published') {
      throw new ForbiddenException('Can only create versions from published templates');
    }
    
    // Create new draft version
    const newVersion = {
      ...sourceTemplate.toObject(),
      _id: new Types.ObjectId(),
      templateName: `${sourceTemplate.templateName} (v${sourceTemplate.version + 1})`,
      version: sourceTemplate.version + 1,
      status: 'draft' as const,
      parentTemplateId: sourceTemplate._id,
      isDefault: false,
      publishedAt: undefined,
      publishedBy: undefined,
      lastModifiedBy: userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    const [template] = await this.templateModel.create([newVersion], { session });
    
    this.logger.log(`Created version ${template.version} of template ${templateId}`);
    
    return template;
  }

  /**
   * Archive a published template
   */
  async archive(
    templateId: string,
    userId: string,
    session?: ClientSession,
  ): Promise<InterviewTemplateV2> {
    const template = await this.findById(templateId, session);
    
    if (template.status !== 'published') {
      throw new ForbiddenException('Can only archive published templates');
    }
    
    if (template.isDefault) {
      throw new ConflictException('Cannot archive default template');
    }
    
    template.status = 'archived';
    template.archivedAt = new Date();
    template.lastModifiedBy = userId;
    
    await template.save({ session });
    
    this.logger.log(`Archived template ${templateId}`);
    
    return template;
  }

  // ===== Specialized Queries =====

  /**
   * Get default template for organization
   */
  async getDefaultForOrganization(
    organisationId: string,
    session?: ClientSession,
  ): Promise<InterviewTemplateV2 | null> {
    return this.templateModel
      .findOne({
        organisationId,
        isDefault: true,
        status: 'published',
      })
      .session(session);
  }

  /**
   * Get template with specific page
   */
  async findTemplateByPageId(
    pageId: string,
    session?: ClientSession,
  ): Promise<InterviewTemplateV2 | null> {
    return this.templateModel
      .findOne({
        'pages.pageId': pageId,
      })
      .session(session);
  }

  /**
   * Get template analytics
   */
  async getTemplateAnalytics(
    templateId: string,
  ): Promise<{
    totalInterviews: number;
    activeInterviews: number;
    completedInterviews: number;
    avgCompletionTime?: number;
    dropOffPoints: Record<string, number>;
  }> {
    // TODO: Get interview statistics from a dedicated analytics service
    // to avoid circular dependencies between modules
    const totalInterviews = 0;
    const activeInterviews = 0;
    const completedInterviews = 0;
    
    // TODO: Calculate actual analytics from page instances
    
    return {
      totalInterviews,
      activeInterviews,
      completedInterviews,
      dropOffPoints: {},
    };
  }

  // ===== Private Helper Methods =====

  private createPageDefinitions(pageDtos: CreatePageDefDto[]): InterviewPageDefV2[] {
    return pageDtos.map(dto => this.createPageDefinition(dto));
  }


  private createPageDefinition(dto: CreatePageDefDto): InterviewPageDefV2 {
    const page: any = {
      pageId: uuidv4(),
      pageName: dto.pageName,
      pageTitle: dto.pageTitle,
      pageDescription: dto.pageDescription,
      defaultOrder: dto.defaultOrder,
      pageType: dto.pageType,
      flow: {
        rules: dto.flow.rules.map(rule => ({
          ruleId: uuidv4(),
          ruleName: rule.ruleName,
          priority: rule.priority,
          when: rule.when,
          goToPageId: rule.goToPageId,
          isActive: true,
          description: rule.description,
        })),
        defaultNext: dto.flow.defaultNext,
        allowBack: dto.flow.allowBack ?? true,
        skipIf: dto.flow.skipIf,
        isTerminal: dto.flow.isTerminal ?? false,
      },
      isActive: true,
      isRequired: dto.isRequired ?? false,
      applicableAccountTypes: dto.applicableAccountTypes,
      beneficiaryType: dto.pageType === 'beneficiary' 
        ? dto.metadata?.beneficiaryType 
        : undefined,
      validationRules: [],
      uiSchema: dto.uiSchema,
      metadata: dto.metadata,
    };
    
    return page as InterviewPageDefV2;
  }

  private validatePageFlowReferences(pages: any[]): void {
    const pageIds = new Set(pages.map(p => p.pageId));
    
    for (const page of pages) {
      // Check rule references
      for (const rule of page.flow.rules) {
        if (!pageIds.has(rule.goToPageId)) {
          throw new BadRequestException(
            `Page ${page.pageName} references non-existent page ${rule.goToPageId}`
          );
        }
      }
      
      // Check default next reference (unless terminal)
      if (!page.flow.isTerminal && page.flow.defaultNext && !pageIds.has(page.flow.defaultNext.pageId)) {
        throw new BadRequestException(
          `Page ${page.pageName} default next references non-existent page ${page.flow.defaultNext.pageId}`
        );
      }
    }
  }

  private validateTemplateCompleteness(template: InterviewTemplateV2): void {
    const templateObj = template.toObject();
    
    if (templateObj.pages.length === 0) {
      throw new BadRequestException('Template must have at least one page');
    }
    
    // Ensure at least one entry point
    const hasStartPage = templateObj.startPageId 
      ? templateObj.pages.some(p => p.pageId === templateObj.startPageId)
      : true;
      
    if (!hasStartPage) {
      throw new BadRequestException('Template start page not found');
    }
    
    // Validate all non-terminal pages have complete flow
    for (const page of templateObj.pages) {
      if (!page.flow.isTerminal && !page.flow.defaultNext) {
        throw new BadRequestException(
          `Page ${page.pageName} must have either default next or be terminal`
        );
      }
    }
    
    // Validate beneficiary pages exist if required
    if (templateObj.accountConfig?.requireBeneficiaries) {
      const hasBeneficiaryPages = templateObj.pages.some(
        p => p.pageType === 'beneficiary'
      );
      
      if (!hasBeneficiaryPages) {
        throw new BadRequestException(
          'Template requires beneficiary pages but none found'
        );
      }
    }
  }

  private async validateUniqueTemplateName(
    organisationId: string,
    templateName: string,
    excludeId?: string,
    session?: ClientSession,
  ): Promise<void> {
    const filter: FilterQuery<InterviewTemplateV2> = {
      organisationId,
      templateName,
    };
    
    if (excludeId) {
      filter._id = { $ne: excludeId };
    }
    
    const existing = await this.templateModel.findOne(filter).session(session);
    
    if (existing) {
      throw new ConflictException(`Template name '${templateName}' already exists`);
    }
  }
}