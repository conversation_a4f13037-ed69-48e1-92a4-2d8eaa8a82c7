import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ValidationErrorDto {
  @ApiProperty()
  field!: string;

  @ApiProperty()
  message!: string;

  @ApiPropertyOptional()
  code?: string;

  @ApiPropertyOptional()
  context?: any;
}

export class PageValidationDto {
  @ApiProperty()
  pageId!: string;

  @ApiProperty()
  pageName!: string;

  @ApiProperty()
  isValid!: boolean;

  @ApiPropertyOptional({ type: [ValidationErrorDto] })
  errors?: ValidationErrorDto[];

  @ApiPropertyOptional({ type: [String] })
  warnings?: string[];
}

export class TemplateValidationV2Dto {
  @ApiProperty()
  templateId!: string;

  @ApiProperty()
  templateName!: string;

  @ApiProperty()
  isValid!: boolean;

  @ApiProperty()
  canPublish!: boolean;

  @ApiPropertyOptional({ type: [ValidationErrorDto] })
  templateErrors?: ValidationErrorDto[];

  @ApiProperty({ type: [PageValidationDto] })
  pageValidations!: PageValidationDto[];

  @ApiPropertyOptional({ type: [String] })
  flowWarnings?: string[];

  @ApiPropertyOptional()
  flowGraph?: {
    nodes: Array<{ id: string; label: string; type: string }>;
    edges: Array<{ from: string; to: string; condition?: string }>;
    orphanedPages: string[];
    circularReferences: string[][];
  };
}