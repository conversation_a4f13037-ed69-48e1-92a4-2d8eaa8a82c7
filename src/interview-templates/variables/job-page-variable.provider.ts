import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

export class JobPageVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      jobDescription: {
        description: 'Detailed job description',
        type: VariableType.STRING,
        example: 'Responsible for developing and maintaining web applications',
      },
      industry: {
        description: 'Industry or sector of work',
        type: VariableType.STRING,
        example: 'Technology',
      },
      occupation: {
        description: 'Specific occupation or profession',
        type: VariableType.STRING,
        example: 'Software Developer',
      },
      responsibilities: {
        description: 'Key job responsibilities',
        type: VariableType.STRING,
        example: 'Code review, feature development, bug fixes',
      },
    };
  }
}