import { NotificationTypeEnum } from 'src/clients/types/notification-types.enum';
import { CreateClientDto } from '../dto/v1/create-client.dto';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { InterviewStatusEnum } from '../types/client-interview-status';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import { Client } from 'src/clients/schemas/clients.schema';

export interface BaseClientQueueJob {
  organisationId?: string;
}

export interface CreateInterviewJobData extends BaseClientQueueJob {
  clientId: string;
  createClientDto: CreateClientDto;
}

export interface UpdateClientStatusJobData extends BaseClientQueueJob {
  clientId: string;
  status: ClientStatusEnum;
}

export interface SendNotificationJobData extends BaseClientQueueJob {
  clientId: string;
  notificationMethods: NotificationTypeEnum[];
}

export interface UpdateClientInterviewStatusJobData extends BaseClientQueueJob {
  clientId: string;
  interviewStatus: InterviewStatusEnum;
  organisationId: string;
}

export interface UpdateClientInterviewCompletionJobData extends BaseClientQueueJob {
  interviewId: string;
  clientId: string;
  isPrimary: boolean;
  docusignSelected: boolean;
}

export interface UpdateClientStatusAfterEnvelopeJobData extends BaseClientQueueJob {
  clientId: string;
  docusignSelected: boolean;
}

export interface UpdateLastContactActivityTimestampJobData extends BaseClientQueueJob {
  clientId: string;
  isPrimary: boolean;
}

export interface ResendTextJobData {
  clientId: string;
  primaryInterviewId?: string;
  secondaryInterviewId?: string;
  organisationId: string;
}

export type ClientQueueJobData =
  | CreateInterviewJobData
  | UpdateClientStatusJobData
  | SendNotificationJobData
  | UpdateClientInterviewStatusJobData
  | UpdateClientInterviewCompletionJobData
  | UpdateClientStatusAfterEnvelopeJobData
  | UpdateLastContactActivityTimestampJobData
  | ResendTextJobData;