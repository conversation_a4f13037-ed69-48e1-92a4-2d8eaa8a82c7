import mongoose from 'mongoose';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { UserStatusesEnum } from 'src/shared/types/rbac/statuses.enum';
export type UpsertAdvisorDetails = {
  email?: string;
  newEmail?: string;
  oldEmail?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  organisationId?: mongoose.Types.ObjectId;
  role?: RolesEnum;
  status?: UserStatusesEnum;
};
