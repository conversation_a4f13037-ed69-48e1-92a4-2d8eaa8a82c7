import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { PractifiNameMapper } from './name.mapper';
import { PractifiAddressMapper } from './address.mapper';
import { PractifiSSNMapper } from './ssn.mapper';
import { PractifiDOBMapper } from './dob.mapper';
import { PractifiPhoneMapper } from './phone.mapper';
import { PractifiUSCitizenMapper } from './us-citizen.mapper';
import { PractifiEmploymentMapper } from './employment.mapper';
import { PractifiPrimaryBeneficiariesMapper } from './primary-beneficiaries.mapper';
import { PractifiContingentBeneficiariesMapper } from './contingent-beneficiaries.mapper';
import { PractifiCompanyMapper } from './company.mapper';
import { PractifiVIPMapper } from './vip.mapper';
import { PractifiJobDescriptionMapper } from './job-description.mapper';
import { PractifiCustomQuestionsMapper } from './custom-questions.mapper';
import { PractifiConflictOfInterestMapper } from './conflict-of-interest.mapper';
import { PractifiCRM } from '../crm.practifi';
import { PageUpdateMapper } from '../../crm.interface';

export function practifiMappers(
  this: InstanceType<typeof PractifiCRM>,
): PageUpdateMapper {
  return {
    [PagesEnum.NAME]: new PractifiNameMapper(this),
    [PagesEnum.ADDRESS]: new PractifiAddressMapper(this),
    [PagesEnum.SSN]: new PractifiSSNMapper(this),
    [PagesEnum.DOB]: new PractifiDOBMapper(this),
    [PagesEnum.PHONE]: new PractifiPhoneMapper(this),
    [PagesEnum.US_CITIZEN]: new PractifiUSCitizenMapper(this),
    [PagesEnum.EMPLOYMENT]: new PractifiEmploymentMapper(this),
    [PagesEnum.PRIMARY_BENEFICIARIES]: new PractifiPrimaryBeneficiariesMapper(this),
    [PagesEnum.CONTINGENT_BENEFICIARIES]: new PractifiContingentBeneficiariesMapper(this),
    [PagesEnum.COMPANY]: new PractifiCompanyMapper(this),
    [PagesEnum.VIP]: new PractifiVIPMapper(this),
    [PagesEnum.JOB]: new PractifiJobDescriptionMapper(this),
    [PagesEnum.CUSTOM_QUESTIONS]: new PractifiCustomQuestionsMapper(this),
    [PagesEnum.CONFLICTS_OF_INTEREST]: new PractifiConflictOfInterestMapper(this),
  };
} 