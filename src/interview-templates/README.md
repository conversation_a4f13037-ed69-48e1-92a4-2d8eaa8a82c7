# Interview Templates API Versioning

This module implements a clean versioning architecture for the Interview Templates API. It allows for backward compatibility while enabling new features and improvements in future versions.

## Architecture Overview

```
interview-templates/
├── controllers/
│   ├── shared/           # Common controllers for all versions
│   ├── v1/              # Version 1 controllers
│   └── v2/              # Version 2 controllers (future)
├── services/
│   ├── shared/          # Shared business logic across versions
│   ├── v1/             # Version 1 specific services
│   └── v2/             # Version 2 specific services (future)
├── dto/
│   ├── shared/         # Common DTOs across versions
│   ├── v1/            # Version 1 specific DTOs
│   └── v2/            # Version 2 specific DTOs (future)
├── schemas/           # Database schemas (shared across versions)
├── types/            # TypeScript types (shared across versions)
└── tests/           # Tests organized by version
```

## API Endpoints

### Legacy (Unversioned) - Maintained for Backward Compatibility
```
/organisations/:organisationId/interview-templates
```

### Version 1 (Default)
```
/v1/organisations/:organisationId/interview-templates
```

### Version 2 (Future)
```
/v2/organisations/:organisationId/interview-templates
```

## Usage Examples

### Accessing V1 API (Default)
```bash
# These are equivalent due to defaultVersion: '1' configuration
GET /organisations/123/interview-templates
GET /v1/organisations/123/interview-templates
```

### Accessing V2 API (Future)
```bash
GET /v2/organisations/123/interview-templates
```

## Implementation Guidelines

### Adding a New Version

1. **Create Version Directory Structure**
   ```bash
   mkdir -p src/interview-templates/controllers/v3
   mkdir -p src/interview-templates/services/v3
   mkdir -p src/interview-templates/dto/v3
   ```

2. **Implement Controllers**
   ```typescript
   @Controller({ path: '/organisations/:organisationId/interview-templates', version: '3' })
   @ApiTags('Interview Templates V3')
   export class InterviewTemplatesV3Controller {
     // Implementation
   }
   ```

3. **Create Version-Specific Services**
   ```typescript
   @Injectable()
   export class InterviewTemplatesV3Service {
     constructor(
       private readonly sharedService: InterviewTemplatesSharedService,
     ) {}
   }
   ```

4. **Update Module**
   ```typescript
   @Module({
     controllers: [
       InterviewTemplatesController,        // Legacy
       InterviewTemplatesV1Controller,      // V1
       InterviewTemplatesV2Controller,      // V2
       InterviewTemplatesV3Controller,      // V3 - New
     ],
     providers: [
       // Add V3 services
     ],
   })
   ```

### Best Practices

1. **Shared Logic**: Use `services/shared/` for common business logic to avoid duplication
2. **Backward Compatibility**: Always maintain older versions until properly deprecated
3. **DTOs**: Create version-specific DTOs while reusing shared ones where possible
4. **Documentation**: Update Swagger tags and API documentation for each version
5. **Testing**: Create version-specific tests in organized directories

### Migration Strategy

1. **Phase 1**: Implement V1 with current functionality
2. **Phase 2**: Add new features in V2 while maintaining V1
3. **Phase 3**: Deprecate legacy endpoints with proper notices
4. **Phase 4**: Eventually remove deprecated versions after migration period

## Current Status

- ✅ **V1**: Implemented with current functionality
- 🚧 **V2**: Foundation created, ready for new features
- ❌ **Legacy**: Maintained for backward compatibility

## Shared Components

### InterviewTemplatesSharedService
Provides common functionality:
- `findWithPagination()`: Standardized pagination logic
- `validateTemplateExists()`: Template existence validation
- `validateTemplateOwnership()`: Organization ownership checks

### Shared DTOs
- `InterviewTemplateResponseDto`: Base response structure
- `InterviewTemplatePaginationResponseDto`: Pagination response format

## Testing

Version-specific tests should be organized as:
```
tests/
├── v1/
│   ├── interview-templates-v1.controller.spec.ts
│   └── interview-templates-v1.service.spec.ts
├── v2/
│   ├── interview-templates-v2.controller.spec.ts
│   └── interview-templates-v2.service.spec.ts
└── shared/
    └── interview-templates-shared.service.spec.ts
```