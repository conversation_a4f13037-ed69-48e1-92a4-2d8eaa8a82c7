import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { SalesforceCRM } from 'src/integrations/crm/salesforce/crm.salesforce';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

interface SSNPageData {
  ssn: string;
}

export class SalesforceSSNMapper implements FormUpdateMapper {
  constructor(private salesforceCRM: SalesforceCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: SSNPageData,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    await this.salesforceCRM.contactService.updateContact(contact.crmClientId, {
      Onbord_SSN__c: pageData.ssn,
    });
  }
}
