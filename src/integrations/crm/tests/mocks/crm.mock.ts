import { CRM } from 'src/integrations/crm/crm.interface';

export const crmMock = {
  pageUpdateMapper: {} as any,
  authenticate: jest.fn(),
  createContact: jest.fn(),
  updateContact: jest.fn(),
  getContact: jest.fn(),
  getContacts: jest.fn(),
  getAllUsers: jest.fn(),
  getType: jest.fn(),
  updatePageInfo: jest.fn(),
  initMappers: jest.fn(),
  initCrm: jest.fn(),
  getAuthenticatedUserId: jest.fn(),
  sendEmail: jest.fn(),
  getPhoneNumbersOfContact: jest.fn(),
  getMainMobileOfContact: jest.fn(),
  getMainEmailOfContact: jest.fn(),
  logCommunication: jest.fn(),
} as CRM;
