export enum ClientQueueJobType {
  CREATE_DRAFT_CLIENT = 'create_draft_client',
  STORE_CLIENT = 'store_client',
  CREATE_INTERVIEW = 'create_interview',
  UPDATE_STATUS = 'update_client_status',
  SEND_NOTIFICATION = 'send_notification',
  UPDATE_INTERVIEW_STATUS = 'update_interview_status',
  UPDATE_CLIENT_INTERVIEW_COMPLETION = 'update_client_interview_completion',
  UPDATE_CLIENT_STATUS_AFTER_ENVELOPE = 'update_client_status_after_envelope',
  UPDATE_LAST_CONTACT_ACTIVITY_TIMESTAMP = 'update_last_contact_activity_timestamp',
  RESEND_TEXT = 'resend_text',
}