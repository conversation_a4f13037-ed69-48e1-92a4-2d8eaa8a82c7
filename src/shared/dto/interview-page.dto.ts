import { ApiProperty } from "@nestjs/swagger";
import { Expose, Transform, Type } from "class-transformer";
import { ElementViewDto } from "./element.dto";
import { Allow, IsArray, ValidateNested } from "class-validator";
import { ObjectId } from "mongoose";
import { InterviewPageStatusEnum } from "src/interview-templates/types/interview-page-status.enum";
export class CustomQuestionViewDto {
  @ApiProperty()
  @Type(() => String)
  @Expose()
  _id: ObjectId;

  @ApiProperty()
  @Expose()
  question: string;

  @ApiProperty()
  @Expose()
  type: string;

  @ApiProperty()
  @Expose()
  @IsArray()
  @ValidateNested({ each: true })
  options: string[];

  @ApiProperty()
  @Expose()
  required: boolean;

  @ApiProperty()
  @Expose()
  title: string;

  @ApiProperty()
  @Expose()
  description: string;
}


export class PageDataViewDto {
  @ApiProperty()
  @Expose()
  templateName?: string;

  @ApiProperty()
  @Expose()
  @Type(() => CustomQuestionViewDto)
  question?: CustomQuestionViewDto;

  @ApiProperty()
  @Expose()
  instance?: string;

  @ApiProperty()
  @Expose()
  label?: string;
}

export class InterviewPageViewDto {
  @ApiProperty()
  @Expose()
  name: string;

  @ApiProperty()
  @Expose()
  order: number;

  @ApiProperty()
  @Expose()
  @Type(() => ElementViewDto)
  @IsArray()
  @ValidateNested({ each: true })
  elements: ElementViewDto[];

  @ApiProperty()
  @Expose()
  filled: boolean;

  @ApiProperty()
  @Expose()
  status: InterviewPageStatusEnum;

  @ApiProperty()
  @Expose()
  @Type(() => PageDataViewDto)
  data: PageDataViewDto;
}