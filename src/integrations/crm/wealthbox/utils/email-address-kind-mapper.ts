import { EmailTypeEnum } from 'src/integrations/crm/types/generic/email.type';
import { WealthboxEmailAddressKind } from 'src/integrations/crm/wealthbox/dto/email.dto';

export function emailAddressKindToEmailAddressType(emailKind) {
  switch (emailKind) {
    case WealthboxEmailAddressKind.Home:
      return EmailTypeEnum.HOME;
    case WealthboxEmailAddressKind.Work:
      return EmailTypeEnum.WORK;
    case WealthboxEmailAddressKind.Mobile:
      return EmailTypeEnum.HOME;
    default:
      return EmailTypeEnum.OTHER;
  }
}

export function emailAddressTypeToEmailAddressKind(emailType) {
  switch (emailType) {
    case EmailTypeEnum.HOME:
      return WealthboxEmailAddressKind.Home;
    case EmailTypeEnum.WORK:
      return WealthboxEmailAddressKind.Work;
    case EmailTypeEnum.OTHER:
      return WealthboxEmailAddressKind.Other;
    default:
      return WealthboxEmailAddressKind.Other;
  }
}
