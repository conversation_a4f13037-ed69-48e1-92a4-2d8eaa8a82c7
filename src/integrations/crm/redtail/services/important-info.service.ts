import { HttpService } from '@nestjs/axios';
import { Logger } from '@nestjs/common';
import { UpdateImportantInfoRequestDto } from 'src/integrations/crm/redtail/dto/requests/important-info/update-important-info-request.dto';
import {
  GetImportantInformationResponse,
  GetImportantInformationResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/important-info/get-important-info-response.dto';
import {
  UpdateImportantInfoResponse,
  UpdateImportantInfoResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/important-info/update-important-info-response.dto';
import { HttpMethodsEnum } from 'src/shared/types/general/http-methods.enum';
import { OperationType } from 'src/super-http/enums';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { camelize } from 'src/utils';
import { makeRequest } from 'src/utils/makeRequest';
import { snakeify } from 'src/utils/snakeify';
import { ClsService } from 'nestjs-cls';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';

export class RedtailImportantInformationService {
  private readonly baseUrl: string;
  private logger: Logger;

  constructor(
    private httpService: SuperHttpService,
    private authHeader: object,
    private tenantId: string
  ) {
    this.baseUrl = process.env.REDTAIL_BASE_URL;
    this.logger = new Logger('RedtailImportantInformationService');
  }

  /**
   * Updates the important information for a contact.
   * @param contactId - The ID of the contact.
   * @param info - The updated important information.
   * @returns A promise that resolves to the updated important information response.
   */
  public async updateImportantInformation(
    contactId: string,
    info: UpdateImportantInfoRequestDto,
  ): Promise<UpdateImportantInfoResponse> {
    const url = `${this.baseUrl}/contacts/${contactId}/important_information`;
    const response = await this.httpService.put<UpdateImportantInfoResponseDto>(
      url,
      this.tenantId,
      snakeify(info),
      { 
        rateLimiter: 'redtail', 
        integrationName: 'redtail', 
        operation: OperationType.UPDATE, 
        entityType: 'important-info',
        entityId: contactId,
        wideCacheInvalidation: true
      },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }

  /**
   * Retrieves important information for a CRM contact.
   * @param crmContactId - The ID of the CRM contact.
   * @returns A promise that resolves to the important information response.
   */
  public async getImportantInformation(
    crmContactId: string,
  ): Promise<GetImportantInformationResponse> {
    const url = `${this.baseUrl}/contacts/${crmContactId}/important_information`;
    const response = await this.httpService.get<GetImportantInformationResponseDto>(
      url,
      this.tenantId,
      { 
        rateLimiter: 'redtail', 
        integrationName: 'redtail', 
        operation: OperationType.GET, 
        entityType: 'important-info',
        entityId: crmContactId,
        forceFresh: false,
        cacheKeysObject: { crmContactId }
      },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }
}
