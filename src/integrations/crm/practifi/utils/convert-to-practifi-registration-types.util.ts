import { AccountTypeEnum } from "src/shared/types/accounts/account-type.enum";

const practifiRegistrationTypes = {
    [AccountTypeEnum.SingleNameBrokerage]: 'brokerage',
    [AccountTypeEnum.JointNameBrokerage]: 'joint',
    [AccountTypeEnum.Ira]: 'IRA',
    [AccountTypeEnum.RothIra]: 'IRA Roth',
    [AccountTypeEnum.OfflineTrust]: 'Trust',
    [AccountTypeEnum.OfflineInheritedIra]: 'Offline Inherited IRA',
    [AccountTypeEnum.OfflineCorporate]: 'Corporation',
    [AccountTypeEnum.OfflineOther]: 'Offline Other',
    [AccountTypeEnum.OfflineSimpleIra]: 'Simple IRA'
}

const practifiTypesMappedWithRedtailTypes = {
  'joint': AccountTypeEnum.JointNameBrokerage,
  'IRA': AccountTypeEnum.Ira,
  'IRA Roth': AccountTypeEnum.RothIra,
  'brokerage': AccountTypeEnum.SingleNameBrokerage,
  'Trust': AccountTypeEnum.OfflineTrust,
  'Offline Inherited IRA': AccountTypeEnum.OfflineInheritedIra,
  'Simple IRA': AccountTypeEnum.OfflineSimpleIra,
  'Corporation': AccountTypeEnum.OfflineCorporate,
  'Offline Other': AccountTypeEnum.OfflineOther,
}

export const handleConvertTypes = (type: AccountTypeEnum) => practifiRegistrationTypes[type];

export const handleConvertPractifiTypeToUnified = (type: string) => practifiTypesMappedWithRedtailTypes[type];