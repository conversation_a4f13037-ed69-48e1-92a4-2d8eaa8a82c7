import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

export class AddressPageVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      address1: {
        description: 'Primary address line',
        type: VariableType.STRING,
        example: '123 Main Street',
      },
      address2: {
        description: 'Secondary address line (apartment, suite, etc.)',
        type: VariableType.STRING,
        example: 'Apt 4B',
        isConditional: true,
      },
      city: {
        description: 'City name',
        type: VariableType.STRING,
        example: 'New York',
      },
      state: {
        description: 'State or province',
        type: VariableType.STRING,
        example: 'NY',
      },
      zipCode: {
        description: 'ZIP or postal code',
        type: VariableType.STRING,
        example: '10001',
      },
      country: {
        description: 'Country name',
        type: VariableType.STRING,
        example: 'United States',
      },
      fullAddress: {
        description: 'Complete formatted address',
        type: VariableType.ADDRESS,
        example: '123 Main Street, Apt 4B, New York, NY 10001',
      },
    };
  }
}