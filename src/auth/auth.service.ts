import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { AuthErrorCodeEnum, ResetPasswordActions } from './auth.types';
import * as AWS from 'aws-sdk';
import { Request, Response } from 'express';
import { PasswordAlreadySetException } from './exceptions/invalid-new-password.exception';
import {
  AuthFlowType,
  AuthParametersType,
  InitiateAuthResponse,
} from 'aws-sdk/clients/cognitoidentityserviceprovider';
import { LoginDto } from './dto/login.dto';
import { MfaRequiredResponseDto, MfaDto } from './dto/mfa.dto';
import {
  NewPasswordRequiredResponseDto,
  SetNewPasswordDto,
  NonMfaLoginResponseDto,
} from './dto/new-password.dto';
import { ResetPasswordDto } from './dto/reset.dto';
import { SetPasswordDto } from './dto/set-password.dto';
import { SignoutDto } from './dto/logout.dto';
import { RefreshTokenDto } from './dto/refresh.dto';
import { CognitoService } from '../shared/services/cognito.service';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { ConfigService } from '@nestjs/config';
import { WorkEnvironmentsEnum } from 'src/shared/types/general/env';
import { UserStatusesEnum } from 'src/shared/types/rbac/statuses.enum';

@Injectable()
export class AuthService {
  private env: string;

  constructor(
    private readonly cognitoService: CognitoService,
    private readonly advisorsCrudService: AdvisorsCrudService,
    private readonly configService: ConfigService,
  ) {
    this.env = this.configService.get<string>('WORK_ENV');
  }

  /**
   * Authenticates a user with the provided login credentials.
   * @param loginDto - The login credentials of the user.
   * @param response - The response object to send the authentication result.
   * @returns A promise that resolves to one of the following response DTOs:
   * - MfaRequiredResponseDto: If multi-factor authentication is required.
   * - NewPasswordRequiredResponseDto: If a new password is required.
   * - NonMfaLoginResponseDto: If the login is successful without multi-factor authentication.
   * @throws An error if there is an authentication error.
   */
  public async authenticateUser(
    loginDto: LoginDto,
    response: Response,
  ): Promise<
    | MfaRequiredResponseDto
    | NewPasswordRequiredResponseDto
    | NonMfaLoginResponseDto
  > {
    try {
      const authParameters = this.getAuthParameters(loginDto);
      const initiateAuthRes = await this.cognitoService.initiateAuth(
        this.getAuthFlow('USER_PASSWORD_AUTH', authParameters),
      );
      const handlerResult = this.handleInitiateAuth(initiateAuthRes, response);
      if (handlerResult) {
        return handlerResult;
      }

      const {
        _id: advisorId,
        organisation: { _id: organisationIdObj },
      } = await this.advisorsCrudService.findOne({
        'personalInfo.email': loginDto.email,
      });
      return { advisorId, organisationId: organisationIdObj.toString() };
    } catch (error) {
      throw this.handleAuthError(error as AWS.AWSError);
    }
  }

  /**
   * Responds to the MFA challenge by providing the MFA code and other necessary parameters.
   * @param mfaDto - The MFA DTO containing the MFA code and email.
   * @param response - The response object to set the authentication cookies.
   * @returns An object containing the advisor ID and organisation ID.
   */
  public async respondToMfaChallenge(mfaDto: MfaDto, response: Response) {
    const challengeResponses = {
      SMS_MFA_CODE: mfaDto.mfaCode,
      USERNAME: mfaDto.email,
    };

    const params = {
      ChallengeName: 'SMS_MFA',
      ClientId: process.env.AWS_COGNITO_CLIENT_ID,
      ChallengeResponses: challengeResponses,
      Session: mfaDto.session,
    };

    const cognitoResponse = await this.cognitoService.respondToAuthChallenge(
      params,
    );
    this.setAuthCookies(cognitoResponse, response);

    const {
      _id: advisorId,
      organisation: { _id: organisationId },
    } = await this.advisorsCrudService.findOne({
      'personalInfo.email': mfaDto.email,
    });

    return { advisorId, organisationId: organisationId.toString() };
  }

  /**
   * Resets the password for a user or resend welcome email if user did not change their temporary password.
   * @param resetPasswordDto - The DTO containing the email and new password.
   * @returns A promise that resolves to the result of the password reset operation.
   */
  public async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const { email } = resetPasswordDto;
    const userPoolId = process.env.AWS_COGNITO_USER_POOL_ID;

    // Get the current user status
    const user = await this.cognitoService.getCognitoUser(email);
    if (!user) {
      throw new Error('User not found');
    }

    const userStatus = user.UserStatus;

    // Check if the user is in FORCE_CHANGE_PASSWORD status
    if (userStatus === 'FORCE_CHANGE_PASSWORD') {
      // We cannot reset the password, need to resend the invitation
      await this.cognitoService.adminResendInvite(email);
      return {
        action: ResetPasswordActions.RESEND,
      };
    }

    // User can reset their password normally
    const params = {
      UserPoolId: userPoolId,
      Username: email,
    };
    await this.cognitoService.adminResetUserPassword(params);
    return {
      action: ResetPasswordActions.RESET,
    };
  }

  /**
   * Sets the password for a user.
   * @param setPasswordDto - The data required to set the password.
   */
  public async setPassword(setPasswordDto: SetPasswordDto) {
    const params = {
      ClientId: process.env.AWS_COGNITO_CLIENT_ID,
      Username: setPasswordDto.email,
      ConfirmationCode: setPasswordDto.confirmationCode,
      Password: setPasswordDto.password,
    };
    await this.cognitoService.confirmForgotPassword(params);
  }

  /**
   * Refreshes the authentication token using the provided refresh token.
   * @param refreshTokenDto - The refresh token DTO.
   * @param response - The HTTP response object.
   * @returns A promise that resolves to the updated authentication cookies.
   */
  public async refreshToken(
    refreshTokenDto: RefreshTokenDto,
    response: Response,
  ) {
    const authParameters = {
      REFRESH_TOKEN: refreshTokenDto.refreshToken,
    };

    const refreshResponse = await this.cognitoService.initiateAuth(
      this.getAuthFlow('REFRESH_TOKEN_AUTH', authParameters),
    );
    return this.setAuthCookies(refreshResponse, response);
  }

  /**
   * Signs out a user and clears authentication cookies.
   * @param signoutDto - The signout data transfer object.
   * @param response - The HTTP response object.
   * @returns A promise that resolves when the user is signed out.
   */
  public async signOut(signoutDto: SignoutDto, response: Response) {
    const params = {
      UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
      Username: signoutDto.email,
    };

    this.clearAuthCookies(response);

    return this.cognitoService.adminUserGlobalSignOut(params);
  }

  /**
   * Sets a new password for a user that has not set their password yet.
   * @param setNewPasswordDto - The DTO containing the necessary information to set the new password.
   * @throws {PasswordAlreadySetException} - If the user's password has already been set.
   */
  public async newUserPassword(setNewPasswordDto: SetNewPasswordDto) {
    const loginAuthParameters = {
      USERNAME: setNewPasswordDto.email,
      PASSWORD: setNewPasswordDto.oldPassword,
    };

    const authChallengeParams = {
      ChallengeName: 'NEW_PASSWORD_REQUIRED',
      ClientId: process.env.AWS_COGNITO_CLIENT_ID,
      ChallengeResponses: {
        NEW_PASSWORD: setNewPasswordDto.newPassword,
        USERNAME: setNewPasswordDto.email,
      },
      Session: '',
    };

    const authResponse = await this.cognitoService.initiateAuth(
      this.getAuthFlow('USER_PASSWORD_AUTH', loginAuthParameters),
    );

    if (authResponse.ChallengeName !== 'NEW_PASSWORD_REQUIRED') {
      throw new PasswordAlreadySetException();
    }

    authChallengeParams.Session = authResponse.Session;

    await this.cognitoService.respondToAuthChallenge(authChallengeParams);
    await this.advisorsCrudService.update(
      { 'personalInfo.email': setNewPasswordDto.email },
      { status: UserStatusesEnum.ACTIVE },
    );
  }

  /**
   * Deletes a user from cognito.
   * @param username - The username of the user to be deleted.
   * @returns A promise that resolves when the user is successfully deleted.
   */
  public async deleteUser(username: string) {
    return this.cognitoService.adminDeleteUser(username);
  }

  /**
   * Returns an object representing the authentication flow.
   * @param authFlowType - The type of authentication flow.
   * @param authParameters - The parameters required for authentication.
   * @returns An object representing the authentication flow.
   */
  private getAuthFlow = (
    authFlowType: AuthFlowType,
    authParameters: AuthParametersType,
  ) => ({
    AuthFlow: authFlowType,
    ClientId: process.env.AWS_COGNITO_CLIENT_ID,
    AuthParameters: authParameters,
  });

  /**
   * Sets the authentication cookies in the response object.
   * @param authResult - The authentication result containing the access token and refresh token.
   * @param response - The response object to set the cookies on.
   */
  private setAuthCookies = (
    authResult: InitiateAuthResponse,
    response: Response,
  ): void => {
    const sameSite =
      this.env === WorkEnvironmentsEnum.Staging ? 'none' : 'strict';

    response.cookie(
      'access_token',
      authResult.AuthenticationResult.AccessToken,
      { httpOnly: true, secure: true, sameSite },
    );

    response.cookie(
      'refresh_token',
      authResult.AuthenticationResult.RefreshToken,
      { httpOnly: true, secure: true, sameSite },
    );
  };

  /**
   * Clears the authentication cookies from the response.
   * @param response - The HTTP response object.
   */
  private clearAuthCookies = (response: Response): void => {
    response.clearCookie('access_token');
    response.clearCookie('refresh_token');
  };

  /**
   * Handles authentication errors and returns the appropriate error object based on the error code.
   * @param error - The AWS.AWSError object representing the authentication error.
   * @returns The corresponding error object based on the error code.
   */
  private handleAuthError = (error: AWS.AWSError): Error => {
    switch (error.code) {
      case AuthErrorCodeEnum.NotAuthorizedException:
        return new UnauthorizedException('Invalid credentials', error.message);
      case AuthErrorCodeEnum.UserNotFoundException:
        return new NotFoundException('User not found', error.message);
      default:
        return new InternalServerErrorException(
          'An unexpected error occurred',
          error.message,
        );
    }
  };

  private getAuthParameters(loginDto: LoginDto) {
    return {
      USERNAME: loginDto.email,
      PASSWORD: loginDto.password,
    };
  }

  /**
   * Handles the response from the InitiateAuth API call.
   * @param initiateAuthRes - The response from the InitiateAuth API call.
   * @param response - The HTTP response object.
   * @returns If the ChallengeName is 'NEW_PASSWORD_REQUIRED', returns an object with newPasswordRequired and session properties.
   * If the ChallengeName is 'SMS_MFA', returns an object with mfaRequired and session properties.
   * Otherwise, no explicit return value.
   */
  private handleInitiateAuth(
    initiateAuthRes: InitiateAuthResponse,
    response: Response,
  ): void | MfaRequiredResponseDto | NewPasswordRequiredResponseDto {
    if (initiateAuthRes.ChallengeName === 'NEW_PASSWORD_REQUIRED') {
      return {
        newPasswordRequired: true,
        session: initiateAuthRes.Session,
      };
    }

    if (initiateAuthRes.ChallengeName === 'SMS_MFA') {
      return {
        mfaRequired: true,
        session: initiateAuthRes.Session,
      };
    }

    this.setAuthCookies(initiateAuthRes, response);
  }

  /**
   * Changes the password for a user.
   *
   * @param setNewPasswordDto - The DTO containing the old and new passwords.
   * @param request - The request object containing the access token.
   */
  public async changePassword(
    setNewPasswordDto: SetNewPasswordDto,
    request: Request,
  ) {
    const params = {
      PreviousPassword: setNewPasswordDto.oldPassword,
      ProposedPassword: setNewPasswordDto.newPassword,
      AccessToken: request.cookies.access_token,
    };
    await this.cognitoService.changePassword(params);
  }
}
