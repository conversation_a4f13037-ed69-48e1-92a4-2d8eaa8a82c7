export interface CustomField {
  fullName: string;
  label: string;
  type: string;
  length?: number;
  defaultValue?: string;
  visibleLines?: number;
  precision?: number;
  scale?: number;
  securitySettings?: {
    fieldPermissions: {
      readable: boolean;
      editable: boolean;
      field: string;
      permissionSets?: string[];
      profiles?: {
        default: boolean;
        profileName: string;
      }[];
    }[];
  };
}

export interface MetadataCreateResult {
  success: boolean;
  fullName: string;
}

export type commonConfigs = {
  headers: {
    Authorization: string;
    'Content-Type': string;
  };
  timeout: number;
}
