import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { AddressUpdateRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/company-request.dto';
import { Country } from 'src/shared/types/general/country.enum';
import { PractifiCRM } from '../crm.practifi';

export class PractifiCompanyMapper implements FormUpdateMapper {
  constructor(private practifiCRM: PractifiCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    update: AddressUpdateRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    await this.practifiCRM.customObjectService.createCompanyAddress({
      Name: update.name,
      City: update.address.city,
      PostalCode: update.address.zip,
      State: update.address.state,
      Street: update.address.line1,
      Country: Country.US,
      UsageType: 'Work',
      practifi__Contact__c: contact.crmClientId
    });
    
    await this.practifiCRM.contactService.updateContact(contact.crmClientId, {
      practifi__Employer__c: update.name,
    });
  }
} 