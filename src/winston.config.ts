import {
  WINSTON_MODULE_NEST_PROVIDER,
  utilities as nestWinstonModuleUtilities,
  WinstonModule,
} from 'nest-winston';
import WinstonCloudWatch from 'winston-cloudwatch';
import * as winston from 'winston';

export default {
  level: 'info',
  transports: [
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.ms(),
        nestWinstonModuleUtilities.format.nestLike('Onbord', {
          colors: true,
          prettyPrint: true,
        }),
      ),
    }),
    new WinstonCloudWatch({
      awsRegion: process.env.AWS_REGION,
      logGroupName: `onbord-backend-${process.env.WORK_ENV || 'dev'}`,
      logStreamName: `onbord-backend-${process.env.WORK_ENV || 'dev'}`,
      messageFormatter: (item) => {
        return `${item.level}: ${item.message}`;
      },
    }),
  ],
};
