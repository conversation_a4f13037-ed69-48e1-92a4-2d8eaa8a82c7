import { Injectable } from '@nestjs/common';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { generateRequestConfigs } from '../utils/generate-request-configs.util';

@Injectable()
export class SalesforceSuperHTTPConfigsService {
    constructor(
      private readonly httpService: SuperHttpService,
      private tenantId: string
    ) {}

    setRequestConfigs(accessToken: string) {
      this.httpService.clearInterceptors();
      const requestConfigs = generateRequestConfigs(accessToken);
      const { withInterceptors } = requestConfigs;
      const { type, successCallback, errorCallback } = withInterceptors;
      this.httpService.addInterceptor(type, successCallback, errorCallback);
    }
} 