import { Module, forwardRef } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { SalesforceController } from './salesforce.controller';
import { SalesforceService } from './salesforce.service';
import { AdvisorsModule } from 'src/advisors/advisors.module';
import { SalesforceContactService } from './services/salesforce-contact.service';
import { SalesforceTaskService } from './services/salesforce-task.service';
import { SalesforceUserService } from './services/salesforce-user.service';

@Module({
  imports: [
    HttpModule,
    forwardRef(() => AdvisorsModule),
  ],
  controllers: [SalesforceController],
  providers: [
    SalesforceService,
    SalesforceContactService,
    SalesforceTaskService,
    SalesforceUserService,
  ],
  exports: [SalesforceService],
})
export class SalesforceModule {} 