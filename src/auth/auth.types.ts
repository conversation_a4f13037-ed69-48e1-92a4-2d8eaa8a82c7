export enum ResetPasswordActions {
  RESET = 'RESET',
  RESEND = 'RESEND',
}
export enum AuthScheme {
  BASIC = 'Basic',
  USER_KEY = 'UserKeyAuth',
  BEARER = 'Bearer',
}

export type ApiKey = {
  apiKey: string;
};

export type Basic = {
  username: string;
  password: string;
};

export type OAuth = {
  accessToken: string;
  refreshToken: string;
  instanceUrl?: string;
  lastRefresh?: Date;
  shouldRefresh?: boolean;
};

export type OAuthCode = {
  authorisationCode: string;
};

export type UserKey = {
  userKey: string;
};

// Cognito types
export interface CognitoAuthResponse {
  getAccessToken(): { getJwtToken(): string };
  getRefreshToken(): { getToken(): string };
}

export type CognitoError = {
  code: string;
};

// Auth Exceptions
export enum AuthErrorCodeEnum {
  NotAuthorizedException = 'NotAuthorizedException',
  UserNotFoundException = 'UserNotFoundException',
}

// Passportjs types
export type JWTStratPayload = {
  sub: string;
  username: string;
};

export type RedtailCredentials = Basic &
  ApiKey &
  UserKey & { databaseId: string; userId: string };

export type Credentials = Basic | ApiKey | UserKey | OAuth | OAuthCode;

export type CrmCredentials = RedtailCredentials | Credentials;
