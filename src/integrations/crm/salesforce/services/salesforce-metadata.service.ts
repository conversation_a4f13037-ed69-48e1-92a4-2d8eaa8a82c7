import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { Connection } from 'jsforce';
import { Logger } from 'winston';
import { CustomField } from '../types/metadata.types';
import { OAuth } from 'src/auth/auth.types';

@Injectable()
export class SalesforceMetadataService {
  constructor(private readonly logger: Logger) {}

  async initializeCustomFields(
    connection: Connection,
    customFields: CustomField[],
    authenticate: () => Promise<OAuth>,
  ): Promise<Map<string, string | number>> {
    try {
      this.logger.debug('Initializing Salesforce custom fields');
      const metadata = connection.metadata;

      // Describe the Contact object to see which fields already exist
      const describe = await connection.describe('Contact');
      const existingFieldNames = new Set(describe.fields.map(field => field.name));

      // Filter out fields that already exist
      const fieldsToCreate = customFields.filter(field => {
        const fieldName = field.fullName.split('.')[1]; // "Contact.Field__c" -> "Field__c"
        return !existingFieldNames.has(fieldName);
      });

      const permSetName = 'OnboardCustomFields';

      // Step 1: Ensure Permission Set exists (without field permissions yet)
      await this.createPermissionSetIfNeeded(metadata, permSetName);

      // Step 2: Create any missing fields in batches
      if (fieldsToCreate.length > 0) {
        this.logger.debug(`Creating ${fieldsToCreate.length} missing custom fields in Salesforce`);

        for (let i = 0; i < fieldsToCreate.length; i += 10) {
          const batch = fieldsToCreate.slice(i, i + 10);

          const results = await Promise.allSettled(
            batch.map(field => this.createField(metadata, field, authenticate))
          );

          for (const [index, result] of results.entries()) {
            if (result.status === 'rejected') {
              this.logger.error(`Failed to create field ${batch[index].fullName}`, {
                error: result.reason
              });
            }
          }

          // Wait between batches to avoid rate limits
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // Step 3: Now that fields are created, update the permission set to give them field permissions
      await this.updatePermissionSetFieldPermissions(metadata, permSetName, customFields);

      // Verification results
      const verificationResults = new Map<string, string | number>();
      verificationResults.set('totalFieldsRequired', customFields.length);
      verificationResults.set('fieldsCreated', fieldsToCreate.length);

      this.logger.info('CRM initialization completed', {
        totalFields: customFields.length,
        fieldsCreated: fieldsToCreate.length
      });

      return verificationResults;
    } catch (error) {
      this.logger.error('Error initializing Salesforce custom fields', {
        error: error.message,
        stack: error.stack,
      });
      throw new HttpException(
        'Failed to initialize Salesforce custom fields',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async createField(
    metadata: any,
    field: CustomField,
    authenticate: () => Promise<OAuth>,
    attempts = 3
  ): Promise<void> {
    try {
      // Build the fieldMetadata object dynamically based on field type
      const fieldMetadata: any = {
        fullName: field.fullName,
        label: field.label,
        type: field.type
      };
  
      // Add properties conditionally, ensuring they are defined and correct
      switch (field.type) {
        case 'Text':
          if (typeof field.length === 'number') {
            fieldMetadata.length = field.length;
          } else {
            fieldMetadata.length = 100; // Default length if not provided
          }
          break;
        
        case 'Checkbox':
          // defaultValue should be a boolean
          fieldMetadata.defaultValue = typeof field.defaultValue === 'string' 
            ? field.defaultValue === 'true'
            : Boolean(field.defaultValue);
          break;
  
        case 'Currency':
        case 'Number':
          // precision and scale must be integers, provide defaults if missing
          fieldMetadata.precision = typeof field.precision === 'number' ? field.precision : 18;
          fieldMetadata.scale = typeof field.scale === 'number' ? field.scale : 2;
          break;
  
        case 'LongTextArea':
          // length and visibleLines must be integers
          fieldMetadata.length = typeof field.length === 'number' ? field.length : 32768;
          fieldMetadata.visibleLines = typeof field.visibleLines === 'number' ? field.visibleLines : 3;
          break;
  
        // Add other cases as needed for different field types (Date, Picklist, etc.)
  
        default:
          // For types that do not require these numeric properties, omit them
          // If the field has properties that are undefined, do not add them.
          break;
      }
  
      await metadata.create('CustomField', fieldMetadata);
    } catch (error) {
      if (attempts > 1 && error.message?.includes('INVALID_SESSION_ID')) {
        await authenticate();
        return this.createField(metadata, field, authenticate, attempts - 1);
      }
      throw error;
    }
  }
  

  private async createPermissionSetIfNeeded(
    metadata: any,
    permSetName: string
  ): Promise<void> {
    const existingPermSets = await metadata.list([{ type: 'PermissionSet', folder: null }]);
    if (!existingPermSets.some(ps => ps.fullName === permSetName)) {
      // Create new permission set with minimal info
      try {
        await metadata.create('PermissionSet', {
          fullName: permSetName,
          label: 'Onboard Custom Fields',
          description: 'Permission set for Onboard custom fields access'
        });
        // Wait for permission set creation to complete
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        this.logger.warn('Error creating permission set', { error: error.message });
        throw error;
      }
    }
  }

  private async updatePermissionSetFieldPermissions(
    metadata: any,
    permSetName: string,
    customFields: CustomField[]
  ): Promise<void> {
    try {
      // Retrieve the existing permission set first
      const [existingPermSet] = await metadata.read('PermissionSet', [permSetName]);

      if (!existingPermSet) {
        throw new Error(`Permission Set ${permSetName} not found`);
      }

      const newFieldPermissions = customFields.map(field => ({
        field: field.fullName,
        readable: true,
        editable: true
      }));

      // Merge new field permissions with existing ones
      const existingFieldPermissions = existingPermSet.fieldPermissions || [];
      const existingFieldNames = new Set(existingFieldPermissions.map(fp => fp.field));

      for (const fp of newFieldPermissions) {
        if (!existingFieldNames.has(fp.field)) {
          existingFieldPermissions.push(fp);
        }
      }

      existingPermSet.fieldPermissions = existingFieldPermissions;

      // (Optional) ensure object-level permissions on Contact if needed
      const existingObjectPermissions = existingPermSet.objectPermissions || [];
      const contactPerm = existingObjectPermissions.find(op => op.object === 'Contact');
      if (!contactPerm) {
        existingObjectPermissions.push({
          object: 'Contact',
          allowCreate: true,
          allowDelete: true,
          allowEdit: true,
          allowRead: true,
          modifyAllRecords: true,
          viewAllRecords: true
        });
        existingPermSet.objectPermissions = existingObjectPermissions;
      }

      // Update the permission set with full metadata (not partial)
      await metadata.update('PermissionSet', existingPermSet);

      this.logger.info('Successfully updated permission set with field permissions', {
        permSetName,
        fieldCount: newFieldPermissions.length
      });
    } catch (error) {
      this.logger.error('Error updating permission set', {
        error: error.message,
        permSetName
      });
      throw error;
    }
  }
}
