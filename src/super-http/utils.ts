import { createHash } from 'crypto';
import { OperationType } from './enums';

export interface CacheKeyParams {
    tenantId: string;
    integrationName: string;
    entityType: string;
    operation?: OperationType;
    entityId?: string;
    cacheKeysObject?: Record<string, any>;
    url?: string;
}

/**
 * Generates the base parts of a cache key that are common across all patterns
 * @private
 * @param params Cache key parameters
 * @returns Array of base key parts [tenantId, integrationName, entityType]
 * @throws Error if required parameters are missing
 */
const generateBaseCacheKeyParts = (params: CacheKeyParams): string[] => {
    const { tenantId, integrationName, entityType } = params;
    
    if (!tenantId || !integrationName || !entityType) {
        throw new Error('tenantId, integrationName, and entityType are required');
    }
    
    return [tenantId, integrationName, entityType];
};

/**
 * Generates a cache key prefix for invalidating all operations of an entity type
 * Pattern: [tenantId]::[integrationName]::[entityType]
 * Example: tenant123::hubspot::contact
 * 
 * Use this when you want to invalidate ALL cached data for a specific entity type,
 * regardless of operation or entityId
 * 
 * @param params Cache key parameters
 * @returns Cache key prefix string
 */
export const generateEntityTypePrefix = (params: CacheKeyParams): string => {
    const parts = generateBaseCacheKeyParts(params);
    return parts.join('::');
};

/**
 * Generates a specific cache key for operation+entityId combinations
 * Pattern: [tenantId]::[integrationName]::[entityType]::[operation]::[entityId]::[url]
 * Example: tenant123::hubspot::contact::get::abc123::https://api.example.com/contacts
 * 
 * Use this when you want to invalidate cache for a specific entity,
 * such as when updating a single contact's information
 * 
 * @param params Cache key parameters (operation and entityId required)
 * @returns Specific cache key string
 * @throws Error if operation is missing
 */
export const generateSpecificCacheKey = (params: CacheKeyParams): string => {
    const { operation, entityId, url } = params;
    if (!operation) {
        throw new Error('operation is required for specific cache key');
    }
    
    if (!entityId) {
        throw new Error('entityId is required for specific cache key');
    }
    
    const parts = [
        ...generateBaseCacheKeyParts(params),
        operation,
        entityId,
        url
    ].filter(Boolean);
    
    return parts.join('::');
};

/**
 * Generates a full cache key including all optional parameters
 * Pattern: [tenantId]::[integrationName]::[entityType]::[operation]::[entityId?]::[url?]::[cacheKeysHash?]
 * Example: tenant123::hubspot::contact::list::null::https://api.example.com/contacts::a1b2c3d4
 * 
 * Use this for storing and retrieving cached data with specific filters or parameters.
 * The cacheKeysObject is hashed to create a consistent identifier for the query parameters.
 * 
 * @param params Cache key parameters (operation required)
 * @returns Full cache key string
 * @throws Error if operation is missing
 */
export const generateFullCacheKey = (params: CacheKeyParams): string => {
    const { operation, entityId, cacheKeysObject, tenantId, url } = params;
    if (!tenantId) {
        throw new Error('tenantId is required for caching');
    }

    if (!operation) {
        throw new Error('operation is required for full cache key');
    }

    const parts = [
        ...generateBaseCacheKeyParts(params),
        operation,
        entityId,
        url,
        cacheKeysObject ? hashCacheKeysObject(cacheKeysObject) : null
    ].filter(Boolean);

    return parts.join('::');
};

/**
 * Creates a deterministic hash of an object for cache key generation
 * @private
 * @param obj Object to hash
 * @returns MD5 hash of the sorted stringified object
 */
const hashCacheKeysObject = (obj: Record<string, any>): string => {
    const sortedStr = JSON.stringify(obj, Object.keys(obj).sort());
    return createHash('md5').update(sortedStr).digest('hex');
};