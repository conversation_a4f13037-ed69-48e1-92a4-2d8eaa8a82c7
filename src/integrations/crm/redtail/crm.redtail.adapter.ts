import { CrmAdapter } from 'src/integrations/crm/crm.adapter.interface';
import { RedtailAddress } from 'src/integrations/crm/redtail/types/address.type';
import { RedtailContact } from 'src/integrations/crm/redtail/types/contact.type';
import { RedtailEmail } from 'src/integrations/crm/redtail/types/email.type';
import {
  RedtailAddressTypeEnum,
  RedtailPhoneTypeEnum,
  RedtailEmailTypeEnum,
} from 'src/integrations/crm/redtail/types/enums';
import { RedtailPhone } from 'src/integrations/crm/redtail/types/phone.type';
import { GenericCrmAccount } from 'src/integrations/crm/types/accounts/crm-account.type';
import { CrmContact } from 'src/integrations/crm/types/contacts/crm-contact.type';
import {
  GenericAddress,
  AddressTypeEnum,
} from 'src/integrations/crm/types/generic/address.type';
import {
  GenericEmail,
  EmailTypeEnum,
} from 'src/integrations/crm/types/generic/email.type';
import {
  GenericPhone,
  PhoneTypeEnum,
} from 'src/integrations/crm/types/generic/phone.type';
import { Camelize } from 'src/shared/types/monads';
import { camelize } from 'src/utils';

export class RedtailAdapter implements CrmAdapter {
  public mapContact(contact: RedtailContact): CrmContact {
    if (!contact) throw new Error('Contact is undefined');
    return {
      id: contact.id?.toString(),
      firstName: contact.first_name,
      lastName: contact.last_name,
      citizenship: 'US',
      accounts: [],
      dob: contact.dob,
      middleName: contact.middle_name,
      residence: contact.addresses?.find((address) => address.is_primary)
        ?.country,
      suffix: contact.suffix,
      taxId: '',
      profileUrl: `${
        process.env.REDTAIL_FRONTEND_URL
      }contacts/${contact.id?.toString()}`,
      additionalInfo: {
        addresses: contact.addresses?.map((address) =>
          this.mapAddress(address),
        ),
        companyAssociation: '',
        employmentStatus: '',
        emails: contact.emails?.map((email) => this.mapEmail(camelize(email))),
        industryAffiliation: '',
        jobDescription: '',
        phones: contact.phones?.map((phone) => this.mapPhone(camelize(phone))),
      },
    };
  }

  public mapAddress(address: Camelize<RedtailAddress>): GenericAddress {
    if (!address) throw new Error('Address is undefined');
    return {
      id: address.id?.toString(),
      street: address.streetAddress,
      city: address.city,
      state: address.state,
      zip: address.zip,
      type: AddressTypeEnum[RedtailAddressTypeEnum[address.addressType]],
      country: address.country,
      title: address.customTypeTitle,
    };
  }

  public mapAccount(account: GenericCrmAccount): GenericCrmAccount {
    throw new Error('Method not implemented.');
  }

  public mapPhone(phone: Camelize<RedtailPhone>): GenericPhone {
    return {
      id: phone.id?.toString(),
      number: phone.number,
      type: PhoneTypeEnum[RedtailPhoneTypeEnum[phone.phoneType]],
      ext: phone.extension,
      isPrimary: phone.isPrimary,
    };
  }

  public mapEmail(email: Camelize<RedtailEmail>): GenericEmail {
    return {
      id: email.id?.toString(),
      address: email.address,
      isPrimary: email.isPrimary,
      type: EmailTypeEnum[RedtailEmailTypeEnum[email.emailType]],
    };
  }
}
