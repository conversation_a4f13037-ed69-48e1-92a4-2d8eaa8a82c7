import { Logger } from '@nestjs/common';
import { KMS } from 'aws-sdk';

const kms = new KMS({
  region: process.env.AWS_REGION || 'us-east-1',
});

export async function encrypt(text: string): Promise<string> {
  const params: KMS.EncryptRequest = {
    KeyId: process.env.AWS_KMS_KEY_ID,
    Plaintext: Buffer.from(text),
  };

  const result = await kms.encrypt(params).promise();
  return result.CiphertextBlob.toString('base64');
}

export async function decryptCredentials(
  encryptedCredentials: string,
): Promise<any> {
  const logger = new Logger('decryptCredentials');
  try {
    const params: KMS.DecryptRequest = {
      CiphertextBlob: Buffer.from(encryptedCredentials, 'base64'),
    };
    const result = await kms.decrypt(params).promise();
    const plaintext = result.Plaintext.toString();
    return JSON.parse(plaintext);
  } catch (err) {
    logger.error(`Failed to decrypt and parse credentials: ${err.message}`);
    throw new Error('Failed to decrypt and parse credentials');
  }
}
