
resource "aws_s3_bucket" "organisation_documents_bucket" {
  bucket = var.bucket_name
  lifecycle {
    prevent_destroy = true  # To prevent accidental destruction
  }
}
resource "aws_s3_bucket_server_side_encryption_configuration" "example" {
  bucket = aws_s3_bucket.organisation_documents_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm     = "AES256"
    }
  }
}

resource "aws_s3_bucket_ownership_controls" "example" {
  bucket = aws_s3_bucket.organisation_documents_bucket.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "organisation_documents_bucket_acl" {
  depends_on = [aws_s3_bucket_ownership_controls.example]

  bucket = aws_s3_bucket.organisation_documents_bucket.id
  acl    = "private"
}

resource "aws_s3_bucket_public_access_block" "example" {
    bucket = aws_s3_bucket.organisation_documents_bucket.id

    block_public_acls   = false
    block_public_policy = true
    ignore_public_acls  = false
    restrict_public_buckets = true
}