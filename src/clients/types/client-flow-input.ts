import { NotificationTypeEnum } from 'src/clients/types/notification-types.enum';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import { ContactDto } from 'src/clients/dto/v1/create-client.dto';

export interface CreateClientFlowInput {
  clientId: string;
  primaryContact: ContactDto;
  secondaryContact?: ContactDto;
  primaryAdvisor: AdvisorWithRole;
  primaryCSA: AdvisorWithRole;
  notificationMethods?: NotificationTypeEnum[];
  customTemplates?: any[];
  doClientProfiling?: boolean;
  organisationId: string;
  sendAdv2b?: boolean;
}
  