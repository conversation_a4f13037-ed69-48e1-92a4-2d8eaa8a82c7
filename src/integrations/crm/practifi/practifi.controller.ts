import { Controller, Get, Post, Query, Res, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Response } from 'express';

import { PractifiService } from './practifi.service';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { ClsService } from 'nestjs-cls';

@Controller('integrations/practifi')
export class PractifiController {
  constructor(
    private readonly practifiService: PractifiService,
    private readonly cls: ClsService,
  ) {}

  @Get('callback')
  async handleCallback(
    @Query('code') code: string,
    @Query('state') state: string,
    @Res() res: Response,
  ) {
    return this.practifiService.handleCallback(code, state, res);
  }

  @Post('link')
  @UseGuards(AuthGuard('jwt'))
  async getPractifiAuthorisationUri(): Promise<string> {
    const advisor = this.cls.get(ClsDataEnum.Advisor);
    return this.practifiService.getPractifiAuthorisationUrl(advisor._id);
  }
}
