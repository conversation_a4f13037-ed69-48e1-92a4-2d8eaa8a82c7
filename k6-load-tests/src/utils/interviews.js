import http from 'k6/http';
import { check, sleep } from 'k6';

import { ERROR_RATE, VUs } from "../config/constants/k6.js";
import { BASE_URL } from "../config/constants/shared.js";
import { getDynamicSleep } from "./k6-utilities.js";
import { interviewPagesToUpdate } from "../test-data/e2e-client-interview.js";

const _VUs = VUs['e2e-client-interview'];

const responseVerifier = (response) => check(response, {
  'got interview successfully': (r) => r.status === 200,
  'has interview data': (r) => {
    try {
      const body = JSON.parse(r.body);
      return body._id !== undefined &&
        body.template &&
        Array.isArray(body.template.pages) &&
        body.client;
    } catch (e) {
      console.error('Failed to parse interview response:', r.body);
      return false;
    }
  }
});

export function getInterviewByClientId(clientId, headers) {
  console.log('\n🔍 Fetching interview for client:', clientId);
  
  const getInterviewResponse = http.get(
    `${BASE_URL}/interviews/client/${clientId}?relatesTo=Primary`,
    { headers }
  );

  const verifyInterview = responseVerifier(getInterviewResponse)

  if (!verifyInterview) {
    console.log('❌ Failed to verify interview structure:', getInterviewResponse.body);
    ERROR_RATE.add(1);
    return;
  }

  console.log('✅ Successfully fetched interview');
  return JSON.parse(getInterviewResponse.body);
}

export function getInterviewById(interviewId, headers) {
  console.log('\n🔍 Fetching interview:', interviewId);
  
  const getInterviewResponse = http.get(
    `${BASE_URL}/interviews/${interviewId}`,
    { headers }
  );

  const verifyInterview = responseVerifier(getInterviewResponse)

  if (!verifyInterview) {
    console.log('❌ Failed to verify interview structure:', getInterviewResponse.body);
    ERROR_RATE.add(1);
    return;
  }

  console.log('✅ Successfully fetched interview');
  return JSON.parse(getInterviewResponse.body);
}

export function ensureAllPagesSynced(interviewId, headers) {
  const maxAttempts = Math.max(5, Math.ceil(10 / Math.log2(_VUs + 1)));
  const baseSleepTime = getDynamicSleep(7, _VUs);
  let attempts = 0;

  console.log('\n🔄 Starting sync check for all pages...');

  while (attempts < maxAttempts) {
    console.log(`\n📊 Sync Check Progress:`);
    console.log(`├─ Attempt: ${attempts + 1}/${maxAttempts}`);
    console.log(`└─ VU: ${_VUs}`);

    const currentInterview = getInterviewById(interviewId, headers);
    if (!currentInterview) {
      console.log('❌ Failed to retrieve interview state');
      return false;
    }

    const nonSyncedPages = currentInterview.template.pages
      .filter(page => page.status !== 'SYNCED')
      .map(page => ({ name: page.name, status: page.status }));

    if (nonSyncedPages.length === 0) {
      console.log('\n✨ All pages are synced successfully!');
      return true;
    }

    console.log('\n⏳ Pages still syncing:');
    nonSyncedPages.forEach((page, index, array) => {
      const prefix = index === array.length - 1 ? '└─' : '├─';
      console.log(`${prefix} ${page.name}: ${page.status}`);
    });

    const adjustedSleep = Math.ceil(baseSleepTime * (1 + (nonSyncedPages.length / 5)));
    console.log(`\n⌛ Waiting ${adjustedSleep}s before next check...`);
    sleep(adjustedSleep);
    attempts++;
  }

  console.log(`\n❌ Sync check failed after ${maxAttempts} attempts`);
  return false;
}

export function processUpdatePage(interviewId, pagesToProcess, headers) {
  const sleepTime = getDynamicSleep(2, _VUs);

  console.log('\n📝 Starting page updates...');

  for (const pageUpdate of pagesToProcess) {
    console.log(`\n🔄 Processing page: ${pageUpdate.page.name}`);
    console.log('📦 Update payload:', JSON.stringify(pageUpdate, null, 2));

    const updateResponse = http.patch(
      `${BASE_URL}/interviews/${interviewId}`,
      JSON.stringify(pageUpdate),
      {
        headers,
        tags: {type: 'interview_update'}
      }
    );

    console.log(`📊 Response status: ${updateResponse.status === 200 ? '✅' : '❌'} ${updateResponse.status}`);

    if (updateResponse.status !== 200) {
      console.log('❌ Update failed:', updateResponse.body);
      return false;
    }

    if (!check(updateResponse, {
      'interview page update successful': (r) => r.status === 200,
      'page update response valid': (r) => {
        try {
          return r.status === 200;
        } catch (e) {
          console.log('❌ Failed to parse response:', e.message);
          return false;
        }
      }
    })) {
      console.log(`❌ Failed to update page ${pageUpdate.page.name}`);
      return false;
    }

    console.log('⌛ Waiting before next update...');
    sleep(sleepTime);
  }

  console.log('\n✨ All page updates completed successfully!');
  return true;
}

export function updatePages(interview, clientId, headers) {
  console.log('\n🔄 Starting full interview update process...');
  
  // First attempt all pages
  console.log('\n📝 Updating all pages...');
  const updateSuccess = processUpdatePage(interview._id, interviewPagesToUpdate, headers);
  if (!updateSuccess) {
    console.log('❌ Failed to update pages');
    return false;
  }

  // Wait for sync with dynamic timing
  const syncCheckTime = getDynamicSleep(3, _VUs);
  const maxAttempts = Math.max(5, Math.ceil(10 / Math.log2(_VUs + 1)));
  let attempts = 0;

  console.log('\n🔄 Starting sync verification...');

  while (attempts < maxAttempts) {
    console.log(`\n📊 Sync Check Progress:`);
    console.log(`├─ Attempt: ${attempts + 1}/${maxAttempts}`);

    const currentInterview = getInterviewById(interview._id, headers);
    if (!currentInterview) {
      console.log('❌ Failed to fetch interview');
      return false;
    }

    const nonSyncedPages = currentInterview.template.pages
      .filter(page => page.status !== 'SYNCED')
      .map(page => page.name);

    if (nonSyncedPages.length === 0) {
      console.log('\n✨ All pages synced successfully!');
      return true;
    }

    console.log('\n⏳ Pages still syncing:');
    nonSyncedPages.forEach((page, index, array) => {
      const prefix = index === array.length - 1 ? '└─' : '├─';
      console.log(`${prefix} ${page}`);
    });

    if (attempts === maxAttempts - 1 && nonSyncedPages.length <= 3) {
      console.log('\n🔄 Final retry for remaining pages...');
      const retryPages = interviewPagesToUpdate.filter(page =>
        nonSyncedPages.includes(page.page.name)
      );
      processUpdatePage(interview._id, retryPages, headers);
    }

    console.log('\n⌛ Waiting before next check...');
    sleep(syncCheckTime);
    attempts++;
  }

  console.log('\n❌ Sync verification failed after all attempts');
  return false;
}

export function finishInterview(interviewId, headers) {
  console.log('\n🏁 Starting interview completion process...');

  if (!ensureAllPagesSynced(interviewId, headers)) {
    console.log('❌ Cannot finish interview - pages not synced');
    return false;
  }

  console.log('\n📤 Submitting interview completion...');
  const finishResponse = http.post(
    `${BASE_URL}/interviews/${interviewId}/finish`,
    null,
    {
      headers,
      tags: { type: 'interview_finish' }
    }
  );

  const finishSuccess = check(finishResponse, {
    'interview completion successful': (r) => {
      return [200, 201].includes(r.status)
    },
  });

  if (!finishSuccess) {
    console.log('❌ Failed to complete interview:', finishResponse.body);
    return false;
  }

  console.log('\n🎉 Interview completed successfully!');
  return true;
}

export function ensureBeneficiaryPagesSynced(interviewId, headers) {
  console.log('\n🔍 Checking beneficiary pages sync status...');

  const currentInterview = getInterviewById(interviewId, headers);
  if (!currentInterview) {
    console.log('❌ Failed to fetch interview while checking sync status');
    return false;
  }

  const primaryPage = currentInterview.template.pages.find(p => p.name === 'primary_beneficiaries');
  const contingentPage = currentInterview.template.pages.find(p => p.name === 'contingent_beneficiaries');

  console.log('\n📋 Beneficiary Pages Status:');
  console.log('├─ Primary:', primaryPage?.status ? `${primaryPage.status === 'SYNCED' ? '✅' : '⏳'} ${primaryPage.status}` : '❓ Not found');
  console.log('└─ Contingent:', contingentPage?.status ? `${contingentPage.status === 'SYNCED' ? '✅' : '⏳'} ${contingentPage.status}` : '❓ Not found');

  const synced = primaryPage?.status === 'SYNCED' && contingentPage?.status === 'SYNCED';
  if (!synced) {
    console.log('\n⚠️  Beneficiary pages are not synced');
    return false;
  }

  console.log('\n✨ All beneficiary pages are synced!');
  return true;
}