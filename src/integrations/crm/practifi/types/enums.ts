export enum USCitizen {
  US_CITIZEN = 'US Citizen',
  US = 1,
}

export enum PractifiEntityTypes {
  ACCOUNT = 'account',
  COMPANY_ADDRESS = 'companyAddress',
  CONTACT = 'contact',
  ASSET = 'asset',
  RELATIONSHIP = 'relationship',
  ROLE = 'role',
  NOTICE_POST = 'noticePost',
  TASK = 'task',
  RECORD_TYPE = 'recordType',
  NOTE = 'note',
  NOTE_LINK = 'noteLink',
  USER = 'user'
}

export enum BeneficiaryEnum {
  CBEN = 'Contingent',
  PBEN = 'Primary',
  ROLE = 'Beneficiary',
}

export enum DefaultTagsEnum {
  OnBord = 'OnBord',
}

export enum AccountStage {
  PROSPECT = 'Prospect',
  LOST_PROSPECT = 'Lost Prospect',
  CLIENT = 'Client',
  LOST_CLIENT = 'Lost Client',
}

export enum TaskStatus {
  NOT_STARTED = 'Not Started',
  COMPLETED = 'Completed',
}

export enum TaskPriority {
  HIGH = 'High',
  NORMAL = 'Normal',
}

export enum AccountTypes {
  HOUSEHOLD_PROSPECT = 'Household_Prospect',
  INDIVIDUAL = 'Individual',
}