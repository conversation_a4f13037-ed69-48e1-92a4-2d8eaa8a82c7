services:
  mongodb:
    image: mongo:5
    container_name: mongodb
    command: ["--replSet", "rs0", "--bind_ip_all"]
    ports:
      - 27017:27017
    volumes:
      - mongodb_data:/data/db
    healthcheck:
      test: mongosh --eval "try { rs.status().ok } catch (e) { rs.initiate({_id:'rs0',members:[{_id:0,host:'mongodb:27017'}]}).ok }"
      interval: 10s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - onbord-development

  mongo-express:
    image: mongo-express
    restart: always
    ports:
      - 8081:8081
    environment:
      ME_CONFIG_MONGODB_URL: mongodb://mongodb:27017/?replicaSet=rs0
    networks:
      - onbord-development
    depends_on:
      mongodb:
        condition: service_healthy

  redis:
    image: redis:6
    container_name: redis
    restart: always
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    ports:
      - 6379:6379
    volumes:
      - redis_data:/data/redis
    networks:
      - onbord-development
    entrypoint: redis-server --appendonly yes --requirepass $REDIS_PASSWORD

  onbord-backend:
    container_name: onbord-backend
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
      - ~/.aws:/root/.aws
    ports:
      - "3000:3000"
      - "9229:9229"
    networks:
      - onbord-development
    depends_on:
      mongodb:
        condition: service_healthy
      redis:
        condition: service_started
    environment:
      - NODE_ENV=development
      - DD_AGENT_HOST=datadog-agent
      - REDIS_HOST=redis
      - AWS_SDK_LOAD_CONFIG=1
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - DATABASE_URL=mongodb://mongodb:27017/?directConnection=true&replicaSet=rs0
    command: npm run start:debug

  portainer:
    image: portainer/portainer-ce:latest
    ports:
      - 9443:9443
    volumes:
      - data:/data
      - /var/run/docker.sock:/var/run/docker.sock
    restart: unless-stopped

volumes:
  data:
  mongodb_data:
  redis_data:

networks:
  onbord-development:
    name: onbord-development
