import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsEmail, IsString, IsEnum, IsUUID } from 'class-validator';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { UserStatusesEnum } from 'src/shared/types/rbac/statuses.enum';
export class InviteUserDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  lastName: string;

  @IsNotEmpty()
  @IsEnum(RolesEnum)
  @ApiProperty()
  role: RolesEnum;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  organisationId: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  phoneNumber: string;
}
