import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PractifiCRM } from '../crm.practifi';

interface JobDescriptionPageData {
  type: string;
  description: string;
}

export class PractifiJobDescriptionMapper implements FormUpdateMapper {
  constructor(private practifiCRM: PractifiCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: JobDescriptionPageData,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    await this.practifiCRM.contactService.updateContact(contact.crmClientId, {
      practifi__Occupation__c: pageData.description,
      ...(pageData.type && { Title: pageData.type }),
    });
  }
} 