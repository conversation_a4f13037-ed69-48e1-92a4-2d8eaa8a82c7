import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { NamePageUpdateDto } from 'src/shared/types/pages/dto/name-update.dto';

export class RedtailNameMapper implements FormUpdateMapper {
  constructor(private redtailCrm: RedtailCRM) {}

  /**
   * Maps the page data to the corresponding fields in Redtail CRM for a given client.
   * @param clientCrmId The ID of the client in Redtail CRM.
   * @param organisation The organisation associated with the client.
   * @param pageData The page data to be mapped.
   * @returns A Promise that resolves when the mapping is complete.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: NamePageUpdateDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    await this.redtailCrm.updateContactName(
      contact.crmClientId,
      pageData.firstName,
      pageData.lastName,
      pageData.middleName,
      pageData.suffix,
    );
  }
}
