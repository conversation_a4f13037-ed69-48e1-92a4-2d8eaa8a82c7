import { CustomQuestion } from 'src/organisations/schemas/organisation.schema';
import { OrganisationStatusEnum } from 'src/organisations/types/organisation-status.enum';
import { AssetViewDto } from 'src/shared/dto/asset.dto';
import { IntegrationDto } from 'src/shared/dto/integration.dto';
import { Address } from 'src/shared/schemas/address.schema';
import { CRMEnum } from 'src/shared/types/integrations';

import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { CustomQuestionDto } from 'src/organisations/dto/custom-questions/custom-question.dto';
import { AddressDto } from 'src/shared/dto/address.dto';
import { PaginationResponseDto } from 'src/shared/dto/pagination.dto';
import { NotificationViewDto } from 'src/organisations/dto/notification-template/notification-view.dto';

export class OrganisationViewDTO {
  @ApiProperty()
  @Expose()
  @Type(() => String)
  _id: string;

  @ApiProperty()
  @Expose()
  @Type(() => String)
  name: string;

  @ApiProperty({ required: false })
  @Expose()
  @Type(() => String)
  riaName?: string;

  @ApiProperty({ required: false })
  @Expose()
  @Type(() => String)
  email?: string;

  @ApiProperty({ required: false })
  @Expose()
  @Type(() => String)
  phone?: string;

  @ApiProperty({ required: false })
  @Expose()
  @Type(() => Boolean)
  docuSign?: boolean;

  @ApiProperty({ type: AddressDto, required: false })
  @Expose()
  @Type(() => AddressDto)
  address?: AddressDto;

  @ApiProperty({ type: [IntegrationDto], required: false })
  @Expose()
  @Type(() => IntegrationDto)
  externalAccounts?: IntegrationDto[];

  @ApiProperty({ type: [AssetViewDto], required: false })
  @Expose()
  @Type(() => AssetViewDto)
  assets?: AssetViewDto[];

  @ApiProperty({ required: false })
  @Expose()
  createdAt?: Date;

  @ApiProperty({ required: false })
  @Expose()
  updatedAt?: Date;

  @ApiProperty({ type: [String], required: false })
  @Expose()
  masterAccounts?: string[];

  @ApiProperty({ type: 'object', required: false })
  @Expose()
  configuration?: Record<string, any>;

  @ApiProperty({ type: [CustomQuestionDto], required: false })
  @Expose()
  @Type(() => CustomQuestionDto)
  customQuestions?: CustomQuestionDto[];

  @ApiProperty({ enum: OrganisationStatusEnum, required: false })
  @Expose()
  status?: OrganisationStatusEnum;

  @ApiProperty({ required: false })
  @Expose()
  crmInitialized: boolean;

  @ApiProperty({ enum: CRMEnum, required: false })
  @Expose()
  selectedCRM?: CRMEnum;

  @ApiProperty({ required: false })
  @Expose()
  transitions;

  @ApiProperty({ required: false })
  @Expose()
  @Type(() => NotificationViewDto)
  notificationTemplates: NotificationViewDto[];

  @ApiProperty({ required: false })
  @Expose()
  allowSendTransitionNotifications;

  @ApiProperty({ required: false })
  @Expose()
  @Type(() => Boolean)
  smsNotificationsEnabled: boolean;
}

export class OrganisationViewListItemDTO extends OrganisationViewDTO {
  @ApiProperty({ required: false })
  @Expose()
  totalAdvisors: number;

  @ApiProperty({ required: false })
  @Expose()
  totalCompletedClients: number;

  @ApiProperty({ required: false })
  @Expose()
  totalIncompleteClients: number;
}

export class OrganisationViewListDTO extends PaginationResponseDto<
  OrganisationViewListItemDTO[]
> {}
