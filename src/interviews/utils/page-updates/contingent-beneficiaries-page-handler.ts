import { isEmpty } from 'lodash';
import { InterviewPage } from 'src/interview-templates/schemas/v1/interview.template';
import { GenericPageHandler } from 'src/interviews/utils/page-updates/generic-page-handler';

/**
 * A page handler for the Contingent Beneficiaries page.
 * Extends the GenericPageHandler class.
 */
export class ContingentBeneficiariesPageHandler extends GenericPageHandler {
  /**
   * Determines whether the page should be updated.
   * @param page - The InterviewPage object to check.
   * @returns A boolean indicating whether the page should be updated.
   */
  protected shouldUpdate(page: InterviewPage): boolean {
    if (isEmpty(page.data?.instance)) {
      return false;
    }
    const updatedInstanceName = this.updateDto.page.data?.instance?.name;
    const updatedInstanceLabel = this.updateDto.page.data?.instance?.label;
    const instanceLabel = page.data?.label;
    const instanceName = page.data?.instance;
    return (
      instanceName === updatedInstanceName &&
      updatedInstanceLabel === instanceLabel &&
      (super.shouldUpdate(page) || this.context.fillContingentBeneficiaries)
    );
  }
}
