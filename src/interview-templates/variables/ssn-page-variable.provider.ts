import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

export class SsnPageVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      ssn: {
        description: 'Social Security Number supplied by the user',
        type: VariableType.STRING,
        example: '***********',
      },
      last4: {
        description: 'Last 4 digits of SSN',
        type: VariableType.STRING,
        example: '6789',
      },
    };
  }
}