import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { HttpException, HttpStatus } from '@nestjs/common';
import { InterviewV2CoreService } from './interview-core.service';
import { Interview } from '../../../schemas/v1/interview.schema';
import { ClientsV1Service } from 'src/clients/clients.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { CreateInterviewDto } from 'src/interviews/dto/v1/create-interview.dto';
import { InterviewTemplatesV2Service } from 'src/interview-templates/services/v2/interview-templates.service';
import { CreateInterviewV2Dto } from 'src/interviews/dto/v2/create-interview-v2.dto';

describe('InterviewV2CoreService', () => {
  let service: InterviewV2CoreService;
  let interviewModel: Model<Interview>;
  let interviewTemplateService: InterviewTemplatesV2Service;
  let clientsService: ClientsV1Service;
  let organisationsService: OrganisationsService;

  const mockSession: ClientSession = {} as ClientSession;

  const mockInterviewModel = {
    create: jest.fn(),
    find: jest.fn(),
    findOne: jest.fn(),
    findById: jest.fn(),
    findByIdAndDelete: jest.fn(),
  };

  const mockInterviewTemplateService = {
    getDefaultTemplate: jest.fn(),
    getMergedTemplate: jest.fn(),
  };

  const mockClientsV1Service = {
    findOne: jest.fn(),
  };

  const mockOrganisationsService = {
    findOne: jest.fn(),
  };

  const mockDefaultTemplate = {
    _id: 'default-template-id',
    name: 'Default Template',
    pages: [
      { name: PagesEnum.NAME, filled: false },
      { name: PagesEnum.ADDRESS, filled: false },
      { name: PagesEnum.PRIMARY_BENEFICIARIES, filled: false },
      { name: PagesEnum.CONTINGENT_BENEFICIARIES, filled: false },
    ],
    toObject: function() { return this; },
  };

  const mockClient = {
    _id: 'client-id',
    organisationId: 'org-id',
    primaryContact: {
      accounts: [
        { type: AccountTypeEnum.Ira, label: 'IRA Account' },
        { type: AccountTypeEnum.SingleNameBrokerage, label: 'Brokerage Account' },
      ],
    },
    secondaryContact: {
      accounts: [
        { type: AccountTypeEnum.RothIra, label: 'Roth IRA Account' },
      ],
    },
  };

  const mockOrganisation = {
    _id: 'org-id',
    assets: [
      { assetType: 'logo', assetLocation: 'https://example.com/logo.png' },
      { assetType: 'other', assetLocation: 'https://example.com/other.png' },
    ],
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InterviewV2CoreService,
        {
          provide: getModelToken(Interview.name),
          useValue: mockInterviewModel,
        },
        {
          provide: InterviewTemplatesV2Service,
          useValue: mockInterviewTemplateService,
        },
        {
          provide: ClientsV1Service,
          useValue: mockClientsV1Service,
        },
        {
          provide: OrganisationsService,
          useValue: mockOrganisationsService,
        },
      ],
    }).compile();

    service = module.get<InterviewV2CoreService>(InterviewV2CoreService);
    interviewModel = module.get<Model<Interview>>(getModelToken(Interview.name));
    interviewTemplateService = module.get<InterviewTemplatesV2Service>(InterviewTemplatesV2Service);
    clientsService = module.get<ClientsV1Service>(ClientsV1Service);
    organisationsService = module.get<OrganisationsService>(OrganisationsService);
    interviewTemplateService = module.get<InterviewTemplatesV2Service>(InterviewTemplatesV2Service);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const baseCreateDto: CreateInterviewV2Dto = {
      clientId: 'client-id',
      name: 'Test Interview',
      email: '<EMAIL>',
      accounts: [],
      docusignSelected: true,
      isPrimary: true,
    };

    it('should create an interview with default template', async () => {
      const expectedInterview = {
        _id: 'interview-id',
        ...baseCreateDto,
        template: mockDefaultTemplate,
        client: mockClient._id,
        documents: [],
      };

      mockInterviewTemplateService.getDefaultTemplate.mockResolvedValue(mockDefaultTemplate);
      mockClientsV1Service.findOne.mockResolvedValue(mockClient);
      mockInterviewModel.create.mockResolvedValue([expectedInterview]);

      const result = await service.create(baseCreateDto, mockSession);

      expect(mockInterviewTemplateService.getDefaultTemplate).toHaveBeenCalled();
      expect(mockClientsV1Service.findOne).toHaveBeenCalledWith({ _id: 'client-id' }, mockSession);
      expect(mockInterviewModel.create).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            ...baseCreateDto,
            template: expect.any(Object),
            client: mockClient._id,
            documents: [],
            docusignSelected: true,
            createdAt: expect.any(Date),
            updatedAt: expect.any(Date),
          }),
        ]),
        { session: mockSession }
      );
      expect(result).toEqual(expectedInterview);
    });

    it('should create an interview with custom templates', async () => {
      const createDtoWithCustomTemplates = {
        ...baseCreateDto,
        customTemplates: ['custom-template-1', 'custom-template-2'],
        doClientProfiling: true,
      };

      const mergedTemplate = {
        ...mockDefaultTemplate,
        name: 'Merged Template',
        pages: [...mockDefaultTemplate.pages, { name: 'CUSTOM_PAGE', filled: false }],
      };

      mockInterviewTemplateService.getDefaultTemplate.mockResolvedValue(mockDefaultTemplate);
      mockInterviewTemplateService.getMergedTemplate.mockResolvedValue(mergedTemplate);
      mockClientsV1Service.findOne.mockResolvedValue(mockClient);
      mockInterviewModel.create.mockResolvedValue([{ ...createDtoWithCustomTemplates, template: mergedTemplate }]);

      await service.create(createDtoWithCustomTemplates, mockSession);

      expect(mockInterviewTemplateService.getMergedTemplate).toHaveBeenCalledWith(
        ['custom-template-1', 'custom-template-2', 'default-template-id'],
        mockSession
      );
    });

    it('should handle already onboarded scenario', async () => {
      const createDtoAlreadyOnboarded = {
        ...baseCreateDto,
        isAlreadyOnbord: true,
      };

      const templateWithFilledPages = {
        ...mockDefaultTemplate,
        pages: mockDefaultTemplate.pages.map(p => ({ ...p, filled: true })),
      };

      mockInterviewTemplateService.getDefaultTemplate.mockResolvedValue({ ...mockDefaultTemplate });
      mockClientsV1Service.findOne.mockResolvedValue(mockClient);
      mockInterviewModel.create.mockResolvedValue([{ ...createDtoAlreadyOnboarded, template: templateWithFilledPages }]);

      await service.create(createDtoAlreadyOnboarded, mockSession);

      expect(mockInterviewModel.create).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            template: expect.objectContaining({
              pages: expect.arrayContaining([
                expect.objectContaining({ filled: true }),
              ]),
            }),
          }),
        ]),
        { session: mockSession }
      );
    });

    it('should include beneficiary pages for IRA accounts', async () => {
      mockInterviewTemplateService.getDefaultTemplate.mockResolvedValue(mockDefaultTemplate);
      mockClientsV1Service.findOne.mockResolvedValue(mockClient);
      mockInterviewModel.create.mockResolvedValue([{}]);

      await service.create(baseCreateDto, mockSession);

      const createCall = mockInterviewModel.create.mock.calls[0][0][0];
      const templatePages = createCall.template.pages;

      // Should have duplicate beneficiary pages for IRA account
      const primaryBeneficiaryPages = templatePages.filter(p => p.name === PagesEnum.PRIMARY_BENEFICIARIES);
      const contingentBeneficiaryPages = templatePages.filter(p => p.name === PagesEnum.CONTINGENT_BENEFICIARIES);

      expect(primaryBeneficiaryPages.length).toBe(1);
      expect(contingentBeneficiaryPages.length).toBe(1);
      expect(primaryBeneficiaryPages[0].data).toEqual({
        instance: AccountTypeEnum.Ira,
        label: 'IRA Account',
      });
    });

    it('should exclude beneficiary pages for non-IRA accounts', async () => {
      const clientWithoutIRA = {
        ...mockClient,
        primaryContact: {
          accounts: [
            { type: AccountTypeEnum.SingleNameBrokerage, label: 'Brokerage Account' },
            { type: AccountTypeEnum.JointNameBrokerage, label: 'Joint Account' },
          ],
        },
      };

      mockInterviewTemplateService.getDefaultTemplate.mockResolvedValue(mockDefaultTemplate);
      mockClientsV1Service.findOne.mockResolvedValue(clientWithoutIRA);
      mockInterviewModel.create.mockResolvedValue([{}]);

      await service.create(baseCreateDto, mockSession);

      const createCall = mockInterviewModel.create.mock.calls[0][0][0];
      const templatePages = createCall.template.pages;

      // Should not have beneficiary pages
      const primaryBeneficiaryPages = templatePages.filter(p => p.name === PagesEnum.PRIMARY_BENEFICIARIES);
      const contingentBeneficiaryPages = templatePages.filter(p => p.name === PagesEnum.CONTINGENT_BENEFICIARIES);

      expect(primaryBeneficiaryPages.length).toBe(0);
      expect(contingentBeneficiaryPages.length).toBe(0);
    });

    it('should handle secondary contact accounts when isPrimary is false', async () => {
      const createDtoSecondary = {
        ...baseCreateDto,
        isPrimary: false,
      };

      mockInterviewTemplateService.getDefaultTemplate.mockResolvedValue(mockDefaultTemplate);
      mockClientsV1Service.findOne.mockResolvedValue(mockClient);
      mockInterviewModel.create.mockResolvedValue([{}]);

      await service.create(createDtoSecondary, mockSession);

      const createCall = mockInterviewModel.create.mock.calls[0][0][0];
      const templatePages = createCall.template.pages;

      // Should have beneficiary pages for Roth IRA from secondary contact
      const primaryBeneficiaryPages = templatePages.filter(p => p.name === PagesEnum.PRIMARY_BENEFICIARIES);
      expect(primaryBeneficiaryPages.length).toBe(1);
      expect(primaryBeneficiaryPages[0].data).toEqual({
        instance: AccountTypeEnum.RothIra,
        label: 'Roth IRA Account',
      });
    });

    it('should handle client with no accounts', async () => {
      const clientWithNoAccounts = {
        ...mockClient,
        primaryContact: { accounts: [] },
        secondaryContact: { accounts: [] },
      };

      mockInterviewTemplateService.getDefaultTemplate.mockResolvedValue(mockDefaultTemplate);
      mockClientsV1Service.findOne.mockResolvedValue(clientWithNoAccounts);
      mockInterviewModel.create.mockResolvedValue([{}]);

      await service.create(baseCreateDto, mockSession);

      const createCall = mockInterviewModel.create.mock.calls[0][0][0];
      const templatePages = createCall.template.pages;

      // Should not have beneficiary pages
      const beneficiaryPages = templatePages.filter(
        p => p.name === PagesEnum.PRIMARY_BENEFICIARIES || p.name === PagesEnum.CONTINGENT_BENEFICIARIES
      );
      expect(beneficiaryPages.length).toBe(0);
    });

    it('should handle multiple IRA accounts', async () => {
      const clientWithMultipleIRAs = {
        ...mockClient,
        primaryContact: {
          accounts: [
            { type: AccountTypeEnum.Ira, label: 'IRA Account 1' },
            { type: AccountTypeEnum.RothIra, label: 'Roth IRA Account' },
            { type: AccountTypeEnum.Ira, label: 'IRA Account 2' },
          ],
        },
      };

      mockInterviewTemplateService.getDefaultTemplate.mockResolvedValue(mockDefaultTemplate);
      mockClientsV1Service.findOne.mockResolvedValue(clientWithMultipleIRAs);
      mockInterviewModel.create.mockResolvedValue([{}]);

      await service.create(baseCreateDto, mockSession);

      const createCall = mockInterviewModel.create.mock.calls[0][0][0];
      const templatePages = createCall.template.pages;

      // Should have beneficiary pages for each IRA account
      const primaryBeneficiaryPages = templatePages.filter(p => p.name === PagesEnum.PRIMARY_BENEFICIARIES);
      expect(primaryBeneficiaryPages.length).toBe(3);
    });
  });

  describe('find', () => {
    it('should find interviews with given filter', async () => {
      const filter = { client: 'client-id' };
      const expectedInterviews = [{ _id: 'interview-1' }, { _id: 'interview-2' }];

      mockInterviewModel.find.mockResolvedValue(expectedInterviews);

      const result = await service.find(filter);

      expect(mockInterviewModel.find).toHaveBeenCalledWith(filter);
      expect(result).toEqual(expectedInterviews);
    });
  });

  describe('findAll', () => {
    it('should find all interviews', async () => {
      const expectedInterviews = [{ _id: 'interview-1' }, { _id: 'interview-2' }];

      mockInterviewModel.find.mockResolvedValue(expectedInterviews);

      const result = await service.findAll();

      expect(mockInterviewModel.find).toHaveBeenCalledWith();
      expect(result).toEqual(expectedInterviews);
    });
  });

  describe('findOne', () => {
    it('should find one interview and enrich with client data', async () => {
      const filter = { _id: 'interview-id' };
      const mockInterview = {
        _id: 'interview-id',
        client: 'client-id',
        toObject: jest.fn().mockReturnValue({ _id: 'interview-id', client: 'client-id' }),
      };

      mockInterviewModel.findOne.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          session: jest.fn().mockResolvedValue(mockInterview),
        }),
      });
      mockClientsV1Service.findOne.mockResolvedValue(mockClient);

      const result = await service.findOne(filter, mockSession);

      expect(mockInterviewModel.findOne).toHaveBeenCalledWith(filter);
      expect(mockClientsV1Service.findOne).toHaveBeenCalledWith({ _id: 'client-id' }, mockSession, false, false);
      expect(result).toEqual({
        _id: 'interview-id',
        client: mockClient,
      });
    });

    it('should return null if interview not found', async () => {
      mockInterviewModel.findOne.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          session: jest.fn().mockResolvedValue(null),
        }),
      });

      const result = await service.findOne({ _id: 'non-existent' });

      expect(result).toBeNull();
    });

    it('should skip enrichment when skipEnrich is true', async () => {
      const mockInterview = {
        _id: 'interview-id',
        client: 'client-id',
        toObject: jest.fn().mockReturnValue({ _id: 'interview-id', client: 'client-id' }),
      };

      mockInterviewModel.findOne.mockReturnValue({
        sort: jest.fn().mockReturnValue({
          session: jest.fn().mockResolvedValue(mockInterview),
        }),
      });
      mockClientsV1Service.findOne.mockResolvedValue(mockClient);

      await service.findOne({ _id: 'interview-id' }, mockSession, true);

      expect(mockClientsV1Service.findOne).toHaveBeenCalledWith({ _id: 'client-id' }, mockSession, false, true);
    });
  });

  describe('findById', () => {
    it('should find interview by id and enrich with client and organization logo', async () => {
      const mockInterview = {
        _id: 'interview-id',
        client: 'client-id',
        toObject: jest.fn().mockReturnValue({ _id: 'interview-id', client: 'client-id' }),
      };

      mockInterviewModel.findById.mockResolvedValue(mockInterview);
      mockClientsV1Service.findOne.mockResolvedValue(mockClient);
      mockOrganisationsService.findOne.mockResolvedValue(mockOrganisation);

      const result = await service.findById('interview-id', mockSession) as any;

      expect(mockInterviewModel.findById).toHaveBeenCalledWith('interview-id');
      expect(mockClientsV1Service.findOne).toHaveBeenCalledWith({ _id: 'client-id' }, mockSession, false, false);
      expect(mockOrganisationsService.findOne).toHaveBeenCalledWith('org-id');
      expect(result).toEqual({
        _id: 'interview-id',
        client: mockClient,
        organisationLogo: 'https://example.com/logo.png',
      });
    });

    it('should throw HttpException if interview not found', async () => {
      mockInterviewModel.findById.mockResolvedValue(null);

      await expect(service.findById('non-existent')).rejects.toThrow(
        new HttpException('Interview not found', HttpStatus.NOT_FOUND)
      );
    });

    it('should handle empty interview result', async () => {
      mockInterviewModel.findById.mockResolvedValue({});

      await expect(service.findById('interview-id')).rejects.toThrow(
        new HttpException('Interview not found', HttpStatus.NOT_FOUND)
      );
    });

    it('should handle organization without logo', async () => {
      const mockInterview = {
        _id: 'interview-id',
        client: 'client-id',
        toObject: jest.fn().mockReturnValue({ _id: 'interview-id', client: 'client-id' }),
      };

      const orgWithoutLogo = {
        _id: 'org-id',
        assets: [
          { assetType: 'other', assetLocation: 'https://example.com/other.png' },
        ],
      };

      mockInterviewModel.findById.mockResolvedValue(mockInterview);
      mockClientsV1Service.findOne.mockResolvedValue(mockClient);
      mockOrganisationsService.findOne.mockResolvedValue(orgWithoutLogo);

      const result = await service.findById('interview-id') as any;

      expect(result.organisationLogo).toBeUndefined();
    });

    it('should handle organization with no assets', async () => {
      const mockInterview = {
        _id: 'interview-id',
        client: 'client-id',
        toObject: jest.fn().mockReturnValue({ _id: 'interview-id', client: 'client-id' }),
      };

      const orgWithNoAssets = {
        _id: 'org-id',
        assets: undefined,
      };

      mockInterviewModel.findById.mockResolvedValue(mockInterview);
      mockClientsV1Service.findOne.mockResolvedValue(mockClient);
      mockOrganisationsService.findOne.mockResolvedValue(orgWithNoAssets);

      const result = await service.findById('interview-id') as any;

      expect(result.organisationLogo).toBeUndefined();
    });
  });

  describe('remove', () => {
    it('should remove interview by id', async () => {
      const deletedInterview = { _id: 'interview-id', client: 'client-id' };

      mockInterviewModel.findByIdAndDelete.mockResolvedValue(deletedInterview);

      const result = await service.remove('interview-id', mockSession);

      expect(mockInterviewModel.findByIdAndDelete).toHaveBeenCalledWith('interview-id', { session: mockSession });
      expect(result).toEqual(deletedInterview);
    });

    it('should handle remove without session', async () => {
      const deletedInterview = { _id: 'interview-id', client: 'client-id' };

      mockInterviewModel.findByIdAndDelete.mockResolvedValue(deletedInterview);

      const result = await service.remove('interview-id');

      expect(mockInterviewModel.findByIdAndDelete).toHaveBeenCalledWith('interview-id', { session: undefined });
      expect(result).toEqual(deletedInterview);
    });
  });

  describe('setUpAdditionalPagesInInterviewTemplate (private method)', () => {
    // Testing through create method since it's private
    const baseCreateDto: CreateInterviewV2Dto = {
      clientId: 'client-id',
      name: 'Test Interview',
      email: '<EMAIL>',
      accounts: [],
      docusignSelected: true,
      isPrimary: true,
    };

    it('should handle edge case with undefined accounts on contact', async () => {
      const clientWithUndefinedAccounts = {
        ...mockClient,
        primaryContact: undefined,
        secondaryContact: undefined,
      };

      mockInterviewTemplateService.getDefaultTemplate.mockResolvedValue(mockDefaultTemplate);
      mockClientsV1Service.findOne.mockResolvedValue(clientWithUndefinedAccounts);
      mockInterviewModel.create.mockResolvedValue([{}]);

      await service.create(baseCreateDto, mockSession);

      const createCall = mockInterviewModel.create.mock.calls[0][0][0];
      const templatePages = createCall.template.pages;

      // Should not have beneficiary pages
      const beneficiaryPages = templatePages.filter(
        p => p.name === PagesEnum.PRIMARY_BENEFICIARIES || p.name === PagesEnum.CONTINGENT_BENEFICIARIES
      );
      expect(beneficiaryPages.length).toBe(0);
    });

    it('should preserve non-beneficiary pages', async () => {
      const templateWithExtraPages = {
        ...mockDefaultTemplate,
        pages: [
          { name: PagesEnum.NAME, filled: false },
          { name: PagesEnum.ADDRESS, filled: false },
          { name: 'CUSTOM_PAGE', filled: false },
          { name: PagesEnum.PRIMARY_BENEFICIARIES, filled: false },
          { name: PagesEnum.CONTINGENT_BENEFICIARIES, filled: false },
        ],
      };

      const clientWithoutIRA = {
        ...mockClient,
        primaryContact: {
          accounts: [{ type: AccountTypeEnum.SingleNameBrokerage, label: 'Brokerage' }],
        },
      };

      mockInterviewTemplateService.getDefaultTemplate.mockResolvedValue(templateWithExtraPages);
      mockClientsV1Service.findOne.mockResolvedValue(clientWithoutIRA);
      mockInterviewModel.create.mockResolvedValue([{}]);

      await service.create(baseCreateDto, mockSession);

      const createCall = mockInterviewModel.create.mock.calls[0][0][0];
      const templatePages = createCall.template.pages;

      // Should preserve non-beneficiary pages
      expect(templatePages.some(p => p.name === PagesEnum.NAME)).toBe(true);
      expect(templatePages.some(p => p.name === PagesEnum.ADDRESS)).toBe(true);
      expect(templatePages.some(p => p.name === 'CUSTOM_PAGE')).toBe(true);

      // Should not have beneficiary pages
      expect(templatePages.some(p => p.name === PagesEnum.PRIMARY_BENEFICIARIES)).toBe(false);
      expect(templatePages.some(p => p.name === PagesEnum.CONTINGENT_BENEFICIARIES)).toBe(false);
    });
  });

  describe('error scenarios', () => {
    const baseCreateDto: CreateInterviewV2Dto = {
      clientId: 'client-id',
      name: 'Test Interview',
      email: '<EMAIL>',
      accounts: [],
      docusignSelected: true,
      isPrimary: true,
    };

    it('should propagate error from interviewTemplateService.getDefaultTemplate', async () => {
      const error = new Error('Template service error');
      mockInterviewTemplateService.getDefaultTemplate.mockRejectedValue(error);

      await expect(service.create(baseCreateDto)).rejects.toThrow(error);
    });

    it('should propagate error from clientsService.findOne', async () => {
      const error = new Error('Client not found');
      mockInterviewTemplateService.getDefaultTemplate.mockResolvedValue(mockDefaultTemplate);
      mockClientsV1Service.findOne.mockRejectedValue(error);

      await expect(service.create(baseCreateDto)).rejects.toThrow(error);
    });

    it('should propagate error from interviewModel.create', async () => {
      const error = new Error('Database error');
      mockInterviewTemplateService.getDefaultTemplate.mockResolvedValue(mockDefaultTemplate);
      mockClientsV1Service.findOne.mockResolvedValue(mockClient);
      mockInterviewModel.create.mockRejectedValue(error);

      await expect(service.create(baseCreateDto)).rejects.toThrow(error);
    });

    it('should propagate error from getMergedTemplate', async () => {
      const createDtoWithCustomTemplates = {
        ...baseCreateDto,
        customTemplates: ['custom-template-1'],
      };

      const error = new Error('Merge template error');
      mockInterviewTemplateService.getDefaultTemplate.mockResolvedValue(mockDefaultTemplate);
      mockInterviewTemplateService.getMergedTemplate.mockRejectedValue(error);
      mockClientsV1Service.findOne.mockResolvedValue(mockClient);

      await expect(service.create(createDtoWithCustomTemplates)).rejects.toThrow(error);
    });
  });
});