import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { USCitizenRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/us-citizen-request.dto';
import { RedtailCitizenshipEnum } from 'src/integrations/crm/redtail/types/enums';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { NonCitizenEmailContext } from 'src/templates/mail/non-citizen/non-citizen.context';
import { EmailTemplateNameEnum } from 'src/templates/mail/types';

export class RedtailUSCitizenMapper implements FormUpdateMapper {
  constructor(private redtailCrm: RedtailCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: USCitizenRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    if (pageData.citizen && pageData.resident) {
      await this.redtailCrm.updateContactPersonalProfile(contact.crmClientId, {
        citizenship: RedtailCitizenshipEnum.US,
      });
      return;
    }

    const context: NonCitizenEmailContext = {
      clientFirstName: contact.firstName,
      clientLastName: contact.lastName,
      subject: `Non-Citizen Status`,
    };

    return this.redtailCrm.sendEmail({
      to: client.primaryAdvisor.email,
      templateName: EmailTemplateNameEnum.NonCitizen,
      subject: `Non-Citizen Status`,
      context,
      organisation, // Pass the organisation parameter
    });
  }
}
