import { Test } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { advisorMockObj } from 'src/advisors/tests/mocks/advisor.mock';
import { AdvisorsDocusignService } from 'src/advisors/services/advisors.docusign.service';
import { Advisor } from 'src/advisors/schemas/advisors.schema';

import {
  CRMEnum,
  DocumentSigningEnum,
  IntegrationEnum,
} from 'src/shared/types/integrations';
import { Integration } from 'src/shared/schemas/integration.schema';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

jest.mock('aws-sdk', () => {
  const mKMS = {
    encrypt: jest.fn().mockReturnValue({
      promise: jest.fn().mockReturnValue({ CiphertextBlob: 'encrypted' }),
    }),
    decrypt: jest.fn().mockReturnValue({
      promise: jest.fn().mockReturnValue({ Plaintext: 'decrypted' }),
    }),
    promise: jest.fn(),
  };
  return { KMS: jest.fn(() => mKMS) };
});

describe('AdvisorsDocusignService', () => {
  const mockExec = jest.fn();

  let model: jest.Mocked<Model<Advisor>>;

  let service: AdvisorsDocusignService;
  let docusignService: DocusignService;
  let logger: jest.Mocked<Logger>;

  const mockAccountInfo = {
    accountId: 'accountId',
  };

  const mockSchwabTemplates = {
    envelopeTemplates: [
      {
        templateId: 'templateId',
      },
    ],
  };

  beforeEach(async () => {
    const moduleRef = await Test.createTestingModule({
      providers: [
        AdvisorsDocusignService,
        {
          provide: getModelToken(Advisor.name),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn().mockImplementation(() => {
              return {
                skip: jest.fn().mockReturnThis(),
                limit: jest.fn().mockReturnThis(),
                exec: mockExec,
              };
            }),
            create: jest.fn().mockResolvedValue(advisorMockObj),
            updateOne: jest.fn(),
            deleteOne: jest.fn(),
          },
        },
        {
          provide: DocusignService,
          useValue: {
            getAccountInfo: jest.fn().mockResolvedValue(mockAccountInfo),
            getSchwabEnvelopeTemplate: jest.fn().mockResolvedValue(mockSchwabTemplates),
          },
        },
        {
          provide: WINSTON_MODULE_PROVIDER,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
            info: jest.fn(),
          },
        },
      ],
    }).compile();

    model = moduleRef.get<jest.Mocked<Model<Advisor>>>(
      getModelToken(Advisor.name),
    );

    service = moduleRef.get<AdvisorsDocusignService>(AdvisorsDocusignService);
    logger = moduleRef.get<jest.Mocked<Logger>>(WINSTON_MODULE_PROVIDER);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('linkDocusign', () => {
    const accessToken = 'accessToken';
    const refreshToken = 'refreshToken';
    const expiresIn = '28000';
    const lastRefresh = new Date();

    const dto = {
      accessToken,
      refreshToken,
      expiresIn,
      lastRefresh,
    };

    const adv: any = {
      ...advisorMockObj,
      integrations: [
        {
          integrationType: IntegrationEnum.DocumentSigning,
          integrationConfig: {
            name: DocumentSigningEnum.Docusign,
            credentials: 'encrypted',
          },
        },
      ],
    };

    it('should throw an error if no accessToken is provided', async () => {
      await expect(
        service.linkDocusign('123', { ...dto, accessToken: null }),
      ).rejects.toThrow(Error('Access token or refresh token not found.'));
    });

    it('should throw an error if no refresh is provided', async () => {
      await expect(
        service.linkDocusign('123', { ...dto, refreshToken: null }),
      ).rejects.toThrow(Error('Access token or refresh token not found.'));
    });

    it('should throw an error if advisor not found', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce({});

      await expect(service.linkDocusign('123', dto)).rejects.toThrow(
        Error('Cannot add integration, entity not found.'),
      );
    });

    it('should throw an error if docusign is already linked', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce({
        ...advisorMockObj,
        integrations: [
          {
            integrationType: IntegrationEnum.DocumentSigning,
            integrationConfig: {},
          },
        ],
      } as any);

      await expect(service.linkDocusign('123', dto)).rejects.toThrow(
        Error('Integration already exists.'),
      );
    });

    it('should link an advisor to docusign', async () => {
      jest
        .spyOn(model, 'findOne')
        .mockResolvedValueOnce({ ...advisorMockObj, integrations: [] } as any)
        .mockResolvedValue(adv as any);
      jest.spyOn(model, 'updateOne').mockResolvedValueOnce(adv);

      await expect(service.linkDocusign('123', dto)).resolves.toStrictEqual({
        message: 'Docusign linked successfully.',
      });
    });
  });

  describe('unlinkDocusign', () => {
    const integrations = [
      {
        integrationType: IntegrationEnum.DocumentSigning,
        integrationConfig: {
          name: DocumentSigningEnum.Docusign,
          credentials: 'encrypted',
        },
      },
    ];

    it('should throw an error if advisor not found', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce({});

      await expect(service.unlinkDocusign('123')).rejects.toThrow(
        Error('Advisor not found.'),
      );
    });

    it('should successfully unlink an advisor from docusign', async () => {
      jest.spyOn(model, 'findOne').mockResolvedValueOnce({
        ...advisorMockObj,
        integrations,
      } as any);

      jest.spyOn(model, 'updateOne').mockResolvedValueOnce({
        ...advisorMockObj,
        integrations: [],
      } as any);

      await expect(service.unlinkDocusign('123')).resolves.toStrictEqual({
        message: `Docusign unlinked successfully.`,
      });
    });
  });

  describe('findDocusignRefreshCandidates', () => {
    const page = 1;

    const mockAdvisors = (integration: Integration) =>
      [...Array(3).keys()].map(() => ({
        ...advisorMockObj,
        integrations: [integration],
      }));

    beforeEach(() => {
      jest.clearAllMocks();
    });

    it('should return a batch of advisors with docusign integration', async () => {
      const lastRefresh = new Date('2023-06-30T11:37:54.176Z');

      const advisors = mockAdvisors({
        integrationType: IntegrationEnum.DocumentSigning,
        integrationConfig: {
          name: DocumentSigningEnum.Docusign,
          credentials: 'encrypted',
          lastRefresh,
        },
      });

      mockExec.mockResolvedValueOnce(advisors);

      await expect(
        service.findDocusignRefreshCandidates(page),
      ).resolves.toStrictEqual(advisors);
    });

    it('should return an empty array if no advisors found', async () => {
      mockExec.mockResolvedValueOnce([]);

      await expect(
        service.findDocusignRefreshCandidates(page),
      ).resolves.toStrictEqual([]);
    });

    it('should return an empty array if no docusign integrations found', async () => {
      const advisors = mockAdvisors({
        integrationType: IntegrationEnum.Crm,
        integrationConfig: {
          name: CRMEnum.Redtail,
          credentials: 'encrypted',
        },
      });

      mockExec.mockResolvedValueOnce(advisors);

      await expect(
        service.findDocusignRefreshCandidates(page),
      ).resolves.toStrictEqual([]);
    });

    it('should return an empty array if no docusign refresh candidates found', async () => {
      jest.spyOn(global, 'Date').mockRestore();
      const lastRefresh = new Date('2123-09-30T11:37:54.176Z');

      const advisors = mockAdvisors({
        integrationType: IntegrationEnum.DocumentSigning,
        integrationConfig: {
          name: DocumentSigningEnum.Docusign,
          credentials: 'encrypted',
          lastRefresh,
        },
      });

      mockExec.mockResolvedValueOnce(advisors);

      await expect(
        service.findDocusignRefreshCandidates(page),
      ).resolves.toStrictEqual([]);
    });
  });
});
