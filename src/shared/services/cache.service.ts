import { Inject, Injectable } from '@nestjs/common';
import fs from 'fs';
import {
  CACHE_DEFAULT_MAX_SIZE,
  CACHE_DEFAULT_TTL,
  CacheRecord,
} from 'src/utils/cache/types';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { cloneDeep } from 'lodash';
import { unlinkAsync } from 'src/utils/file/file.util';
import { access, unlink } from 'fs/promises';

@Injectable()
export class CacheService {
  private cache: Map<string, CacheRecord>;
  private maxSize = CACHE_DEFAULT_MAX_SIZE;
  private ttl: number = CACHE_DEFAULT_TTL;
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {
    this.cache = new Map<string, CacheRecord>();
  }

  has(key: string): boolean {
    return this.cache.has(key) && !this.isExpired(key);
  }

  set(key: string, file: Express.Multer.File): void {
    const expiresAt = Date.now() + this.ttl;

    this.cache.set(key, { expiresAt, file: cloneDeep(file) });
  }

  async get(key: string): Promise<Express.Multer.File> {
    try {
      await this.evict();
    } catch (err) {
      this.logger.error(
        `CacheService.get : Error evicting expired files: ${err}`,
      );
    }

    const record = this.cache.get(key);

    if (!record) return null;

    // check if file exists in path, if not delete from cache
    if (!record?.file?.path) {
      this.cache.delete(key);
      return null;
    }

    try {
      await access(record.file.path, fs.constants.F_OK);
    } catch (err) {
      this.logger.info(
        `CacheService.get : File ${record.file.path} does not exist, deleting from cache`,
      );
      this.cache.delete(key);
      return null;
    }

    return cloneDeep(record.file);
  }

  isExpired(key: string) {
    const record = this.cache.get(key);
    if (record?.expiresAt <= Date.now()) {
      return true;
    }
    return false;
  }

  async delete(key: string): Promise<void> {
    if (this.cache.has(key)) {
      const record = this.cache.get(key);
      try {
        await unlink(record.file.path);
      } catch (err) {
        // Consistent error logging format
        this.logger.error(
          `CacheService.delete: Error deleting file ${record.file.path}: ${err.message}`,
          {
            key,
            method: 'delete',
            error: err,
          },
        );
      } finally {
        this.cache.delete(key);
      }
    }
  }

  async evict(): Promise<void> {
    // Evict all the expired entries
    const deletionPromises = [];
    for (const [key, record] of this.cache.entries()) {
      if (record.expiresAt < Date.now()) {
        // Collect all promises for deletion
        deletionPromises.push(
          this.delete(key).catch((err) => {
            this.logger.error(
              `CacheService.evict : Error evicting key ${key}: ${err}`,
            );
          }),
        );
      }
    }

    // Wait for all deletions to complete, ignoring individual errors as they're logged already
    await Promise.all(deletionPromises);

    // Shift the cache if it's full
    while (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      try {
        await this.delete(firstKey);
      } catch (err) {
        this.logger.error(
          `CacheService.evict : Error deleting firstKey ${firstKey} on max size condition: ${err}`,
        );
      }
    }
  }
}
