import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SmsService } from './sms.service';
import { TwilioModule } from 'nestjs-twilio';
import { AdvisorsModule } from 'src/advisors/advisors.module';
import { ClsModule } from 'nestjs-cls';
import { OrganisationsModule } from 'src/organisations/organisations.module';

@Module({
  imports: [
    TwilioModule.forRoot({
      accountSid: process.env.TWILIO_ACCOUNT_SID,
      authToken: process.env.TWILIO_AUTH_TOKEN,
    }),
    ConfigModule.forRoot({ envFilePath: '.env' }),
    forwardRef(() => AdvisorsModule),
    forwardRef(() => OrganisationsModule),
    ClsModule,
  ],
  providers: [SmsService],
  exports: [SmsService],
})
export class SmsModule {}
