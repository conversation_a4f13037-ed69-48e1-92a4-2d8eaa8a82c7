import { IsOptional, IsArray, IsEnum, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { PartialType } from '@nestjs/mapped-types';
import { CreateAdvisorDto } from './create-advisor.dto';
import { CreateOrganisationDto } from 'src/organisations/dto/organisation.dto';
import { AdvisorPersonalInfoDto } from 'src/advisors/dto/advisor-personal-info.dto';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { UserStatusesEnum } from 'src/shared/types/rbac/statuses.enum';
import { IntegrationDto } from 'src/shared/dto/integration.dto';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateAdvisorDto extends PartialType(CreateAdvisorDto) {
  @IsOptional()
  @ValidateNested()
  @Type(() => CreateOrganisationDto)
  @ApiProperty()
  organisation?: CreateOrganisationDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => AdvisorPersonalInfoDto)
  @ApiProperty()
  personalInfo?: AdvisorPersonalInfoDto;

  @IsOptional()
  @IsEnum(RolesEnum)
  @ApiProperty()
  role?: RolesEnum;

  @IsOptional()
  @IsEnum(UserStatusesEnum)
  @ApiProperty()
  status?: UserStatusesEnum;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IntegrationDto)
  @ApiProperty()
  integrations?: IntegrationDto[];
}
export enum RequestTypeEnum {
  isResetEmailRequest = 'isResetEmailRequest',
  isStatusChangeRequest = 'isStatusChangeRequest',
  isNameOrRoleChangeRequest = 'isNameOrRoleChangeRequest',
  isPhoneChangeRequest = 'isPhoneChangeRequest',
}
