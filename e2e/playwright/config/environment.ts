export const TEST_CONFIG = {
    baseUrl: process.env.TEST_BASE_URL || 'http://localhost:8080',
    defaultTimeout: 30000,
    testUser: {
        email: process.env.TEST_USER_EMAIL || '<EMAIL>',
        password: process.env.TEST_USER_PASSWORD || 'Hello123!'
    },
    clientDefaults: {
        single: {
            firstName: 'Test',
            lastName: 'User',
            phone: {
                npa: '123',
                nxx: '234',
                line: '3433'
            }
        },
        joint: {
            clientOne: {
                firstName: 'Primary',
                lastName: 'Client',
                phone: {
                    npa: '123',
                    nxx: '234',
                    line: '3433'
                }
            },
            clientTwo: {
                firstName: 'Secondary',
                lastName: 'Client',
                phone: {
                    npa: '321',
                    nxx: '432',
                    line: '4334'
                }
            }
        },
        generateEmail: (prefix: string) => `${prefix}.${Date.now()}@nexwave.uk`
    }
}; 