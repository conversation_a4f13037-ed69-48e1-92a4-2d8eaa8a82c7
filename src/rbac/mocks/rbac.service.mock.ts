import { AddPermissionDto } from '../dto/add-permission.dto';
import { CreateRoleDto } from '../dto/create-role.dto';
import { DeletePermissionDto } from '../dto/delete-permission.dto';

export const mockRbacService = {
  findAllRoles: jest.fn().mockResolvedValue([]),
  findOneRole: jest.fn().mockResolvedValue({ name: 'CompanyAdmin' }),
  createRole: jest
    .fn()
    .mockImplementation((role: CreateRoleDto) => Promise.resolve({})),
  deleteRole: jest.fn().mockResolvedValue({}),
  addPermission: jest
    .fn()
    .mockImplementation((roleId: string, addPermissionDto: AddPermissionDto) =>
      Promise.resolve({}),
    ),
  deletePermission: jest
    .fn()
    .mockImplementation(
      (roleId: string, deletePermissionDto: DeletePermissionDto) =>
        Promise.resolve({}),
    ),
};
