import { Module, Global, forwardRef } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { AdvisorsModule } from 'src/advisors/advisors.module';
import { OrganisationsModule } from 'src/organisations/organisations.module';
import { ScheduleModule } from '@nestjs/schedule';
import { DocusignService } from './docusign.service';
import { MutexModule } from 'src/mutex/mutex.module';
import { DocusignController } from './docusign.controller';
import { InterviewsModule } from 'src/interviews/interviews.module';
import { ClientsModule } from 'src/clients/clients.module';
import { SmsModule } from 'src/notifications/sms/sms.module';

@Global()
@Module({
  imports: [
    HttpModule,
    ScheduleModule.forRoot(),
    MutexModule,
    SmsModule,
    forwardRef(() => AdvisorsModule),
    forwardRef(() => OrganisationsModule),
    forwardRef(() => InterviewsModule),
    forwardRef(() => ClientsModule),
  ],
  providers: [DocusignService],
  controllers: [DocusignController],
  exports: [DocusignService],
})
export class DocusignModule {}
