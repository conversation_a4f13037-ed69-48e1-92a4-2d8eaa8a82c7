import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { EmailTemplateNameEnum } from 'src/templates/mail/types';

@Schema({ timestamps: true })
export class EmailTemplate extends Document {
  @Prop({ required: true })
  emailTemplateName: string;

  @Prop({ 
    required: true, 
    type: String, 
    enum: EmailTemplateNameEnum 
  })
  emailTemplateType: EmailTemplateNameEnum;

  @Prop({ required: true })
  emailTemplateContent: string;

  @Prop({ 
    type: MongooseSchema.Types.ObjectId, 
    required: true, 
    index: true 
  })
  organisationId: MongooseSchema.Types.ObjectId;

  @Prop({ 
    type: MongooseSchema.Types.ObjectId, 
    required: false 
  })
  advisorId?: MongooseSchema.Types.ObjectId;

  @Prop({ 
    type: Boolean, 
    default: true 
  })
  isActive: boolean;
}

export const EmailTemplateSchema = SchemaFactory.createForClass(EmailTemplate); 