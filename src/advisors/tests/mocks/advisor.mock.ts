import { RolesEnum } from 'src/shared/types/rbac/roles.enum';

export const advisorMock = {
  _id: '64aebec4fa8ac207dd73cff1',
  cognitoUserId: '123456789',
  organisation: {
    organisationId: '64f9d18098dc182eb74aa4ce',
    name: 'Onbord',
    phone: '+351915956736',
    address: {
      street: '29522 Aglae Mountain',
      city: 'East Providence',
      state: 'South Carolina',
      zip: '07392-4762',
    },
    email: '<EMAIL>',
    setupComplete: true,
  },
  personalInfo: {
    firstName: '<PERSON>',
    lastName: 'Doe',
    address: {
      street: '456 Park Ave',
      city: 'New York',
      state: 'NY',
      zip: '10001',
      country: 'USA',
    },
    phone: '************',
    email: '<EMAIL>',
  },
  integrations: [
    {
      integrationType: 'CRM',
      integrationConfig: {
        name: '<PERSON><PERSON>',
        credentials: {
          userKey: 'userKey',
          username: 'username',
          password: 'password',
        },
      },
    },
  ],
  crmId: 1,
  role: RolesEnum.CompanyAdmin,
  createdAt: '2023-07-10T14:43:25.060Z',
  updatedAt: '2023-07-10T14:43:25.060Z',
  toObject: jest.fn(),
};

export const advisorMockObj = {
  ...advisorMock,
  toObject: jest.fn().mockReturnValue(advisorMock),
};

export const expectedDetails = {
  organisationId: advisorMock.organisation.organisationId,
  role: advisorMock.role,
  firstName: advisorMock.personalInfo.firstName,
  lastName: advisorMock.personalInfo.lastName,
  phone: advisorMock.personalInfo.phone,
  newEmail: '<EMAIL>',
  oldEmail: advisorMock.personalInfo.email,
};
