import { parsePhoneNumber } from 'libphonenumber-js';
import { isEmpty } from 'lodash';
import {
  getCompanyNameFromCompanyAssociation,
  getTickerSymbolFromCompanyAssociation,
} from 'src/integrations/crm/redtail/utils/company-association-parser';
import { AddressTypeEnum } from 'src/integrations/crm/types/generic/address.type';
import { EmailTypeEnum } from 'src/integrations/crm/types/generic/email.type';
import { PhoneTypeEnum } from 'src/integrations/crm/types/generic/phone.type';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';
import { getContactByFileName } from 'src/utils/pdf-mappers/utils/getContactByFileName';

export const contactGeneralMappers = {
  // General Mappers
  'contact.fullName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return contact ? `${contact?.firstName} ${contact?.lastName}` : '';
    },
  },
  'contact.firstName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return contact?.firstName || '';
    },
  },
  'contact.lastName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return contact?.lastName || '';
    },
  },
  'contact.middleName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return contact?.middleName || '';
    },
  },
  'contact.suffix': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return contact?.suffix || '';
    },
  },
  'contact.taxId': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return contact?.taxId || '';
    },
  },
  'contact.dob': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return contact?.dob || '';
    },
  },
  'contact.citizenship': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return contact?.citizenship || '';
    },
  },
  'contact.residence': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return contact?.residence || '';
    },
  },
  'contact.employmentStatus': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      if (!contact) return '';

      return contact?.additionalInfo?.employmentStatus || '';
    },
  },
  'contact.employer': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);

      const workAddress = contact.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.WORK,
      );
      return workAddress?.title || '';
    },
  },
  'contact.industryAffiliation.companyName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      const industryAffiliation = contact.additionalInfo?.industryAffiliation;

      if (isEmpty(industryAffiliation)) return '';

      return industryAffiliation;
    },
  },
  'contact.companyAssociation.name': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      const companyAssociationValue = contact.additionalInfo.companyAssociation;

      if (isEmpty(companyAssociationValue)) return '';

      return !isEmpty(companyAssociationValue)
        ? getCompanyNameFromCompanyAssociation(companyAssociationValue)
        : '';
    },
  },
  'contact.companyAssociation.tickerSymbol': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      const companyAssociationValue = contact.additionalInfo.companyAssociation;

      return !isEmpty(companyAssociationValue)
        ? getTickerSymbolFromCompanyAssociation(companyAssociationValue)
        : '';
    },
  },
  // Phone Mappers
  'contact.phones.work.number': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      if (!contact.additionalInfo.phones) return '';

      const phone = contact.additionalInfo.phones.find(
        (phone) => phone.type === PhoneTypeEnum.WORK,
      )?.number;
      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'contact.phones.home.number': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      if (isEmpty(contact.additionalInfo.phones)) return '';

      const phone = contact.additionalInfo.phones.find(
        (phone) => phone.type === PhoneTypeEnum.HOME,
      )?.number;
      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'contact.phones.mobile.number': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      if (!contact.additionalInfo.phones) return '';

      const phone = contact.additionalInfo.phones.find(
        (phone) => phone.type === PhoneTypeEnum.MOBILE,
      )?.number;
      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'contact.emails.work.address': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      if (isEmpty(contact.additionalInfo.emails)) return '';

      return (
        contact.additionalInfo.emails.find(
          (email) => email.type === EmailTypeEnum.WORK,
        )?.address || ''
      );
    },
  },
  'contact.emails.home.address': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      if (isEmpty(contact.additionalInfo.emails)) return '';

      return (
        contact.additionalInfo.emails.find(
          (email) => email.type === EmailTypeEnum.HOME,
        )?.address || ''
      );
    },
  },

  // Address Mappers
  'contact.addresses.home.street': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      if (!contact.additionalInfo?.addresses) return '';

      const addresses = contact.additionalInfo?.addresses;
      return (
        addresses.find((address) => address.type === AddressTypeEnum.HOME)
          ?.street || ''
      );
    },
  },
  'contact.addresses.home.city': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      if (!contact.additionalInfo?.addresses) return '';

      return (
        contact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        )?.city || ''
      );
    },
  },
  'contact.addresses.home.state': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      if (!contact.additionalInfo?.addresses) return '';

      return (
        contact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        )?.state || ''
      );
    },
  },
  'contact.addresses.home.zip': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      if (!contact.additionalInfo?.addresses) return '';

      return (
        contact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        )?.zip || ''
      );
    },
  },
  'contact.addresses.home.country': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      if (!contact.additionalInfo?.addresses) return '';

      return (
        contact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        )?.country || 'USA'
      );
    },
  },
  'contact.addresses.work.street': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      if (!contact.additionalInfo?.addresses) return '';

      const addresses = contact.additionalInfo?.addresses;
      return (
        addresses.find((address) => address.type === AddressTypeEnum.WORK)
          ?.street || ''
      );
    },
  },
  'contact.addresses.work.city': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return (
        contact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.WORK,
        )?.city || ''
      );
    },
  },
  'contact.addresses.work.state': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return (
        contact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.WORK,
        )?.state || ''
      );
    },
  },
  'contact.addresses.work.zip': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return (
        contact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.WORK,
        )?.zip || ''
      );
    },
  },
  'contact.addresses.work.country': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return (
        contact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.WORK,
        )?.country || 'USA'
      );
    },
  },
  'contact.addresses.mailing.street': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      if (!contact.additionalInfo?.addresses) return '';

      const addresses = contact.additionalInfo?.addresses;
      return (
        addresses.find((address) => address.type === AddressTypeEnum.MAILING)
          ?.street || ''
      );
    },
  },
  'contact.addresses.mailing.city': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return (
        contact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.MAILING,
        )?.city || ''
      );
    },
  },
  'contact.addresses.mailing.state': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return (
        contact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.MAILING,
        )?.state || ''
      );
    },
  },
  'contact.addresses.mailing.zip': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return (
        contact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.MAILING,
        )?.zip || ''
      );
    },
  },
  'contact.addresses.mailing.country': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      return (
        contact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.MAILING,
        )?.country || 'USA'
      );
    },
  },
  'contact.account#.advisoryFee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.accounts[accountIndex]?.advisoryRate || '',
  },
  // Account Mappers
  'contact.account#.custodian': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      const account = contact.accounts[accountIndex];
      if (account) return 'Charles Schwab';
    },
  },
  'contact.account#.type': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      const account = contact.accounts[accountIndex];
      if (!account) return '';
      return account.type;
    },
  },
  'contact.account#.label': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      const account = contact.accounts[accountIndex];
      if (!account) return '';
      return account.label;
    },
  },
  'contact.account#.ownership': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      const account = contact.accounts[accountIndex];
      if (!account) return '';
      return account.ownership;
    },
  },
  'contact.account#.masterAccountNumber': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      const account = contact.accounts[accountIndex];
      if (!account) return '';
      return account.masterAccountNumber || '';
    },
  },
  'contact.account#.beneficiaries#.percentage': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      const account = contact.accounts[accountIndex];
      if (!account) return '';
      return account.beneficiaries[beneficiaryIndex]?.percentage || '';
    },
  },
  'contact.account#.beneficiaries#.type': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      const account = contact.accounts[accountIndex];
      if (!account) return '';
      return account.beneficiaries[beneficiaryIndex]?.type || '';
    },
  },
  'contact.account#.beneficiaries#.dob': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      const account = contact.accounts[accountIndex];
      if (!account || !account.beneficiaries[beneficiaryIndex]) return '';
      return account.beneficiaries[beneficiaryIndex]?.dob;
    },
  },
  'contact.account#.beneficiaries#.firstName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      const account = contact.accounts[accountIndex];
      if (!account || !account.beneficiaries[beneficiaryIndex]) return '';

      return account.beneficiaries[beneficiaryIndex]?.firstName;
    },
  },
  'contact.account#.beneficiaries#.lastName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      const account = contact.accounts[accountIndex];
      if (!account || !account.beneficiaries[beneficiaryIndex]) return '';
      return account.beneficiaries[beneficiaryIndex]?.lastName;
    },
  },
  'contact.account#.beneficiaries#.fullName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const contact = getContactByFileName(interviewData, fileName);
      const account = contact.accounts[accountIndex];
      if (!account || !account.beneficiaries[beneficiaryIndex]) return '';
      return account.beneficiaries[beneficiaryIndex]?.fullName;
    },
  },
};
