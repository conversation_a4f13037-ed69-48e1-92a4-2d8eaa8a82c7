export const interviewPagesToUpdate = [
  {
    page: {
      name: 'us_citizen',
      data: {
        citizen: true,
        resident: true
      }
    }
  },
  {
    page: {
      name: 'name',
      data: {
        firstName: '<PERSON>',
        lastName: 'Padilha',
        middleName: 'Middle',
        suffix: 'Sr.'
      }
    }
  },
  {
    page: {
      name: 'address',
      data: {
        legalAddress: {
          city: 'Orlando',
          line1: 'Test Rd',
          line2: null,
          state: 'FL',
          zip: '32819'
        }
      }
    }
  },
  {
    page: {
      name: 'ssn',
      data: {
        ssn: '*********'
      }
    }
  },
  {
    page: {
      name: 'dob',
      data: {
        dob: '1994-01-01'
      }
    }
  },
  {
    page: {
      name: 'phone',
      data: {}
    }
  },
  {
    page: {
      name: 'employment',
      data: {
        status: 'Employed',
      }
    }
  },
  {
    page: {
      name: 'company',
      data: {
        name: 'Testarossa Winery',
        address: {
          line1: '300 College Ave',
          line2: null,
          city: 'Los Gatos',
          state: 'CA',
          zip: '95030-7066'
        }
      }
    }
  },
  {
    page: {
      name: 'job',
      data: {
        description: 'Professional',
        type: 'Executive'
      }
    }
  },
  {
    page: {
      name: 'vip',
      data: {}
    }
  },
  {
    page: {
      name: 'conflict_of_interest',
      data: {}
    }
  },
];