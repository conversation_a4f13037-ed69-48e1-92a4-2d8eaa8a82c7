import { HouseholdTitle } from 'src/integrations/crm/wealthbox/dto/household/household.dto';
import { WealthboxContactType } from 'src/integrations/crm/wealthbox/types';
import { WealthboxContact } from 'src/integrations/crm/wealthbox/types/contact.type';
import { WealthboxOrderEnum } from 'src/integrations/crm/wealthbox/types/order.type';
import { WealthboxPaginationMeta } from 'src/integrations/crm/wealthbox/types/pagination.type';

export interface GetContactsRequestDto {
  id?: number;
  contactType?: string;
  name?: string;
  email?: string;
  phone?: string;
  active?: boolean;
  tags?: string[];
  householdTitle?: HouseholdTitle;
  type?: WealthboxContactType;
  order?: WealthboxOrderEnum;
}

export interface GetContactsResponseDto {
  contacts: WealthboxContact[];
  meta: WealthboxPaginationMeta;
}
