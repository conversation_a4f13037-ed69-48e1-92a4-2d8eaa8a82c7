import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsEmail } from 'class-validator';

export class MfaDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  mfaCode: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  session: string;

  @IsNotEmpty()
  @ApiProperty()
  @IsEmail()
  email: string;
}

export class MfaRequiredResponseDto {
  @ApiProperty()
  mfaRequired: boolean;

  @ApiProperty()
  session: string;
}
