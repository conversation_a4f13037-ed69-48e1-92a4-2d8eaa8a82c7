export const contactInterviewStatusAggregateQuery = [
  {
    $lookup: {
      from: 'interviews',
      localField: '_id',
      foreignField: 'client',
      as: 'interviews',
    },
  },
  {
    $addFields: {
      'primaryContact.interview': {
        $arrayElemAt: [
          {
            $filter: {
              input: '$interviews',
              as: 'interview',
              cond: { $eq: ['$$interview.isPrimary', true] },
            },
          },
          0,
        ],
      },
      'secondaryContact.interview': {
        $cond: {
          if: { $ne: ['$secondaryContact', null] },
          then: {
            $arrayElemAt: [
              {
                $filter: {
                  input: '$interviews',
                  as: 'interview',
                  cond: { $eq: ['$$interview.isPrimary', false] },
                },
              },
              0,
            ],
          },
          else: '$$REMOVE',
        },
      },
    },
  },
  {
    $addFields: {
      'primaryContact.interviewComplete': {
        $cond: {
          if: { $eq: ['$primaryContact.interview.isComplete', true] },
          then: true,
          else: false,
        },
      },
      'secondaryContact.interviewComplete': {
        $cond: {
          if: { $eq: ['$secondaryContact.interview.isComplete', true] },
          then: true,
          else: '$$REMOVE',
        },
      },
      'primaryContact.completionPercentage': {
        $cond: {
          if: { $eq: ['$primaryContact.interview.isComplete', true] },
          then: 100,
          else: {
            $cond: {
              if: { $isArray: '$primaryContact.interview.template.pages' },
              then: {
                $ceil: {
                  $multiply: [
                    {
                      $divide: [
                        {
                          $size: {
                            $filter: {
                              input: '$primaryContact.interview.template.pages',
                              as: 'page',
                              cond: '$$page.filled',
                            },
                          },
                        },
                        { $size: '$primaryContact.interview.template.pages' },
                      ],
                    },
                    100,
                  ],
                },
              },
              else: 0,
            },
          },
        },
      },
      'secondaryContact.completionPercentage': {
        $cond: {
          if: { $eq: ['$secondaryContact.interview.isComplete', true] },
          then: 100,
          else: {
            $cond: {
              if: {
                $and: [
                  { $ne: ['$secondaryContact', null] },
                  { $isArray: '$secondaryContact.interview.template.pages' },
                ],
              },
              then: {
                $ceil: {
                  $multiply: [
                    {
                      $divide: [
                        {
                          $size: {
                            $filter: {
                              input:
                                '$secondaryContact.interview.template.pages',
                              as: 'page',
                              cond: '$$page.filled',
                            },
                          },
                        },
                        { $size: '$secondaryContact.interview.template.pages' },
                      ],
                    },
                    100,
                  ],
                },
              },
              else: '$$REMOVE',
            },
          },
        },
      },
    },
  },
  {
    $project: {
      interviews: 0,
      'primaryContact.interview': 0,
      'secondaryContact.interview': 0,
    },
  },
];
