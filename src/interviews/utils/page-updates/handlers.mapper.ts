import { InterviewPageStatusEnum } from 'src/interview-templates/types/interview-page-status.enum';
import { UpdateInterviewDto } from 'src/interviews/dto/v1/update-interview.dto';
import { <PERSON><PERSON>and<PERSON> } from 'src/interviews/types/page-handler.interface';
import { PageUpdateContext } from 'src/interviews/types/page-update-context.type';
import { ContingentBeneficiariesPageHandler } from 'src/interviews/utils/page-updates/contingent-beneficiaries-page-handler';
import { CustomQuestionsPageHandler } from 'src/interviews/utils/page-updates/custom-question-page-handler';
import { GenericPageHandler } from 'src/interviews/utils/page-updates/generic-page-handler';
import { PrimaryBeneficiariesPageHandler } from 'src/interviews/utils/page-updates/primary-beneficiaries-page-handler';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';


/**
 * Returns the appropriate PageHandler based on the provided page name.
 * If no matching <PERSON><PERSON>and<PERSON> is found, returns a GenericPageHandler.
 * @param pageName - The name of the page to get the handler for.
 * @param context - The PageUpdateContext object.
 * @param updateDto - The UpdateInterviewDto object.
 * @returns The appropriate PageHandler for the given page name.
 */
export function getPageHandler(
  pageName: PagesEnum,
  context: PageUpdateContext,
  updateDto: UpdateInterviewDto,
  pageStatus: InterviewPageStatusEnum,
): PageHandler {
  return (
    {
      [PagesEnum.PRIMARY_BENEFICIARIES]: new PrimaryBeneficiariesPageHandler(
        context,
        updateDto,
        pageStatus,
      ),
      [PagesEnum.CONTINGENT_BENEFICIARIES]:
        new ContingentBeneficiariesPageHandler(context, updateDto, pageStatus),
      [PagesEnum.CUSTOM_QUESTIONS]: new CustomQuestionsPageHandler(
        context,
        updateDto,
        pageStatus,
      ),
    }[pageName] || new GenericPageHandler(context, updateDto, pageStatus)
  );
}
