import { HttpService } from '@nestjs/axios';
import { TagGroup, RedtailTagMember } from '../types/tag.type';
import { Logger } from '@nestjs/common';
import { GetTagsResponseDto } from 'src/integrations/crm/redtail/dto/responses/tags/get-tags-response.dto';
import {
  DefaultTagDescriptionsEnum,
  DefaultTagsEnum,
  RedtailCRMEndpointsEnum,
} from 'src/integrations/crm/redtail/types/enums';
import { makeRequest } from 'src/utils/makeRequest';
import { HttpMethodsEnum } from 'src/shared/types/general/http-methods.enum';
import { camelize, snakeify } from 'src/utils';
import { CreateTagGroupRequestDto } from 'src/integrations/crm/redtail/dto/requests/tags/create-tag-group-request.dto';
import { CreateTagGroupResponseDto } from 'src/integrations/crm/redtail/dto/responses/tags/create-tag-group-response.dto';
import { CreateTagMemberResponseDto } from 'src/integrations/crm/redtail/dto/responses/tags/create-tab-member-response.dto';
import { Camelize } from 'src/shared/types/monads';
import { GetTagResponseDto } from 'src/integrations/crm/redtail/dto/responses/tags/get-tag-group-response.dto';
import { isEmpty } from 'lodash';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { OperationType } from 'src/super-http/enums';

export class RedtailTagService {
  private readonly baseUrl: string;
  private logger: Logger;

  constructor(private httpService: SuperHttpService, private authHeader: object, private tenantId: string) {
    this.baseUrl = process.env.REDTAIL_BASE_URL;
    this.logger = new Logger('RedtailCRM');
  }

  /**
   * Initializes the default tags for the OnBord tag group.
   * @returns {Promise<void>}
   */
  async initTags() {
    await this.findOrCreateTagGroup(
      DefaultTagsEnum.OnBord,
      DefaultTagDescriptionsEnum.OnBord,
    );
  }

  /**
   * Retrieves a list of tags from Redtail CRM.
   * @param pagination - The pagination query parameters.
   * @returns A promise that resolves to an array of tag groups.
   */
  async getTags(): Promise<Camelize<TagGroup[]>> {
    const url = `${this.baseUrl}${RedtailCRMEndpointsEnum.Tags}?members=true`;

    const { tag_groups: tags } = await this.httpService.get<GetTagsResponseDto>(
      url,
      this.tenantId,
      { 
        rateLimiter: 'redtail', 
        integrationName: 'redtail', 
        operation: OperationType.LIST, 
        entityType: 'tag',
        cacheKeysObject: { withMembers: true }
      },
      { headers: { ...this.authHeader } }
    );

    return camelize(tags);
  }

  async getTag(tagId: number): Promise<Camelize<TagGroup>> {
    const url = `${this.baseUrl}${RedtailCRMEndpointsEnum.Tags}/${tagId}?members=true`;

    const { tag_group: tag } = await this.httpService.get<GetTagResponseDto>(
      url,
      this.tenantId,
      { 
        rateLimiter: 'redtail', 
        integrationName: 'redtail', 
        operation: OperationType.GET, 
        entityType: 'tag',
        entityId: tagId.toString(),
        cacheKeysObject: { tagId, withMembers: true }
      },
      { headers: { ...this.authHeader } }
    );

    return camelize(tag);
  }

  /**
   * Retrieves a tag group by its name.
   * @param tagName - The name of the tag group to retrieve.
   * @returns A Promise that resolves with the tag group, or undefined if not found.
   */
  async getTagByTagName(
    tagName: string,
  ): Promise<Camelize<TagGroup> | undefined> {
    const tags = await this.getTags();
    return camelize(
      tags.find((tag) => tag.name.toLowerCase() === tagName.toLowerCase()),
    );
  }

  /**
   * Upserts a tag group with the given tag name and description.
   * If a tag group with the given name already exists, returns its ID.
   * Otherwise, creates a new tag group and returns its ID.
   * @param tagName - The name of the tag group to upsert.
   * @param description - The description of the tag group to upsert.
   * @returns The ID of the upserted tag group, or null if the upsert failed.
   */
  public async findOrCreateTagGroup(
    tagName: string,
    description: string,
  ): Promise<Camelize<TagGroup> | undefined> {
    const url = `${this.baseUrl}${RedtailCRMEndpointsEnum.Tags}`;

    const tags = await this.getTags();
    const tag = camelize(
      tags.find((tag) => tag.name.toLowerCase() === tagName.toLowerCase()),
    );

    if (!isEmpty(tag)) {
      return tag;
    }

    const tagRequestBody: CreateTagGroupRequestDto = {
      name: tagName,
      description,
    };
    let tagGroup: Camelize<TagGroup>;
    try {
      const response = await this.httpService.post<CreateTagGroupResponseDto>(
        url,
        this.tenantId,
        snakeify(tagRequestBody),
        { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.CREATE, entityType: 'tag' },
        { headers: { ...this.authHeader } }
      );
      ({ tagGroup } = camelize(response));
    } catch (error) {
      this.logger.warn(
        `Tag group not created. Error: ${
          error.response?.data?.message || error.message
        }`,
      );
    }

    if (!tagGroup?.id) {
      this.logger.warn(`Failed to create '${tagName}' tag group`);
      return null;
    }

    return tagGroup;
  }

  /**
   * Adds a tag to a contact in Redtail CRM.
   * @param tagId - The ID of the tag to add.
   * @param contactId - The ID of the contact to add the tag to.
   * @returns A promise that resolves with the newly created tag member.
   */
  public async tagContact(
    tagId: number,
    contactId: string,
  ): Promise<Camelize<RedtailTagMember>> {
    const tagMembers = (await this.getTag(tagId)).members;
    const tagMember = tagMembers.find(
      (member) => member.contactId?.toString() === contactId,
    );

    if (tagMember) {
      return tagMember;
    }

    const url = `${this.baseUrl}${RedtailCRMEndpointsEnum.Tags}/${tagId}/members`;
    const tagRequestBody = { contactId };

    const response = await this.httpService.post<CreateTagMemberResponseDto>(
      url,
      this.tenantId,
      snakeify(tagRequestBody),
      { 
        rateLimiter: 'redtail', 
        integrationName: 'redtail', 
        operation: OperationType.CREATE, 
        entityType: 'tag',
        entityId: `${tagId}_${contactId}`,
        wideCacheInvalidation: true
      },
      { headers: { ...this.authHeader } }
    );

    return camelize(response.tag_member);
  }

  /**
   * Deletes a tag from a contact in Redtail CRM.
   * @param tagId - The ID of the tag to delete.
   * @param contactId - The ID of the contact to remove the tag from.
   * @returns A Promise that resolves when the tag has been deleted.
   */
  public async deleteTagContact(
    tagId: number,
    contactId: string,
  ): Promise<void> {
    const url = `${this.baseUrl}${RedtailCRMEndpointsEnum.Tags}/${tagId}/members/${contactId}`;

    await this.httpService.delete(
      url,
      this.tenantId,
      { 
        rateLimiter: 'redtail', 
        integrationName: 'redtail', 
        operation: OperationType.DELETE, 
        entityType: 'tag',
        entityId: `${tagId}_${contactId}`,
        wideCacheInvalidation: true
      },
      { headers: { ...this.authHeader } }
    );
  }

  /**
   * Builds metadata for an advisor tag group.
   * @param advisorFirstName - The first name of the advisor.
   * @param advisorLastName - The last name of the advisor.
   * @returns An object containing the tag name and description.
   */
  public buildAdvisorTagGroupMetadata(
    advisorFirstName: string,
    advisorLastName: string,
  ) {
    return {
      tagName: `OnBord Advisor ${advisorFirstName} ${advisorLastName}`,
      tagDescription: `OnBord tag group for Contacts with a Primary Advisor of ${advisorFirstName} ${advisorLastName}`,
    };
  }
}
