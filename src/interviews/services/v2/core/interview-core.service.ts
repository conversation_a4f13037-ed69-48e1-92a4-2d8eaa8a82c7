// src/interviews/services/v2/interview-v2-core.service.ts
import {
  Injectable,
  Inject,
  forwardRef,
  HttpException,
  HttpStatus,
  Logger,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession, FilterQuery, Types } from 'mongoose';
import { InterviewPageInstanceV2 } from 'src/interviews/schemas/v2/interview-page-instance.schema';
import { InterviewAccountInstanceV2 } from 'src/interviews/schemas/v2/interview-account-instance.schema';

import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { CreateInterviewV2Dto } from 'src/interviews/dto/v2/create-interview-v2.dto';
import { InterviewResponseV2Dto } from 'src/interviews/dto/v2/interview-response-v2.dto';
import { InterviewV2 } from 'src/interviews/schemas/v2/interview.schema';
  import { ClientsV2Service } from 'src/clients/services/v2/clients-v2.service';
import { InterviewTemplatesV2Service } from 'src/interview-templates/services/v2/interview-templates.service';
import { InterviewTemplateV2 } from 'src/interview-templates/schemas/v2/interview.template';
import { InterviewComposerService } from 'src/interview-templates/services/v2/interview-composer.service';
import { ComposeInterviewDto } from 'src/interviews/dto/v2/compose-interview.dto';

@Injectable()
export class InterviewCoreService {
  private readonly logger = new Logger(InterviewCoreService.name);

  constructor(
    @InjectModel(InterviewV2.name) 
    private readonly interviewModel: Model<InterviewV2>,
    
    @InjectModel(InterviewPageInstanceV2.name) 
    private readonly pageInstanceModel: Model<InterviewPageInstanceV2>,
    
    @InjectModel(InterviewAccountInstanceV2.name) 
    private readonly accountInstanceModel: Model<InterviewAccountInstanceV2>,
    
    private readonly templateService: InterviewTemplatesV2Service,
    
    private readonly composerService: InterviewComposerService,
    
    @Inject(forwardRef(() => ClientsV2Service)) 
    private readonly clientsService: ClientsV2Service,
  ) {}

  /**
   * Create a new V2 interview with proper account instance setup
   */
  async create(
    dto: CreateInterviewV2Dto,
    session?: ClientSession,
  ): Promise<InterviewResponseV2Dto> {
    // Validate client exists and is V2
    const client = await this.validateClient(dto.clientId, session);
    
    // Get contact based on contact type
    const contact = dto.contactType === 'primary' 
      ? client.primaryContact 
      : client.secondaryContact;
      
    if (!contact) {
      throw new BadRequestException(`${dto.contactType} contact not found for client`);
    }
    
    // Compose interview from templates if accounts exist
    const { composedPages, compositionInfo, baseTemplate } = await this.composeInterviewFromTemplates(
      client,
      contact,
      dto,
      session
    );
    
    // Create interview with composition info
    const interview = await this.createInterview(
      dto, 
      client,
      baseTemplate._id, 
      compositionInfo,
      session
    );
    
    // Initialize account instances
    await this.initializeAccountInstances(interview, client, dto.contactType, session);
    
    // Create page instances from composed pages
    await this.createPageInstancesFromComposition(interview._id, composedPages, session);
    
    // Return formatted response
    return this.formatInterviewResponse(interview, baseTemplate, session);
  }

  /**
   * Find interviews with filters
   */
  async find(filter: FilterQuery<InterviewV2>): Promise<InterviewV2[]> {
    return this.interviewModel
      .find(filter)
      .populate('template')
      .sort({ createdAt: -1 })
      .exec();
  }

  /**
   * Find single interview with enriched data
   */
  async findOne(
    filter: FilterQuery<InterviewV2>,
    session?: ClientSession,
  ): Promise<InterviewV2 | null> {
    return this.interviewModel
      .findOne(filter)
      .populate('template')
      .session(session)
      .exec();
  }

  /**
   * Find interview by ID with validation
   */
  async findById(
    id: string,
    session?: ClientSession,
  ): Promise<InterviewV2> {
    const interview = await this.interviewModel
      .findById(id)
      .populate('template')
      .session(session);
      
    if (!interview) {
      throw new NotFoundException(`Interview ${id} not found`);
    }
    
    return interview;
  }

  /**
   * Remove interview and all related data
   */
  async remove(id: string, session?: ClientSession): Promise<void> {
    // Delete page instances first
    await this.pageInstanceModel.deleteMany({ interviewId: id }, { session });
    
    // Delete interview
    const result = await this.interviewModel.findByIdAndDelete(id, { session });
    
    if (!result) {
      throw new NotFoundException(`Interview ${id} not found`);
    }
  }

  // ===== Private Helper Methods =====

  private async validateClient(clientId: string, session?: ClientSession) {
    const client = await this.clientsService.findOne({ _id: clientId }, session);
    
    if (!client) {
      throw new NotFoundException(`Client ${clientId} not found`);
    }
    
    if (client.apiVersion !== 'v2') {
      throw new BadRequestException('V2 interviews can only be created for V2 clients');
    }
    
    return client;
  }

  private async resolveTemplate(
    templateId: string | undefined,
    organisationId: string,
    session?: ClientSession
  ): Promise<InterviewTemplateV2> {
    if (templateId) {
      const template = await this.templateService.findById(templateId, session);
      if (template.organisationId.toString() !== organisationId.toString()) {
        throw new BadRequestException('Template does not belong to client organization');
      }
      return template;
    }
    
    // Get default template for organization
    const defaultTemplate = await this.templateService.getDefaultForOrganization(
      organisationId,
      session
    );
    
    if (!defaultTemplate) {
      throw new NotFoundException('No default template found for organization');
    }
    
    return defaultTemplate;
  }

  private async createInterview(
    dto: CreateInterviewV2Dto,
    client: any,
    templateId: string,
    compositionInfo: any,
    session?: ClientSession
  ): Promise<InterviewV2> {
    const interviewData = {
      client: dto.clientId,
      organisationId: client.organisationId,
      advisor: client.primaryAdvisor?.id,
      template: templateId,
      contactType: dto.contactType,
      branch: dto.branch || 'main',
      parentInterview: dto.parentInterviewId,
      status: 'pending' as const,
      metadata: dto.metadata,
      compositionInfo,
      accountInstances: [], // Will be populated next
    };
    
    const [interview] = await this.interviewModel.create([interviewData], { session });
    return interview;
  }

  private async initializeAccountInstances(
    interview: InterviewV2,
    client: any,
    contactType: 'primary' | 'secondary',
    session?: ClientSession
  ): Promise<void> {
    const contact = contactType === 'primary' 
      ? client.primaryContact 
      : client.secondaryContact;
      
    if (!contact?.accounts?.length) {
      return;
    }
    
    const accountInstances = [];
    
    for (const account of contact.accounts) {
      const requiresBeneficiaries = this.requiresBeneficiaries(account.type);
      
      const instance: any = {
        accountId: account._id.toString(),
        accountType: account.type,
        accountLabel: account.label,
        requiresBeneficiaries,
      };
      
      if (requiresBeneficiaries) {
        // Create beneficiary page instances
        const beneficiaryPageIds = await this.createBeneficiaryPageInstances(
          interview._id,
          account,
          session
        );
        instance.beneficiaryPageIds = beneficiaryPageIds;
      }
      
      accountInstances.push(instance);
    }
    
    // Update interview with account instance count
    interview.accountInstanceCount = accountInstances.length;
    await interview.save({ session });
    
    // Save account instances to separate collection
    if (accountInstances.length > 0) {
      await this.accountInstanceModel.insertMany(
        accountInstances.map(instance => ({
          interviewId: interview._id.toString(),
          ...instance,
        })),
        { session }
      );
    }
  }

  private requiresBeneficiaries(accountType: string): boolean {
    return [
      AccountTypeEnum.Ira,
      AccountTypeEnum.RothIra,
    ].includes(accountType as AccountTypeEnum);
  }

  private async createBeneficiaryPageInstances(
    interviewId: string,
    account: any,
    session?: ClientSession
  ): Promise<{ primary?: string; contingent?: string }> {
    const interview = await this.interviewModel
      .findById(interviewId)
      .populate('template')
      .session(session);
      
    const template = interview!.template as unknown as InterviewTemplateV2;
    const beneficiaryPages = template.pages.filter(
      page => page.pageType === 'primary-beneficiaries' || page.pageType === 'contingent-beneficiaries'
    );
    
    const result: { primary?: string; contingent?: string } = {};
    
    for (const pageTemplate of beneficiaryPages) {
      const beneficiaryType = pageTemplate.pageType === 'primary-beneficiaries' ? 'primary' : 'contingent';
      
      const instance = await this.pageInstanceModel.create([{
        interviewId,
        pageId: pageTemplate.pageId,
        pageName: pageTemplate.pageName,
        visitOrder: -1, // Not visited yet
        status: 'pending',
        syncStatus: 'pending',
        accountContext: {
          accountId: account._id.toString(),
          accountType: 'beneficiary',
          accountSubtype: beneficiaryType,
          accountName: `${account.accountLabel} - ${beneficiaryType} beneficiaries`,
        },
      }], { session });
      
      if (beneficiaryType === 'primary') {
        result.primary = instance[0]._id.toString();
      } else if (beneficiaryType === 'contingent') {
        result.contingent = instance[0]._id.toString();
      }
    }
    
    return result;
  }

  private async createInitialPageInstances(
    interview: InterviewV2,
    template: InterviewTemplateV2,
    session?: ClientSession
  ): Promise<void> {
    // Create placeholder instances for standard pages
    const standardPages = template.pages
      .filter(page => page.pageType === 'standard' && page.isActive)
      .sort((a, b) => a.defaultOrder - b.defaultOrder);
      
    if (standardPages.length === 0) {
      throw new BadRequestException('Template has no active pages');
    }
    
    // Mark interview as ready with start page
    interview.status = 'pending';
    interview.startedAt = new Date();
    await interview.save({ session });
  }

  private async formatInterviewResponse(
    interview: InterviewV2,
    template: InterviewTemplateV2,
    session?: ClientSession
  ): Promise<InterviewResponseV2Dto> {
    const firstPage = template.pages
      .filter(p => p.isActive)
      .sort((a, b) => a.defaultOrder - b.defaultOrder)[0];
    
    // Load account instances from separate collection
    const accountInstances = await this.accountInstanceModel
      .find({ interviewId: interview._id.toString() })
      .session(session);
      
    return {
      id: interview._id.toString(),
      clientId: interview.client.toString(),
      templateId: interview.template.toString(),
      contactType: interview.contactType,
      status: interview.status as any,
      startPageId: template.startPageId || firstPage?.pageId,
      accountInstances: accountInstances.map(instance => ({
        accountId: instance.accountId,
        accountType: instance.accountType,
        accountLabel: instance.accountLabel,
        requiresBeneficiaries: instance.requiresBeneficiaries,
        beneficiaryPages: instance.beneficiaryPages,
      })),
      createdAt: (interview as any).createdAt,
      startedAt: interview.startedAt,
      completedAt: interview.completedAt,
    };
  }

  /**
   * Mark interview as complete
   */
  async markComplete(
    interviewId: string,
    session?: ClientSession,
  ): Promise<void> {
    await this.interviewModel.findByIdAndUpdate(
      interviewId,
      {
        $set: {
          status: 'completed',
          isComplete: true,
          completedAt: new Date(),
        },
      },
      { session },
    );
  }

  /**
   * Compose interview from base and account templates
   */
  private async composeInterviewFromTemplates(
    client: any,
    contact: any,
    dto: CreateInterviewV2Dto,
    session?: ClientSession
  ): Promise<any> {
    // If no accounts, just use base template
    if (!contact.accounts || contact.accounts.length === 0) {
      const baseTemplate = await this.resolveTemplate(
        dto.templateId,
        client.organisationId.toString(),
        session
      );
      
      return {
        composedPages: baseTemplate.pages,
        compositionInfo: {
          baseTemplateId: baseTemplate._id.toString(),
          accountTemplates: [],
          composedAt: new Date()
        },
        baseTemplate
      };
    }
    
    // Build composition DTO
    const composeDto: ComposeInterviewDto = {
      clientId: dto.clientId,
      organisationId: client.organisationId.toString(),
      baseTemplateId: dto.templateId,
      contactType: dto.contactType,
      accounts: contact.accounts.map((account: any) => ({
        accountId: account._id.toString(),
        accountType: account.type,
        accountLabel: account.label || account.accountLabel,
        templateId: account.templateId // Use account-specific template if set
      }))
    };
    
    // Use composer service to combine templates
    return this.composerService.composeInterview(composeDto, session);
  }

  /**
   * Create page instances from composed pages
   */
  private async createPageInstancesFromComposition(
    interviewId: string,
    composedPages: any[],
    session?: ClientSession
  ): Promise<void> {
    const pageInstances = composedPages.map(page => ({
      interviewId,
      pageId: page.pageId,
      pageName: page.pageName,
      visitOrder: -1, // Not visited yet
      status: 'pending',
      syncStatus: 'pending',
      accountContext: page.accountContext
    }));
    
    await this.pageInstanceModel.insertMany(pageInstances, { session });
  }
}