import { Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable, UnauthorizedException } from '@nestjs/common';
import { passportJwtSecret } from 'jwks-rsa';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { JWTStratPayload } from '../auth.types';
import { ClsService } from 'nestjs-cls';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { OrganisationsService } from 'src/organisations/organisations.service';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  constructor(
    private readonly advisorsCrudService: AdvisorsCrudService,
    private readonly organisationsService: OrganisationsService,
    private readonly clsService: ClsService,
  ) {
    const issuer = `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.AWS_COGNITO_USER_POOL_ID}`;
    super({
      jwtFromRequest: JwtStrategy.cookieAuthHeaderExtractor,
      ignoreExpiration: false,
      _audience: process.env.AWS_COGNITO_CLIENT_ID,
      issuer,
      algorithms: ['RS256'],
      secretOrKeyProvider: passportJwtSecret({
        cache: true,
        rateLimit: true,
        jwksRequestsPerMinute: 5,
        jwksUri: `${issuer}/.well-known/jwks.json`,
      }),
    });
  }

  async validate(payload: JWTStratPayload) {
    if (!payload) {
      throw new UnauthorizedException();
    }

    const advisor = await this.advisorsCrudService.findOne({
      'personalInfo.email': payload.username,
    });

    const org = await this.organisationsService.findOne(
      advisor.organisation._id.toString(),
    );

    if (!advisor) {
      throw new UnauthorizedException();
    }
    this.clsService.set(ClsDataEnum.Advisor, advisor);
    this.clsService.set(ClsDataEnum.Organisation, org);
    return advisor;
  }

  static cookieAuthHeaderExtractor(req) {
    return (
      req?.cookies?.access_token ||
      req?.headers?.authorization?.replace('Bearer ', '') ||
      null
    );
  }
}
