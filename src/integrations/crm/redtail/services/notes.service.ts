import { HttpService } from '@nestjs/axios';
import { Logger } from '@nestjs/common';
import { CreateNoteRequestDto } from 'src/integrations/crm/redtail/dto/requests/notes/create-note-request.dto';
import {
  GetCategoriesResponse,
  GetCategoriesResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/categories/get-categories-response.dto';
import {
  CreateNoteResponse,
  CreateNoteResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/notes/create-note-response.dto';
import {
  GetNoteByIdResponse,
  GetNoteByIdResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/notes/get-note-response.dto';
import {
  GetNotesResponse,
  GetNotesResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/notes/get-notes-response.dto';
import { HttpMethodsEnum } from 'src/shared/types/general/http-methods.enum';
import { OperationType } from 'src/super-http/enums';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { camelize } from 'src/utils';
import { makeRequest } from 'src/utils/makeRequest';
import { snakeify } from 'src/utils/snakeify';

export class RedtailNotesService {
  private readonly baseUrl: string;
  private logger: Logger;

  constructor(
    private httpService: SuperHttpService,
    private authHeader: object,
    private tenantId: string
  ) {
    this.baseUrl = process.env.REDTAIL_BASE_URL;
    this.logger = new Logger('RedtailNotesService');
  }

  /**
   * Creates a note for a given contact in Redtail CRM.
   * @param contactId The ID of the contact to create the note for.
   * @param noteData The data for the note to be created.
   * @returns A Promise that resolves to the created note.
   */
  public async createNote(
    contactId: string,
    noteData: CreateNoteRequestDto,
  ): Promise<CreateNoteResponse> {
    const url = `${this.baseUrl}/contacts/${contactId}/notes`;
    const response = await this.httpService.post<CreateNoteResponseDto>(
      url,
      this.tenantId,
      snakeify(noteData),
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.CREATE,
        entityType: 'note',
        entityId: contactId,
        wideCacheInvalidation: true
      },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }

  /**
   * Deletes a note for a specific contact.
   * @param contactId - The ID of the contact.
   * @param noteId - The ID of the note to be deleted.
   * @returns A Promise that resolves when the note is successfully deleted.
   */
  public async deleteNote(contactId: string, noteId: number): Promise<void> {
    const url = `${this.baseUrl}/contacts/${contactId}/notes/${noteId}`;
    await this.httpService.delete(
      url,
      this.tenantId,
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.DELETE,
        entityType: 'note',
        entityId: `${contactId}_${noteId}`,
        wideCacheInvalidation: true
      },
      { headers: { ...this.authHeader } }
    );
  }

  /**
   * Retrieves notes for a given contact ID and page number.
   * @param contactId - The ID of the contact to retrieve notes for.
   * @param page - The page number of notes to retrieve. Defaults to 1.
   * @returns A Promise that resolves to a GetNotesResponse object.
   */
  public async getNotes(
    contactId: string,
    page: number = 1,
  ): Promise<GetNotesResponse> {
    const url = `${this.baseUrl}/contacts/${contactId}/notes?page=${page}`;
    const response = await this.httpService.get<GetNotesResponseDto>(
      url,
      this.tenantId,
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.LIST,
        entityType: 'note',
        entityId: contactId
      },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }

  /**
   * Retrieves a note by its ID for a specific contact.
   * @param contactId - The ID of the contact to retrieve the note for.
   * @param noteId - The ID of the note to retrieve.
   * @returns A Promise that resolves to a GetNoteByIdResponse object.
   */
  public async getNoteById(
    contactId: string,
    noteId: number,
  ): Promise<GetNoteByIdResponse> {
    const url = `${this.baseUrl}/contacts/${contactId}/notes/${noteId}`;
    const response = await this.httpService.get<GetNoteByIdResponseDto>(
      url,
      this.tenantId,
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.GET,
        entityType: 'note',
        entityId: `${contactId}_${noteId}`
      },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }

  /**
   * Retrieves the categories from the Redtail CRM API.
   * @returns A promise that resolves to a `GetCategoriesResponse` object.
   */
  public async getCategories(): Promise<GetCategoriesResponse> {
    const url = `${this.baseUrl}/lists/categories`;
    const response = await this.httpService.get<GetCategoriesResponseDto>(
      url,
      this.tenantId,
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.LIST,
        entityType: 'note'
      },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }
}
