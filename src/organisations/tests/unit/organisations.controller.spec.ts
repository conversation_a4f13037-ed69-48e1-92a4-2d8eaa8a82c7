import { Test, TestingModule } from '@nestjs/testing';
import { OrganisationsController } from 'src/organisations/organisations.controller';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { CreateOrganisationDto } from 'src/organisations/dto/create-organisation.dto';
import { UpdateOrganisationDto } from 'src/organisations/dto/update-organisation.dto';
import * as organisationMock from 'src/organisations/tests/mocks/organisations.mock.json';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { CACHE_MANAGER } from '@nestjs/cache-manager';

describe('OrganisationsController', () => {
  let controller: OrganisationsController;
  let service: OrganisationsService;
  let transactionManager: TransactionManager;
  const mockOrganisationService = {
    findAll: jest.fn(),
    create: jest.fn(),
    findOne: jest.fn(),
    update: jest.fn(),
    remove: jest.fn(),
    linkDocusign: jest.fn(),
    unlinkDocusign: jest.fn(),
  };

  const id = organisationMock.id;
  const mockCreateOrganisationDto = organisationMock as CreateOrganisationDto;
  const mockUpdateOrganisationDto = organisationMock as UpdateOrganisationDto;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [OrganisationsController],
      providers: [
        { provide: OrganisationsService, useValue: mockOrganisationService },
        {
          provide: TransactionManager,
          useValue: {
            runTransaction: jest.fn(),
          },
        },
        {
          provide: CACHE_MANAGER,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            del: jest.fn(),
            reset: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<OrganisationsController>(OrganisationsController);
    service = module.get<OrganisationsService>(OrganisationsService);
    transactionManager = module.get<TransactionManager>(TransactionManager);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should create a new organisation', async () => {
    const response = { organisationMock };

    mockOrganisationService.create.mockResolvedValue(response);
    (transactionManager.runTransaction as jest.Mock).mockImplementation(() =>
      mockOrganisationService.create(mockCreateOrganisationDto),
    );

    expect(await controller.create(mockCreateOrganisationDto)).toStrictEqual(
      response,
    );
    expect(mockOrganisationService.create).toHaveBeenCalledWith(
      mockCreateOrganisationDto,
    );
  });

  it.skip('should find all and return an array of organisations', async () => {
    const response = {
      result: [organisationMock],
      totalResults: 1,
    };
    mockOrganisationService.findAll.mockResolvedValue(response);

    expect(await controller.findAll({})).toBe(response);
    expect(mockOrganisationService.findAll).toHaveBeenCalled();
  });

  it.skip('should find one organisation and return a single organisation by ID', async () => {
    const response = organisationMock;
    mockOrganisationService.findOne.mockResolvedValue(response);
    expect(await controller.findOne(id)).toContainEqual(response);
    expect(mockOrganisationService.findOne).toHaveBeenCalledWith(id);
  });

  it('should update an existing organisation', async () => {
    const response = {
      query: {
        _id: organisationMock.id,
        mockUpdateOrganisationDto,
      },
    };

    mockOrganisationService.update.mockResolvedValue(response);
    (transactionManager.runTransaction as jest.Mock).mockImplementation(() =>
      mockOrganisationService.update(id, mockUpdateOrganisationDto),
    );

    expect(
      await controller.update(id, mockUpdateOrganisationDto),
    ).toStrictEqual(response);
    expect(mockOrganisationService.update).toHaveBeenCalledWith(
      id,
      mockUpdateOrganisationDto,
    );
  });

  it('should remove an organisation by ID', async () => {
    const response = {
      query: {
        _id: organisationMock.id,
      },
    };

    mockOrganisationService.remove.mockResolvedValue(response);
    (transactionManager.runTransaction as jest.Mock).mockImplementation(() =>
      mockOrganisationService.remove(id),
    );

    expect(await controller.remove(id)).toStrictEqual(response);
    expect(mockOrganisationService.remove).toHaveBeenCalledWith(id);
  });

  it('should unlink docusign', async () => {
    jest
      .spyOn(service, 'unlinkDocusign')
      .mockResolvedValue({ message: 'success' });

    expect(await controller.unlinkDocusign(id)).toStrictEqual({
      message: 'success',
    });
  });
});
