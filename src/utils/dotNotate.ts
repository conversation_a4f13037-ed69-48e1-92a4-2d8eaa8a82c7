import { isEmpty } from 'lodash';

/**
 * Recursively flattens an object with nested properties into a single-level object with dot notation keys.
 * @param target - The object to assign the flattened properties to.
 * @param source - The object to flatten.
 * @param current - The current key being processed (used for recursion).
 */
export function dotNotate(target: object, source: object, current = null) {
  current = current || '';
  Object.keys(source).forEach(function (key) {
    const curr = source[key];
    const isValidObject = [
      source[key] !== null,
      typeof curr === 'object',
      !isEmpty(curr),
      !(source[key] instanceof Date),
    ].reduce((acc, curr) => acc && curr, true);

    if (isValidObject) {
      dotNotate(target, source[key], current + key + '.');
    } else {
      target[current + key] = source[key];
    }
  });
}
