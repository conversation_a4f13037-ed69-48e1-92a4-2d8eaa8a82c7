import { brokerageStatementUploaded } from 'src/templates/mail/statement-uploaded/statement-uploaded.template';
import { clientWelcomeEmailTemplate } from './client-welcome/client-welcome.template';
import { desktopInterviewEmailTemplate } from './desktop-interview/desktop-interview.template';
import { failedTextMessagesEmailTemplate } from './failed-text-messages/failed-text-messages.template';
import { nonCitizenEmailTemplate } from './non-citizen/non-citizen.template';
import { readyToSignEmailTemplate } from './ready-to-sign/ready-to-sign.template';

import { EmailTemplates, EmailTemplateNameEnum, IEmailContext } from './types';

/**
 * Factory function that returns an email template function based on the provided type.
 * @param type - The type of email template to retrieve.
 * @returns The email template function.
 * @throws An error if the template function for the provided type is not found.
 */
export function emailTemplateMapper<T extends keyof EmailTemplates>(type: T) {
  const templates: EmailTemplates = {
    [EmailTemplateNameEnum.ClientWelcome]: clientWelcomeEmailTemplate,
    [EmailTemplateNameEnum.DesktopInterview]: desktopInterviewEmailTemplate,
    [EmailTemplateNameEnum.FailedTextMessage]: failedTextMessagesEmailTemplate,
    [EmailTemplateNameEnum.NonCitizen]: nonCitizenEmailTemplate,
    [EmailTemplateNameEnum.ReadyToSign]: readyToSignEmailTemplate,
    [EmailTemplateNameEnum.StatementUploaded]: brokerageStatementUploaded,
  };

  const templateFn = templates[type];

  if (!templateFn) {
    throw new Error(`Template for type ${type} not found.`);
  }

  return templateFn();
}
