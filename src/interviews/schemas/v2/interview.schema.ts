import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';
import { Factory } from 'nestjs-seeder';

@Schema({ timestamps: true, collection: 'interviews_v2' })
export class  InterviewV2 extends Document {

  @Prop({ 
    type: SchemaTypes.ObjectId, 
    ref: 'Client', 
    required: true,
    index: true 
  })
  client!: string;

  @Prop({ 
    type: SchemaTypes.ObjectId, 
    ref: 'Organisation', 
    required: true,
    index: true 
  })
  organisationId!: string;

  @Prop({ 
    type: SchemaTypes.ObjectId, 
    ref: 'User', 
    required: true,
    index: true 
  })
  advisor!: string;

  @Prop({ 
    type: String, 
    required: true,
    default: 'v2',
    enum: ['v1', 'v2']
  })
  apiVersion!: string;

  @Prop({ 
    type: SchemaTypes.ObjectId, 
    ref: 'InterviewTemplateV2', 
    required: true 
  })
  template!: string;

  @Prop({ 
    type: String, 
    required: true,
    enum: ['primary', 'secondary'],
    default: 'primary'
  })
  contactType!: 'primary' | 'secondary';

  @Prop({ 
    type: String, 
    default: 'main', 
    index: true 
  })
  branch!: string;

  @Prop({ 
    type: SchemaTypes.ObjectId, 
    ref: 'InterviewV2', 
    default: null 
  })
  parentInterview?: string;  // For branched interviews

  @Prop({ 
    type: Boolean, 
    default: false 
  })
  sealed!: boolean;  // Prevent modifications after completion

  @Prop({ 
    type: Boolean, 
    default: false 
  })
  isComplete!: boolean;

  // Reference to account instances (moved to separate collection)
  @Prop({ 
    type: Number,
    default: 0
  })
  accountInstanceCount!: number;  // Denormalized count for quick queries

  @Prop({ 
    type: String,
    enum: ['pending', 'in_progress', 'completed', 'abandoned'],
    default: 'pending',
    index: true
  })
  status!: string;

  @Prop({ type: Date })
  startedAt?: Date;

  @Prop({ type: Date })
  completedAt?: Date;

  @Prop({ type: Date })
  abandonedAt?: Date;

  // Metadata for tracking
  @Prop({ 
    type: Object,
    default: {}
  })
  metadata?: {
    deviceType?: string;
    userAgent?: string;
    ipAddress?: string;
    sessionId?: string;
  };

  // Template composition tracking
  @Prop({ 
    type: Object,
    default: {}
  })
  compositionInfo?: {
    baseTemplateId: string;
    accountTemplates: Array<{
      accountId: string;
      templateId: string;
      accountType: string;
    }>;
    composedAt: Date;
  };
}

export const InterviewV2Schema = SchemaFactory.createForClass(InterviewV2);

// Indexes
InterviewV2Schema.index({ client: 1, status: 1 });
InterviewV2Schema.index({ client: 1, contactType: 1 });
InterviewV2Schema.index({ template: 1, status: 1 });
InterviewV2Schema.index({ createdAt: -1 });
InterviewV2Schema.index({ organisationId: 1, status: 1 });
InterviewV2Schema.index({ advisor: 1, status: 1 });
InterviewV2Schema.index({ apiVersion: 1, client: 1 });