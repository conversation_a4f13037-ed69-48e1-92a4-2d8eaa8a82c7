import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, OnModuleInit, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { InterviewTemplatesModule } from 'src/interview-templates/interview-templates.module';
import { ClientsModule } from 'src/clients/clients.module';
import { AdvisorsModule } from 'src/advisors/advisors.module';
import { ConfigModule } from '@nestjs/config';
import { InterviewsService } from './interviews.service';
import { InterviewsV1Controller } from './controllers/v1/interviews-v1.controller';
import { InterviewsV2Controller } from './controllers/v2/interviews-v2.controller';
import { Interview, InterviewSchema } from './schemas/v1/interview.schema';
import { InterviewV2, InterviewV2Schema } from './schemas/v2/interview.schema';
import { InterviewPageInstanceV2, InterviewPageInstanceV2Schema } from './schemas/v2/interview-page-instance.schema';
import { InterviewAccountInstanceV2, InterviewAccountInstanceV2Schema } from './schemas/v2/interview-account-instance.schema';
import { Asset, AssetSchema } from 'src/shared/schemas/asset.schema';
import { OrganisationsModule } from 'src/organisations/organisations.module';
import { MailModule } from 'src/notifications/mail/mail.module';
import { ClsModule } from 'nestjs-cls';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { CacheService } from 'src/shared/services/cache.service';
import { INTERVIEW_QUEUE } from 'src/interviews/constants/interview-queue.constant';
import { INTERVIEW_V2_QUEUE } from 'src/interviews/constants/interview-v2-queue.constant';
import { BullModule, InjectQueue } from '@nestjs/bullmq';
import { BullBoardModule } from '@bull-board/nestjs';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { InterviewPageProcessor } from 'src/interviews/interview.processor';
import { InterviewV2Processor } from 'src/interviews/services/v2/processor/interview.processor';
import { QueueLogModule } from 'src/queue-log/queue-log.module';
import { QueueRegistryService } from 'src/queue-log/queue-registry.service';
import { Queue } from 'bullmq';
import { CLIENT_QUEUE } from 'src/shared/constants/client.constant';
import { CacheModule } from '@nestjs/cache-manager';
import { InterviewCoreService } from './services/v2/core/interview-core.service';
import { InterviewV2NavigationService } from './services/v2/navigation/interview-navigation.service';
import { FlowEvaluationService } from './services/v2/navigation/interview-flow-evaluation.service';
import { InterviewV2AuditService } from './services/v2/audit/interview-audit.service';
import { InterviewPagesService } from './services/v2/pages/interview-pages.service';
import { InterviewDocumentsService } from './services/v2/documents/interview-documents.service';
import { InterviewEnvelopeService } from './services/v2/envelope/interview-envelope.service';
import { InterviewNotificationService } from './services/v2/notifications/interview-notification.service';
import { InterviewV2QueueService } from './services/v2/queue/interview-queue.service';
import { InterviewV2Facade } from './services/v2/interview-v2.facade';
import { InterviewV2Service } from './services/v2/interview-v2.service';
import { InterviewsV1Service } from './services/v1/interviews-v1.service';
import { DocusignModule } from 'src/integrations/docusign/docusign.module';
import { CRMModule } from 'src/integrations/crm/crm.module';
import { AuditsModule } from 'src/audits/audits.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Interview.name, schema: InterviewSchema },
      { name: InterviewV2.name, schema: InterviewV2Schema },
      { name: InterviewPageInstanceV2.name, schema: InterviewPageInstanceV2Schema },
      { name: InterviewAccountInstanceV2.name, schema: InterviewAccountInstanceV2Schema },
    ]),
    MongooseModule.forFeature([{ name: Asset.name, schema: AssetSchema }]),
    InterviewTemplatesModule,
    forwardRef(() => ClientsModule),
    ConfigModule,
    forwardRef(() => AdvisorsModule),
    forwardRef(() => OrganisationsModule),
    forwardRef(() => DocusignModule),
    forwardRef(() => CRMModule),
    forwardRef(() => AuditsModule),
    MailModule,
    BullModule.registerQueue({
      name: INTERVIEW_QUEUE.NAME,
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
      },
    }),
    BullModule.registerQueue({
      name: CLIENT_QUEUE.NAME,
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
      },
    }),
    BullModule.registerQueue({
      name: INTERVIEW_V2_QUEUE.NAME,
      defaultJobOptions: {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 1000,
        },
      },
    }),
    BullBoardModule.forFeature({
      name: INTERVIEW_QUEUE.NAME,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: INTERVIEW_V2_QUEUE.NAME,
      adapter: BullMQAdapter,
    }),
    CacheModule.register(),
    ClsModule,
    forwardRef(() => QueueLogModule),
  ],
  controllers: [InterviewsV1Controller, InterviewsV2Controller],
  providers: [
    InterviewCoreService,
    InterviewV2NavigationService,
    FlowEvaluationService,
    InterviewV2AuditService,
    InterviewPagesService,
    InterviewDocumentsService,
    InterviewEnvelopeService,
    InterviewNotificationService,
    InterviewV2QueueService,
    InterviewV2Service,
    InterviewV2Facade,
    InterviewsV1Service,
    InterviewsService, // v1 compatibility layer
    TransactionManager,
    CacheService,
    InterviewPageProcessor,
    InterviewV2Processor,
  ],
  exports: [InterviewV2Facade, InterviewsV1Service, InterviewsService, InterviewCoreService],
})
export class InterviewsModule implements OnModuleInit {
  private readonly logger = new Logger(InterviewsModule.name);
  constructor(
    private readonly queueRegistryService: QueueRegistryService,
    @InjectQueue(INTERVIEW_QUEUE.NAME) private readonly interviewQueue: Queue,
    @InjectQueue(INTERVIEW_V2_QUEUE.NAME) private readonly interviewV2Queue: Queue,
  ) {}

  onApplicationBootstrap() {
    // Register the queue in QueueRegistryService
    this.logger.log('InterviewsModule onApplicationBootstrap triggered.');
    this.queueRegistryService.registerQueue(INTERVIEW_QUEUE.NAME, this.interviewQueue);
    this.queueRegistryService.registerQueue(INTERVIEW_V2_QUEUE.NAME, this.interviewV2Queue);
  }

  onModuleInit() {
    // Register the queue when the module initializes
    this.queueRegistryService.registerQueue(INTERVIEW_QUEUE.NAME, this.interviewQueue);
    this.queueRegistryService.registerQueue(INTERVIEW_V2_QUEUE.NAME, this.interviewV2Queue);
  }
}