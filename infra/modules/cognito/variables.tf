variable "user_pool_name" {
  description = "The name of the user pool"
}
# START ---- Invitation event config on admin_create_user event
variable "invitation_email_message" {
  description = "The HTML or text template for invitation email when admin creates user"
}
variable "invitation_email_subject" {
  description = "The subject to use for invitation email when admin creates user"
}
variable "invitation_email_sms_text" {
  description = "The SMS text for invitation email when admin creates user"
}
# END ---- Invitation event config on admin_create_user event
# MFA Authentication SMS Text
variable "mfa_SMS_text" {
  description = "Your authentication code is {####}"
}
# ARN of email address that is already verified in SES (with onbord.io domain)
variable "ses_verified_email_identity_arn" {
  description = "ARN of verified email identity from SES"
}
variable "client_name" {
  description = "The name of the client"
}
variable "callback_urls" {
  description = "List of allowed callback URLs for the identity providers."
  type        = list(string)
}
variable "default_redirect_uri" {
  description = "The default redirect URI. Must be in the list of callback URLs."
  type        = string
}
variable "logout_urls" {
  description = "List of allowed logout URLs for the identity providers."
  type        = list(string)
}
