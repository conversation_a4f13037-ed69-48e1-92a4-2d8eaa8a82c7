import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

export class RedtailDOBMapper implements FormUpdateMapper {
  constructor(private redtailCrm: RedtailCRM) {}

  /**
   * Maps the date of birth (dob) data from a page to a Redtail CRM contact.
   * @param clientCrmId The ID of the client's CRM record in Redtail.
   * @param organisation The organisation associated with the client.
   * @param pageData The data from the page containing the dob information.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    // FIXME: Add DTO for dob page data.
    pageData: any,
  ) {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    await this.redtailCrm.patchContact(contact.crmClientId, {
      dob: pageData.dob,
    });
  }
}
