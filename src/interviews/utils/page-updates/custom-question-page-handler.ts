import { isEmpty } from 'lodash';
import { InterviewPage } from 'src/interview-templates/schemas/v1/interview.template';
import { GenericPageHandler } from 'src/interviews/utils/page-updates/generic-page-handler';

/**
 * A class that handles updates for custom question pages.
 * Extends the GenericPageHandler class.
 */
export class CustomQuestionsPageHandler extends GenericPageHandler {
  /**
   * Determines whether the page should be updated with the new data.
   * @param page - The current InterviewPage object.
   * @returns A boolean indicating whether the page should be updated.
   */
  protected shouldUpdate(page: InterviewPage): boolean {
    // First check if it's the same page name
    if (!super.shouldUpdate(page)) {
      return false;
    }

    // If there's no question data at all, we can't update
    if (isEmpty(this.updateDto.page.data?.question)) {
      return false;
    }

    // If the page data has the same question ID, we should update it
    if (page.data?.question?._id && 
        this.updateDto.page.data?.question?._id && 
        page.data.question._id.toString() === this.updateDto.page.data.question._id.toString()) {
      return true;
    }

    // Fallback behavior, compare the question text
    const updatedQuestion = this.updateDto.page.data?.question?.question;
    const currentQuestion = page.data?.question?.question;

    // If either question is missing, we can't safely compare, so don't update
    if (!updatedQuestion || !currentQuestion) {
      return false;
    }

    // Compare the questions, ignoring whitespace and case
    return updatedQuestion.trim().toLowerCase() === currentQuestion.trim().toLowerCase();
  }
}