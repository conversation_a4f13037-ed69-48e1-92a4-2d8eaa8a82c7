import { HttpException } from '@nestjs/common';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { BeneficiariesPageDataDto } from 'src/integrations/crm/redtail/form-mappers/dto/add-beneficiary-request.dto';
import {
  RedtailAccountTypeEnum,
  RedtailBeneficiaryEnum,
} from 'src/integrations/crm/redtail/types/enums';
import { computeBeneficiaryName } from 'src/integrations/crm/redtail/utils/beneficiary-name-parser';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

export class RedtailPrimaryBeneficiariesMapper implements FormUpdateMapper {
  constructor(private redtailCrm: RedtailCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: BeneficiariesPageDataDto,
  ): Promise<any> {
    const { beneficiaries } = pageData;
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    const { crmClientId } = contact;
    if (beneficiaries && beneficiaries.length > 0) {
      const redtailAccounts =
        await this.redtailCrm.redtailAccountsService.getAccounts(
          crmClientId,
          RedtailAccountTypeEnum.BROKERAGE_ACCOUNT,
        );

      const account = redtailAccounts?.find((x) => {
        const accountLabel = this.redtailCrm.redtailAccountsService
          .getLabelFromAccountNumber(x?.number)
          .toLowerCase();

        return pageData.instance.label.toLowerCase().includes(accountLabel);
      });

      if (!account) {
        throw new HttpException(
          `Account ${pageData.instance.label} not found`,
          404,
        );
      }

      await this.redtailCrm.redtailAccountsService.deleteAccountBeneficiariesByType(
        account.id,
        RedtailBeneficiaryEnum.PRIMARY,
      );

      const promises = beneficiaries.map((beneficiary) => {
        return this.redtailCrm.redtailAccountsService.addAccountBeneficiary(
          account.id,
          {
            name: computeBeneficiaryName(beneficiary),
            percentage: beneficiary.allocationAmount,
            beneficiaryType: RedtailBeneficiaryEnum.PRIMARY,
          },
        );
      });

      return Promise.all(promises);
    }
  }
}
