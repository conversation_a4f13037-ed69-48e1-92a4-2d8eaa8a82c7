export function fileFilter(
  req: any,
  file: Express.Multer.File,
  callback: (error: Error | null, acceptFile: boolean) => void,
) {
  switch (file.fieldname) {
    case 'team':
      if (file.mimetype === 'application/json') {
        // Accept the JSON file
        callback(null, true);
      } else {
        // Reject non-JSON files
        callback(
          new Error('Invalid file type for team. Only JSON is allowed'),
          false,
        );
      }
      break;
    case 'file':
      if (file.mimetype === 'text/csv') {
        // Accept the CSV file
        callback(null, true);
      } else {
        // Reject non-CSV files
        callback(
          new Error('Invalid file type for file. Only CSV is allowed'),
          false,
        );
      }
      break;
    case 'adv2B':
      // Check if the file is present and if it's a PDF
      if (file && file.mimetype === 'application/pdf') {
        // Accept the PDF file
        callback(null, true);
      } else if (!file) {
        // If there's no file, it's optional, accept the request but without a file
        callback(null, true);
      } else {
        // Reject non-PDF files
        callback(
          new Error('Invalid file type for adv2B. Only PDF is allowed'),
          false,
        );
      }
      break;
    case 'statement':
      // Check if the file is present and if it's a PDF
      if (
        (file && file.mimetype === 'application/pdf') ||
        file.mimetype === 'image/jpeg' ||
        file.mimetype === 'image/png'
      ) {
        // Accept the PDF file
        callback(null, true);
      } else if (!file) {
        // If there's no file, it's optional, accept the request but without a file
        callback(null, true);
      } else {
        // Reject non-PDF files
        callback(
          new Error(
            'Invalid file type for statement. Only PDF, JPEG or PNG are allowed',
          ),
          false,
        );
      }
      break;
    default:
      // If the fieldname is unknown, reject the file
      callback(new Error('Invalid field name'), false);
  }
}
