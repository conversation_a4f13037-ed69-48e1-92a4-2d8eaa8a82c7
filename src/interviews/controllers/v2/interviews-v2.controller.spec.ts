import { Test, TestingModule } from '@nestjs/testing';
import { InterviewsV2Controller } from './interviews-v2.controller';
import { InterviewV2Facade } from '../../services/v2/interview-v2.facade';
import { CreateInterviewDto } from '../../dto/v1/create-interview.dto';
import { UpdateInterviewDto } from '../../dto/v1/update-interview.dto';
import { GetInterviewQueryDto } from '../../dto/v1/get-interview-query.dto';
import { UploadDocumentDto } from '../../dto/v1/upload-document.dto';
import { InterviewViewDto } from '../../dto/v1/view-interview.dto';
import { HttpException, HttpStatus } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { Response } from 'express';

// Mock class-transformer
jest.mock('class-transformer', () => ({
  plainToInstance: jest.fn(),
}));

describe('InterviewsV2Controller', () => {
  let controller: InterviewsV2Controller;
  let facade: jest.Mocked<InterviewV2Facade>;
  let mockResponse: Partial<Response>;

  const mockPlainToInstance = plainToInstance as jest.MockedFunction<typeof plainToInstance>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [InterviewsV2Controller],
      providers: [
        {
          provide: InterviewV2Facade,
          useValue: {
            create: jest.fn(),
            findById: jest.fn(),
            findOne: jest.fn(),
            enqueueFlow: jest.fn(),
            upload: jest.fn(),
            complete: jest.fn(),
            sendDesktopInterviewEmail: jest.fn(),
            sendNonCitizenEmail: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<InterviewsV2Controller>(InterviewsV2Controller);
    facade = module.get(InterviewV2Facade);
    
    mockResponse = {
      send: jest.fn(),
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('should delegate to facade create method', async () => {
      const dto: CreateInterviewDto = {
        clientId: 'client123',
        docusignSelected: true,
        email: '<EMAIL>',
        name: 'Test Interview',
        accounts: [],
        customTemplates: [],
      };
      const expected = { _id: 'interview123' };
      facade.create.mockResolvedValue(expected as any);

      const result = await controller.create(dto);

      expect(facade.create).toHaveBeenCalledWith(dto);
      expect(result).toBe(expected);
    });
  });

  describe('findOne', () => {
    it('should apply DTO transformation like V1', async () => {
      const interviewId = 'interview123';
      const rawInterview = { _id: interviewId, __v: 0, internalField: 'hidden' };
      const transformedInterview = { _id: interviewId };
      
      facade.findById.mockResolvedValue(rawInterview as any);
      mockPlainToInstance.mockReturnValue(transformedInterview as any);

      const result = await controller.findOne(interviewId);

      expect(facade.findById).toHaveBeenCalledWith(interviewId, undefined, true);
      expect(mockPlainToInstance).toHaveBeenCalledWith(
        InterviewViewDto,
        rawInterview,
        {
          excludeExtraneousValues: true,
          enableImplicitConversion: true,
        }
      );
      expect(result).toBe(transformedInterview);
    });
  });

  describe('findByClientId', () => {
    it('should return single interview object like V1', async () => {
      const clientId = 'client123';
      const query: GetInterviewQueryDto = { relatesTo: 'Primary' };
      const expected = { _id: 'interview123', client: clientId };
      
      facade.findOne.mockResolvedValue(expected as any);

      const result = await controller.findByClientId(clientId, query);

      expect(facade.findOne).toHaveBeenCalledWith({
        client: clientId,
        isPrimary: true,
      });
      expect(result).toBe(expected);
    });

    it('should throw 404 when interview not found', async () => {
      const clientId = 'client123';
      const query: GetInterviewQueryDto = {};
      
      facade.findOne.mockResolvedValue(null);

      await expect(controller.findByClientId(clientId, query)).rejects.toThrow(
        new HttpException('Interview not found', HttpStatus.NOT_FOUND)
      );
    });
  });

  describe('update', () => {
    it('should delegate to facade enqueueFlow method', async () => {
      const id = 'interview123';
      const dto: UpdateInterviewDto = { page: { name: 'test', data: {} } } as any;
      const expected = { updated: true };
      
      facade.enqueueFlow.mockResolvedValue(expected as any);

      const result = await controller.update(id, dto);

      expect(facade.enqueueFlow).toHaveBeenCalledWith(id, dto);
      expect(result).toBe(expected);
    });
  });

  describe('upload', () => {
    it('should delegate to facade upload method', async () => {
      const id = 'interview123';
      const files = [{ originalname: 'test.pdf' }] as Express.Multer.File[];
      const dto: UploadDocumentDto = { documentType: 'test' } as any;
      const expected = 'upload-result';
      
      facade.upload.mockResolvedValue(expected);
      
      await controller.upload(id, files, dto, mockResponse as Response);

      expect(facade.upload).toHaveBeenCalledWith(id, files, dto);
      expect(mockResponse.send).toHaveBeenCalledWith(expected);
    });
  });

  describe('finish', () => {
    it('should delegate to facade complete method', async () => {
      const id = 'interview123';
      const expected = { completed: true };
      
      facade.complete.mockResolvedValue(expected as any);

      const result = await controller.finish(id);

      expect(facade.complete).toHaveBeenCalledWith(id);
      expect(result).toBe(expected);
    });
  });

  describe('sendDesktopInterviewEmail', () => {
    it('should use GET method and correct path like V1', async () => {
      const interviewId = 'interview123';
      const expected = { sent: true };
      
      facade.sendDesktopInterviewEmail.mockResolvedValue(expected);

      const result = await controller.sendDesktopInterviewEmail(interviewId);

      expect(facade.sendDesktopInterviewEmail).toHaveBeenCalledWith(interviewId);
      expect(result).toBe(expected);
    });
  });

  describe('sendNonCitizenEmail', () => {
    it('should use GET method and correct path like V1', async () => {
      const interviewId = 'interview123';
      const expected = { sent: true };
      
      facade.sendNonCitizenEmail.mockResolvedValue(expected);

      const result = await controller.sendNonCitizenEmail(interviewId);

      expect(facade.sendNonCitizenEmail).toHaveBeenCalledWith(interviewId);
      expect(result).toBe(expected);
    });
  });

  describe('Backward Compatibility', () => {
    it('should maintain same method signatures as V1', () => {
      // Verify all methods exist and are functions
      expect(typeof controller.create).toBe('function');
      expect(typeof controller.findOne).toBe('function');
      expect(typeof controller.findByClientId).toBe('function');
      expect(typeof controller.update).toBe('function');
      expect(typeof controller.upload).toBe('function');
      expect(typeof controller.finish).toBe('function');
      expect(typeof controller.sendDesktopInterviewEmail).toBe('function');
      expect(typeof controller.sendNonCitizenEmail).toBe('function');
    });
  });
});
