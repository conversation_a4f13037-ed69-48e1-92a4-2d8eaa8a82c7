import { Inject, Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { ClsService } from 'nestjs-cls';
import { Logger } from 'winston';
import * as dataguardian from 'data-guardian';
import { isEmpty } from 'lodash';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';

@Injectable()
export class LoggerMiddleware implements NestMiddleware {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private logger: Logger,
    private clsService: ClsService,
  ) {}

  use(req: Request, res: Response, next: NextFunction): void {
    try {
      const { method, originalUrl, ip, body } = req;
      const userAgent = req.get('user-agent') || '';
      const start = Date.now();
      const maskedBody = !isEmpty(body)
        ? JSON.stringify(
            dataguardian.maskData(body, {
              fixedMaskLength: true,
              maskLength: 4,
            }) || {},
          )
        : '';

      res.on('finish', () => {
        const duration = Date.now() - start;
        const { statusCode } = res;
        const requestId = this.clsService.get('ID');
        const advisor = this.clsService.get(ClsDataEnum.Advisor);
        let message = `${requestId} - ${method} ${originalUrl} ${statusCode} [${duration}ms] IP: ${ip} - UserAgent: ${userAgent} \nBody: ${
          maskedBody || 'No body'
        }`;
        message += advisor ? `\nAdvisor: ${advisor?._id}` : '';
        this.logger.info(message);
      });
    } catch (error) {
      this.logger.error("Couldn't log request", error);
    } finally {
      next();
    }
  }
}
