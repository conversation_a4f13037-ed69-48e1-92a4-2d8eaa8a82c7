import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';
import { USCitizenRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/us-citizen-request.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import {
  WealthboxStoredClientInfo,
  parseDataNoteYAML,
  updateDataNoteYAML,
} from 'src/integrations/crm/wealthbox/utils/data-parsing';
import { objectToYAML } from 'src/utils/yaml-parser';
import { NonCitizenEmailContext } from 'src/templates/mail/non-citizen/non-citizen.context';
import { EmailTemplateNameEnum } from 'src/templates/mail/types';

export class WealthboxUSCitizenMapper implements FormUpdateMapper {
  constructor(private WealthboxCrm: WealthboxCRM) {}

  /**
   * Maps page data to update US citizen information in Wealthbox CRM.
   * @param interview - The enriched interview object.
   * @param client - The enriched client object.
   * @param organisation - The organisation object.
   * @param pageData - The US citizen request data.
   * @returns A promise that resolves when the mapping is complete.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: USCitizenRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    if (pageData.citizen && pageData.resident) {
      const dataNote = await this.WealthboxCrm.notesService.getDataNoteContent(
        contact.crmClientId,
      );
      if (!dataNote) {
        await this.WealthboxCrm.notesService.upsertDataNote(
          contact.crmClientId,
          {
            linked_to: [
              {
                id: Number(contact.crmClientId),
                type: 'Contact',
              },
            ],
            content: objectToYAML({
              additionalInfo: {
                isUSCitizen: pageData.citizen,
                isUSResident: pageData.resident,
              },
              accounts: [],
            }),
          },
        );
        return;
      }

      const contactData: WealthboxStoredClientInfo = parseDataNoteYAML(
        dataNote?.content,
      );

      contactData.additionalInfo = {
        ...contactData.additionalInfo,
        isUSCitizen: pageData.citizen,
        isUSResident: pageData.resident,
      };

      await this.WealthboxCrm.notesService.upsertDataNote(contact.crmClientId, {
        linked_to: dataNote.linked_to,
        content: updateDataNoteYAML(contactData),
      });
      return;
    }

    const context: NonCitizenEmailContext = {
      clientFirstName: contact.firstName,
      clientLastName: contact.lastName,
      subject: `Non-Citizen Status`,
    };

    return this.WealthboxCrm.sendEmail({
      to: client.primaryAdvisor.email,
      templateName: EmailTemplateNameEnum.NonCitizen,
      subject: `Non-Citizen Status`,
      context,
      organisation, // Pass the organisation parameter
    });
  }
}
