import { Injectable } from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { Seeder, DataFactory } from 'nestjs-seeder';
import { Advisor } from 'src/advisors/schemas/advisors.schema';
import { advisorsData } from './advisors';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { Client } from 'src/clients/schemas/clients.schema';
import { InterviewTemplate } from 'src/interview-templates/schemas/v1/interview.template';
import { Interview } from 'src/interviews/schemas/v1/interview.schema';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
@Injectable()
export class AdvisorsOrgSeeder implements Seeder {
  constructor(
    @InjectModel(Client.name) private readonly clientModel: Model<Client>,
    @InjectModel(Advisor.name) private readonly advisorModel: Model<Advisor>,
    @InjectModel(InterviewTemplate.name)
    private readonly interviewTemplateModel: Model<InterviewTemplate>,
    @InjectModel(Interview.name)
    private readonly interviewModel: Model<Interview>,
    @InjectModel(Organisation.name)
    private readonly organisationModel: Model<Organisation>,
    @InjectConnection() private readonly connection: mongoose.Connection,
  ) {}

  async seed(): Promise<any> {
    const session = await this.connection.startSession();
    session.startTransaction();
    try {
      const organisation = await this.organisationModel.create(
        DataFactory.createForClass(Organisation).generate(1)[0],
      );

      const advisors = advisorsData.map(
        (advisor) =>
          DataFactory.createForClass(Advisor).generate(1, {
            advisor,
            organisation,
          })[0],
      );

      const createdAdvisors = await this.advisorModel.insertMany(advisors);

      const pages = Object.values(PagesEnum).filter(
        (page) => page !== PagesEnum.CUSTOM_QUESTIONS,
      );

      const interviewTemplate = await this.interviewTemplateModel.create(
        DataFactory.createForClass(InterviewTemplate).generate(1, { pages }),
      );

      const client = await this.clientModel.create(
        DataFactory.createForClass(Client).generate(1, {
          advisor: createdAdvisors[0],
          organisation,
        })[0],
      );

      const interview = DataFactory.createForClass(Interview).generate(1, {
        template: interviewTemplate[0],
        clientId: client._id,
      });

      await this.interviewModel.create(interview);
    } catch (error) {
      session.abortTransaction();
    } finally {
      console.log('Seeding AdvisorsOrgSeeder completed.');
      session.endSession();
    }
  }

  async drop(): Promise<any> {
    await this.advisorModel.deleteMany({});
    return this.organisationModel.deleteMany({});
  }
}
