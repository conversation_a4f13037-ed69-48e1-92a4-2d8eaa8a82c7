WORK_ENV=staging  # staging, production
API_BASE_URL="http://localhost:3000"
#######################
#          mail       #
#######################

MAIL_HOST=smtp.example.com
MAIL_USER=<EMAIL>
MAIL_PASSWORD=topsecret
MAIL_FROM=<EMAIL>

#######################
#         db          #
#######################

DATABASE_URL="mongodb://mongo1:30001,mongo2:30002,mongo3:30003/test?replicaSet=my-replica-set"

#######################
#         sms         #
#######################

TWILIO_PHONE_NUMBER=""
TWILIO_ACCOUNT_SID=""
TWILIO_AUTH_TOKEN=""

#######################
#      docusign       #
#######################

DOCUSIGN_CLIENT_ID=""
DOCUSIGN_CLIENT_SECRET=""
DOCUSIGN_API_BASE_URL="https://demo.docusign.net/restapi"
DOCUSIGN_OAUTH_ACCESS_TOKEN_URI="https://account-d.docusign.com/oauth/token"
DOCUSIGN_OAUTH_REDIRECT_URI="http://localhost:3000/integrations/docusign/callback"
DOCUSIGN_CALLBACK_REDIRECT_URI="http://localhost:8080"
DOCUSIGN_CALLBACK_ERROR_REDIRECT_URI="http://localhost:8080/error"
DOCUSIGN_STATUS_WEBHOOK_URI="http://localhost:3000/integrations/docusign/status/webhook"

#######################
#      redtail        #
#######################

REDTAIL_API_KEY=""
REDTAIL_BASE_URL=""

#######################
#         aws         #
#######################

# config
AWS_REGION="us-east-1"

# s3
AWS_S3_BUCKET="onbord-docusign-advisory-documents"

# cognito
AWS_COGNITO_USER_POOL_ID="us-east-1_UoPDBiY1a"
AWS_COGNITO_CLIENT_ID="48f22d29070j6kjgc6s4ohi91f"
AWS_COGNITO_JWKS_URL="https://cognito-idp.us-east-1.amazonaws.com/us-east-1_UoPDBiY1a"

# kms
AWS_KMS_KEY_ID=""

#S3 Bucket for Org Docs
ORG_DOCS_BUCKET_NAME="test-bucket-onbord"

WEALTHBOX_FRONTEND_URL="https://app.crmworkspace.com/"
WEALTHBOX_BASE_URL="https://api.crmworkspace.com/v1"
WEALTHBOX_BASE_AUTH_URL="https://app.crmworkspace.com"
WEALTHBOX_OAUTH_REDIRECT_URI="https://localhost:3000/integrations/wealthbox/callback"
WEALTHBOX_CLIENT_ID="XYZ"
WEALTHBOX_CLIENT_SECRET="XYZ"
WEALTHBOX_CALLBACK_REDIRECT_URI="http://localhost:8080"
WEALTHBOX_CALLBACK_ERROR_REDIRECT_URI="http://localhost:8080/error"