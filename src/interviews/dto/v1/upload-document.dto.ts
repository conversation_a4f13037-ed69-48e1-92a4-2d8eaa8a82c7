import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { AccountFeaturesEnum } from 'src/shared/types/accounts/account-features.enum';

export class UploadDocumentDto {
  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  documentName: string;

  @IsString()
  @IsOptional()
  @ApiProperty()
  accountId?: string;

  @IsOptional()
  @IsEnum(AccountFeaturesEnum)
  @ApiProperty({ enum: AccountFeaturesEnum, enumName: 'AccountFeaturesEnum' })
  feature?: AccountFeaturesEnum;
}
