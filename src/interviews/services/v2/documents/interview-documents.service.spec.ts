import { Test, TestingModule } from '@nestjs/testing';
import { HttpException, HttpStatus } from '@nestjs/common';
import { getModelToken, getConnectionToken } from '@nestjs/mongoose';
import { ConfigService } from '@nestjs/config';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { getQueueToken } from '@nestjs/bullmq';
import { Model, ClientSession, Connection } from 'mongoose';
import { InterviewDocumentsService } from './interview-documents.service';
import { Interview } from '../../../schemas/v1/interview.schema';
import { Asset } from 'src/shared/schemas/asset.schema';
import { ClientsV1Service } from 'src/clients/clients.service';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { CacheService } from 'src/shared/services/cache.service';
import { UploadDocumentDto } from '../../../dto/v1/upload-document.dto';
import { RequiredDocumentDto } from '../../../dto/v1/required-document.dto';
import { AccountFeaturesEnum } from 'src/shared/types/accounts/account-features.enum';
import { AccountClientDocumentsEnum, AccountAdvisoryDocumentsEnum } from 'src/shared/types/accounts/account-documents.enum';
import { AssetTypeEnum } from 'src/shared/types/general/asset-types.enum';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { ClientQueueJobType } from 'src/clients/types/client-queue-job.enum';
import { CLIENT_QUEUE } from 'src/shared/constants/client.constant';
import AWS from 'aws-sdk';
import * as fs from 'fs';
import * as fileUtils from 'src/utils/file/file.util';
import { getFileCacheKey } from 'src/utils/cache/cache.util';

// Mock AWS SDK
jest.mock('aws-sdk', () => ({
  S3: jest.fn(() => ({
    getObject: jest.fn(() => ({
      createReadStream: jest.fn(() => {
        const { PassThrough } = require('stream');
        const stream = new PassThrough();
        setTimeout(() => {
          stream.write(Buffer.from('mock pdf content'));
          stream.end();
        }, 0);
        return stream;
      }),
    })),
  })),
  KMS: jest.fn(() => ({
    encrypt: jest.fn(),
    decrypt: jest.fn(),
  })),
}));

// Mock file system operations
jest.mock('fs', () => ({
  ...jest.requireActual('fs'),
  createWriteStream: jest.fn(() => {
    const { PassThrough } = require('stream');
    const stream = new PassThrough();
    stream.on('pipe', () => {
      setTimeout(() => stream.emit('finish'), 10);
    });
    return stream;
  }),
  existsSync: jest.fn(),
}));

// Mock file utilities
jest.mock('src/utils/file/file.util', () => ({
  mkdirAsync: jest.fn().mockResolvedValue(undefined),
  unlinkAsync: jest.fn().mockResolvedValue(undefined),
}));

describe('InterviewDocumentsService', () => {
  let service: InterviewDocumentsService;
  let interviewModel: Model<Interview>;
  let assetModel: Model<Asset>;
  let clientsService: ClientsV1Service;
  let docusignService: DocusignService;
  let organisationsService: OrganisationsService;
  let cacheService: CacheService;
  let configService: ConfigService;
  let connection: Connection;
  let clientQueue: any;
  let logger: any;
  let mockSession: ClientSession;

  const mockInterviewId = '507f1f77bcf86cd799439011';
  const mockClientId = '507f1f77bcf86cd799439012';
  const mockOrganisationId = '507f1f77bcf86cd799439013';
  const mockAdvisorId = '507f1f77bcf86cd799439014';
  const mockEnvelopeId = 'envelope-123';

  const mockInterview: Partial<Interview> = {
    _id: mockInterviewId,
    client: { _id: { toString: () => mockClientId } } as any,
    envelopeId: mockEnvelopeId,
    documents: [],
    isPrimary: true,
  };

  const mockClient = {
    _id: mockClientId,
    organisationId: { toString: () => mockOrganisationId },
    primaryAdvisor: { id: { toString: () => mockAdvisorId } },
    fileUploadsNo: 5,
  };

  const mockFiles: Express.Multer.File[] = [
    {
      fieldname: 'files',
      originalname: 'test.pdf',
      encoding: '7bit',
      mimetype: 'application/pdf',
      buffer: Buffer.from('test'),
      size: 1024,
    } as Express.Multer.File,
  ];

  beforeEach(async () => {
    // Create mock session
    mockSession = {
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      abortTransaction: jest.fn(),
      endSession: jest.fn(),
    } as any;

    // Create mocks
    const mockInterviewModelMethods = {
      updateOne: jest.fn().mockResolvedValue({ acknowledged: true }),
      findById: jest.fn().mockResolvedValue(mockInterview),
    };

    const mockAssetModelMethods = {
      findOne: jest.fn(() => ({
        exec: jest.fn().mockResolvedValue({
          assetId: AccountAdvisoryDocumentsEnum.SchwabIraAccountApplication,
          assetType: AssetTypeEnum.Document,
        }),
      })),
    };

    logger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
    };

    clientQueue = {
      add: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InterviewDocumentsService,
        {
          provide: getModelToken(Interview.name),
          useValue: mockInterviewModelMethods,
        },
        {
          provide: getModelToken(Asset.name),
          useValue: mockAssetModelMethods,
        },
        {
          provide: ClientsV1Service,
          useValue: {
            findOne: jest.fn().mockResolvedValue(mockClient),
            update: jest.fn().mockResolvedValue({}),
          },
        },
        {
          provide: DocusignService,
          useValue: {
            addClientDocuments: jest.fn().mockResolvedValue({}),
          },
        },
        {
          provide: OrganisationsService,
          useValue: {
            findOne: jest.fn().mockResolvedValue({
              assets: [
                {
                  assetType: AssetTypeEnum.AdvisoryAgreement,
                  assetId: 'advisory-agreement-123',
                  assetKey: 'org/advisory-agreement.pdf',
                },
              ],
            }),
          },
        },
        {
          provide: CacheService,
          useValue: {
            has: jest.fn().mockReturnValue(false),
            get: jest.fn(),
            set: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('test-bucket'),
          },
        },
        {
          provide: getConnectionToken(),
          useValue: {
            startSession: jest.fn().mockResolvedValue(mockSession),
          },
        },
        {
          provide: getQueueToken(CLIENT_QUEUE.NAME),
          useValue: clientQueue,
        },
        {
          provide: WINSTON_MODULE_PROVIDER,
          useValue: logger,
        },
      ],
    }).compile();

    service = module.get<InterviewDocumentsService>(InterviewDocumentsService);
    interviewModel = module.get<Model<Interview>>(getModelToken(Interview.name));
    assetModel = module.get<Model<Asset>>(getModelToken(Asset.name));
    clientsService = module.get<ClientsV1Service>(ClientsV1Service);
    docusignService = module.get<DocusignService>(DocusignService);
    organisationsService = module.get<OrganisationsService>(OrganisationsService);
    cacheService = module.get<CacheService>(CacheService);
    configService = module.get<ConfigService>(ConfigService);
    connection = module.get<Connection>(getConnectionToken());
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('upload', () => {
    it('should successfully upload documents with existing envelope', async () => {
      const dto: UploadDocumentDto = {
        documentName: 'test-document',
        accountId: 'account-123',
        feature: AccountFeaturesEnum.MoneyLink,
      };

      const result = await service.upload(mockInterview as Interview, mockFiles, dto);

      expect(result).toBe('Document successfully uploaded.');
      expect(docusignService.addClientDocuments).toHaveBeenCalledWith({
        organisationId: mockOrganisationId,
        advisorId: mockAdvisorId,
        envelopeId: mockEnvelopeId,
        files: mockFiles,
      });
      expect(interviewModel.updateOne).toHaveBeenCalled();
      expect(clientsService.update).toHaveBeenCalledWith(
        mockClientId,
        { fileUploadsNo: 6, status: ClientStatusEnum.Sent },
        mockSession,
      );
      expect(clientQueue.add).toHaveBeenCalledWith(
        ClientQueueJobType.UPDATE_LAST_CONTACT_ACTIVITY_TIMESTAMP,
        { clientId: mockClientId, isPrimary: true },
      );
    });

    it('should throw error when envelope does not exist', async () => {
      const interviewWithoutEnvelope = { ...mockInterview, envelopeId: null };

      await expect(
        service.upload(interviewWithoutEnvelope as Interview, mockFiles),
      ).rejects.toThrow(
        new HttpException('Cannot upload to non-existent envelope', HttpStatus.BAD_REQUEST),
      );
    });

    it('should handle upload without document metadata', async () => {
      const result = await service.upload(mockInterview as Interview, mockFiles);

      expect(result).toBe('Document successfully uploaded.');
      expect(interviewModel.updateOne).not.toHaveBeenCalled();
      expect(clientsService.update).not.toHaveBeenCalled();
    });

    it('should handle transaction properly on success', async () => {
      const dto: UploadDocumentDto = {
        documentName: 'test-document',
      };

      await service.upload(mockInterview as Interview, mockFiles, dto);

      expect(connection.startSession).toHaveBeenCalled();
      expect(mockSession.startTransaction).toHaveBeenCalled();
      expect(mockSession.commitTransaction).toHaveBeenCalled();
      expect(mockSession.endSession).toHaveBeenCalled();
    });

    it('should handle transaction properly on failure', async () => {
      const error = new Error('Database error');
      jest.spyOn(docusignService, 'addClientDocuments').mockRejectedValueOnce(error);

      await expect(
        service.upload(mockInterview as Interview, mockFiles),
      ).rejects.toThrow();

      expect(mockSession.abortTransaction).toHaveBeenCalled();
      expect(mockSession.endSession).toHaveBeenCalled();
    });

    it('should use provided session if available', async () => {
      const providedSession = { ...mockSession } as ClientSession;

      await service.upload(mockInterview as Interview, mockFiles, undefined, providedSession);

      expect(connection.startSession).not.toHaveBeenCalled();
      expect(providedSession.startTransaction).not.toHaveBeenCalled();
      expect(providedSession.commitTransaction).not.toHaveBeenCalled();
      expect(providedSession.endSession).not.toHaveBeenCalled();
    });
  });

  describe('selectClientFilesToFetch', () => {
    it('should select files for MoneyLink feature', () => {
      const accountId = 'account-123';
      const features = [AccountFeaturesEnum.MoneyLink];

      const result = service.selectClientFilesToFetch(features, accountId);

      expect(result).toEqual([
        {
          documentType: AccountClientDocumentsEnum.CancelledCheck,
          accountId,
          feature: AccountFeaturesEnum.MoneyLink,
        },
      ]);
    });

    it('should select files for Acat feature', () => {
      const accountId = 'account-123';
      const features = [AccountFeaturesEnum.Acat];

      const result = service.selectClientFilesToFetch(features, accountId);

      expect(result).toEqual([
        {
          documentType: AccountClientDocumentsEnum.BrokerageStatement,
          accountId,
          feature: AccountFeaturesEnum.Acat,
        },
      ]);
    });

    it('should handle IraDistribution feature (no files)', () => {
      const accountId = 'account-123';
      const features = [AccountFeaturesEnum.IraDistribution];

      const result = service.selectClientFilesToFetch(features, accountId);

      expect(result).toEqual([]);
    });

    it('should throw error for unknown feature', () => {
      const accountId = 'account-123';
      const features = ['UnknownFeature' as AccountFeaturesEnum];

      expect(() => service.selectClientFilesToFetch(features, accountId)).toThrow(
        new HttpException(
          'Account opening files not found for account feature UnknownFeature',
          HttpStatus.NOT_FOUND,
        ),
      );
    });

    it('should handle multiple features', () => {
      const accountId = 'account-123';
      const features = [AccountFeaturesEnum.MoneyLink, AccountFeaturesEnum.Acat];

      const result = service.selectClientFilesToFetch(features, accountId);

      expect(result).toHaveLength(2);
      expect(result).toContainEqual({
        documentType: AccountClientDocumentsEnum.CancelledCheck,
        accountId,
        feature: AccountFeaturesEnum.MoneyLink,
      });
      expect(result).toContainEqual({
        documentType: AccountClientDocumentsEnum.BrokerageStatement,
        accountId,
        feature: AccountFeaturesEnum.Acat,
      });
    });
  });

  describe('getAccountAdvisoryFileTypes', () => {
    it('should return file types for IRA accounts', () => {
      const accountType = 'Ira';
      const features: AccountFeaturesEnum[] = [];

      const result = service.getAccountAdvisoryFileTypes(accountType, features);

      expect(result).toContain(AccountAdvisoryDocumentsEnum.SchwabIraAccountApplication);
    });

    it('should return file types for Roth IRA accounts', () => {
      const accountType = 'RothIra';
      const features: AccountFeaturesEnum[] = [];

      const result = service.getAccountAdvisoryFileTypes(accountType, features);

      expect(result).toContain(AccountAdvisoryDocumentsEnum.SchwabIraAccountApplication);
    });

    it('should return file types for Brokerage accounts', () => {
      const accountType = 'SingleNameBrokerage';
      const features: AccountFeaturesEnum[] = [];

      const result = service.getAccountAdvisoryFileTypes(accountType, features);

      expect(result).toContain(AccountAdvisoryDocumentsEnum.SchwabOnePersonalAccountApplication);
    });

    it('should include MoneyLink form for MoneyLink feature', () => {
      const accountType = 'SingleNameBrokerage';
      const features = [AccountFeaturesEnum.MoneyLink];

      const result = service.getAccountAdvisoryFileTypes(accountType, features);

      expect(result).toContain(AccountAdvisoryDocumentsEnum.SchwabMoneyLinkElectronicTransferForm);
    });

    it('should include transfer form for Acat feature', () => {
      const accountType = 'Ira';
      const features = [AccountFeaturesEnum.Acat];

      const result = service.getAccountAdvisoryFileTypes(accountType, features);

      expect(result).toContain(AccountAdvisoryDocumentsEnum.SchwabTransferAccountForm);
    });

    it('should include distribution form for IraDistribution feature', () => {
      const accountType = 'Ira';
      const features = [AccountFeaturesEnum.IraDistribution];

      const result = service.getAccountAdvisoryFileTypes(accountType, features);

      expect(result).toContain(AccountAdvisoryDocumentsEnum.SchwabIraDistributionForm);
    });

    it('should throw error for unknown feature', () => {
      const accountType = 'Ira';
      const features = ['UnknownFeature' as AccountFeaturesEnum];

      expect(() => service.getAccountAdvisoryFileTypes(accountType, features)).toThrow(
        new HttpException(
          'Account opening files not found for account feature UnknownFeature',
          HttpStatus.NOT_FOUND,
        ),
      );
    });

    it('should handle multiple features', () => {
      const accountType = 'Ira';
      const features = [
        AccountFeaturesEnum.MoneyLink,
        AccountFeaturesEnum.Acat,
        AccountFeaturesEnum.IraDistribution,
      ];

      const result = service.getAccountAdvisoryFileTypes(accountType, features);

      expect(result).toContain(AccountAdvisoryDocumentsEnum.SchwabIraAccountApplication);
      expect(result).toContain(AccountAdvisoryDocumentsEnum.SchwabMoneyLinkElectronicTransferForm);
      expect(result).toContain(AccountAdvisoryDocumentsEnum.SchwabTransferAccountForm);
      expect(result).toContain(AccountAdvisoryDocumentsEnum.SchwabIraDistributionForm);
    });
  });

  describe('addRequiredDocument', () => {
    it('should add a required document to interview', async () => {
      const dto: RequiredDocumentDto = {
        document: AccountClientDocumentsEnum.CancelledCheck,
      };

      await service.addRequiredDocument(mockInterviewId, dto);

      expect(interviewModel.updateOne).toHaveBeenCalledWith(
        { _id: mockInterviewId },
        {
          $push: {
            documents: {
              name: dto.document,
              updatedAt: null,
            },
          },
        },
      );
    });
  });

  describe('removeRequiredDocument', () => {
    it('should remove a required document from interview', async () => {
      const dto: RequiredDocumentDto = {
        document: AccountClientDocumentsEnum.CancelledCheck,
      };

      const interviewWithDocs = {
        ...mockInterview,
        documents: [
          { name: AccountClientDocumentsEnum.CancelledCheck, updatedAt: new Date() },
          { name: AccountClientDocumentsEnum.BrokerageStatement, updatedAt: null },
        ],
      };

      jest.spyOn(interviewModel, 'findById').mockResolvedValueOnce(interviewWithDocs);

      await service.removeRequiredDocument(mockInterviewId, dto);

      expect(interviewModel.updateOne).toHaveBeenCalledWith(
        { _id: mockInterviewId },
        {
          $set: {
            documents: [
              { name: AccountClientDocumentsEnum.BrokerageStatement, updatedAt: null },
            ],
          },
        },
      );
    });

    it('should throw error if interview not found', async () => {
      const dto: RequiredDocumentDto = {
        document: AccountClientDocumentsEnum.CancelledCheck,
      };

      jest.spyOn(interviewModel, 'findById').mockResolvedValueOnce(null);

      await expect(service.removeRequiredDocument(mockInterviewId, dto)).rejects.toThrow(
        new HttpException('Interview not found', HttpStatus.NOT_FOUND),
      );
    });
  });

  describe('getFilesFromS3', () => {
    beforeEach(() => {
      (fs.existsSync as jest.Mock).mockReturnValue(false);
    });

    it('should return file from cache if available', async () => {
      const cachedFile: Express.Multer.File = {
        fieldname: 'files',
        originalname: 'cached.pdf',
        encoding: '7bit',
        mimetype: 'application/pdf',
        path: '/cached/path.pdf',
      } as Express.Multer.File;

      jest.spyOn(cacheService, 'has').mockReturnValue(true);
      jest.spyOn(cacheService, 'get').mockResolvedValue(cachedFile);

      const result = await service.getFilesFromS3([
        AccountAdvisoryDocumentsEnum.SchwabIraAccountApplication,
      ]);

      expect(result).toEqual([cachedFile]);
      expect(logger.info).toHaveBeenCalledWith(expect.stringContaining('File fetched from cache'));
    });

    it('should download from S3 if not in cache', async () => {
      const fileNames = [AccountAdvisoryDocumentsEnum.SchwabIraAccountApplication];

      const result = await service.getFilesFromS3(fileNames);

      expect(result).toHaveLength(1);
      expect(result[0].originalname).toBe(`${fileNames[0]}.pdf`);
      expect(fileUtils.mkdirAsync).toHaveBeenCalled();
      expect(cacheService.set).toHaveBeenCalled();
    });

    it('should handle advisory agreement with organisation ID', async () => {
      const result = await service.getFilesFromS3(
        [AccountAdvisoryDocumentsEnum.AdvisoryAgreement],
        mockOrganisationId,
      );

      expect(organisationsService.findOne).toHaveBeenCalledWith(mockOrganisationId);
      expect(result).toHaveLength(1);
      expect(result[0].originalname).toBe('AdvisoryAgreement.pdf');
    });

    it('should handle cache error gracefully', async () => {
      const cacheError = new Error('Cache error');
      jest.spyOn(cacheService, 'has').mockReturnValue(true);
      jest.spyOn(cacheService, 'get').mockRejectedValue(cacheError);

      const result = await service.getFilesFromS3([
        AccountAdvisoryDocumentsEnum.SchwabIraAccountApplication,
      ]);

      expect(logger.error).toHaveBeenCalledWith(
        expect.stringContaining('Error fetching file from cache'),
        cacheError,
      );
      expect(result).toHaveLength(1);
    });

    it('should handle S3 download error', async () => {
      const s3Error = new Error('S3 download failed');

      // Mock AWS.S3 constructor to return our mock instance
      (AWS.S3 as jest.Mock).mockImplementation(() => ({
        getObject: jest.fn().mockReturnValue({
          createReadStream: jest.fn(() => {
            const { PassThrough } = require('stream');
            const stream = new PassThrough();
            setTimeout(() => stream.emit('error', s3Error), 0);
            return stream;
          }),
        }),
      }));

      await expect(
        service.getFilesFromS3([AccountAdvisoryDocumentsEnum.SchwabIraAccountApplication]),
      ).rejects.toThrow(HttpException);
    });

    it('should create directory if it does not exist', async () => {
      (fs.existsSync as jest.Mock).mockReturnValue(false);

      await service.getFilesFromS3([AccountAdvisoryDocumentsEnum.SchwabIraAccountApplication]);

      expect(fileUtils.mkdirAsync).toHaveBeenCalledWith(
        expect.any(String),
        { recursive: true },
      );
    });
  });

  describe('getAccountOpeningAdvisoryFiles', () => {
    it('should process multiple accounts', async () => {
      const accounts = [
        {
          type: 'Ira',
          features: [AccountFeaturesEnum.MoneyLink],
          label: 'IRA Account 1',
        },
        {
          type: 'SingleNameBrokerage',
          features: [AccountFeaturesEnum.Acat],
          label: 'Brokerage Account',
        },
      ] as any;

      const mockS3Files: Express.Multer.File[] = [
        {
          fieldname: 'files',
          originalname: 'SchwabIraAccountApplication.pdf',
          encoding: '7bit',
          mimetype: 'application/pdf',
          path: '/path1.pdf',
        } as Express.Multer.File,
        {
          fieldname: 'files',
          originalname: 'SchwabMoneyLinkElectronicTransferForm.pdf',
          encoding: '7bit',
          mimetype: 'application/pdf',
          path: '/path2.pdf',
        } as Express.Multer.File,
      ];

      jest.spyOn(service, 'getFilesFromS3').mockResolvedValue(mockS3Files);

      const result = await service.getAccountOpeningAdvisoryFiles(accounts, mockOrganisationId);

      expect(service.getFilesFromS3).toHaveBeenCalledTimes(2);
      expect(result).toHaveLength(4); // 2 files for each account
    });

    it('should add account suffix to file names', async () => {
      const accounts = [
        {
          type: 'Ira',
          features: [],
          label: 'Primary IRA',
        },
      ] as any;

      const mockFile: Express.Multer.File = {
        fieldname: 'files',
        originalname: 'SchwabIraAccountApplication.pdf',
        encoding: '7bit',
        mimetype: 'application/pdf',
        path: '/path.pdf',
      } as Express.Multer.File;

      jest.spyOn(service, 'getFilesFromS3').mockResolvedValue([mockFile]);

      const result = await service.getAccountOpeningAdvisoryFiles(accounts);

      expect(result[0].originalname).toMatch(/SchwabIraAccountApplication.*\.pdf/);
    });

    it('should handle empty accounts array', async () => {
      const getFilesFromS3Spy = jest.spyOn(service, 'getFilesFromS3');

      const result = await service.getAccountOpeningAdvisoryFiles([]);

      expect(result).toEqual([]);
      expect(getFilesFromS3Spy).not.toHaveBeenCalled();

      getFilesFromS3Spy.mockRestore();
    });

    it('should skip accounts without advisory file types', async () => {
      const getFilesFromS3Spy = jest.spyOn(service, 'getFilesFromS3');

      const accounts = [
        {
          type: 'UnknownType',
          features: [],
          label: 'Unknown Account',
        },
      ] as any;

      const result = await service.getAccountOpeningAdvisoryFiles(accounts);

      expect(result).toEqual([]);
      expect(getFilesFromS3Spy).not.toHaveBeenCalled();

      getFilesFromS3Spy.mockRestore();
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors in upload', async () => {
      const dbError = new Error('Database connection failed');
      jest.spyOn(connection, 'startSession').mockRejectedValueOnce(dbError);

      await expect(
        service.upload(mockInterview as Interview, mockFiles),
      ).rejects.toThrow(new HttpException(dbError.message, HttpStatus.INTERNAL_SERVER_ERROR));
    });

    it('should handle missing asset in getFilesFromS3', async () => {
      jest.spyOn(assetModel, 'findOne').mockReturnValue({
        exec: jest.fn().mockResolvedValue(null),
      } as any);

      // Should still work but with undefined assetId
      const result = await service.getFilesFromS3([
        AccountAdvisoryDocumentsEnum.SchwabIraAccountApplication,
      ]);

      expect(result).toHaveLength(1);
    });

    it('should handle file write stream errors', async () => {
      const writeError = new Error('Write failed');
      const unlinkSpy = jest.spyOn(fileUtils, 'unlinkAsync');

      (fs.createWriteStream as jest.Mock).mockReturnValue({
        on: jest.fn((event, handler) => {
          if (event === 'error') {
            setTimeout(() => handler(writeError), 0);
          }
          return { on: jest.fn() };
        }),
      });

      await expect(
        service.getFilesFromS3([AccountAdvisoryDocumentsEnum.SchwabIraAccountApplication]),
      ).rejects.toThrow();

      // Give some time for the async error handling
      await new Promise(resolve => setTimeout(resolve, 50));
      expect(unlinkSpy).toHaveBeenCalled();

      unlinkSpy.mockRestore();
    });
  });
});