import { Injectable } from '@nestjs/common';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { getPageVariableProvider } from './providers.mapper';
import { VariableDefinition } from './types/variable-definition.interface';

@Injectable()
export class VariablesService {
  findByPage(pageName: PagesEnum): Record<string, VariableDefinition> {
    return getPageVariableProvider(pageName).getAvailableVariables();
  }
}