import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';
import { PhoneMapperRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/phone-request.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { phoneTypeToPhoneNumberKind } from 'src/integrations/crm/wealthbox/utils/phone-kind-mapper';
import { WealthboxPhoneNumber } from 'src/integrations/crm/wealthbox/types';
import { PhoneTypeEnum } from 'src/integrations/crm/types/generic/phone.type';
import { isEmpty } from 'lodash';

export class WealthboxPhoneMapper implements FormUpdateMapper {
  constructor(private WealthboxCrmService: WealthboxCRM) {}

  /**
   * Maps page data to create mobile numbers in Wealthbox CRM for a given client and organisation.
   * @param crmClientId The ID of the client in Wealthbox CRM.
   * @param organisation The organisation associated with the client.
   * @param pageData The page data containing the alternate phone numbers to create.
   * @returns A Promise that resolves when all mobile numbers have been created in Wealthbox CRM.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: PhoneMapperRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    if (isEmpty(pageData?.alternatePhones)) return;

    const phoneNumbers: WealthboxPhoneNumber[] = pageData.alternatePhones.map(
      (phone) => ({
        principal: false,
        address: phone.number,
        kind: phoneTypeToPhoneNumberKind(
          PhoneTypeEnum[phone.type.toUpperCase()],
        ),
      }),
    );

    await this.WealthboxCrmService.contactService.updateContact(
      contact.crmClientId,
      {
        phone_numbers: phoneNumbers,
      },
    );
  }
}
