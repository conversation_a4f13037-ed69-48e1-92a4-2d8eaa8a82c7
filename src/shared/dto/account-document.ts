import { IsEnum } from "class-validator";
import { AccountClientDocumentsEnum } from "../types/accounts/account-documents.enum";
import { ApiProperty } from "@nestjs/swagger";
import { Expose, Type } from "class-transformer";
import { ObjectId } from "mongoose";
import { AccountFeaturesEnum } from "../types/accounts/account-features.enum";
import { AccountTypeEnum } from "../types/accounts/account-type.enum";

export class AccountDocumentViewDto {
  @ApiProperty()
  @IsEnum(AccountClientDocumentsEnum)
  @Expose()
  name: AccountClientDocumentsEnum;

  @ApiProperty()
  @Expose()
  @Type(() => String)
  accountId: ObjectId;

  @ApiProperty()
  @Expose()
  updatedAt?: Date;

  @ApiProperty()
  @IsEnum(AccountFeaturesEnum)
  @Expose()
  feature: AccountFeaturesEnum;

  @ApiProperty()
  @Expose()
  label: string;

  @ApiProperty()
  @IsEnum(AccountTypeEnum)
  @Expose()
  type: AccountTypeEnum;
}