import { isEmpty } from 'lodash';

/**
 * Inserts new data into the user content field.
 *
 * @param userContent - The original user content string.
 * @param newData - The new data to be inserted into the user content.
 * @returns The updated user content string with the new data inserted.
 */
export function insertIntoUserContentField(
  userContent: string,
  newData: object,
): string {
  // Extract existing system data
  const splitContent = userContent ? userContent.split('---\n') : [];
  const existingSystemDataPart = splitContent.length > 1 ? splitContent[1] : '';
  const existingSystemData = existingSystemDataPart
    .split('\n')
    .reduce((acc, line) => {
      const [key, value] = line.split(':').map((s) => s.trim());
      if (key) acc[key] = value;
      return acc;
    }, {});

  // Merge new data with existing system data
  const mergedSystemData = { ...existingSystemData, ...newData };

  // Format the merged system data as a string
  const systemDataEntries = Object.entries(mergedSystemData)
    .map(([key, value]) => `${key}: ${value}`)
    .join('\n');

  // Construct the new user content string
  return `${splitContent[0]}\n---\n${systemDataEntries}`;
}

/**
 * Parses CRM text data and returns an object containing key-value pairs.
 * @param crmText The CRM text data to parse.
 * @returns An object containing key-value pairs parsed from the CRM text data.
 */
export function parseUserContentFieldAsJson(crmText: string): object {
  if (isEmpty(crmText)) return {};

  const systemDataPart = crmText.split('---\n')[1];
  if (!systemDataPart) return {};

  return systemDataPart.split('\n').reduce((acc, line) => {
    const [key, value] = line.split(':').map((s) => s.trim());
    acc[key] = value;
    return acc;
  }, {});
}

/**
 * Retrieves the value associated with the specified key from the CRM text data.
 * @param crmText - The CRM text data to parse.
 * @param key - The key to search for in the CRM text data.
 * @returns The value associated with the specified key, or undefined if the key is not found.
 */
export function getFromUserContentField(
  crmText: string,
  key: string,
): string | undefined {
  const systemData = parseUserContentFieldAsJson(crmText);
  if (!systemData) {
    return undefined;
  }
  return systemData[key];
}

/**
 * Removes a specific key from the CRM text data.
 *
 * @param crmText - The CRM text data to parse and update.
 * @param keyToRemove - The key to be removed from the CRM data.
 * @returns The updated CRM text data without the specified key.
 */
export function removeFromUserContentField(
  crmText: string,
  keyToRemove: string,
): string {
  if (!crmText) return crmText;
  // Extract the user content (everything before the delimiter)
  const userContent = crmText.split('---\n')[0];

  // Parse the system data part
  const systemData = parseUserContentFieldAsJson(crmText);

  // Remove the specified key
  delete systemData[keyToRemove];

  // Format the updated system data as a string
  const updatedSystemDataEntries = Object.entries(systemData)
    .map(([key, value]) => `${key}: ${value}`)
    .join('\n');

  // Construct the new CRM text with the updated system data
  return `${userContent}\n---\n${updatedSystemDataEntries}`;
}
