import { parsePhoneNumber } from 'libphonenumber-js';
import { callbackPromise } from 'nodemailer/lib/shared';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';

export const primaryAdvisorGeneralMappers = {
  'primaryAdvisor.firstName': {
    field: ['primaryAdvisor.firstName'],
  },
  'primaryAdvisor.lastName': {
    field: ['primaryAdvisor.lastName'],
  },
  'primaryAdvisor.fullName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return `${interviewData.primaryAdvisor.firstName} ${interviewData.primaryAdvisor.lastName}`;
    },
  },
  'primaryAdvisor.mobile': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const phone = interviewData.primaryAdvisor.mobile;
      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'primaryAdvisor.email': {
    field: ['primaryAdvisor.email'],
  },
};
