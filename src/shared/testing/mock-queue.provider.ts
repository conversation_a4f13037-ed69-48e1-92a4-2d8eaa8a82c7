import { Provider } from '@nestjs/common';
import { getQueueToken } from '@nestjs/bullmq';
import IORedis from 'ioredis-mock';

const mockRedis = new IORedis();

export const createMockQueueProvider = (queueName: string): Provider => ({
  provide: getQueueToken(queueName),
  useValue: {
    add: jest.fn().mockResolvedValue({}),
    addBulk: jest.fn().mockResolvedValue([]),
    opts: {
      connection: mockRedis
    },
    client: mockRedis,
    isReady: jest.fn().mockReturnValue(true)
  },
}); 