import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { SalesforceCRM } from 'src/integrations/crm/salesforce/crm.salesforce';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

interface JobDescriptionPageData {
  type: string;
  description: string;
  // responsibilities?: string;
  // department?: string;
}

export class SalesforceJobDescriptionMapper implements FormUpdateMapper {
  constructor(private salesforceCRM: SalesforceCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: JobDescriptionPageData,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    await this.salesforceCRM.contactService.updateContact(contact.crmClientId, {
      ...(pageData.type && { Onbord_Title__c: pageData.type }),
      Onbord_Job_Description__c: pageData.description,
      // Job_Responsibilities__c: pageData.responsibilities,
      // Department__c: pageData.department,
    });
  }
} 