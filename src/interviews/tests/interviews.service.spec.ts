import { Test, TestingModule } from '@nestjs/testing';
import { HttpException } from '@nestjs/common';
import mongoose, { Model } from 'mongoose';
import { InterviewsService } from 'src/interviews/interviews.service';
import { CreateInterviewDto } from 'src/interviews/dto/v1/create-interview.dto';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { Interview } from 'src/interviews/schemas/v1/interview.schema';
import { getModelToken, getConnectionToken } from '@nestjs/mongoose';
import { InterviewTemplatesService } from 'src/interview-templates/interview-templates.service';
import { ClientsV1Service } from 'src/clients/clients.service';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { Asset } from 'src/shared/schemas/asset.schema';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { ConfigService } from '@nestjs/config/dist/config.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { organisationMock } from 'src/organisations/tests/mocks/organisation.mock';
import { MailService } from 'src/notifications/mail/mail.service';
import { ClsService } from 'nestjs-cls';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { mockAdvisorsCrudService } from 'src/advisors/tests/mocks/advisors.service.mock';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { CacheService } from 'src/shared/services/cache.service';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { AdvisorsDocusignService } from 'src/advisors/services/advisors.docusign.service';
import { INTERVIEW_QUEUE } from '../constants/interview-queue.constant';
import { createMockQueueProvider } from '../../shared/testing/mock-queue.provider';
import { getQueueToken } from '@nestjs/bullmq';
import { createMockRedisConnection } from '../../shared/testing/mock-redis';
import { CLIENT_QUEUE } from 'src/shared/constants/client.constant';

describe('InterviewsService', () => {
  let service: InterviewsService;

  let assetModel: Model<Asset>;
  let mockModel,
    mockTemplateService,
    mockClientsV1Service,
    mockCrmService,
    mockMailService,
    mockClsService,
    mockAdvisorsDocusignService;

  const mockClient = {
    _id: 1,
    primaryAdvisor: { id: 1 },
    primaryContact: {
      crmClientId: '1',
    },
    organisationId: 5,
  };

  const mockPopulate = jest.fn().mockReturnThis();
  const mockToObject = jest.fn().mockReturnThis();
  const mockSession = jest.fn().mockReturnThis();
  const mockSort = jest.fn().mockReturnThis();
  beforeEach(async () => {
    mockModel = {
      create: jest.fn(),
      find: jest.fn(),
      findOne: jest.fn().mockReturnValue({
        sort: mockSort,
        populate: mockPopulate,
        session: mockSession,
        toObject: mockToObject,
      }),
      findByIdAndDelete: jest.fn(),
      findByIdAndUpdate: jest.fn(),
    };

    mockTemplateService = {
      findOne: jest.fn(),
      getDefaultTemplate: jest.fn().mockReturnValue({
        _id: 'xyz',
      }),
      getMergedTemplate: jest.fn().mockReturnValue({
        _id: 'xyz',
        toObject: jest.fn().mockReturnValue({
          pages: [
            {
              name: PagesEnum.NAME,
            },
          ],
        }),
      }),
    };

    mockClientsV1Service = {
      findOne: jest.fn().mockResolvedValue(mockClient),
      updateClientCompletionPercentage: jest.fn(),
      updateLastContactActivityTimestamp: jest.fn(),
    };

    mockCrmService = {
      getCrmInstance: jest.fn(),
      updatePageInfo: jest.fn(),
    };

    mockMailService = {
      sendMail: jest.fn(),
    };

    mockClsService = {
      get: jest.fn(),
    };

    mockAdvisorsDocusignService = {
      findDocusignIntegration: jest.fn(),
    };

    const mockRedis = createMockRedisConnection();
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InterviewsService,
        {
          provide: getQueueToken(INTERVIEW_QUEUE.NAME),
          useValue: {
            add: jest.fn(),
            opts: {
              connection: mockRedis
            },
            client: mockRedis
          }
        },
         {
          provide: getQueueToken(CLIENT_QUEUE.NAME),
          useValue: {
            add: jest.fn(),
            opts: {
              connection: mockRedis
            },
            client: mockRedis
          }
        },
        {
          provide: getConnectionToken(),
          useValue: {
            startSession: jest.fn().mockReturnValue({
              startTransaction: jest.fn(),
              commitTransaction: jest.fn(),
              abortTransaction: jest.fn(),
              endSession: jest.fn(),
            }),
          },
        },
        {
          provide: WINSTON_MODULE_PROVIDER,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
            info: jest.fn(),
          },
        },
        {
          provide: getModelToken(Interview.name),
          useValue: mockModel,
        },
        {
          provide: getModelToken(Asset.name),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: CacheService,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
          },
        },
        {
          provide: InterviewTemplatesService,
          useValue: mockTemplateService,
        },
        {
          provide: ClientsV1Service,
          useValue: mockClientsV1Service,
        },
        {
          provide: AdvisorsCrmService,
          useValue: mockCrmService,
        },
        {
          provide: AdvisorsCrudService,
          useValue: mockAdvisorsCrudService,
        },
        {
          provide: DocusignService,
          useValue: {
            getAccountInfo: jest.fn(),
            upsertEnvelope: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: OrganisationsService,
          useValue: {
            findOne: jest.fn().mockResolvedValue(organisationMock),
          },
        },
        {
          provide: MailService,
          useValue: mockMailService,
        },
        {
          provide: ClsService,
          useValue: mockClsService,
        },
        {
          provide: TransactionManager,
          useValue: {},
        },
        {
          provide: AdvisorsDocusignService,
          useValue: mockAdvisorsDocusignService,
        },
      ],
    }).compile();

    service = module.get<InterviewsService>(InterviewsService);
    assetModel = module.get<Model<Asset>>(getModelToken(Asset.name));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should create an interview', async () => {
    const dto: CreateInterviewDto = {
      docusignSelected: true,
      customTemplates: ['1'],
      clientId: '1',
      email: '',
      name: '',
      accounts: [],
    };
    mockPopulate.mockResolvedValue(mockClient);
    mockTemplateService.findOne.mockResolvedValueOnce({
      pages: [
        {
          name: PagesEnum.NAME,
        },
      ],
      toObject: jest.fn().mockReturnValue({
        pages: [
          {
            name: PagesEnum.NAME,
          },
        ],
      }),
    });
    mockModel.create.mockResolvedValue(['createdInterview']);
    mockClientsV1Service.findOne.mockResolvedValue({
      _id: dto.clientId,
      primaryContact: {
        accounts: dto.accounts,
      },
    });
    const result = await service.create(dto);

    expect(result).toEqual('createdInterview');
    expect(mockModel.create).toHaveBeenCalled();
  });

  it('should find all interviews', async () => {
    mockModel.find.mockResolvedValue(['interview1', 'interview2']);

    const result = await service.findAll();

    expect(result).toEqual(['interview1', 'interview2']);
    expect(mockModel.find).toHaveBeenCalled();
  });

  it('should find one interview', async () => {
    const mockInterview = {
      _id: '1',
      client: mockClient,
    };
    const filter = { _id: '1' };
    mockPopulate.mockReturnThis();
    mockToObject.mockReturnValueOnce({ _id: '1' });
    const result = await service.findOne(filter, { x: 'x' } as any);
    expect(mockClientsV1Service.findOne).toHaveBeenCalled();
    expect(result).toEqual(mockInterview);
  });

  it('should throw exception when interview not found', async () => {
    mockPopulate.mockReturnThis();
    mockSort.mockReturnThis();
    mockSession.mockResolvedValueOnce(null);
    await expect(
      service.findOne({ _id: '1' }, { x: '' } as any),
    ).resolves.toBeFalsy();
  });


  it('should remove an interview', async () => {
    mockModel.findByIdAndDelete.mockResolvedValue('removedInterview');

    const result = await service.remove('1');

    expect(result).toEqual('removedInterview');
    expect(mockModel.findByIdAndDelete).toHaveBeenCalled();
  });
});
