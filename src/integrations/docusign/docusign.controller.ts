import {
  Body,
  Controller,
  Get,
  Inject,
  Post,
  Query,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { ConfigService } from '@nestjs/config';
import { DocusignService } from './docusign.service';
import { AuthorizationUriDto } from './dto/authorization-uri.dto';
import {
  DocusignJsonSimWebhookDto,
  LegacyDocuSignWebhookDto,
} from './dto/webhook.dto';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

@Controller('integrations/docusign')
export class DocusignController {
  constructor(
    private readonly docusignService: DocusignService,
    private readonly configService: ConfigService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  /**
   * Returns the authorization URI for DocuSign.
   * @param authorizationUriDto - The DTO containing the necessary parameters for the authorization URI.
   * @returns The authorization URI as a string.
   */
  @Post('authorization-uri')
  async getDocusignAuthorizationUri(
    @Body() authorizationUriDto: AuthorizationUriDto,
  ): Promise<string> {
    return this.docusignService.getAuthorizationUri(authorizationUriDto);
  }

  /**
   * Handles the callback from DocuSign authentication flow.
   * @param code - The authorization code returned by DocuSign.
   * @param state - The state parameter passed to DocuSign authentication flow.
   * @param res - The response object to redirect back to frontend.
   * @returns Promise that resolves when the access token is stored and the response is redirected.
   */
  @Get('callback')
  async handleDocusignCallback(
    @Query('code') code: string,
    @Query('state') state: string,
    @Res() res: Response,
  ): Promise<void> {
    try {
      const { advisorId, organisationId } = JSON.parse(state) || {};

      // Exchange the authorization code for an access (and refresh) jwt token
      const accessToken = await this.docusignService.getAccessToken(code);

      // Store the access (and refresh) jwt token in either the advisor or organisation document
      await this.docusignService.storeAccessToken(
        { advisorId, organisationId },
        accessToken,
      );

      // Redirect back to frontend
      res.redirect(
        this.configService.get<string>('DOCUSIGN_CALLBACK_REDIRECT_URI'),
      );
    } catch (error) {
      this.logger.error(error);
      res.redirect(
        this.configService.get<string>('DOCUSIGN_CALLBACK_ERROR_REDIRECT_URI'),
      );
    }
  }

  /**
   * Handles incoming webhook requests from DocuSign triggered by Envelope status changes.
   * @param payload - The payload of the webhook request.
   * @returns A Promise that resolves to a string indicating that the webhook was handled.
   */
  @Post('status/webhook')
  async handleWebhook(
    @Body() payload: LegacyDocuSignWebhookDto | DocusignJsonSimWebhookDto,
  ): Promise<string> {
    await this.docusignService.processWebhook(payload);
    return 'Webhook handled';
  }
}
