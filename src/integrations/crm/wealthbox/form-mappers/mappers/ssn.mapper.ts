import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { insertIntoUserContentField } from 'src/integrations/crm/wealthbox/utils/data-parsing/general-text-field';
import { GenericUserDefinedFieldsEnum } from 'src/integrations/crm/types/generic/user-defined-fields.enum';

export class SSNPageUpdateDto {
  ssn: string;
}
export class WealthboxSSNMapper implements FormUpdateMapper {
  constructor(private WealthboxCrmService: WealthboxCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: SSNPageUpdateDto,
  ) {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const crmContact = await this.WealthboxCrmService.contactService.getContact(
      contact.crmClientId,
    );

    await this.WealthboxCrmService.contactService.updateContact(
      contact.crmClientId,
      {
        background_information: insertIntoUserContentField(
          crmContact.background_info,
          {
            [GenericUserDefinedFieldsEnum.SSN]: pageData.ssn,
          },
        ),
      },
    );
  }
}
