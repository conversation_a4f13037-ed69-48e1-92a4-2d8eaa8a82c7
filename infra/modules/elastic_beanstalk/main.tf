locals {
  ssm_parameters = jsondecode(var.ssm_json)
}

data "aws_caller_identity" "current" {}


resource "aws_iam_role" "elastic_beanstalk_role" {
  name = "aws-elasticbeanstalk-ec2-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Principal = {
          Service = ["ec2.amazonaws.com", "elasticbeanstalk.amazonaws.com"]
        }
        Effect = "Allow"
      }
    ]
  })
}

resource "aws_iam_role_policy" "ssm_role_policy" {
  name = "onbord-backend-${var.environment}-ssm-role-policy"
  role = aws_iam_role.elastic_beanstalk_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "ssm:GetParameters",
          "ssm:GetParameter"
        ]
        Effect   = "Allow"
        Resource = ["arn:aws:ssm:us-east-1:094396706397:parameter/onbord/backend/*"]
      }
    ]
  })
}

resource "aws_iam_policy" "ssm_policy" {
  name = "onbord-backend-${var.environment}-ssm-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "ssm:GetParameters",
          "ssm:GetParameter"
        ]
        Effect   = "Allow"
        Resource = ["arn:aws:ssm:us-east-1:094396706397:parameter/onbord/backend/*"]
      }
    ]
  })
}

resource "aws_iam_policy" "kms_policy" {
  name = "onbord-backend-${var.environment}-kms-policy"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey"
        ],
        Effect   = "Allow",
        Resource = [var.encryption_key_id, "arn:aws:cognito-idp:us-east-1:094396706397:userpool/us-east-1_UoPDBiY1a"]
      }
    ]
  })
}

resource "aws_iam_policy" "cognito_admin_policies_set" {
  name        = "CognitoAdminPoliciesSet-${var.environment}"
  description = "Policy to allow Cognito admin user actions"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "cognito-idp:*",
        ],
        Resource = "arn:aws:cognito-idp:${var.aws_region}:094396706397:userpool/${var.cognito_user_pool_id}"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "cognito_admin_policies_set_attachment" {
  role       = aws_iam_role.elastic_beanstalk_role.name
  policy_arn = aws_iam_policy.cognito_admin_policies_set.arn
}

resource "aws_iam_role_policy_attachment" "elastic_beanstalk_kms_policy" {
  role       = aws_iam_role.elastic_beanstalk_role.name
  policy_arn = aws_iam_policy.kms_policy.arn
}

resource "aws_iam_instance_profile" "elastic_beanstalk_profile" {
  name = "aws-elasticbeanstalk-ec2-profile-${var.environment}"
  role = aws_iam_role.elastic_beanstalk_role.name
}

resource "aws_iam_role_policy_attachment" "elastic_beanstalk_ssm_policy" {
  role       = aws_iam_role.elastic_beanstalk_role.name
  policy_arn = aws_iam_policy.ssm_policy.arn
}

resource "aws_iam_role_policy_attachment" "elastic_beanstalk_policy" {
  role       = aws_iam_role.elastic_beanstalk_role.name
  policy_arn = "arn:aws:iam::aws:policy/AWSElasticBeanstalkWebTier"
}

resource "aws_iam_role_policy_attachment" "elastic_beanstalk_service_policy" {
  role       = aws_iam_role.elastic_beanstalk_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSElasticBeanstalkService"
}

resource "aws_iam_role_policy_attachment" "elastic_beanstalk_service_cloudwatch_policy" {
  role       = aws_iam_role.elastic_beanstalk_role.name
  policy_arn = "arn:aws:iam::aws:policy/CloudWatchLogsFullAccess"
}

resource "aws_iam_role_policy_attachment" "elastic_beanstalk_service_enhanced_health_policy" {
  role       = aws_iam_role.elastic_beanstalk_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSElasticBeanstalkEnhancedHealth"
}
resource "aws_iam_role_policy_attachment" "elastic_beanstalk_service_xray_full_access_policy" {
  role       = aws_iam_role.elastic_beanstalk_role.name
  policy_arn = "arn:aws:iam::aws:policy/AWSXrayFullAccess"
}

resource "aws_iam_policy" "new_relic_policy" {
  name = "onbord-backend-${var.environment}-new-relic-policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "cloudwatch:GetMetricStatistics",
          "cloudwatch:ListMetrics",
          "tag:GetResources",
          "tag:GetTagKeys",
          "tag:GetTagValues",
          "ec2:DescribeInstances",
          "ec2:DescribeRegions",
          "elasticloadbalancing:DescribeLoadBalancers",
          "elasticloadbalancing:DescribeTargetGroups",
          "elasticloadbalancing:DescribeTargetHealth",
          "autoscaling:DescribeAutoScalingGroups"
        ]
        Resource = ["*"]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "elastic_beanstalk_new_relic_policy" {
  role       = aws_iam_role.elastic_beanstalk_role.name
  policy_arn = aws_iam_policy.new_relic_policy.arn
}

resource "aws_s3_bucket" "eb_bucket" {
  bucket = var.source_bundle_bucket
}

resource "aws_s3_bucket_versioning" "eb_bucket_versioning" {
  bucket = aws_s3_bucket.eb_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_object" "object" {
  bucket = aws_s3_bucket.eb_bucket.bucket
  key    = var.source_bundle_key
  source = "../../../${var.source_bundle_key}"
}

resource "aws_elastic_beanstalk_application" "app_vpc" {
  name        = "${var.name}-${var.environment}-vpc"
  description = "Onbord Backend Application VPC - ${var.environment}"
}

resource "aws_elastic_beanstalk_application_version" "app_version_vpc" {
  application = aws_elastic_beanstalk_application.app_vpc.name
  name        = "${var.name}-${var.environment}-vpc-${var.commit_sha}"
  bucket      = aws_s3_bucket.eb_bucket.bucket
  key         = var.source_bundle_key

  depends_on = [
    aws_s3_bucket_object.object
  ]
}

resource "aws_elastic_beanstalk_environment" "ebs_env_vpc" {
  #  TODO:  Detect when code has changed and only then update the environment (currently it always updates)

  name                = "${var.name}-${var.environment}-vpc"
  application         = aws_elastic_beanstalk_application.app_vpc.name
  version_label       = aws_elastic_beanstalk_application_version.app_version_vpc.name
  solution_stack_name = var.solution_stack_name

  lifecycle {
    ignore_changes = [
      setting
    ]
  }


  setting {
    namespace = "aws:elb:listener:443"
    name      = "ListenerProtocol"
    value     = "HTTPS"
  }

  setting {
    namespace = "aws:elb:listener:443"
    name      = "SSLCertificateId"
    value     = var.certificate_arn
  }

  setting {
    namespace = "aws:elb:listener:443"
    name      = "InstancePort"
    value     = "80"
  }

  setting {
    namespace = "aws:elb:listener:443"
    name      = "InstanceProtocol"
    value     = "HTTP"
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "IamInstanceProfile"
    value     = aws_iam_instance_profile.elastic_beanstalk_profile.name
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "InstanceType"
    value     = var.instance_type
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "NODE_ENV"
    value     = "production"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "WORK_ENV"
    value     = var.work_env
  }
  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "ORG_DOCS_BUCKET_NAME"
    value     = var.org_docs_bucket_name
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "CORS_ORIGIN"
    value     = var.origin
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "BASE_DOMAIN_NAME"
    value     = var.domain_name
  }


  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "AWS_COGNITO_USER_POOL_ID"
    value     = var.cognito_user_pool_id
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "AWS_COGNITO_CLIENT_ID"
    value     = var.cognito_user_pool_client_id
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "AWS_REGION"
    value     = var.aws_region
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "AWS_KMS_KEY_ID"
    value     = var.encryption_key_id
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "REDIS_HOST"
    value     = var.redis_host
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "REDIS_PORT"
    value     = var.redis_port
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "REDIS_PASSWORD"
    value     = var.redis_password
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MinSize"
    value     = var.min_size
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "MaxSize"
    value     = var.max_size
  }

  setting {
    namespace = "aws:elasticbeanstalk:environment"
    name      = "EnvironmentType"
    value     = "LoadBalanced"
  }

  setting {
    namespace = "aws:elasticbeanstalk:environment:proxy"
    name      = "ProxyServer"
    value     = "nginx"
  }

  setting {
    namespace = "aws:elasticbeanstalk:application:environment"
    name      = "PORT"
    value     = var.app_port
  }

  setting {
    namespace = "aws:autoscaling:updatepolicy:rollingupdate"
    name      = "RollingUpdateType"
    value     = "Health"
  }

  setting {
    namespace = "aws:elasticbeanstalk:command"
    name      = "DeploymentPolicy"
    value     = "RollingWithAdditionalBatch"
  }

  setting {
    namespace = "aws:elb:policies"
    name      = "ConnectionSettingIdleTimeout"
    value     = "300"
  }
  setting {
    namespace = "aws:elasticbeanstalk:command"
    name      = "BatchSizeType"
    value     = "Fixed"
  }

  setting {
    namespace = "aws:elasticbeanstalk:command"
    name      = "BatchSize"
    value     = "1"
  }

  setting {
    namespace = "aws:autoscaling:launchconfiguration"
    name      = "SecurityGroups"
    value     = var.elastic_beanstalk_security_group_id
  }

  setting {
    namespace = "aws:elasticbeanstalk:cloudwatch:logs"
    name      = "StreamLogs"
    value     = "true"
  }

  setting {
    namespace = "aws:autoscaling:asg"
    name      = "Availability Zones"
    value     = "Any"
  }

  setting {
    namespace = "aws:elasticbeanstalk:healthreporting:system"
    name      = "SystemType"
    value     = "enhanced"
  }

  setting {
    namespace = "aws:elasticbeanstalk:environment:process:default"
    name      = "HealthCheckPath"
    value     = var.healthcheck_url
  }

  setting {
    namespace = "aws:ec2:vpc"
    name      = "VPCId"
    value     = var.vpc_id
  }

  setting {
    namespace = "aws:ec2:vpc"
    name      = "Subnets"
    value     = var.private_subnet_id
  }

  setting {
    namespace = "aws:ec2:vpc"
    name      = "ELBSubnets"
    value     = var.public_subnet_id
  }

  setting {
    namespace = "aws:ec2:vpc"
    name      = "AssociatePublicIpAddress"
    value     = "true"
  }

  setting {
    namespace = "aws:ec2:vpc"
    name      = "ELBScheme"
    value     = "public"
  }

  setting {
    namespace = "aws:elasticbeanstalk:xray"
    name      = "XRayEnabled"
    value     = "true"
  }

  depends_on = [
    aws_s3_bucket_object.object,
  ]
}

data "aws_elb" "eb_lb" {
  name = aws_elastic_beanstalk_environment.ebs_env_vpc.load_balancers[0]
}

// NAT Gateway
resource "aws_eip" "nat" {
  vpc = true
}

resource "aws_nat_gateway" "nat_gateway" {
  allocation_id = aws_eip.nat.id
  subnet_id     = var.public_subnet_id
  tags = {
    Name = "onbord-${var.environment}-nat-gateway"
  }
  depends_on = [aws_internet_gateway.igw]
}

// Internet gateway
resource "aws_internet_gateway" "igw" {
  vpc_id = var.vpc_id
  tags = {
    Name = "onbord-${var.environment}-igw"
  }
}

// Route tables
resource "aws_route_table" "nat_route_table" {
  vpc_id = var.vpc_id

  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.nat_gateway.id
  }

  tags = {
    Name = "onbord-${var.environment}-nat-route-table"
  }
}

resource "aws_route_table" "public_route_table" {
  vpc_id = var.vpc_id
  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.igw.id
  }
  tags = {
    Name = "onbord-${var.environment}-public-igw-route-table"
  }
}

// Route table associations
resource "aws_route_table_association" "public_route_table_association" {
  subnet_id      = var.public_subnet_id
  route_table_id = aws_route_table.public_route_table.id
}

resource "aws_route_table_association" "nat_route_table_association" {
  subnet_id      = var.private_subnet_id
  route_table_id = aws_route_table.nat_route_table.id
}

resource "aws_security_group_rule" "allow_https" {
  type              = "ingress"
  from_port         = 443
  to_port           = 443
  protocol          = "tcp"
  security_group_id = tolist(data.aws_elb.eb_lb.security_groups)[0]
  cidr_blocks       = ["0.0.0.0/0"]
  lifecycle {
    ignore_changes = [security_group_id]
  }
}

resource "aws_iam_policy" "eb_security_group_policy" {
  name        = "eb-security-group-policy-${var.environment}"
  description = "Policy to allow Elastic Beanstalk to manage security groups and access S3 objects"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "ec2:DescribeSecurityGroups",
          "ec2:AuthorizeSecurityGroupIngress",
          "ec2:RevokeSecurityGroupIngress",
          "ec2:AuthorizeSecurityGroupEgress",
          "ec2:RevokeSecurityGroupEgress"
        ],
        Effect   = "Allow",
        Resource = "*"
      },
      {
        Action = [
          "s3:PutObject",
          "s3:PutObjectAcl",
          "s3:GetObject",
          "s3:DeleteObject",
          "s3:ListBucket"
        ],
        Effect   = "Allow",
        Resource = "arn:aws:s3:::${var.org_docs_bucket_name}/*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "eb_security_group_policy_attachment" {
  role       = aws_iam_role.elastic_beanstalk_role.name
  policy_arn = aws_iam_policy.eb_security_group_policy.arn
}

resource "aws_security_group_rule" "redis_access" {
  type              = "egress"
  from_port         = 6379
  to_port           = 6379
  protocol          = "tcp"
  security_group_id = var.elastic_beanstalk_security_group_id
  cidr_blocks       = ["0.0.0.0/0"]
}
