import {
  Body,
  Controller,
  Param,
  Post,
  UploadedFile,
  UseInterceptors,
  Query,
  Get,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ClientSession } from 'mongoose';
import { SmsService } from 'src/notifications/sms/sms.service';
import { Transaction } from 'src/shared/decorators/transaction.decorator';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { TransitionsService } from './transitions.service';
import {
  RegisterEmailQueryDto,
  RegisterEmailRequestDto,
} from 'src/transitions/dto/register-email.dto';
import { MAX_FILE_SIZE } from 'src/shared/types/general/asset-types.enum';
import { fileFilter } from 'src/utils/filters/files.filter';
import { BrokerageStatementUploadQueryDto } from 'src/transitions/dto/public/brokerage-statement-upload.dto';
import { VerifyNonSolicitAgreementQueryDto } from 'src/transitions/dto/public/verify-non-solicit.dto';
import { LegacyDocuSignWebhookDto } from 'src/integrations/docusign/dto/webhook.dto';

@Controller('/transitions')
export class TransitionsPublicController {
  constructor(
    private readonly transitionsService: TransitionsService,
    private transactionManager: TransactionManager,
  ) {}

  @Post('/:id/non-solicit-agreement/verify')
  @Transaction()
  async verifyNonSolicitAgreement(
    @Param('id') transitionId: string,
    @Body() body: LegacyDocuSignWebhookDto,
    @Query() query: VerifyNonSolicitAgreementQueryDto,
    session?: ClientSession,
  ) {
    await this.transitionsService.verifyNonSolicitAgreement(
      transitionId,
      body,
      query,
      session,
    );
    return 'ok';
  }

  // Controller to upload brokerage statement to transition
  @Post('/:id/brokerage-statement')
  @UseInterceptors(
    FileInterceptor('statement', {
      limits: { fileSize: MAX_FILE_SIZE },
      fileFilter,
    }),
  )
  @Transaction()
  uploadBrokerageStatement(
    @Param('id') transitionId: string,
    @Query() query: BrokerageStatementUploadQueryDto,
    @UploadedFile() file: Express.Multer.File,
    session?: ClientSession,
  ) {
    return this.transitionsService.uploadBrokerageStatement(
      transitionId,
      query,
      file,
      session,
    );
  }

  @Post('/:id/email')
  @Transaction()
  registerEmail(
    @Param('id') transitionId: string,
    @Body() body: RegisterEmailRequestDto,
    @Query() query: RegisterEmailQueryDto,
    session?: ClientSession,
  ) {
    return this.transitionsService.registerContactEmail(
      transitionId,
      body,
      query,
    );
  }

  @Get('/:id/info')
  async getTransitionInfo(@Param('id') transitionId: string) {
    return this.transitionsService.getPublicTransitionInfo(transitionId);
  }
}
