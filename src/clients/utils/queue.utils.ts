import { createHash } from 'crypto';

export interface ClientHashData {
  organisationId: string;
  primaryContact: {
    email: string;
    firstName: string;
    lastName: string;
  };
  primaryAdvisor: {
    id: string;
  };
}

export const generateClientHash = (
  organisationId: string,
  clientData: ClientHashData
): string => {
  const relevantFields = {
    organisationId,
    email: clientData.primaryContact.email?.toLowerCase(),
    firstName: clientData.primaryContact.firstName?.toLowerCase(),
    lastName: clientData.primaryContact.lastName?.toLowerCase(),
    advisorId: clientData.primaryAdvisor.id,
  };

  return createHash('sha256')
    .update(JSON.stringify(relevantFields))
    .digest('hex');
};
