/**
 * Global Jest test setup
 * This file contains setup code that executes before/after each test
 */
import {
  captureGlobalState,
  performMemoryCleanup,
  cleanupEventLoopHandles
} from './test-memory-utils';

// Type definitions for Node.js garbage collector and other internal methods
declare global {
  namespace NodeJS {
    interface Global {
      gc?: () => void;
    }
    
    interface Process {
      // @ts-ignore - internal Node.js method
      _getActiveHandles?: () => unknown[];
    }
  }
}

// Mute console.error during tests to reduce noise from experimental warnings
const originalConsoleError = console.error;
console.error = (...args) => {
  if (
    args[0] && 
    typeof args[0] === 'string' && 
    (args[0].includes('ExperimentalWarning') || 
     args[0].includes('DeprecationWarning') ||
     args[0].includes('AWS SDK for JavaScript'))
  ) {
    return;
  }
  originalConsoleError(...args);
};

// Enhanced version of performDeepCleanup
const performDeepCleanup = () => {
  // Clear all mocks
  jest.clearAllMocks();
  jest.restoreAllMocks();
  
  // Reset modules to clean up module mocks
  jest.resetModules();
  
  // Use real timers to clean up any timers
  jest.useRealTimers();
  
  // Use the utility function to clean up event loop handles
  cleanupEventLoopHandles();
  
  // Use the comprehensive memory cleanup utility
  performMemoryCleanup();
};

// Capture initial global state before any tests run
beforeAll(() => {
  captureGlobalState();
});

// Clean up after each test
afterEach(() => {
  performDeepCleanup();
});

// Final cleanup after all tests
afterAll(() => {
  // Restore original console.error
  console.error = originalConsoleError;
  
  // One last deep cleanup
  performDeepCleanup();
});

// Export the cleanup function so tests can call it manually if needed
// Make it globally available
// @ts-ignore - intentionally adding to global
global.performDeepCleanup = performDeepCleanup; 