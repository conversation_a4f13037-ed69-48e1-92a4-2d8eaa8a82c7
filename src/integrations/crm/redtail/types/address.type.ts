export interface RedtailAddress {
  id?: number;
  addressable_id?: number;
  addressable_type?: string;
  street_address?: string;
  secondary_address?: string | null;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  address_type?: number;
  address_type_description?: string;
  custom_type_title?: string | null;
  description?: string | null;
  is_primary?: boolean;
  is_preferred?: boolean;
  is_shared?: boolean;
  deleted?: boolean;
  created_at?: string;
  updated_at?: string;
}
