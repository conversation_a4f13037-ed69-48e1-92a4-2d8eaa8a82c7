variable "environment" {
  description = "The environment (staging, production etc)."
  type        = string
}

variable "request_count_threshold" {
  description = "The threshold for the number of requests."
  type        = number
  default     = 5000
}

variable "healthy_host_count_threshold" {
  description = "The threshold for the number of healthy hosts."
  type        = number
  default     = 3
}

variable "latency_threshold" {
  description = "The threshold for the latency."
  type        = number
  default     = 200
}
