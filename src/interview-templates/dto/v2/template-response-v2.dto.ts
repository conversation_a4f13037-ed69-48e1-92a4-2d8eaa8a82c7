import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class PageDefResponseDto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  pageName!: string;

  @ApiProperty()
  pageTitle!: string;

  @ApiPropertyOptional()
  pageDescription?: string;

  @ApiProperty()
  defaultOrder!: number;

  @ApiProperty()
  pageType!: string;

  @ApiProperty()
  flow!: any;

  @ApiPropertyOptional()
  isRequired?: boolean;

  @ApiPropertyOptional()
  applicableAccountTypes?: string[];

  @ApiPropertyOptional()
  uiSchema?: any;

  @ApiPropertyOptional()
  metadata?: any;

  @ApiProperty()
  createdAt!: Date;

  @ApiProperty()
  updatedAt!: Date;
}

export class TemplateResponseV2Dto {
  @ApiProperty()
  id!: string;

  @ApiProperty()
  templateName!: string;

  @ApiPropertyOptional()
  description?: string;

  @ApiProperty({ enum: ['draft', 'published', 'archived', 'deprecated'] })
  status!: string;

  @ApiProperty()
  version!: number;

  @ApiPropertyOptional()
  isDefault?: boolean;

  @ApiProperty({ type: [PageDefResponseDto] })
  pages!: PageDefResponseDto[];

  @ApiPropertyOptional()
  config?: any;

  @ApiPropertyOptional()
  accountConfig?: any;

  @ApiPropertyOptional()
  tags?: string[];

  @ApiProperty()
  organisationId!: string;

  @ApiProperty()
  createdAt!: Date;

  @ApiProperty()
  updatedAt!: Date;

  @ApiPropertyOptional()
  publishedAt?: Date;

  @ApiPropertyOptional()
  publishedBy?: string;
}