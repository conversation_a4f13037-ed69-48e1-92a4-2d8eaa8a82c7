import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { ConflictsOfInterestRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/conflicts-of-interest-request.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { AccountClientDocumentsEnum } from 'src/shared/types/accounts/account-documents.enum';
import { PractifiCRM } from '../crm.practifi';
import { TaskStatus, TaskPriority } from '../types/enums';
export class PractifiConflictOfInterestMapper implements FormUpdateMapper {
  constructor(private practifiCRM: PractifiCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: ConflictsOfInterestRequestDto,
  ): Promise<void> {
    const subject = 'Obtain BD/FINRA approval letter for account opening';
    const description =
      'Client employed by or associated with B/D, FINRA or Stock Exchange. Email for approval letter and submit to Schwab.';
    const { _id: interviewId } = interview;
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    const crmClientId = contact.crmClientId;

    const record = await this.practifiCRM.contactService.getContact(crmClientId);

    // Remove FINRA Document from required documents in case the client changes their answer
    if (!pageData.companyName) {
      await this.practifiCRM.interviewsService.removeRequiredDocument(
        interviewId,
        {
          document: AccountClientDocumentsEnum.FinraDocument,
        },
      );

      return;
    }

    // Add FINRA Document to required documents in the interview
    if (interview.docusignSelected) {
      await this.practifiCRM.interviewsService.addRequiredDocument(
        interviewId,
        {
          document: AccountClientDocumentsEnum.FinraDocument,
        },
      );
    }

    // Update contact with industry affiliation
    await this.practifiCRM.contactService.updateContact(crmClientId, {
      Description: `${record.Description ? record.Description : ''}\n employed by a company ${pageData.companyName} \n`,
    });

    // Create a task in Practifi
    await this.practifiCRM.taskService.createTask({
      Subject: subject,
      Description: description,
      WhatId: record.AccountId, // Related To ID (Account)
      OwnerId: client.primaryAdvisor.crmId, // Assigned To ID
      Status: TaskStatus.NOT_STARTED,
      Priority: TaskPriority.HIGH,
      ActivityDate: new Date().toISOString().split('T')[0], // Due Date
    });
  }
}
