import { HttpService } from '@nestjs/axios';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable, Logger, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { AxiosError, AxiosRequestConfig } from 'axios';
import Bottleneck from '@maselious/bottleneck';
import { Cache } from 'cache-manager';
import { ClsService } from 'nestjs-cls';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { OperationType } from 'src/super-http/enums';
import { RequestOptions, SuperHttpModuleConfig } from 'src/super-http/interfaces';
import { CacheKeyParams, generateEntityTypePrefix, generateFullCacheKey, generateSpecificCacheKey } from 'src/super-http/utils';

@Injectable()
export class SuperHttpService implements OnModuleInit, OnModuleDestroy {
    private rateLimiters: Map<string, Bottleneck> = new Map();
    private defaultRateLimiterKey: string;
    private readonly logger = new Logger(SuperHttpService.name);
    private initialized = false;

    constructor(
        private readonly httpService: HttpService,
        @Inject(CACHE_MANAGER) private cacheManager: Cache,
        @Inject('HTTP_MODULE_CONFIG')
        private readonly config: SuperHttpModuleConfig,
        private readonly clsService?: ClsService,
    ) {
        this.defaultRateLimiterKey = config.defaultRateLimiter || Object.keys(config.rateLimiters)[0];
    }

    async onModuleInit() {
        await this.initializeRateLimiters();
        this.initialized = true;
    }

    async onModuleDestroy() {
        await Promise.all(
            Array.from(this.rateLimiters.values()).map(limiter =>
                limiter.disconnect(true)
            )
        );
    }

    private async initializeRateLimiters() {
        this.logger.log('Initializing rate limiters...');

        if (!this.config.rateLimiters || Object.keys(this.config.rateLimiters).length === 0) {
            this.logger.warn('No rate limiters configured. Initializing default rate limiter.');
            this.rateLimiters.set('default', new Bottleneck({ maxConcurrent: 10, minTime: 100 }));
            return;
        }

        for (const [name, config] of Object.entries(this.config.rateLimiters)) {
            try {
                let limiterConfig: Bottleneck.ConstructorOptions = {
                    id: `ratelimiter-${name}`,
                    minTime: config.minTime,
                    maxConcurrent: config.maxConcurrent,
                    reservoir: config.reservoir,
                    reservoirRefreshAmount: config.reservoirRefreshAmount,
                    reservoirRefreshInterval: config.reservoirRefreshInterval,
                    highWater: config.highWater || 1000,
                    strategy: Bottleneck.strategy.BLOCK,
                    timeout: config.timeout || 60000,
                };

                // Try to use Redis if configured
                if (this.config.redis) {
                    try {
                        limiterConfig = {
                            ...limiterConfig,
                            datastore: "ioredis",
                            clearDatastore: false,
                            clientOptions: {
                                host: this.config.redis.host,
                                port: this.config.redis.port,
                                password: this.config.redis.password,
                                tls: process.env.WORK_ENV !== 'dev' ? {
                                    rejectUnauthorized: false,
                                } : undefined,
                                connectTimeout: 10000,
                            },
                            timeout: 10000,
                        };
                    } catch (redisError) {
                        this.logger.warn(`Failed to initialize Redis for rate limiter ${name}, falling back to memory: ${redisError.message}`);
                    }
                }

                this.logger.log(`Initializing rate limiter: ${name}`);
                const limiter = new Bottleneck(limiterConfig);

                // Setup event handlers
                limiter.on('error', error => {
                    this.logger.error(`Rate limiter ${name} error: ${error.message}`, error.stack);
                });

                limiter.on('dropped', async (dropped) => {
                    this.logger.warn(`Rate limiter ${name} dropped job`);
                });
                limiter.on('retry', (error, jobInfo) => {
                    this.logger.warn(
                        `Rate limiter ${name} retrying job ${jobInfo.options?.id || 'unknown'} after error: ${error}`,
                    );
                });

                limiter.on('failed', async (error, jobInfo) => {
                    this.logger.warn(
                        `Job ${jobInfo.options?.id || 'unknown'} failed: ${error.message}. Retry ${jobInfo.retryCount}`,
                    );

                    if (error instanceof AxiosError) {
                        if (error.response?.status === 429) {
                            const retryAfter = parseInt(error.response.headers['retry-after'] || '60', 10);
                            return retryAfter * 1000;
                        }
                        if (error.response?.status >= 500) {
                            return jobInfo.retryCount < 3 ? (jobInfo.retryCount + 1) * 1000 : null;
                        }
                        if (error.response?.status === 401) {
                            return null;
                        }
                    }

                    if (jobInfo.retryCount < 3) {
                        return Math.min((jobInfo.retryCount + 1) * 1000, 5000);
                    }
                    return null;
                });

                this.rateLimiters.set(name, limiter);
                this.logger.log(`Rate limiter ${name} initialized successfully`);
            } catch (error) {
                this.logger.error(`Failed to initialize rate limiter ${name}: ${error.message}`);
                // Create a fallback in-memory limiter with more conservative limits
                const fallbackLimiter = new Bottleneck({
                    minTime: Math.max(config.minTime * 2, 100),
                    maxConcurrent: Math.min(config.maxConcurrent, 5),
                    highWater: 100,
                    strategy: Bottleneck.strategy.OVERFLOW,
                });
                this.rateLimiters.set(name, fallbackLimiter);
                this.logger.warn(`Created conservative fallback in-memory rate limiter for ${name}`);
            }
        }
    }

    private getRateLimiter(name?: string): Bottleneck {
        if (!this.initialized) {
            throw new Error('Rate limiters not initialized yet');
        }

        const limiterName = name || this.defaultRateLimiterKey;
        const limiter = this.rateLimiters.get(limiterName);

        if (!limiter) {
            throw new Error(`Rate limiter "${limiterName}" not found. Available rate limiters: ${Array.from(this.rateLimiters.keys()).join(', ')}`);
        }

        return limiter;
    }

    private async makeHttpRequest<T>(
        method: 'get' | 'post' | 'put' | 'patch' | 'delete',
        url: string,
        tenantId: string,
        data?: any,
        axiosConfig: AxiosRequestConfig = {},
    ): Promise<T> {
        this.logger.debug(`Making ${method.toUpperCase()} request to ${url}`);
        const config = {
            ...axiosConfig,
            headers: {
                ...axiosConfig.headers,
                'X-Tenant-ID': tenantId,
            },
            timeout: axiosConfig.timeout || this.config.timeout || 30000, // Default 30 seconds if not specified
        };

        try {
            const response = await this.httpService.axiosRef.request<T>({
                method,
                url,
                data,
                ...config,
            });

            this.logger.debug(`Request to ${url} completed successfully`);
            return response.data;
        } catch (error) {
            if (error.code === 'ECONNABORTED') {
                this.logger.error(`Request to ${url} timed out after ${config.timeout}ms`);
                throw new Error(`Request timeout: The request to ${url} took longer than ${config.timeout}ms`);
            }
            if (error?.response) {
                // Log detailed error information for HTTP errors
                this.logger.error(`Request to ${url} failed: ${JSON.stringify({
                    status: error.response?.status,
                    statusText: error.response?.statusText,
                    data: error.response?.data,
                    headers: error.response?.headers
                })}`);
            } else {
                this.logger.error(`Request to ${url} failed: ${error?.message}`);
            }
            throw error;
        }
    }

    private async getCachedData<T>({
        cacheKey,
        forceFresh = false,
        tenantId,
        ttl,
    }: {
        cacheKey: string;
        forceFresh?: boolean;
        tenantId: string;
        ttl: number;
    }): Promise<{ data: T | null; isCached: boolean }> {
        const startTime = Date.now();
        // Skip cache operations if tenantId is not available
        if (!tenantId) {
            this.logger.log(`[Cache] Skipping cache operations - no tenantId available`);
            return { data: null, isCached: false };
        }

        if (!forceFresh) {
            try {
                this.logger.log(`[Cache] Attempting to get data from cache for key: ${cacheKey}`);
                const cachedData = await this.cacheManager.get<T>(cacheKey);
                const duration = Date.now() - startTime;

                if (cachedData) {
                    this.logger.log(`[Cache] HIT - Retrieved data from cache for key: ${cacheKey} (took ${duration}ms)`);
                    return { data: typeof cachedData === 'string' ? JSON.parse(cachedData) : cachedData, isCached: true };
                }
                this.logger.log(`[Cache] MISS - No data found in cache for key: ${cacheKey} (took ${duration}ms)`);
            } catch (cacheError) {
                const duration = Date.now() - startTime;
                this.logger.error(`[Cache] ERROR - Cache read operation failed for ${cacheKey}: ${cacheError.message} (took ${duration}ms)`);
                // Continue without cache on error
            }
        } else {
            this.logger.log(`[Cache] BYPASS - Cache bypassed for ${cacheKey} due to forceFresh flag`);
        }

        return { data: null, isCached: false };
    }

    private async safeCacheSet(cacheKey: string, data: any, ttl: number): Promise<void> {
        const startTime = Date.now();
        try {
            this.logger.log(`[Cache] Attempting to set cache for key: ${cacheKey} with TTL: ${ttl}s`);
            // Ensure data is serializable by converting to JSON string and back
            const stringifiedData = JSON.stringify(data);
            await this.cacheManager.set(cacheKey, stringifiedData, ttl);
            const duration = Date.now() - startTime;
            this.logger.log(`[Cache] Successfully set cache for ${cacheKey} (took ${duration}ms)`);
        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`[Cache] ERROR - Failed to set cache for ${cacheKey}: ${error.message} (took ${duration}ms)`);
            // Continue execution without throwing
        }
    }

    private async safeCacheInvalidate(keys: string[]): Promise<void> {
        const startTime = Date.now();
        this.logger.log(`[Cache] Starting batch invalidation for ${keys.length} keys`);

        for (const key of keys) {
            const keyStartTime = Date.now();
            try {
                this.logger.log(`[Cache] Attempting to invalidate cache for key: ${key}`);
                await this.cacheManager.del(key);
                const keyDuration = Date.now() - keyStartTime;
                this.logger.log(`[Cache] Successfully invalidated cache for ${key} (took ${keyDuration}ms)`);
            } catch (error) {
                const keyDuration = Date.now() - keyStartTime;
                this.logger.error(`[Cache] ERROR - Failed to invalidate cache for ${key}: ${error.message} (took ${keyDuration}ms)`);
                // Continue with next key
            }
        }

        const totalDuration = Date.now() - startTime;
        this.logger.log(`[Cache] Completed batch invalidation of ${keys.length} keys (total time: ${totalDuration}ms)`);
    }

    async get<T = any>(
        url: string,
        tenantId: string,
        options: RequestOptions,
        axiosConfig: AxiosRequestConfig = {},
    ): Promise<T> {
        const {
            ttl = this.config.redis?.ttl || 3600, // Use a default of 3600 seconds if redis is null
            forceFresh = false,
            cacheKeysObject,
            rateLimiter,
            entityType,
            operation = OperationType.GET,
            entityId,
            integrationName,
        } = options;

        if (!tenantId) {
            throw new Error('tenantId is required for get request');
        }

        // Skip cache if required data is not available
        const shouldUseCache = tenantId && entityType && operation && integrationName;
        let cacheKey = '';
        this.logger.log(`Should use cache: ${shouldUseCache}, forceFresh: ${forceFresh}`);
        if (shouldUseCache && !forceFresh) {
            cacheKey = generateFullCacheKey({
                tenantId,
                integrationName,
                entityType,
                operation,
                entityId,
                cacheKeysObject,
                url
            });
            this.logger.log(`Cache key for GET request to ${url}: ${cacheKey}`);
            // Check cache first
            const { data: cachedData, isCached } = await this.getCachedData<T>({
                cacheKey,
                forceFresh,
                tenantId,
                ttl
            });

            if (isCached) {
                this.logger.log(`Cache hit for ${cacheKey}`);
                return cachedData as T;
            }
        }

        // If not in cache or force fresh, make the request
        const limiter = this.getRateLimiter(rateLimiter);

        try {
            this.logger.log(`Making GET request to ${url}`);
            const result = await limiter.schedule(async () => {
                this.logger.log(`INSIDE LIMITER:: Making GET request to ${url}`);
                const result = await this.makeHttpRequest<T>('get', url, tenantId, undefined, axiosConfig);
                // Only set cache if we have all required data
                if (shouldUseCache && !forceFresh) {
                    await this.safeCacheSet(cacheKey, result, ttl);
                }
                return result;
            });
            return result;
        } catch (error) {
            this.logger.error(`Error in GET request for ${url}: ${error.message}`, error.stack);
            throw error;
        }
    }

    async post<T = any>(
        url: string,
        tenantId: string, // Required parameter
        data: any,
        options: RequestOptions,
        axiosConfig: AxiosRequestConfig = {},
    ): Promise<T> {
        const {
            rateLimiter,
            entityType,
            operation = OperationType.CREATE,
            entityId,
            integrationName,
            wideCacheInvalidation = true
        } = options;
        if (!tenantId) {
            throw new Error('tenantId is required for post request');
        }
        const limiter = this.getRateLimiter(rateLimiter);
        this.logger.log(`OUTSIDE LIMITER:: Making POST request to ${url}`);
        try {
            this.logger.log(`Making POST request to ${url}`);
            const result = await limiter.schedule(async () => {
                this.logger.log(`INSIDE LIMITER:: Making POST request to ${url}`);
                const result = await this.makeHttpRequest<T>('post', url, tenantId, data, axiosConfig);

                // Handle cache invalidation
                if (wideCacheInvalidation) {
                    await this.invalidateEntityTypeCache({
                        tenantId,
                        integrationName,
                        entityType,
                        operation,
                        url
                    });
                } else {
                    if (!entityId) {
                        throw new Error('entityId is required when wideCacheInvalidation is false');
                    }
                    await this.invalidateSpecificCache({
                        tenantId,
                        integrationName,
                        entityType,
                        operation,
                        entityId,
                        url
                    });
                }

                return result;
            });
            return result;
        } catch (error) {
            this.logger.error(`Error in POST request for ${url}: ${error.message}`, error.stack);
            throw error;
        }
    }

    async put<T = any>(
        url: string,
        tenantId: string, // Required parameter
        data: any,
        options: RequestOptions,
        axiosConfig: AxiosRequestConfig = {},
    ): Promise<T> {
        const {
            rateLimiter,
            entityType,
            operation = OperationType.UPDATE,
            entityId,
            integrationName,
            wideCacheInvalidation = true
        } = options;
        if (!tenantId) {
            throw new Error('tenantId is required for put request');
        }
        const limiter = this.getRateLimiter(rateLimiter);

        try {
            const result = await limiter.schedule(async () => {
                const result = await this.makeHttpRequest<T>('put', url, tenantId, data, axiosConfig);

                if (wideCacheInvalidation) {
                    await this.invalidateEntityTypeCache({
                        tenantId,
                        integrationName,
                        entityType,
                        operation,
                        url
                    });
                } else {
                    await this.invalidateSpecificCache({
                        tenantId,
                        integrationName,
                        entityType,
                        operation,
                        entityId,
                        url
                    });
                }

                return result;
            });
            return result;
        } catch (error) {
            this.logger.error(`Error in PUT request for ${url}: ${error.message}`, error.stack);
            throw error;
        }
    }

    async patch<T = any>(
        url: string,
        tenantId: string, // Required parameter
        data: any,
        options: RequestOptions,
        axiosConfig: AxiosRequestConfig = {},
    ): Promise<T> {
        const {
            rateLimiter,
            entityType,
            operation = OperationType.PATCH,
            entityId,
            integrationName,
            wideCacheInvalidation = true
        } = options;
        if (!tenantId) {
            throw new Error('tenantId is required for patch request');
        }
        const limiter = this.getRateLimiter(rateLimiter);

        try {
            const result = await limiter.schedule(async () => {
                const result = await this.makeHttpRequest<T>('patch', url, tenantId, data, axiosConfig);

                if (wideCacheInvalidation) {
                    await this.invalidateEntityTypeCache({
                        tenantId,
                        integrationName,
                        entityType,
                        operation,
                        url
                    });
                } else {
                    await this.invalidateSpecificCache({
                        tenantId,
                        integrationName,
                        entityType,
                        operation,
                        entityId,
                        url
                    });
                }

                return result;
            });
            return result;
        } catch (error) {
            this.logger.error(`Error in PATCH request for ${url}: ${error.message}`, error.stack);
            throw error;
        }
    }

    async delete<T = any>(
        url: string,
        tenantId: string, // Required parameter
        options: RequestOptions,
        axiosConfig: AxiosRequestConfig = {},
    ): Promise<T> {
        const {
            rateLimiter,
            entityType,
            operation = OperationType.DELETE,
            entityId,
            integrationName,
            wideCacheInvalidation = true
        } = options;
        if (!tenantId) {
            throw new Error('tenantId is required for delete request');
        }
        const limiter = this.getRateLimiter(rateLimiter);

        try {
            const result = await limiter.schedule(async () => {
                const result = await this.makeHttpRequest<T>('delete', url, tenantId, undefined, axiosConfig);

                if (wideCacheInvalidation) {
                    await this.invalidateEntityTypeCache({
                        tenantId,
                        integrationName,
                        entityType,
                        operation,
                        url
                    });
                } else {
                    if (!entityId) {
                        throw new Error('entityId is required when wideCacheInvalidation is false');
                    }
                    await this.invalidateSpecificCache({
                        tenantId,
                        integrationName,
                        entityType,
                        operation,
                        entityId,
                        url
                    });
                }

                return result;
            });
            return result;
        } catch (error) {
            this.logger.error(`Error in DELETE request for ${url}: ${error.message}`, error.stack);
            throw error;
        }
    }

    async getRateLimiterStats(name?: string): Promise<{
        running: number;
        queued: number;
        reservoir: number | null;
        clusterQueued?: number;
    }> {
        const limiter = this.getRateLimiter(name);
        const [running, queued, reservoir, counts] = await Promise.all([
            limiter.running(),
            limiter.queued(),
            limiter.currentReservoir(),
            limiter.counts(),
        ]);

        return {
            running,
            queued,
            reservoir,
            clusterQueued: counts?.QUEUED,
        };
    }

    async invalidateEntityTypeCache(params: CacheKeyParams): Promise<void> {
        const startTime = Date.now();
        try {
            const prefix = generateEntityTypePrefix(params);
            this.logger.log(`[Cache] Starting entity type cache invalidation for ${params.entityType} with prefix ${prefix}`);

            let keys: string[] = [];
            try {
                keys = await this.cacheManager.store.keys(`${prefix}*`);
                const keysCount = keys.length;
                this.logger.log(`[Cache] Found ${keysCount} keys to invalidate for prefix ${prefix}`);
            } catch (error) {
                const duration = Date.now() - startTime;
                this.logger.error(`[Cache] ERROR - Failed to get keys for prefix ${prefix}: ${error.message} (took ${duration}ms)`);
                return; // Exit gracefully if we can't get keys
            }

            await this.safeCacheInvalidate(keys);
            const duration = Date.now() - startTime;
            this.logger.log(`[Cache] Completed entity type cache invalidation for ${params.entityType} (took ${duration}ms)`);
        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`[Cache] ERROR - Cache invalidation failed for entity type ${params.entityType}: ${error.message} (took ${duration}ms)`);
            // Continue execution without throwing
        }
    }

    async invalidateSpecificCache(params: CacheKeyParams): Promise<void> {
        const startTime = Date.now();
        try {
            const cacheKey = generateSpecificCacheKey(params);
            this.logger.log(`[Cache] Starting specific cache invalidation for key: ${cacheKey}`);
            await this.safeCacheInvalidate([cacheKey]);
            const duration = Date.now() - startTime;
            this.logger.log(`[Cache] Completed specific cache invalidation (took ${duration}ms)`);
        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`[Cache] ERROR - Cache invalidation failed for specific key: ${error.message} (took ${duration}ms)`);
        }
    }

    async checkLimiterHealth(name?: string): Promise<boolean> {
        try {
            const limiter = this.getRateLimiter(name);
            const ready = await limiter.ready();
            return ready;
        } catch (error) {
            this.logger.error(`Health check failed for rate limiter ${name}: ${error.message}`);
            return false;
        }
    }

    addInterceptor(type, successCallback, errorCallback) {
        if (type === 'request') {
            this.httpService.axiosRef.interceptors.request.use(
                successCallback,
                errorCallback,
            );
        }
        if (type === 'response') {
            this.httpService.axiosRef.interceptors.response.use(
                successCallback,
                errorCallback,
            );
        }
    }

    clearInterceptors() {
        this.httpService.axiosRef.interceptors.request.clear();
        this.httpService.axiosRef.interceptors.response.clear();
    }
}
