import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsBoolean, IsEnum, IsArray, IsOptional, ArrayNotEmpty } from 'class-validator';
import { QuestionTypeEnum } from 'src/shared/types/interview-questions/question-types.enum';

export class CreateCustomQuestionDto {
  @ApiProperty({ required: true })
  @IsString()
  question: string;

  @ApiProperty({ required: true })
  @IsEnum(QuestionTypeEnum)
  type: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  @ArrayNotEmpty()
  @IsString({ each: true })
  options?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ required: true })
  @IsBoolean()
  required: boolean;
}
