import { PartialType } from '@nestjs/mapped-types';
import { AdvisorWithR<PERSON> } from 'src/advisors/dto/advisor-with-role.dto';
import { ContactDto } from 'src/clients/dto/v1/create-client.dto';

export class CreateContactDto {
  primaryContact: ContactDto;
  secondaryContact: ContactDto;
  primaryAdvisor?: AdvisorWithRole;
  secondaryAdvisor?: AdvisorWithRole;
  primaryCSA?: AdvisorWithRole;
  secondaryCSA?: AdvisorWithRole;
}

export class CreateContactResponseDto {
  primaryContactCrmId: string;
  secondaryContactCrmId: string;
}

export class UpdateContactDto extends PartialType(CreateContactDto) {}
export class UpdateContactResponseDto {
  primaryContactCrmId: string;
  secondaryContactCrmId: string;
}
