import {
  InterviewDataWithCrmInfo,
  MergedContact,
} from 'src/interviews/types/interview-data.type';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { AccountDto } from 'src/shared/types/accounts/account.dto';

export function getInterviewCoapplicant(
  interviewData: InterviewDataWithCrmInfo,
  account: AccountDto,
): MergedContact {
  if (account.type !== AccountTypeEnum.JointNameBrokerage) {
    return;
  }
  return interviewData.secondaryContact;
}
