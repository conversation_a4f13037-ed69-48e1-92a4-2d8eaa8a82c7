import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { SalesforceCRM } from 'src/integrations/crm/salesforce/crm.salesforce';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

interface Beneficiary {
  firstName: string;
  lastName: string;
  allocationAmount: string;
  dob?: string;
}

interface PrimaryBeneficiariesPageData {
  instance: { name: string, label: string };
  beneficiaries: Beneficiary[];
}

export class SalesforcePrimaryBeneficiariesMapper implements FormUpdateMapper {
  constructor(private salesforceCRM: SalesforceCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: PrimaryBeneficiariesPageData,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const connectedOAccounts: {type: string, name: string, id: string}[] = await this.salesforceCRM.customAccountService.getLinkedAccounts(contact.crmClientId);
    const accountRecordId = connectedOAccounts.find(el => el.name === pageData.instance.label)?.id;

    const linkedBeneficiaries = await this.salesforceCRM.customAccountService.getLinkedBeneficiaries(accountRecordId);

    if (linkedBeneficiaries.count !== 0) {
      const linkedBeneficiariesIds = linkedBeneficiaries.records.map(el => el.id);
      const beneficiariesDeleteRequests = linkedBeneficiariesIds.map(id => this.salesforceCRM.customAccountService.deleteLinkedBeneficiaries(id));

      await Promise.all(
        [
          ...beneficiariesDeleteRequests,
        ]
      );
    }

    // If no beneficiaries provided, just return after deleting existing ones
    if (!pageData?.beneficiaries?.length) {
      return;
    }

    // Format beneficiaries data for Salesforce
    const beneficiariesData = pageData.beneficiaries.map((ben, index) =>
      this.salesforceCRM.customAccountService.createPrimaryBeneficiary({
        Name: `${ben.firstName} ${ben.lastName}`,
        Percentage__c: ben.allocationAmount,
        Date_of_Birth__c: ben.dob,
      })
    );
    const beneficiariesResponses = await Promise.all([...beneficiariesData]);

    const successfullBenResponsesIds = beneficiariesResponses.filter(el => el.id);

    const junctionObjects = [];

    successfullBenResponsesIds.map(el => junctionObjects.push(this.salesforceCRM.customAccountService.linkAccountToPrimaryBeneficiary(el.id, accountRecordId)));

    await Promise.all(
      [
        ...junctionObjects,
      ]
    );
  }
}
