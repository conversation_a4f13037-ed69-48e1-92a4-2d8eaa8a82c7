import { OnWorkerE<PERSON>, Processor, WorkerHost } from '@nestjs/bullmq';
import { forwardRef, Inject, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueLogService } from 'src/queue-log/queue-log.service';
import { QueueLogStatusEnum } from 'src/queue-log/enums/queue-log-status.enum';
import { ResourceOptionsEnum } from 'src/shared/types/queue-log/queue';
import { SaveJobsDto } from 'src/queue-log/dto/save-jobs.dto';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { UpdateInterviewDto } from 'src/interviews/dto/v1/update-interview.dto';
import { INTERVIEW_QUEUE } from 'src/interviews/constants/interview-queue.constant';
import { InterviewsService } from 'src/interviews/interviews.service';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { InjectConnection } from '@nestjs/mongoose';
import { ClientSession, Connection } from 'mongoose';
import { InterviewPageStatusEnum } from 'src/interview-templates/types/interview-page-status.enum';
import { InterviewViewDto } from 'src/interviews/dto/v1/extended-interview.dto';
import { InterviewQueueJobType } from './types/interview-queue-job.enum';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';

// --- Type Definitions ---
export interface InterviewJobDataBase {
  interviewId: string;
  organisationId: string; // Keep original organisationId for logging
}

export interface InterviewJobData extends InterviewJobDataBase {
  updateInterviewDto?: UpdateInterviewDto;
}

export interface UpdateInterviewPageStatusJobData extends InterviewJobDataBase {
  updateInterviewDto: UpdateInterviewDto; // Make non-optional for this type
  pageStatus: InterviewPageStatusEnum;
}

// Context fetched during processing
interface JobContext {
  organisation: Organisation;
  advisor: AdvisorWithRole;
  interview: InterviewViewDto;
}

@Processor(INTERVIEW_QUEUE.NAME, {
  concurrency: 1, // Process one job at a time per worker instance
  // Configure job attempts/backoff in the Queue options when adding jobs
  // or in the defaultJobOptions of the BullModule registration.
})
export class InterviewPageProcessor extends WorkerHost {
  private readonly loggerContext = InterviewPageProcessor.name;

  constructor(
    private readonly interviewsService: InterviewsService,
    @Inject(forwardRef(() => OrganisationsService))
    private readonly organisationsService: OrganisationsService,
    @Inject(forwardRef(() => AdvisorsCrudService))
    private readonly advisorsCrudService: AdvisorsCrudService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    @InjectConnection() private readonly connection: Connection,
    @Inject(forwardRef(() => QueueLogService))
    private readonly queueLogService: QueueLogService,
  ) {
    super();
  }

  /**
   * Processes a single job attempt within a transaction.
   * Throws an error on failure, letting BullMQ handle retries.
   */
  async process(
    job: Job<InterviewJobData | UpdateInterviewPageStatusJobData>,
  ): Promise<any> {
    const { interviewId } = job.data;
    const jobName = job.name;
    this.logger.log(
      `Processing job ${job.id} (${jobName}), attempt ${job.attemptsMade + 1} for interview ${interviewId}`,
      this.loggerContext,
    );

    if (!interviewId) {
      // Fail fast if basic data is missing
      throw new Error(`Job ${job.id}: Interview ID not provided`);
    }

    let session: ClientSession | null = null;
    try {
      session = await this.connection.startSession();
      session.startTransaction({
        readConcern: { level: 'snapshot' },
        writeConcern: { w: 'majority' },
      });

      this.logger.debug(
        `Job ${job.id} (${jobName}): Transaction started for interview ${interviewId}`,
        this.loggerContext,
      );

      // 1. Fetch necessary data within the transaction
      const interview = await this.interviewsService.findById(
        interviewId,
        session,
        true, // Ensure population if needed
      );
      if (!interview) {
        // No point retrying if interview doesn't exist
        await session.abortTransaction();
        throw new Error(
          `Job ${job.id} (${jobName}): Interview not found: ${interviewId}. Aborting transaction.`,
        );
      }

      const context = await this.setupContext(interview, session);

      // 2. Execute job-specific logic
      let result: any;
      switch (jobName.toLowerCase()) {
        case InterviewQueueJobType.UPDATE_PAGE:
          result = await this.handleUpdatePages(
            job.data as UpdateInterviewPageStatusJobData,
            context,
            session,
          );
          break;
        case InterviewQueueJobType.MARK_COMPLETE:
          result = await this.handleMarkInterviewAsComplete(context, session);
          break;
        case InterviewQueueJobType.SEND_NOTIFICATION:
          result = await this.handleSendNotification(context, session);
          break;
        case InterviewQueueJobType.PREPARE_DOCUSIGN_ENVELOPE:
          result = await this.handlePrepareDocusignEnvelope(context, session);
          break;
        default:
          // Fail fast for unsupported types
          await session.abortTransaction();
          throw new Error(`Job ${job.id}: Unsupported job type: ${jobName}`);
      }

      // 3. Commit transaction
      await session.commitTransaction();
      this.logger.debug(
        `Job ${job.id} (${jobName}): Transaction committed successfully for interview ${interviewId}`,
        this.loggerContext,
      );
      return result;
    } catch (error) {
      // Log the error for this attempt
      this.logger.error(
        `Job ${job.id} (${jobName}): Failed attempt ${job.attemptsMade + 1} for interview ${interviewId}. Error: ${error.message}`,
        this.loggerContext,
        error.stack, // Include stack trace for detailed debugging
      );

      // Abort transaction if it's still active and session exists
      if (session?.inTransaction()) {
        try {
          await session.abortTransaction();
          this.logger.warn(
            `Job ${job.id} (${jobName}): Transaction aborted due to error for interview ${interviewId}`,
            this.loggerContext,
          );
        } catch (abortError) {
          this.logger.error(
            `Job ${job.id} (${jobName}): Failed to abort transaction for interview ${interviewId} after error. Abort Error: ${abortError.message}`,
            this.loggerContext,
            abortError.stack,
          );
        }
      }
      // Rethrow the error to signal failure to BullMQ, triggering retry logic
      throw error;
    } finally {
      // Ensure session is always ended
      if (session) {
        await session.endSession();
      }
    }
  }

  /**
   * Sets up the context (advisor, organisation) for the job.
   * @param interview The interview object (already fetched).
   * @param session The database session.
   * @returns JobContext containing advisor, organisation, and interview.
   * @throws Error if required context data is missing.
   */
  private async setupContext(
    interview: InterviewViewDto,
    session: ClientSession,
  ): Promise<JobContext> {
    const client = interview.client;
    if (!client?.primaryAdvisor?.id || !client?.organisationId) {
      throw new Error(
        `Interview ${interview._id}: Client context incomplete (missing primaryAdvisor or organisationId).`,
      );
    }

    const advisor = await this.advisorsCrudService.findOne(
      { _id: client.primaryAdvisor.id },
      session,
    );
    if (!advisor) {
      throw new Error(
        `Interview ${interview._id}: Primary advisor (${client.primaryAdvisor.id}) not found.`,
      );
    }

    const organisation = await this.organisationsService.findOne(
      client.organisationId.toString(),
      session,
    );
    if (!organisation) {
      throw new Error(
        `Interview ${interview._id}: Organisation (${client.organisationId}) not found.`,
      );
    }

    return { advisor, organisation, interview };
  }

  // --- Job-Specific Handlers (Implementations remain the same as previous version) ---

  private async handleUpdatePages(
    data: UpdateInterviewPageStatusJobData,
    context: JobContext,
    session: ClientSession,
  ) {
    const { interviewId, updateInterviewDto, pageStatus } = data;
    this.logger.debug(
      `Handling ${InterviewQueueJobType.UPDATE_PAGE} for interview ${interviewId}`,
      this.loggerContext,
    );
    if (!updateInterviewDto || !pageStatus) {
      throw new Error(
        `Interview ${interviewId}: Missing data for UPDATE_PAGE job.`,
      );
    }
    return this.interviewsService.updateInterviewPages(
      interviewId,
      updateInterviewDto,
      pageStatus,
      session,
    );
  }

  private async handleMarkInterviewAsComplete(
    context: JobContext,
    session: ClientSession,
  ) {
    const interviewId = context.interview._id.toString();
    this.logger.debug(
      `Handling ${InterviewQueueJobType.MARK_COMPLETE} for interview ${interviewId}`,
      this.loggerContext,
    );
    return this.interviewsService.markInterviewAsComplete(interviewId, session);
  }

  private async handleSendNotification(
    context: JobContext,
    session: ClientSession,
  ) {
    const interviewId = context.interview._id.toString();
    this.logger.debug(
      `Handling ${InterviewQueueJobType.SEND_NOTIFICATION} for interview ${interviewId}`,
      this.loggerContext,
    );
    // Ensure interviewsService.sendNotification is idempotent
    return this.interviewsService.sendNotification(interviewId, session);
  }

  private async handlePrepareDocusignEnvelope(
    context: JobContext,
    session: ClientSession,
  ) {
    const interviewId = context.interview._id.toString();
    this.logger.debug(
      `Handling ${InterviewQueueJobType.PREPARE_DOCUSIGN_ENVELOPE} for interview ${interviewId}`,
      this.loggerContext,
    );
    // Ensure interviewsService.prepareDocusignEnvelope is idempotent
    return this.interviewsService.prepareDocusignEnvelope(interviewId, session);
  }

  // --- Worker Event Handlers ---

  @OnWorkerEvent('completed')
  async onCompleted(job: Job<InterviewJobData | UpdateInterviewPageStatusJobData>) {
    this.logger.log(
      `Job ${job.id} (${job.name}) completed successfully after ${job.attemptsMade + 1} attempts for interview ${job.data.interviewId}`,
      this.loggerContext,
    );

    // Log completion (best effort)
    const payload: SaveJobsDto = {
      jobId: job.id.toString(),
      resource: ResourceOptionsEnum.INTERVIEW,
      queueName: INTERVIEW_QUEUE.NAME,
      organisationId: job.data.organisationId,
      status: QueueLogStatusEnum.COMPLETED,
      result: `Job ${job.name} completed successfully.`,
    };

    try {
      await this.queueLogService.save(payload);
    } catch (logError) {
      this.logger.error(
        `Job ${job.id} (${job.name}): Failed to save completion log for interview ${job.data.interviewId}. Error: ${logError.message}`,
        this.loggerContext,
        logError.stack,
      );
    }
  }

  @OnWorkerEvent('failed')
  async onFailed(
    job: Job<InterviewJobData | UpdateInterviewPageStatusJobData>,
    error: Error, // The error that caused the final failure
  ) {
    // This runs after all BullMQ retry attempts have failed
    this.logger.error(
      `Job ${job.id} (${job.name}) ultimately failed after ${job.attemptsMade} attempts for interview ${job.data.interviewId}. Final Error: ${error.message}`,
      this.loggerContext,
      error.stack, // Log the final error stack
    );

    const { interviewId, organisationId } = job.data;

    // --- Best Effort: Mark interview page as FAILED (if applicable) ---
    if (
      job.name.toLowerCase() === InterviewQueueJobType.UPDATE_PAGE &&
      'updateInterviewDto' in job.data
    ) {
      const updateDto = (job.data as UpdateInterviewPageStatusJobData)
        .updateInterviewDto;
      try {
        this.logger.warn(
          `Job ${job.id} (${job.name}): Attempting final action to mark interview ${interviewId} page status as FAILED.`,
          this.loggerContext,
        );
        // Run outside a transaction for simplicity in the failure handler
        await this.interviewsService.updateInterviewPages(
          interviewId,
          updateDto,
          InterviewPageStatusEnum.FAILED,
          undefined, // No session/transaction
        );
        this.logger.warn(
          `Job ${job.id} (${job.name}): Successfully marked interview ${interviewId} page status as FAILED during final failure handling.`,
          this.loggerContext,
        );
      } catch (updateError) {
        this.logger.error(
          `Job ${job.id} (${job.name}): CRITICAL - Failed to mark interview ${interviewId} page status as FAILED during final failure handling. Update Error: ${updateError.message}`,
          this.loggerContext,
          updateError.stack,
        );
        // Consider adding monitoring/alerting here for manual intervention
      }
    }

    // --- Best Effort: Log the final failure ---
    const payload: SaveJobsDto = {
      jobId: job.id.toString(),
      resource: ResourceOptionsEnum.INTERVIEW,
      queueName: INTERVIEW_QUEUE.NAME,
      organisationId,
      status: QueueLogStatusEnum.FAILED,
      error: `Final failure after ${job.attemptsMade} attempts: ${error.message}`, // Add context to logged error
    };

    try {
      await this.queueLogService.save(payload);
    } catch (logError) {
      this.logger.error(
        `Job ${job.id} (${job.name}): Failed to save final failure log for interview ${interviewId}. Log Error: ${logError.message}`,
        this.loggerContext,
        logError.stack,
      );
    }
  }
}
