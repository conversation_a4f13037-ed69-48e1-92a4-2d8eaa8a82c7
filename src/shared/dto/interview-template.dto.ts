import { ApiProperty } from "@nestjs/swagger";
import { Expose, Type } from "class-transformer";
import { IsArray, IsEnum, ValidateNested } from "class-validator";
import { ObjectId } from "mongoose";
import { InterviewTemplateTypeEnum } from "src/interview-templates/types/interview-template-type.enum";
import { InterviewPageViewDto } from "./interview-page.dto";

export class InterviewTemplateViewDto {
  @ApiProperty()
  @Expose()
  @Type(() => String)
  organisationId: ObjectId;

  @ApiProperty()
  @Expose()
  templateName: string;

  @ApiProperty()
  @Expose()
  @Type(() => InterviewPageViewDto)
  @IsArray()
  @ValidateNested({ each: true })
  pages: InterviewPageViewDto[];

  @ApiProperty()
  @IsEnum(InterviewTemplateTypeEnum)
  @Expose()
  type: InterviewTemplateTypeEnum;

  @ApiProperty()
  @Expose()
  default: boolean;
}