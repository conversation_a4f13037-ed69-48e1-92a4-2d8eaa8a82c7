/* eslint-disable n/handle-callback-err */
import { HttpException, HttpStatus } from '@nestjs/common';
import { ClientSession } from 'mongoose';

export function Transaction(): MethodDecorator {
  return (target, key, descriptor: PropertyDescriptor) => {
    const originalMethod = descriptor.value;
    descriptor.value = async function (...args: any[]) {
      const transactionManager = this.transactionManager;

      if (!transactionManager) {
        throw new Error(
          'TransactionManager not found. Did you forget to inject it?',
        );
      }

      return transactionManager
        .runTransaction(async (session: ClientSession) => {
          return originalMethod.apply(this, [...args, session]);
        })
        .catch((error: Error) => {
          throw new HttpException(
            {
              status: HttpStatus.INTERNAL_SERVER_ERROR,
              error: error.message,
              ...(error.stack ? { stack: error.stack } : {}),
            },
            HttpStatus.INTERNAL_SERVER_ERROR,
          );
        });
    };
    return descriptor;
  };
}
