import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  HttpCode,
} from '@nestjs/common';
import { AuditsService } from './audits.service';
import { CreateAuditDto } from './dto/create-audit.dto';
import { AuthGuard } from '@nestjs/passport';
import { Roles, RolesGuard } from 'src/shared/guards/roles.guard';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { OrganisationGuard } from 'src/shared/guards/organisation.guard';

@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'), OrganisationGuard, RolesGuard)
@Controller('organisations/:organisationId/audits')
export class AuditsController {
  constructor(private auditsService: AuditsService) {}

  @Post()
  @HttpCode(200)
  create(@Body() createAuditDto: CreateAuditDto) {
    return this.auditsService.create(createAuditDto);
  }

  @Get()
  @Roles(RolesEnum.SuperAdmin)
  findAll(@Param('organisationId') organisationId: string) {
    return this.auditsService.findAll({
      organisationId,
    });
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.auditsService.findOne(id);
  }
}
