import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { WealthboxNameMapper } from './mappers/name.mapper';
import { WealthboxAddressMapper } from 'src/integrations/crm/wealthbox/form-mappers/mappers/address.mapper';
import { WealthboxSSNMapper } from 'src/integrations/crm/wealthbox/form-mappers/mappers/ssn.mapper';
import { WealthboxDOBMapper } from 'src/integrations/crm/wealthbox/form-mappers/mappers/dob.mapper';
import { WealthboxPhoneMapper } from 'src/integrations/crm/wealthbox/form-mappers/mappers/phone.mapper';
import { WealthboxConflictOfInterestMapper } from 'src/integrations/crm/wealthbox/form-mappers/mappers/conflict-of-interest.mapper';
import { WealthboxUSCitizenMapper } from 'src/integrations/crm/wealthbox/form-mappers/mappers/us-citizen.mapper';
import { WealthboxEmploymentMapper } from 'src/integrations/crm/wealthbox/form-mappers/mappers/employment.mapper';
import { WealthboxPrimaryBeneficiariesMapper } from 'src/integrations/crm/wealthbox/form-mappers/mappers/primary-beneficiaries.mapper';
import { WealthboxVIPMapper } from 'src/integrations/crm/wealthbox/form-mappers/mappers/vip.mapper';
import { PageUpdateMapper } from 'src/integrations/crm/crm.interface';
import { WealthboxCompanyMapper } from 'src/integrations/crm/wealthbox/form-mappers/mappers/company.mapper';
import { WealthboxJobDescriptionMapper } from 'src/integrations/crm/wealthbox/form-mappers/mappers/job-description.mapper';
import { WealthboxContingentBeneficiariesMapper } from 'src/integrations/crm/wealthbox/form-mappers/mappers/contingent-beneficiaries.mapper';
import { WealthboxCustomQuestionsMapper } from 'src/integrations/crm/wealthbox/form-mappers/mappers/custom-questions.mapper';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';

export function wealthboxMappers(
  this: InstanceType<typeof WealthboxCRM>,
): PageUpdateMapper {
  return {
    [PagesEnum.NAME]: new WealthboxNameMapper(this),
    [PagesEnum.ADDRESS]: new WealthboxAddressMapper(this),
    [PagesEnum.SSN]: new WealthboxSSNMapper(this),
    [PagesEnum.DOB]: new WealthboxDOBMapper(this),
    [PagesEnum.PHONE]: new WealthboxPhoneMapper(this),
    [PagesEnum.CONFLICTS_OF_INTEREST]: new WealthboxConflictOfInterestMapper(
      this,
    ),
    [PagesEnum.US_CITIZEN]: new WealthboxUSCitizenMapper(this),
    [PagesEnum.EMPLOYMENT]: new WealthboxEmploymentMapper(this),
    [PagesEnum.PRIMARY_BENEFICIARIES]: new WealthboxPrimaryBeneficiariesMapper(
      this,
    ),
    [PagesEnum.CONTINGENT_BENEFICIARIES]:
      new WealthboxContingentBeneficiariesMapper(this),
    [PagesEnum.COMPANY]: new WealthboxCompanyMapper(this),
    [PagesEnum.VIP]: new WealthboxVIPMapper(this),
    [PagesEnum.JOB]: new WealthboxJobDescriptionMapper(this),
    [PagesEnum.CUSTOM_QUESTIONS]: new WealthboxCustomQuestionsMapper(this),
  };
}
