#!/bin/bash

# This script provisions the necessary resources for Terraform to operate effectively.
# It creates an S3 bucket for storing the Terraform state file and a DynamoDB table for managing locks on the state file.

# Get environment and region
environment=${TF_VAR_environment}
region=${AWS_REGION:-us-east-1}

# Validate environment
if [[ -z "$environment" ]]; then
    echo "Error: environment must be specified" >&2
    exit 1
fi

if [[ -z "$region" ]]; then
    echo "Error: region must be specified" >&2
    exit 1
fi

# Define your bucket and DynamoDB table prefixes
bucket_prefix="onbord-backend-tf-state"
dynamodb_table_prefix="onbord-backend-tf-state-lock"

# Check and create S3 bucket if it does not exist
if aws s3api head-bucket --bucket ${bucket_prefix}-${environment} --region $region 2>/dev/null; then
    echo "Bucket ${bucket_prefix}-${environment} already exists. Skipping creation."
else
    if [ $region == 'us-east-1' ]; then
        aws s3api create-bucket --bucket ${bucket_prefix}-${environment} --region $region
    else
        aws s3api create-bucket --bucket ${bucket_prefix}-${environment} --region $region --create-bucket-configuration LocationConstraint=$region
    fi
    # Enable versioning on the S3 bucket
    aws s3api put-bucket-versioning --bucket ${bucket_prefix}-${environment} --versioning-configuration Status=Enabled
    echo "Bucket ${bucket_prefix}-${environment} created and versioning enabled."
fi

# Check and create DynamoDB table if it does not exist
if aws dynamodb describe-table --table-name ${dynamodb_table_prefix}-${environment} --region $region 2>/dev/null; then
    echo "DynamoDB table ${dynamodb_table_prefix}-${environment} already exists. Skipping creation."
else
    aws dynamodb create-table --table-name ${dynamodb_table_prefix}-${environment} --attribute-definitions AttributeName=LockID,AttributeType=S --key-schema AttributeName=LockID,KeyType=HASH --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 --region $region
    echo "DynamoDB table ${dynamodb_table_prefix}-${environment} created."
fi
