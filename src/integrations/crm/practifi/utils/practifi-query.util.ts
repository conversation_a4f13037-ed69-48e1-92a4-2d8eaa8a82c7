import { SuperHttpService } from "src/super-http/super-http.service";
import { OperationType } from "src/super-http/enums";
import { commonConfigs } from "../types/types";

// To correctly work with Salesforce SOQL
// ---------------------------------------------------------------
// this -> T&|!(){}[ ]^"~*?:\'+-
// should be converted into
// this -> T%26|!(){}[+]^\"~*?:\\\'%2B-

const charsToConvert = '+&%';

export const formatName = (name: string) => {
  const escapedString = name.replace(/\\/g, "\\\\").replace(/'/g, "\\'").replace(/"/g, "\\\"");
  let formattedName = '';
  for (let i = 0; i < escapedString.length; i++) {
    let char = escapedString[i];
    if (charsToConvert.includes(char)) {
      char = encodeURIComponent(char);
    }
    formattedName += char;
  }
  return formattedName;
}

export const practifiQuery = async (query: string | undefined, url: string, httpService: SuperHttpService, configs: commonConfigs, entityType: string, tenantId: string) => {
  if (!query) {
    return { records: [] };
  }

  const formattedQuery = query.replace(/ /g, '+');
  return await httpService.get(
    `${url}/query?q=${formattedQuery}`,
    tenantId,
    {
      rateLimiter: 'practifi',
      integrationName: 'practifi',
      operation: OperationType.GET,
      entityType,
      cacheKeysObject: {
        query: formattedQuery,
      },
    },
    configs
  );
}