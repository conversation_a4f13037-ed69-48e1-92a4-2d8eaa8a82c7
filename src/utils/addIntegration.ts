import { HttpException, HttpStatus } from '@nestjs/common';
import { FilterQuery, Model } from 'mongoose';
import { Integration } from 'src/shared/schemas/integration.schema';
import { isObjectOfType } from './isObjectOfType';
import { Advisor } from 'src/advisors/schemas/advisors.schema';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { XOR } from 'src/shared/types/monads';
import { isEmpty } from 'lodash';

export async function addIntegration<T extends typeof Model>(
  filter: FilterQuery<XOR<Advisor, Organisation>>,
  integration: Integration,
  model: T,
  isRefresh = false,
) {
  const entity = await model.findOne(filter);
  if (!entity || isEmpty(entity)) {
    throw new HttpException(
      `Cannot add integration, entity not found.`,
      HttpStatus.NOT_FOUND,
    );
  }

  const isOrganisation = isObjectOfType<Organisation>(
    entity,
    'externalAccounts',
  );

  const currentIntegrations = isOrganisation
    ? entity.externalAccounts
    : entity.integrations;

  const key = isOrganisation ? 'externalAccounts' : 'integrations';

  if (isRefresh) {
    const integrationIndex = currentIntegrations.findIndex(
      ({ integrationType }) => integrationType === integration.integrationType,
    );

    if (integrationIndex === -1) {
      throw new HttpException(
        `Cannot refresh integration, integration not found.`,
        HttpStatus.NOT_FOUND,
      );
    }

    currentIntegrations[integrationIndex] = integration;

    return model.updateOne(filter, {
      [key]: currentIntegrations,
      updatedAt: new Date(),
    });
  }

  if (
    currentIntegrations.some(
      ({ integrationType }) => integrationType === integration.integrationType,
    )
  ) {
    throw new HttpException('Integration already exists.', HttpStatus.CONFLICT);
  }

  return model.updateOne(filter, {
    [key]: [...currentIntegrations, integration],
    updatedAt: new Date(),
  });
}
