import { RedtailAddress } from './address.type';
import { RedtailEmail } from './email.type';
import { RedtailPhone } from './phone.type';
import { TagGroup } from './tag.type';
import { RedtailFamily } from './family.type';

export type RedtailSam = {
  id: number;
  contact_id: number;
  objective_id: number | null;
  objective: string | null;
  time_horizon: string | null;
  time_horizon_description: string | null;
  risk_tolerance: string | null;
  risk_tolerance_description: string | null;
  experience_funds: string | null;
  experience_stocks: string | null;
  experience_partnerships: string | null;
  experience_other: string | null;
  deleted: boolean;
  created_at: string;
  updated_at: string;
};

export type RedtailImportantInformation = {
  id: number;
  content: string | null;
  created_at: string;
  updated_at: string;
};

export type RedtailContact = {
  id: number;
  type: string;
  first_name: string;
  middle_name: string | null;
  last_name: string;
  company_name: string | null;
  full_name: string;
  nickname: string | null;
  suffix_id: number | null;
  suffix: string | null;
  job_title: string | null;
  favorite: boolean;
  pronouns: string | null;
  deleted: boolean;
  created_at: string;
  updated_at: string;
  salutation_id: number | null;
  salutation: string | null;
  source_id: number | null;
  source: string | null;
  status_id: number | null;
  status: string | null;
  category_id: number | null;
  category: string | null;
  gender_id: number | null;
  gender: string | null;
  gender_description: string | null;
  spouse_name: string;
  tax_id: string;
  dob: string;
  death_date: string | null;
  client_since: string | null;
  client_termination_date: string | null;
  marital_status_id: number | null;
  marital_status: string | null;
  marital_date: string | null;
  employer_id: number | null;
  employer: string | null;
  designation: string | null;
  referred_by: string | null;
  servicing_advisor_id: number | null;
  servicing_advisor: string | null;
  writing_advisor_id: number | null;
  writing_advisor: string | null;
  added_by: number;
  family: RedtailFamily;
  important_information: RedtailImportantInformation;
  sam: RedtailSam;
  addresses: RedtailAddress[];
  phones: RedtailPhone[];
  emails: RedtailEmail[];
  urls: any[];
  social_medias: any[];
  identifications: any[];
  tag_memberships: TagGroup[];
};
