import { isEmpty } from 'lodash';

/**
 * Checks if an object is of a certain type by checking if it contains a certain attribute
 *
 * @param object The object which's type should be asserted
 * @param attr The attribute to check for
 * @returns {boolean} True if the object is of the type, false otherwise
 */
export function isObjectOfType<T>(object: unknown, attr: string): object is T {
  if (!object || isEmpty(object)) {
    return false;
  }

  return (<T>object)[`${attr}`] !== undefined;
}
