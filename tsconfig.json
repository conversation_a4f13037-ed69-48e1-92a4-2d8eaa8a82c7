{
  "compilerOptions": {
    "module": "commonjs",
    "declaration": true,
    "removeComments": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "target": "es2017",
    "sourceMap": true,
    "outDir": "./dist",
    "baseUrl": "./",
    "incremental": true,
    "skipLibCheck": true,
    "strictNullChecks": false,
    "noImplicitAny": false,
    "resolveJsonModule": true,
    "strictBindCallApply": false,
    "forceConsistentCasingInFileNames": false,
    "noFallthroughCasesInSwitch": false, 
    "esModuleInterop": true,
    "paths": {
      "src/*": ["./src/*"]
    },
    "moduleResolution": "node",
  },
  "include": [
    "src/**/*.ts",
    "scripts/seeding/**/*.ts"
  ],
}
