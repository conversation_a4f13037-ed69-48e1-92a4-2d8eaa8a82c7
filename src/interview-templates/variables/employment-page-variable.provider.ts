import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

export class EmploymentPageVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      employmentStatus: {
        description: 'Employment status (employed, unemployed, retired, etc.)',
        type: VariableType.STRING,
        example: 'employed',
      },
      employer: {
        description: 'Current or previous employer name',
        type: VariableType.STRING,
        example: 'Acme Corporation',
      },
      jobTitle: {
        description: 'Job title or position',
        type: VariableType.STRING,
        example: 'Software Engineer',
      },
      workAddress: {
        description: 'Work location address',
        type: VariableType.ADDRESS,
        example: '456 Business Ave, Chicago, IL 60601',
      },
      yearsOfService: {
        description: 'Years of service with current employer',
        type: VariableType.NUMBER,
        example: 5,
      },
    };
  }
}