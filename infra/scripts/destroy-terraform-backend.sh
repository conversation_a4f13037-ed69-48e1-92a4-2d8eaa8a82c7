#!/bin/bash

# This script destroys the resources for Terraform backend

# Get environment and AWS profile from environment variables
environment=${TF_VAR_environment}
region=us-east-1

# Validate environment
if [[ -z "$environment" ]]; then
    echo "Error: environment must be specified"
    echo "Usage: ./destroy_resources.sh"
    exit 1
fi

# Define your bucket and DynamoDB table prefixes
bucket_prefix="onbord-backend-tf-state"
dynamodb_table_prefix="onbord-backend-tf-state-lock"

# Empty and delete S3 bucket if it exists
if aws s3api head-bucket --bucket ${bucket_prefix}-${environment} --region $region --profile 2>/dev/null; then
    echo "Deleting all objects from bucket ${bucket_prefix}-${environment}."
    aws s3 rm s3://${bucket_prefix}-${environment} --recursive --profile
    echo "Deleting bucket ${bucket_prefix}-${environment}."
    aws s3api delete-bucket --bucket ${bucket_prefix}-${environment} --region $region --profile
    echo "Bucket ${bucket_prefix}-${environment} deleted."
else
    echo "Bucket ${bucket_prefix}-${environment} does not exist. Skipping deletion."
fi

# Delete DynamoDB table if it exists
if aws dynamodb describe-table --table-name ${dynamodb_table_prefix}-${environment} --region $region --profile 2>/dev/null; then
    echo "Deleting DynamoDB table ${dynamodb_table_prefix}-${environment}."
    aws dynamodb delete-table --table-name ${dynamodb_table_prefix}-${environment} --region $region --profile
    echo "DynamoDB table ${dynamodb_table_prefix}-${environment} deleted."
else
    echo "DynamoDB table ${dynamodb_table_prefix}-${environment} does not exist. Skipping deletion."
fi
