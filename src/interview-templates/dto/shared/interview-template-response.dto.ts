import { ApiProperty } from '@nestjs/swagger';
import { InterviewTemplateTypeEnum } from '../../types/interview-template-type.enum';

/**
 * Base response DTO that can be extended by version-specific DTOs
 * This ensures consistency in the response structure across versions
 */
export class InterviewTemplateResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  organisationId: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description?: string;

  @ApiProperty({ enum: InterviewTemplateTypeEnum })
  type: InterviewTemplateTypeEnum;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  pages: any[]; // TODO: Define proper page interface

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}

/**
 * Base pagination response that can be used across versions
 */
export class InterviewTemplatePaginationResponseDto {
  @ApiProperty({ type: [InterviewTemplateResponseDto] })
  data: InterviewTemplateResponseDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  page: number;

  @ApiProperty()
  limit: number;

  @ApiProperty()
  totalPages: number;
}