import { IsString, IsOptional, <PERSON>Array, ValidateNested, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

class AccountTemplateDto {
  @ApiProperty()
  @IsString()
  accountId!: string;

  @ApiProperty({ enum: AccountTypeEnum })
  @IsEnum(AccountTypeEnum)
  accountType!: AccountTypeEnum;

  @ApiProperty()
  @IsString()
  accountLabel!: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  templateId?: string;
}

export class ComposeInterviewDto {
  @ApiProperty()
  @IsString()
  clientId!: string;

  @ApiProperty()
  @IsString()
  organisationId!: string;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  baseTemplateId?: string;

  @ApiProperty({ type: [AccountTemplateDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AccountTemplateDto)
  accounts!: AccountTemplateDto[];

  @ApiProperty({ enum: ['primary', 'secondary'] })
  @IsString()
  contactType!: 'primary' | 'secondary';

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  advisorId?: string;
}