import { HttpException } from '@nestjs/common';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';
import { BeneficiariesPageDataDto } from 'src/integrations/crm/redtail/form-mappers/dto/add-beneficiary-request.dto';

import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import {
  GenericCrmBeneficiaries,
  GenericCrmBeneficiaryEnum,
} from 'src/integrations/crm/types/accounts/crm-account.type';
import {
  WealthboxStoredClientInfo,
  parseDataNoteYAML,
} from 'src/integrations/crm/wealthbox/utils/data-parsing';
import { objectToYAML } from 'src/utils/yaml-parser';
import { getLabelFromAccountNumber } from 'src/integrations/crm/utils/account.utils';
import { isEmpty } from 'lodash';

export class WealthboxContingentBeneficiariesMapper
  implements FormUpdateMapper
{
  constructor(private WealthboxCrm: WealthboxCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: BeneficiariesPageDataDto,
  ): Promise<any> {
    const { beneficiaries } = pageData;

    if (isEmpty(beneficiaries)) return;

    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const dataNote = await this.WealthboxCrm.notesService.getDataNoteContent(
      contact.crmClientId,
    );

    if (!dataNote) {
      await this.WealthboxCrm.notesService.upsertDataNote(contact.crmClientId, {
        linked_to: [
          {
            id: Number(contact.crmClientId),
            type: 'Contact',
          },
        ],
        content: objectToYAML({
          additionalInfo: {
            primaryBeneficiaries: beneficiaries,
          },
          accounts: [],
        }),
      });
      return;
    }

    const contactData: WealthboxStoredClientInfo = parseDataNoteYAML(
      dataNote?.content,
    );
    const contactAccounts = contactData.accounts;
    const accountToUpdate = contactAccounts?.find((x) => {
      const accountLabel = getLabelFromAccountNumber(x?.name).toLowerCase();
      return pageData.instance.label.toLowerCase().includes(accountLabel);
    });

    if (!accountToUpdate) {
      throw new HttpException(
        `Account ${pageData.instance.label} not found`,
        404,
      );
    }

    // insert the new beneficiaries and update the existing ones if there are
    const newBeneficiaries = [];
    pageData.beneficiaries.forEach((beneficiary) => {
      const genericBenef: GenericCrmBeneficiaries = {
        firstName: beneficiary.firstName,
        lastName: beneficiary.lastName,
        fullName: `${beneficiary.firstName} ${beneficiary.lastName}`,
        percentage: beneficiary.allocationAmount,
        type: GenericCrmBeneficiaryEnum.Contingent,
        dob: beneficiary.dob,
      };

      const existingBeneficiary = accountToUpdate.beneficiaries.find(
        (benef) =>
          benef.fullName.toLowerCase() === genericBenef.fullName.toLowerCase(),
      );

      if (existingBeneficiary) {
        existingBeneficiary.percentage = genericBenef.percentage;
      } else {
        newBeneficiaries.push(genericBenef);
      }
    });

    accountToUpdate.beneficiaries.push(...newBeneficiaries);

    await this.WealthboxCrm.notesService.upsertDataNote(contact.crmClientId, {
      linked_to: [
        {
          id: Number(contact.crmClientId),
          type: 'Contact',
        },
      ],
      content: objectToYAML({
        ...contactData,
        accounts: contactAccounts,
      }),
    });
  }
}
