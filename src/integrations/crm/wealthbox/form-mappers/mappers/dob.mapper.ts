import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

export class WealthboxDOBMapper implements FormUpdateMapper {
  constructor(private WealthboxCrm: WealthboxCRM) {}

  /**
   * Maps the date of birth (dob) data from a page to a Wealthbox CRM contact.
   * @param clientCrmId The ID of the client's CRM record in Wealthbox.
   * @param organisation The organisation associated with the client.
   * @param pageData The data from the page containing the dob information.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    // FIXME: Add DTO for dob page data.
    pageData: any,
  ) {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    await this.WealthboxCrm.contactService.updateContact(contact.crmClientId, {
      birth_date: pageData.dob,
    });
  }
}
