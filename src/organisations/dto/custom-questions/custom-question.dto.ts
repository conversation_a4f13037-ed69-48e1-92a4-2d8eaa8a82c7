import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsString } from 'class-validator';

export class CustomQuestionDto {
  @IsString()
  @ApiProperty()
  @Expose()
  question: string;

  @IsString()
  @ApiProperty()
  @Expose()
  @Type(() => String)
  _id: string;

  @IsString()
  @ApiProperty()
  @Expose()
  title?: string;
  

  @IsString()
  @ApiProperty()
  @Expose()
  description?: string;
  

  @IsString()
  @ApiProperty()
  @Expose()
  createdAt?: string;

  @IsString()
  @ApiProperty()
  @Expose()
  updatedAt?: string;
}
