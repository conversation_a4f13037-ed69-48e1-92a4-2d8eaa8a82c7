import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

export class NamePageVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      firstName: {
        description: 'First name of the client',
        type: VariableType.STRING,
        example: '<PERSON>',
      },
      lastName: {
        description: 'Last name of the client',
        type: VariableType.STRING,
        example: '<PERSON>',
      },
      middleName: {
        description: 'Middle name of the client',
        type: VariableType.STRING,
        example: '<PERSON>',
        isConditional: true,
      },
      suffix: {
        description: 'Name suffix (Jr., Sr., III, etc.)',
        type: VariableType.STRING,
        example: 'Jr.',
        isConditional: true,
      },
      fullName: {
        description: 'Complete formatted name',
        type: VariableType.STRING,
        example: '<PERSON> Jr.',
      },
    };
  }
}