import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PractifiAddressUpdateRequestDto } from '../dto/practifi.dto';
import { Country } from 'src/shared/types/general/country.enum';
import { PractifiCRM } from '../crm.practifi';

enum Address {
  PMAILSTREET = 'PersonMailingStreet',
  PMAILCITY = 'PersonMailingCity',
  PMAILSTATE = 'PersonMailingState',
  PMAILPOSTALCODE = 'PersonMailingPostalCode',
  PMAILCOUNTRY = 'PersonMailingCountry',
  MAILSTREET = 'MailingStreet',
  MAILCITY = 'MailingCity',
  MAILSTATE = 'MailingState',
  MAILPOSTALCODE = 'MailingPostalCode',
  MAILCOUNTRY = 'MailingCountry',
  POSTREET = 'PersonOtherStreet',
  POCITY = 'PersonOtherCity',
  POSTATE = 'PersonOtherState',
  POPOSTALCODE = 'PersonOtherPostalCode',
  POCOUNTRY = 'PersonOtherCountry',
  OSTREET = 'OtherStreet',
  OCITY = 'OtherCity',
  OSTATE = 'OtherState',
  OPOSTALCODE = 'OtherPostalCode',
  OCOUNTRY = 'OtherCountry',
}

export class PractifiAddressMapper implements FormUpdateMapper {
  constructor(private practifiCRM: PractifiCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    update: PractifiAddressUpdateRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    let addressBody;
    if (update.mailingAddress) {
      addressBody = {
        [Address.MAILSTREET]: `${update.mailingAddress.line1}${update.mailingAddress.line2 ? update.mailingAddress.line2 : ''}`,
        [Address.MAILCITY]: update.mailingAddress.city,
        [Address.MAILSTATE]: update.mailingAddress.state,
        [Address.MAILPOSTALCODE]: update.mailingAddress.zip,
        [Address.MAILCOUNTRY]: Country.US,
        [Address.OSTREET]: `${update.legalAddress.line1}${update.legalAddress.line2 ? update.legalAddress.line2 : ''}`,
        [Address.OCITY]: update.legalAddress.city,
        [Address.OSTATE]: update.legalAddress.state,
        [Address.OPOSTALCODE]: update.legalAddress.zip,
        [Address.OCOUNTRY]: Country.US,
        practifi__Exclude_Postal_Address_from_Sync__c: true,
        practifi__Exclude_Location_Address_from_Sync__c: true,
      }
    }
    if (!update.mailingAddress) {
      addressBody = {
        [Address.MAILSTREET]: `${update.legalAddress.line1}${update.legalAddress.line2 ? update.legalAddress.line2 : ''}`,
        [Address.MAILCITY]: update.legalAddress.city,
        [Address.MAILSTATE]: update.legalAddress.state,
        [Address.MAILPOSTALCODE]: update.legalAddress.zip,
        [Address.MAILCOUNTRY]: Country.US,
        practifi__Exclude_Postal_Address_from_Sync__c: true,
      }
    }

    await this.practifiCRM.contactService.updateContact(contact.crmClientId, addressBody);
  }
} 