import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { RedtailFamilyService } from '../../services/family.service';
import { RedtailRelationshipEnum } from '../../types/enums';
import { SuperHttpService } from 'src/super-http/super-http.service';

const mockAxios = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
};

describe.skip('RedtailFamilyService', () => {
  let service: RedtailFamilyService;

  beforeEach(async () => {
    service = new RedtailFamilyService(
      {
        axiosRef: mockAxios,
        get: jest.fn(),
        post: jest.fn(),
        put: jest.fn(),
        delete: jest.fn(),
      } as unknown as SuperHttpService,
      {
        Authorization: 'Bearer token',
      },
      'test-tenant-id'
    );
    jest.clearAllMocks();
  });

  describe('getFamily', () => {
    it('should return family data', async () => {
      mockAxios.get.mockResolvedValueOnce({
        data: {
          contact_family: {
            id: 1,
          },
        },
      });
      const result = await service.getFamily('1');
      expect(result).toBeDefined();
    });
  });
  describe('upsertFamily', () => {
    it('should create a new family when none exists', async () => {
      jest.spyOn(service, 'getFamily').mockResolvedValueOnce(null);
      jest.spyOn(service, 'createFamily').mockResolvedValueOnce({
        id: 0,
        name: '',
        created_at: '',
        updated_at: '',
        deleted: false,
        members: [],
      });

      await service.upsertFamily({
        primaryContact: {
          crmClientId: '1',
          firstName: '',
          lastName: '',
          email: '',
          mobile: '',
          skipContactInterview: false,
          accounts: [],
        },
        secondaryContact: {
          crmClientId: '2',
          firstName: '',
          lastName: '',
          email: '',
          mobile: '',
          skipContactInterview: false,
          accounts: [],
        },
      });

      expect(service.createFamily).toHaveBeenCalled();
    });

    it('should add a member to existing family', async () => {
      jest.spyOn(service, 'getFamily').mockResolvedValueOnce({
        members: [{ id: 1, family_id: 1, contact_id: 1 } as any],
        id: 1,
        name: '',
        created_at: '',
        updated_at: '',
        deleted: false,
      });
      jest.spyOn(service, 'addMemberToFamily').mockResolvedValueOnce({
        contact_id: 2,
        family_id: 1,
        created_at: '',
        updated_at: '',
        deleted: false,
        first_name: '',
        last_name: '',
        hoh: false,
        id: 2,
        full_name: '',
        relationship_name: '',
        relationship: 1,
      });

      await service.upsertFamily({
        primaryContact: {
          crmClientId: '1',
          firstName: '',
          lastName: '',
          email: '',
          mobile: '',
          skipContactInterview: false,
          accounts: [],
        },
        secondaryContact: {
          crmClientId: '2',
          firstName: '',
          lastName: '',
          email: '',
          mobile: '',
          skipContactInterview: false,
          accounts: [],
        },
      });

      expect(service.addMemberToFamily).toHaveBeenCalledWith(
        expect.anything(),
        2,
        RedtailRelationshipEnum.SPOUSE,
        false,
      );
    });

    it('should not add an existing member to family', async () => {
      jest.spyOn(service, 'getFamily').mockResolvedValueOnce({
        members: [{ contact_id: 1 } as any, { contact_id: 2 } as any],
        created_at: '',
        updated_at: '',
        deleted: false,
        id: 1,
        name: '',
      });
      jest.spyOn(service, 'addMemberToFamily');

      await service.upsertFamily({
        primaryContact: { crmClientId: 1 } as any,
        secondaryContact: { crmClientId: 2 } as any,
      });

      expect(service.addMemberToFamily).not.toHaveBeenCalled();
    });
  });

  describe('createFamily', () => {
    it('should create a family with only a primary contact', async () => {
      mockAxios.post.mockResolvedValueOnce({ data: { contact_family: {} } });
      const result = await service.createFamily({
        firstName: 'John',
        lastName: 'Doe',
        email: '',
        mobile: '',
        skipContactInterview: false,
        accounts: [],
        crmClientId: '0',
      });
      expect(result).toBeDefined();
    });

    it('should create a family with primary and secondary contacts', async () => {
      mockAxios.post.mockResolvedValueOnce({ data: { contact_family: {} } });
      const result = await service.createFamily(
        {
          firstName: 'John',
          lastName: 'Doe',
          email: '',
          mobile: '',
          skipContactInterview: false,
          accounts: [],
          crmClientId: '0',
        },
        {
          firstName: 'John',
          lastName: 'Doe',
          email: '',
          mobile: '',
          skipContactInterview: false,
          accounts: [],
          crmClientId: '1',
        },
      );
      expect(result).toBeDefined();
    });
  });

  describe('addMemberToFamily', () => {
    it('should add a member to the family', async () => {
      mockAxios.post.mockResolvedValueOnce({ data: { family_member: 'xyz' } });
      await service.addMemberToFamily(
        1,
        '2',
        RedtailRelationshipEnum.SPOUSE,
        false,
      );
    });

    // it('should throw an error if the member cannot be added', async () => {
    //   mockAxios.post.mockRejectedValueOnce(new Error('An error occurred'));
    //   await expect(
    //     service.addMemberToFamily(1, 2, RedtailRelationshipEnum.SPOUSE, false),
    //   ).rejects.toThrow(Error);
    // });
  });
});
