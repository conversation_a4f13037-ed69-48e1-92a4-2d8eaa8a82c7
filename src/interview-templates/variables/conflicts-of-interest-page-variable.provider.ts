import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

export class ConflictsOfInterestPageVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      hasConflicts: {
        description: 'Whether client has conflicts of interest (true/false)',
        type: VariableType.BOOLEAN,
        example: false,
      },
      conflictDetails: {
        description: 'Details about specific conflicts of interest',
        type: VariableType.STRING,
        example: 'Employee of competing financial institution',
        isConditional: true,
      },
      conflictTypes: {
        description: 'Types of conflicts identified',
        type: VariableType.ARRAY,
        example: ['Employment', 'Financial Interest'],
        isConditional: true,
      },
      disclosureDate: {
        description: 'Date conflicts were disclosed',
        type: VariableType.DATE,
        example: '2024-01-15',
        isConditional: true,
      },
    };
  }
}