import { Module, Global } from '@nestjs/common';
import { MutexService } from './mutex.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Mutex, MutexSchema } from './schema/mutex.schema';
import { MutexController } from './mutex.controller';

@Global()
@Module({
  imports: [
    MongooseModule.forFeature([{ name: Mutex.name, schema: MutexSchema }]),
  ],
  controllers: [MutexController],
  providers: [MutexService],
  exports: [MutexService],
})
export class MutexModule {}
