import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsOptional, IsString } from 'class-validator';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';

export class GetOrganisationsQueryDto extends PaginationQueryDto {
  @IsOptional()
  @IsString()
  @ApiProperty()
  name?: string;

  @IsOptional()
  @IsDateString()
  @ApiProperty()
  from?: string;

  @IsOptional()
  @IsDateString()
  @ApiProperty()
  to?: string;
}
