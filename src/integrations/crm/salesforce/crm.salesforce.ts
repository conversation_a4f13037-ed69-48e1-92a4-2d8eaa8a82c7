import { HttpException, HttpStatus } from '@nestjs/common';
import { Connection } from 'jsforce';
import { v4 as uuidv4 } from 'uuid';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import { OAuth } from 'src/auth/auth.types';
import { EnrichedClient, EnrichedContact } from 'src/clients/dto/v1/get-clients.dto';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { InterviewsService } from 'src/interviews/interviews.service';
import { MailService } from 'src/notifications/mail/mail.service';
import { SendEmailOptions } from 'src/notifications/mail/mail.types';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';
import { CrmAuthException } from 'src/shared/exceptions/crm-auth.exception';
import { CRMEnum , CRMType } from 'src/shared/types/integrations';
import { Logger } from 'winston';
import { CRM, PageUpdateMapper } from '../crm.interface';
import { CrmContact } from '../types/contacts/crm-contact.type';
import { CreateContactDto, CreateContactResponseDto, UpdateContactDto, UpdateContactResponseDto } from '../types/create-contact.dto';
import { User } from '../types/user.type';
import { SalesforceAdapter } from './crm.salesforce.adapter';
import { salesforceMappers } from './form-mappers';
import { SalesforceContactService } from './services/salesforce-contact.service';
import { SalesforceTaskService } from './services/salesforce-task.service';
import { SalesforceUserService } from './services/salesforce-user.service';
import { LogCommunicationDto } from 'src/integrations/crm/types/communications/log-communication.dto';
import { GenericEmail } from 'src/integrations/crm/types/generic/email.type';
import { GenericPhone } from 'src/integrations/crm/types/generic/phone.type';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { SalesforcePackageService } from './services/salesforce-package.service';
import { SalesforceCustomObjectsService } from './services/salesforce-custom-objects.service';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { AccountDto } from 'src/shared/types/accounts/account.dto';
import { SalesforceSuperHTTPConfigsService } from './services/salesforce-superhttp-configs.service';

enum SalesforceInitKeys {
  SALESFORCE_INSTALLED = 'packageInstalled'
};
type InitResult = Map<SalesforceInitKeys, string | number>;

export class SalesforceCRM implements CRM {
  pageUpdateMapper: PageUpdateMapper;
  protected readonly type: CRMEnum = CRMEnum.Salesforce;
  protected baseUrl: string;
  protected connection: Connection;
  public contactService: SalesforceContactService;
  public customAccountService: SalesforceCustomObjectsService;
  public taskService: SalesforceTaskService;
  public userService: SalesforceUserService;
  public salesforcePackageService: SalesforcePackageService;
  public superHTTPConfigs: SalesforceSuperHTTPConfigsService;
  private readonly adapter = new SalesforceAdapter();
  protected tenantId: string;

  constructor(
    protected advisor: AdvisorWithRole,
    protected organisation: Organisation,
    protected credentials: OAuth,
    private readonly superHttp: SuperHttpService,
    public interviewsService: InterviewsService,
    protected emailService: MailService,
    protected logger: Logger,
  ) {
    this.baseUrl = credentials.instanceUrl;
    this.tenantId = this.organisation._id.toString();
    this.contactService = new SalesforceContactService(this.superHttp, this.tenantId);
    this.superHTTPConfigs = new SalesforceSuperHTTPConfigsService(this.superHttp, this.tenantId);
    this.customAccountService = new SalesforceCustomObjectsService(this.superHttp, this.tenantId);
    this.taskService = new SalesforceTaskService(this.superHttp, this.tenantId);
    this.userService = new SalesforceUserService(this.superHttp, this.tenantId);
    this.salesforcePackageService = new SalesforcePackageService(this.superHttp, this.tenantId);
    this.initMappers();
    this.initializeConnection();
  }

  initMappers(): PageUpdateMapper {
    this.pageUpdateMapper = salesforceMappers.call(this);
    return this.pageUpdateMapper;
  }

  private initializeConnection(): void {
    try {
      this.logger.debug('Initializing Salesforce connection', {
        baseUrl: this.baseUrl,
        hasAccessToken: !!this.credentials?.accessToken
      });

      if (!this.credentials?.accessToken) {
        throw new Error('Access token not available');
      }

      this.connection = new Connection({
        instanceUrl: this.baseUrl,
        accessToken: this.credentials.accessToken,
        version: '57.0',
        oauth2 : {
          clientId : process.env.SALESFORCE_CLIENT_ID,
          clientSecret : process.env.SALESFORCE_CLIENT_SECRET,
          redirectUri : process.env.SALESFORCE_OAUTH_REDIRECT_URI
        }
      });

      // Initialize services with connection
      this.contactService.setConnection(this.connection);
      this.taskService.setConnection(this.connection);
      this.userService.setConnection(this.connection);
      this.superHTTPConfigs.setRequestConfigs(this.credentials.accessToken);
      this.customAccountService.setConnection(this.connection);
      this.salesforcePackageService.setConnection(this.connection);

      this.logger.debug('Salesforce connection initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Salesforce connection', {
        error: error.message,
        stack: error.stack,
      });
      throw new HttpException(
        'Failed to initialize Salesforce connection',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async checkUnmanagedPackageInstalled() {
    return await this.salesforcePackageService.isPackageInstalled();
  }

  async authenticate(): Promise<OAuth> {
    try {
      const refreshToken = this.credentials.refreshToken;
      const lastRefresh = this.credentials.lastRefresh;
      const lastRefreshTime = new Date(lastRefresh).getTime();
      const oneHourAgo = Date.now() - 1800000;
      const advisorId = this.advisor?._id?.toString();

      if (!advisorId) {
        throw new HttpException(
          'Advisor not found',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      if (lastRefreshTime < oneHourAgo) {
        const tokens = await this.connection.oauth2.refreshToken(refreshToken);

        this.credentials = {
          ...this.credentials,
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token ? tokens.refresh_token : refreshToken,
          lastRefresh: new Date(),
        };

        // Re-initialize connection with new token
        this.initializeConnection();

        return {
          ...this.credentials,
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token ? tokens.refresh_token : refreshToken,
          lastRefresh: new Date(),
          shouldRefresh: true,
        };
      }

      return {
        ...this.credentials,
        shouldRefresh: false,
      };
    } catch (error) {
      const status = error.response?.status || error?.status || error?.code;
      const shouldUnlink = true;

      this.logger.error(
        `Error authenticating with Salesforce: ${error.response?.data ? JSON.stringify(error.response?.data) : error}`,
      );

      throw new CrmAuthException(
        error.message,
        status,
        shouldUnlink,
      );
    }
  }

  async initCrm(): Promise<Map<string, string | number>> {
    const isPackageInstalled = await this.checkUnmanagedPackageInstalled();
    const result: InitResult = new Map<SalesforceInitKeys, string | number>();
    result.set(SalesforceInitKeys.SALESFORCE_INSTALLED, isPackageInstalled ? 1 : 0)
    return Promise.resolve(result);
  }

  public async getType(): Promise<CRMType> {
    return this.type;
  }

  async getContact(id: string): Promise<CrmContact> {
    try {
      const contact = await this.contactService.getContact(id);
      const mappedContact = this.adapter.mapContact(contact);

      const mappedAssets = await this.mapOnbordAccounts(contact.Id);

      if (mappedAssets.length > 0) {
        mappedContact.accounts = mappedAssets;
      }

      return mappedContact;
    } catch (error) {
      this.logger.error('Error getting contact from Salesforce', {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  async getContacts(pagination?: PaginationQueryDto): Promise<CrmContact[]> {
    try {
      const contacts = await this.contactService.getContacts(pagination);
      const mappedContacts = contacts?.records?.map(contact => this.adapter.mapContact(contact));

      return mappedContacts;
    } catch (error) {
      this.logger.error('Error getting contacts from Salesforce', {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  async getAllUsers(pagination?: PaginationQueryDto): Promise<User[]> {
    try {
      const users = await this.userService.getUsers(pagination);
      return users.map(user => ({
        id: user.Id,
        name: `${user.FirstName} ${user.LastName}`,
        email: user.Email,
        crmId: user.Id,
        firstName: user.FirstName,
        lastName: user.LastName,
      }));
    } catch (error) {
      this.logger.error('Error getting users from Salesforce', {
        error: error.message,
        stack: error.stack,
      });
      throw new HttpException(
        'Failed to get users from Salesforce',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createContact(createContactDto: CreateContactDto): Promise<CreateContactResponseDto> {
    try {
      const { primaryContact, secondaryContact } = createContactDto;

      const idempotencyKeyPrimary = uuidv4();
      const idempotencyKeySecondary = uuidv4();

      let primaryContactResponse: any;
      let secondaryContactResponse: any;

      // Create primary contact
      primaryContactResponse = await this.contactService.createContact({
        FirstName: primaryContact.firstName,
        LastName: primaryContact.lastName,
        Email: primaryContact.email,
        MobilePhone: primaryContact.mobile,
      }, idempotencyKeyPrimary);

      if (secondaryContact) {
        if (secondaryContact.crmClientId) {
          await this.contactService.updateContact(secondaryContact.crmClientId, {
            FirstName: secondaryContact.firstName,
            LastName: secondaryContact.lastName,
            Email: secondaryContact.email,
            MobilePhone: secondaryContact.mobile,
          });

          secondaryContactResponse = {};
          secondaryContactResponse.id = secondaryContact.crmClientId;
        }
        if (!secondaryContact.crmClientId) {
          secondaryContactResponse = await this.contactService.createContact({
            FirstName: secondaryContact.firstName,
            LastName: secondaryContact.lastName,
            Email: secondaryContact.email,
            MobilePhone: secondaryContact.mobile,
          }, idempotencyKeySecondary);
        }
      }

      // Create custom accounts
      if ((primaryContact?.accounts && primaryContact.accounts?.length > 0) || (secondaryContact?.accounts && secondaryContact.accounts?.length > 0)) {
        await this.createOnbordAccounts(
          { accounts: primaryContact.accounts, id: primaryContactResponse.id },
          secondaryContact ? { accounts: secondaryContact?.accounts && secondaryContact.accounts?.length > 0 ? secondaryContact.accounts : [], id: secondaryContactResponse.id } : undefined,
        );
      }

      // Create relationship between primary and secondary contacts
      if (primaryContactResponse.id && secondaryContactResponse?.id) {
        await this.contactService.updateContact(primaryContactResponse.id, {
          Onbord_Related_Contact__c: secondaryContactResponse.id,
        });
        await this.contactService.updateContact(secondaryContactResponse.id, {
          Onbord_Related_Contact__c: primaryContactResponse.id,
        });
      }

      return {
        primaryContactCrmId: primaryContactResponse.id,
        secondaryContactCrmId: secondaryContactResponse?.id,
      };
    } catch (error) {
      this.logger.error('Error creating contact in Salesforce', {
        error: error.message,
        createContactDto,
      });
      throw error;
    }
  }

  async updateContact(updateContactDto: UpdateContactDto): Promise<UpdateContactResponseDto> {
    try {
      const primaryData = updateContactDto.primaryContact;
      const secondaryData = updateContactDto.secondaryContact;

      const idempotencyKeySecondary = uuidv4();

      await this.contactService.updateContact(primaryData.crmClientId, {
        FirstName: primaryData.firstName,
        LastName: primaryData.lastName,
        Email: primaryData.email,
        MobilePhone: primaryData.mobile,
        ...(primaryData && secondaryData && { Onbord_Related_Contact__c: secondaryData.crmClientId })
      });

      if (secondaryData) {
        if (secondaryData.crmClientId) {
          await this.contactService.updateContact(secondaryData.crmClientId, {
            FirstName: secondaryData.firstName,
            LastName: secondaryData.lastName,
            Email: secondaryData.email,
            MobilePhone: secondaryData.mobile,
            Onbord_Related_Contact__c: primaryData.crmClientId,
          });
          if (!secondaryData.crmClientId) {
            const secondaryContactResponse = await this.contactService.createContact({
              FirstName: secondaryData.firstName,
              LastName: secondaryData.lastName,
              Email: secondaryData.email,
              MobilePhone: secondaryData.mobile,
              Onbord_Related_Contact__c: primaryData.crmClientId,
            }, idempotencyKeySecondary);
            secondaryData.crmClientId = secondaryContactResponse.id;
          }
        }

      }

      // Create custom accounts
      if ((primaryData?.accounts && primaryData.accounts?.length > 0) || (secondaryData?.accounts && secondaryData.accounts?.length > 0)) {
        await this.createOnbordAccounts(
          { accounts: primaryData.accounts, id: primaryData.crmClientId },
          secondaryData ? { accounts: secondaryData?.accounts && secondaryData.accounts?.length > 0 ? secondaryData.accounts : [], id: secondaryData.crmClientId } : undefined,
        );
      }

      return {
        primaryContactCrmId: primaryData.crmClientId,
        secondaryContactCrmId: secondaryData ? secondaryData.crmClientId : '',
      };
    } catch (error) {
      this.logger.error('Error updating contact in Salesforce', {
        error: error.message,
        updateContactDto,
      });
      throw error;
    }
  }

  async updatePageInfo(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageInfo: { pageName: string; pageData: unknown },
  ): Promise<void> {
    const mapper = this.pageUpdateMapper[pageInfo.pageName];
    if (!mapper) {
      this.logger.warn(`No mapper found for page ${pageInfo.pageName}`);
      return;
    }

    try {
      await mapper.mapPageData(interview, client, organisation, pageInfo.pageData);
    } catch (error) {
      this.logger.error('Error updating page info', {
        error: error.message,
        pageInfo,
        interviewId: interview._id,
      });
      throw error;
    }
  }

  async sendEmail(
    emailOptions: SendEmailOptions,
    contact?: EnrichedContact,
    advisorId?: string,
  ): Promise<void> {
    // Ensure organisation is included in emailOptions
    const emailOptionsWithOrg = {
      ...emailOptions,
      organisation: this.organisation
    };
    return this.emailService.sendEmail(emailOptionsWithOrg, contact, advisorId);
  }

  async getMainEmailOfContact(id: string | number): Promise<GenericEmail> {
    const contact = await this.contactService.getContact(id.toString());
    return this.adapter.toGenericEmail(contact);
  }

  async getPhoneNumbersOfContact(id: string | number): Promise<GenericPhone[]> {
    const contact = await this.contactService.getContact(id.toString());
    return this.adapter.toGenericPhones(contact);
  }

  async getMainMobileOfContact(id: string): Promise<string> {
    const contact = await this.contactService.getContact(id);
    return this.adapter.toMainMobilePhone(contact);
  }

  async getAuthenticatedUserId(): Promise<string> {
    const user = await this.userService.getCurrentUser();
    return user.id;
  }

  async logCommunication(crmClientId: string, logCommunicationDto: LogCommunicationDto): Promise<void> {
    await this.taskService.createCommunicationTask(crmClientId, logCommunicationDto);
  }

  async createOnbordAccounts(
    primAccounts: {
      accounts: AccountDto[],
      id: string
    },
    secAccounts?: {
      accounts: AccountDto[],
      id: string
    }
  ) {
    const primaryAccounts = primAccounts?.accounts ?
      primAccounts.accounts.map(el => this.customAccountService.createCustomAccount({
        Idempotency_key__c: primAccounts.id,
        Name: el.label,
        label__c: el.label,
        advisory_rate__c: el.advisoryRate ? String(el.advisoryRate) : '',
        type__c: el.type,
        ownership__c: el.ownership,
        masterAccountNumber__c: el.masterAccountNumber || '',
        ...(el.features && { features__c: el.features.join(' ') }),
      }, primAccounts.id)) : [];

    const secondaryAccounts = secAccounts?.accounts ?
      secAccounts.accounts.map(el => this.customAccountService.createCustomAccount({
        Idempotency_key__c: secAccounts.id,
        Name: el.label,
        label__c: el.label,
        advisory_rate__c: el.advisoryRate ? String(el.advisoryRate) : '',
        type__c: el.type,
        ownership__c: el.ownership,
        masterAccountNumber__c: el.masterAccountNumber || '',
        ...(el.features && { features__c: el.features.join(' ') }),
      }, secAccounts.id)) : [];

    const accountsResponses = await Promise.all(
      [
        ...primaryAccounts,
        ...secondaryAccounts
      ]
    );

    const successfullAccountsResponsesIds = accountsResponses.filter(acc => acc.response.id);

    const junctionObjects = [];
    for (const el of successfullAccountsResponsesIds) {
      if (el.type === AccountTypeEnum.JointNameBrokerage) {
        junctionObjects.push(this.customAccountService.linkOnbordAccountToContact(primAccounts.id, el.response.id));
        junctionObjects.push(this.customAccountService.linkOnbordAccountToContact(secAccounts.id, el.response.id));
      }
      if (el.type !== AccountTypeEnum.JointNameBrokerage) {
        junctionObjects.push(this.customAccountService.linkOnbordAccountToContact(el.contactId, el.response.id));
      }
    };
    await Promise.all(
      [
        ...junctionObjects,
      ]
    );
  }

  async mapOnbordAccounts(contactId: string) {
    const linkedAccounts: {type: string, name: string, id: string}[] = await this.customAccountService.getLinkedAccounts(contactId);

    const linkedBeneficiariesRequests = linkedAccounts.map(async asset => ({
      ...asset,
      beneficiaries: await this.customAccountService.getAllLinkedBeneficiaries(asset.id)
    }));
    const linkedBeneficiaries = await Promise.all(
      [
        ...linkedBeneficiariesRequests,
      ]
    );

    return linkedBeneficiaries;
  }
}