import { Test, TestingModule } from '@nestjs/testing';
import { InterviewQueueService } from './interview-queue.service';
import { FlowProducer, Queue } from 'bullmq';
import { getQueueToken } from '@nestjs/bullmq';
import { getModelToken } from '@nestjs/mongoose';
import { HttpException, HttpStatus } from '@nestjs/common';
import { INTERVIEW_QUEUE } from 'src/interviews/constants/interview-queue.constant';
import { CLIENT_QUEUE } from 'src/shared/constants/client.constant';
import { InterviewPagesService } from '../pages/interview-pages.service';
import { InterviewEnvelopeService } from '../envelope/interview-envelope.service';
import { InterviewNotificationService } from '../notifications/interview-notification.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { InterviewCoreService } from '../core/interview-core.service';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Interview } from '../../../schemas/v1/interview.schema';
import { InterviewPageStatusEnum } from 'src/interview-templates/types/interview-page-status.enum';
import { CRMEnum } from 'src/shared/types/integrations';
import { CRM_JOBS, CRM_QUEUE } from 'src/integrations/crm/constants/crm.constants';
import { InterviewQueueJobType } from '../../../types/interview-queue-job.enum';
import { ClientQueueJobType } from 'src/clients/types/client-queue-job.enum';
import { InterviewStatusEnum } from 'src/clients/types/client-interview-status';
import { InterviewCompletionParams } from 'src/interviews/types/interview-data.type';
import { UpdateInterviewDto } from '../../../dto/v1/update-interview.dto';
import { Model } from 'mongoose';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';

jest.mock('bullmq');

describe('InterviewQueueService', () => {
  let service: InterviewQueueService;
  let interviewQueue: jest.Mocked<Queue>;
  let clientQueue: jest.Mocked<Queue>;
  let interviewModel: jest.Mocked<Model<Interview>>;
  let pagesService: jest.Mocked<InterviewPagesService>;
  let envelopeService: jest.Mocked<InterviewEnvelopeService>;
  let notificationService: jest.Mocked<InterviewNotificationService>;
  let organisationsService: jest.Mocked<OrganisationsService>;
  let coreService: jest.Mocked<InterviewCoreService>;
  let logger: any;
  let flowProducer: jest.Mocked<FlowProducer>;

  const mockConnectionOpts = {
    host: 'localhost',
    port: 6379,
  };

  beforeEach(async () => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create mock FlowProducer
    flowProducer = {
      add: jest.fn(),
      getFlow: jest.fn(),
      opts: { connection: mockConnectionOpts },
    } as any;
    
    (FlowProducer as jest.MockedClass<typeof FlowProducer>).mockImplementation(() => flowProducer);

    // Create mock queues
    interviewQueue = {
      add: jest.fn(),
      opts: { connection: mockConnectionOpts },
    } as any;

    clientQueue = {
      add: jest.fn(),
      opts: { connection: mockConnectionOpts },
    } as any;

    // Create mock model
    interviewModel = {
      findByIdAndUpdate: jest.fn(),
    } as any;

    // Create mock services
    pagesService = {
      updateInterviewPages: jest.fn(),
    } as any;

    envelopeService = {} as any;

    notificationService = {} as any;

    organisationsService = {
      findOne: jest.fn(),
    } as any;

    coreService = {
      findOne: jest.fn(),
    } as any;

    logger = {
      error: jest.fn(),
      warn: jest.fn(),
      info: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InterviewQueueService,
        {
          provide: getQueueToken(INTERVIEW_QUEUE.NAME),
          useValue: interviewQueue,
        },
        {
          provide: getQueueToken(CLIENT_QUEUE.NAME),
          useValue: clientQueue,
        },
        {
          provide: getModelToken(Interview.name),
          useValue: interviewModel,
        },
        {
          provide: InterviewPagesService,
          useValue: pagesService,
        },
        {
          provide: InterviewEnvelopeService,
          useValue: envelopeService,
        },
        {
          provide: InterviewNotificationService,
          useValue: notificationService,
        },
        {
          provide: OrganisationsService,
          useValue: organisationsService,
        },
        {
          provide: InterviewCoreService,
          useValue: coreService,
        },
        {
          provide: WINSTON_MODULE_PROVIDER,
          useValue: logger,
        },
      ],
    }).compile();

    service = module.get<InterviewQueueService>(InterviewQueueService);
  });

  describe('FlowProducer initialization', () => {
    it('should initialize FlowProducer with correct connection options', () => {
      expect(FlowProducer).toHaveBeenCalledWith({ connection: mockConnectionOpts });
    });
  });

  describe('enqueuePageUpdate', () => {
    const mockInterviewId = '507f1f77bcf86cd799439011';
    const mockUpdateDto: UpdateInterviewDto = {
      page: {
        name: PagesEnum.NAME,
        data: { firstName: 'John', lastName: 'Doe' },
      },
    } as any;

    const mockInterview = {
      _id: mockInterviewId,
      client: {
        organisationId: '507f1f77bcf86cd799439012',
      },
    };

    const mockOrganisation = {
      _id: '507f1f77bcf86cd799439012',
      selectedCRM: CRMEnum.Redtail,
    };

    beforeEach(() => {
      coreService.findOne.mockResolvedValue(mockInterview as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      flowProducer.add.mockResolvedValue({} as any);
    });

    it('should successfully enqueue page update with Redtail CRM', async () => {
      const result = await service.enqueuePageUpdate(mockInterviewId, mockUpdateDto);

      expect(coreService.findOne).toHaveBeenCalledWith(
        { _id: mockInterviewId },
        undefined,
        true
      );
      expect(organisationsService.findOne).toHaveBeenCalledWith(
        mockInterview.client.organisationId.toString()
      );
      expect(pagesService.updateInterviewPages).toHaveBeenCalledWith(
        mockInterviewId,
        mockUpdateDto,
        InterviewPageStatusEnum.IN_PROGRESS
      );
      expect(flowProducer.add).toHaveBeenCalledWith({
        name: InterviewQueueJobType.UPDATE_PAGE.toString(),
        data: {
          interviewId: mockInterviewId,
          organisationId: mockOrganisation._id.toString(),
          updateInterviewDto: mockUpdateDto,
          pageStatus: InterviewPageStatusEnum.SYNCED,
        },
        queueName: INTERVIEW_QUEUE.NAME,
        children: [
          {
            name: CRM_JOBS.UPDATE_PAGE_ANSWER.toString(),
            data: {
              interviewId: mockInterviewId,
              updateInterviewDto: mockUpdateDto,
              organisationId: mockOrganisation._id,
            },
            queueName: CRM_QUEUE.REDTAIL.NAME,
            opts: {
              failParentOnFailure: true,
              attempts: 3,
              backoff: {
                type: 'exponential',
                delay: 1000,
              },
            },
          },
        ],
        opts: {
          failParentOnFailure: true,
          attempts: 3,
          delay: 300,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        },
      });
      expect(result).toEqual(mockInterview);
    });

    it('should throw error when interview is not found', async () => {
      coreService.findOne.mockResolvedValue(null);

      await expect(service.enqueuePageUpdate(mockInterviewId, mockUpdateDto))
        .rejects.toThrow(new HttpException('Interview not found', HttpStatus.NOT_FOUND));

      expect(pagesService.updateInterviewPages).not.toHaveBeenCalled();
      expect(flowProducer.add).not.toHaveBeenCalled();
    });

    it('should handle organization without CRM', async () => {
      organisationsService.findOne.mockResolvedValue({
        _id: '507f1f77bcf86cd799439012',
        selectedCRM: null,
      } as any);

      await expect(service.enqueuePageUpdate(mockInterviewId, mockUpdateDto))
        .rejects.toThrow();
    });

    it('should use correct queue for Wealthbox CRM', async () => {
      organisationsService.findOne.mockResolvedValue({
        ...mockOrganisation,
        selectedCRM: CRMEnum.Wealthbox,
      } as any);

      await service.enqueuePageUpdate(mockInterviewId, mockUpdateDto);

      expect(flowProducer.add).toHaveBeenCalledWith(
        expect.objectContaining({
          children: expect.arrayContaining([
            expect.objectContaining({
              queueName: CRM_QUEUE.WEALTHBOX.NAME,
            }),
          ]),
        })
      );
    });

    it('should use correct queue for Salesforce CRM', async () => {
      organisationsService.findOne.mockResolvedValue({
        ...mockOrganisation,
        selectedCRM: CRMEnum.Salesforce,
      } as any);

      await service.enqueuePageUpdate(mockInterviewId, mockUpdateDto);

      expect(flowProducer.add).toHaveBeenCalledWith(
        expect.objectContaining({
          children: expect.arrayContaining([
            expect.objectContaining({
              queueName: CRM_QUEUE.SALESFORCE.NAME,
            }),
          ]),
        })
      );
    });

    it('should use correct queue for Practifi CRM', async () => {
      organisationsService.findOne.mockResolvedValue({
        ...mockOrganisation,
        selectedCRM: CRMEnum.Practifi,
      } as any);

      await service.enqueuePageUpdate(mockInterviewId, mockUpdateDto);

      expect(flowProducer.add).toHaveBeenCalledWith(
        expect.objectContaining({
          children: expect.arrayContaining([
            expect.objectContaining({
              queueName: CRM_QUEUE.PRACTIFI.NAME,
            }),
          ]),
        })
      );
    });

    it('should handle error and update page status to FAILED', async () => {
      const mockError = new Error('Queue connection failed');
      flowProducer.add.mockRejectedValue(mockError);

      await expect(service.enqueuePageUpdate(mockInterviewId, mockUpdateDto))
        .rejects.toThrow(mockError);

      expect(logger.error).toHaveBeenCalledWith(
        'Failed to enqueue interview update flow',
        {
          error: mockError.message,
          interviewId: mockInterviewId,
        }
      );

      // Should attempt to update page status to FAILED
      expect(pagesService.updateInterviewPages).toHaveBeenCalledTimes(2);
      expect(pagesService.updateInterviewPages).toHaveBeenNthCalledWith(
        2,
        mockInterviewId,
        mockUpdateDto,
        InterviewPageStatusEnum.FAILED
      );
    });

    it('should handle error when updating page status to FAILED also fails', async () => {
      const mockError = new Error('Queue connection failed');
      const updateError = new Error('Database connection failed');
      
      flowProducer.add.mockRejectedValue(mockError);
      pagesService.updateInterviewPages
        .mockResolvedValueOnce(undefined) // First call succeeds
        .mockRejectedValueOnce(updateError); // Second call fails

      await expect(service.enqueuePageUpdate(mockInterviewId, mockUpdateDto))
        .rejects.toThrow(mockError);

      expect(logger.error).toHaveBeenCalledWith(
        'Failed to update page status to FAILED after queue error',
        {
          error: updateError.message,
          interviewId: mockInterviewId,
        }
      );
    });

    it('should handle null/undefined parameters', async () => {
      // Reset mocks to ensure they throw properly
      coreService.findOne.mockReset();
      
      // Mock findOne to throw an error for invalid IDs
      coreService.findOne.mockRejectedValue(new Error('Invalid interview ID'));

      await expect(service.enqueuePageUpdate(null as any, mockUpdateDto))
        .rejects.toThrow();

      await expect(service.enqueuePageUpdate(undefined as any, mockUpdateDto))
        .rejects.toThrow();

      // Reset mock and test null DTO
      coreService.findOne.mockResolvedValue(mockInterview as any);
      pagesService.updateInterviewPages.mockRejectedValue(new Error('Invalid DTO'));
      
      await expect(service.enqueuePageUpdate(mockInterviewId, null as any))
        .rejects.toThrow();
    });

    it('should handle invalid interview ID format', async () => {
      const invalidId = 'invalid-id';
      coreService.findOne.mockRejectedValue(new Error('Invalid ID'));

      await expect(service.enqueuePageUpdate(invalidId, mockUpdateDto))
        .rejects.toThrow();
    });
  });

  describe('enqueueCompletionFlow', () => {
    const mockParams: InterviewCompletionParams = {
      interview: {
        _id: '507f1f77bcf86cd799439011',
      } as any,
      clientId: '507f1f77bcf86cd799439013',
      organisationId: '507f1f77bcf86cd799439012',
      advisorId: '507f1f77bcf86cd799439014',
      primaryContact: {
        email: '<EMAIL>',
      } as any,
      secondaryContact: undefined,
      primaryAdvisor: {
        _id: '507f1f77bcf86cd799439014',
      } as any,
      envelopeId: 'envelope-123',
      docusignSelected: true,
      isPrimary: true,
    };

    const mockOrganisation = {
      _id: '507f1f77bcf86cd799439012',
      selectedCRM: CRMEnum.Redtail,
    };

    beforeEach(() => {
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      flowProducer.getFlow.mockResolvedValue(null);
      flowProducer.add.mockResolvedValue({} as any);
    });

    it('should successfully enqueue completion flow', async () => {
      await service.enqueueCompletionFlow(mockParams);

      expect(organisationsService.findOne).toHaveBeenCalledWith(mockParams.organisationId);
      expect(flowProducer.getFlow).toHaveBeenCalled();
      expect(flowProducer.add).toHaveBeenCalledWith(
        expect.objectContaining({
          queueName: CLIENT_QUEUE.NAME,
          name: ClientQueueJobType.UPDATE_CLIENT_INTERVIEW_COMPLETION.toString(),
          data: expect.objectContaining({
            clientId: mockParams.clientId,
            organisationId: mockParams.organisationId,
            interviewId: mockParams.interview._id.toString(),
            isPrimary: mockParams.isPrimary,
            docusignSelected: mockParams.docusignSelected,
            percentage: 100,
          }),
          children: expect.any(Array),
        })
      );
    });

    it('should throw error when organization is not found', async () => {
      organisationsService.findOne.mockResolvedValue(null);

      await expect(service.enqueueCompletionFlow(mockParams))
        .rejects.toThrow(new HttpException('Organization or CRM not found', HttpStatus.BAD_REQUEST));
    });

    it('should throw error when organization has no CRM', async () => {
      organisationsService.findOne.mockResolvedValue({
        _id: '507f1f77bcf86cd799439012',
        selectedCRM: null,
      } as any);

      await expect(service.enqueueCompletionFlow(mockParams))
        .rejects.toThrow(new HttpException('Organization or CRM not found', HttpStatus.BAD_REQUEST));
    });

    it('should throw error when flow already exists', async () => {
      flowProducer.getFlow.mockResolvedValue({ id: 'existing-flow' } as any);

      await expect(service.enqueueCompletionFlow(mockParams))
        .rejects.toThrow(new HttpException('Interview already in progress', HttpStatus.BAD_REQUEST));
    });

    it('should create complex job hierarchy correctly', async () => {
      await service.enqueueCompletionFlow(mockParams);

      const addCall = flowProducer.add.mock.calls[0][0];
      
      // Root job
      expect(addCall.name).toBe(ClientQueueJobType.UPDATE_CLIENT_INTERVIEW_COMPLETION.toString());
      
      // First child - notification job
      const notificationJob = addCall.children[0];
      expect(notificationJob.name).toBe(InterviewQueueJobType.SEND_NOTIFICATION.toString());
      
      // Second level - update interview status envelope created
      const updateStatusEnvelopeJob = notificationJob.children[0];
      expect(updateStatusEnvelopeJob.name).toBe(ClientQueueJobType.UPDATE_INTERVIEW_STATUS.toString());
      expect(updateStatusEnvelopeJob.data.interviewStatus).toBe(InterviewStatusEnum.EnvelopCreated);
      
      // Third level - update client status after envelope
      const updateClientStatusJob = updateStatusEnvelopeJob.children[0];
      expect(updateClientStatusJob.name).toBe(ClientQueueJobType.UPDATE_CLIENT_STATUS_AFTER_ENVELOPE.toString());
      
      // Fourth level - prepare docusign envelope
      const prepareEnvelopeJob = updateClientStatusJob.children[0];
      expect(prepareEnvelopeJob.name).toBe(InterviewQueueJobType.PREPARE_DOCUSIGN_ENVELOPE.toString());
      
      // Fifth level - update interview status in progress
      const updateStatusInProgressJob = prepareEnvelopeJob.children[0];
      expect(updateStatusInProgressJob.name).toBe(ClientQueueJobType.UPDATE_INTERVIEW_STATUS.toString());
      expect(updateStatusInProgressJob.data.interviewStatus).toBe(InterviewStatusEnum.InProgress);
      
      // Sixth level - mark interview as complete
      const markCompleteJob = updateStatusInProgressJob.children[0];
      expect(markCompleteJob.name).toBe(InterviewQueueJobType.MARK_COMPLETE.toString());
    });

    it('should handle different job configurations based on parameters', async () => {
      // Test with secondary contact
      const paramsWithSecondary = {
        ...mockParams,
        isPrimary: false,
        secondaryContact: { email: '<EMAIL>' } as any,
      };

      await service.enqueueCompletionFlow(paramsWithSecondary);

      expect(flowProducer.add).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            isPrimary: false,
          }),
        })
      );

      // Test without docusign
      const paramsWithoutDocusign = {
        ...mockParams,
        docusignSelected: false,
      };

      await service.enqueueCompletionFlow(paramsWithoutDocusign);

      expect(flowProducer.add).toHaveBeenCalledWith(
        expect.objectContaining({
          data: expect.objectContaining({
            docusignSelected: false,
          }),
        })
      );
    });

    it('should generate unique flow IDs', async () => {
      await service.enqueueCompletionFlow(mockParams);

      const addCall = flowProducer.add.mock.calls[0][0];
      const flowId = addCall.opts.jobId;
      
      expect(flowId).toContain('-update-client');
      expect(flowId).toMatch(/^[\w-]+-update-client$/);
    });

    it('should handle null/undefined required parameters', async () => {
      const invalidParams = {
        ...mockParams,
        interview: null,
      };

      await expect(service.enqueueCompletionFlow(invalidParams as any))
        .rejects.toThrow();
    });
  });

  describe('markInterviewAsComplete', () => {
    const mockInterviewId = '507f1f77bcf86cd799439011';
    const mockSession = {} as any;

    it('should successfully update interview as complete', async () => {
      interviewModel.findByIdAndUpdate.mockResolvedValue({} as any);

      await service.markInterviewAsComplete(mockInterviewId, mockSession);

      expect(interviewModel.findByIdAndUpdate).toHaveBeenCalledWith(
        mockInterviewId,
        {
          $set: {
            isComplete: true,
            updatedAt: expect.any(Date),
          },
        },
        { session: mockSession }
      );
    });

    it('should pass session correctly', async () => {
      const specialSession = { id: 'special-session' } as any;
      
      await service.markInterviewAsComplete(mockInterviewId, specialSession);

      expect(interviewModel.findByIdAndUpdate).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(Object),
        { session: specialSession }
      );
    });

    it('should update timestamp correctly', async () => {
      const beforeCall = new Date();
      
      await service.markInterviewAsComplete(mockInterviewId, mockSession);
      
      const afterCall = new Date();
      const updateCall = interviewModel.findByIdAndUpdate.mock.calls[0][1];
      const updatedAt = updateCall.$set.updatedAt;
      
      expect(updatedAt.getTime()).toBeGreaterThanOrEqual(beforeCall.getTime());
      expect(updatedAt.getTime()).toBeLessThanOrEqual(afterCall.getTime());
    });

    it('should handle database errors', async () => {
      const dbError = new Error('Database connection failed');
      interviewModel.findByIdAndUpdate.mockRejectedValue(dbError);

      await expect(service.markInterviewAsComplete(mockInterviewId, mockSession))
        .rejects.toThrow(dbError);
    });
  });

  describe('getCrmQueueName (private method)', () => {
    // Testing through enqueuePageUpdate
    const mockInterviewId = '507f1f77bcf86cd799439011';
    const mockUpdateDto: UpdateInterviewDto = {
      page: { name: PagesEnum.NAME, data: {} },
    } as any;

    const mockInterview = {
      _id: mockInterviewId,
      client: { organisationId: '507f1f77bcf86cd799439012' },
    };

    beforeEach(() => {
      coreService.findOne.mockResolvedValue(mockInterview as any);
      flowProducer.add.mockResolvedValue({} as any);
    });

    it('should return correct queue name for Redtail', async () => {
      organisationsService.findOne.mockResolvedValue({
        _id: '507f1f77bcf86cd799439012',
        selectedCRM: CRMEnum.Redtail,
      } as any);

      await service.enqueuePageUpdate(mockInterviewId, mockUpdateDto);

      expect(flowProducer.add).toHaveBeenCalledWith(
        expect.objectContaining({
          children: expect.arrayContaining([
            expect.objectContaining({
              queueName: CRM_QUEUE.REDTAIL.NAME,
            }),
          ]),
        })
      );
    });

    it('should return correct queue name for Wealthbox', async () => {
      organisationsService.findOne.mockResolvedValue({
        _id: '507f1f77bcf86cd799439012',
        selectedCRM: CRMEnum.Wealthbox,
      } as any);

      await service.enqueuePageUpdate(mockInterviewId, mockUpdateDto);

      expect(flowProducer.add).toHaveBeenCalledWith(
        expect.objectContaining({
          children: expect.arrayContaining([
            expect.objectContaining({
              queueName: CRM_QUEUE.WEALTHBOX.NAME,
            }),
          ]),
        })
      );
    });

    it('should return correct queue name for Salesforce', async () => {
      organisationsService.findOne.mockResolvedValue({
        _id: '507f1f77bcf86cd799439012',
        selectedCRM: CRMEnum.Salesforce,
      } as any);

      await service.enqueuePageUpdate(mockInterviewId, mockUpdateDto);

      expect(flowProducer.add).toHaveBeenCalledWith(
        expect.objectContaining({
          children: expect.arrayContaining([
            expect.objectContaining({
              queueName: CRM_QUEUE.SALESFORCE.NAME,
            }),
          ]),
        })
      );
    });

    it('should return correct queue name for Practifi', async () => {
      organisationsService.findOne.mockResolvedValue({
        _id: '507f1f77bcf86cd799439012',
        selectedCRM: CRMEnum.Practifi,
      } as any);

      await service.enqueuePageUpdate(mockInterviewId, mockUpdateDto);

      expect(flowProducer.add).toHaveBeenCalledWith(
        expect.objectContaining({
          children: expect.arrayContaining([
            expect.objectContaining({
              queueName: CRM_QUEUE.PRACTIFI.NAME,
            }),
          ]),
        })
      );
    });

    it('should throw error for unsupported CRM type', async () => {
      organisationsService.findOne.mockResolvedValue({
        _id: '507f1f77bcf86cd799439012',
        selectedCRM: 'UnsupportedCRM' as any,
      } as any);

      await expect(service.enqueuePageUpdate(mockInterviewId, mockUpdateDto))
        .rejects.toThrow('Unsupported CRM type');
    });
  });

  describe('Job options and configuration', () => {
    const mockInterviewId = '507f1f77bcf86cd799439011';
    const mockUpdateDto: UpdateInterviewDto = {
      page: { name: PagesEnum.NAME, data: {} },
    } as any;

    const mockInterview = {
      _id: mockInterviewId,
      client: { organisationId: '507f1f77bcf86cd799439012' },
    };

    const mockOrganisation = {
      _id: '507f1f77bcf86cd799439012',
      selectedCRM: CRMEnum.Redtail,
    };

    beforeEach(() => {
      coreService.findOne.mockResolvedValue(mockInterview as any);
      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      flowProducer.add.mockResolvedValue({} as any);
    });

    it('should configure retry attempts correctly', async () => {
      await service.enqueuePageUpdate(mockInterviewId, mockUpdateDto);

      const addCall = flowProducer.add.mock.calls[0][0];
      
      // Parent job retry config
      expect(addCall.opts.attempts).toBe(3);
      expect(addCall.opts.failParentOnFailure).toBe(true);
      
      // Child job retry config
      expect(addCall.children[0].opts.attempts).toBe(3);
      expect(addCall.children[0].opts.failParentOnFailure).toBe(true);
    });

    it('should configure backoff correctly', async () => {
      await service.enqueuePageUpdate(mockInterviewId, mockUpdateDto);

      const addCall = flowProducer.add.mock.calls[0][0];
      
      // Parent job backoff
      expect(addCall.opts.backoff).toEqual({
        type: 'exponential',
        delay: 1000,
      });
      
      // Child job backoff
      expect(addCall.children[0].opts.backoff).toEqual({
        type: 'exponential',
        delay: 1000,
      });
    });

    it('should set correct job delay', async () => {
      await service.enqueuePageUpdate(mockInterviewId, mockUpdateDto);

      const addCall = flowProducer.add.mock.calls[0][0];
      expect(addCall.opts.delay).toBe(300);
    });

    it('should generate unique job IDs for completion flow', async () => {
      const mockParams: InterviewCompletionParams = {
        interview: { _id: '507f1f77bcf86cd799439011' } as any,
        clientId: '507f1f77bcf86cd799439013',
        organisationId: '507f1f77bcf86cd799439012',
        advisorId: '507f1f77bcf86cd799439014',
        primaryContact: { email: '<EMAIL>' } as any,
        primaryAdvisor: { _id: '507f1f77bcf86cd799439014' } as any,
        docusignSelected: true,
        isPrimary: true,
      };

      organisationsService.findOne.mockResolvedValue(mockOrganisation as any);
      flowProducer.getFlow.mockResolvedValue(null);

      await service.enqueueCompletionFlow(mockParams);

      const addCall = flowProducer.add.mock.calls[0][0];
      const flowId = addCall.opts.jobId.split('-')[0];
      
      // Check all jobs have consistent flow ID prefix
      expect(addCall.opts.jobId).toContain(flowId);
      expect(addCall.children[0].opts.jobId).toContain(flowId);
    });
  });

  describe('Edge cases and error scenarios', () => {
    it('should handle network failures gracefully', async () => {
      const networkError = new Error('ECONNREFUSED');
      coreService.findOne.mockRejectedValue(networkError);

      await expect(service.enqueuePageUpdate('507f1f77bcf86cd799439011', {} as any))
        .rejects.toThrow(networkError);
    });

    it('should handle queue connection issues', async () => {
      const queueError = new Error('Redis connection lost');
      flowProducer.add.mockRejectedValue(queueError);

      coreService.findOne.mockResolvedValue({
        _id: '507f1f77bcf86cd799439011',
        client: { organisationId: '507f1f77bcf86cd799439012' },
      } as any);
      organisationsService.findOne.mockResolvedValue({
        _id: '507f1f77bcf86cd799439012',
        selectedCRM: CRMEnum.Redtail,
      } as any);

      await expect(service.enqueuePageUpdate('507f1f77bcf86cd799439011', {} as any))
        .rejects.toThrow(queueError);

      expect(logger.error).toHaveBeenCalled();
    });

    it('should handle invalid ObjectId formats', async () => {
      const invalidId = 'not-a-valid-objectid';
      coreService.findOne.mockRejectedValue(new Error('Invalid ObjectId'));

      await expect(service.enqueuePageUpdate(invalidId, {} as any))
        .rejects.toThrow();
    });

    it('should handle concurrent flow creation', async () => {
      const mockParams: InterviewCompletionParams = {
        interview: { _id: '507f1f77bcf86cd799439011' } as any,
        clientId: '507f1f77bcf86cd799439013',
        organisationId: '507f1f77bcf86cd799439012',
        advisorId: '507f1f77bcf86cd799439014',
        primaryContact: { email: '<EMAIL>' } as any,
        primaryAdvisor: { _id: '507f1f77bcf86cd799439014' } as any,
        docusignSelected: true,
        isPrimary: true,
      };

      organisationsService.findOne.mockResolvedValue({
        _id: '507f1f77bcf86cd799439012',
        selectedCRM: CRMEnum.Redtail,
      } as any);

      // Simulate concurrent flow creation
      flowProducer.getFlow
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce({ id: 'existing-flow' } as any);

      await service.enqueueCompletionFlow(mockParams);

      // Second call should fail
      await expect(service.enqueueCompletionFlow(mockParams))
        .rejects.toThrow(new HttpException('Interview already in progress', HttpStatus.BAD_REQUEST));
    });

    it('should handle partial data scenarios', async () => {
      const partialInterview = {
        _id: '507f1f77bcf86cd799439011',
        client: null,
      };

      coreService.findOne.mockResolvedValue(partialInterview as any);

      await expect(service.enqueuePageUpdate('507f1f77bcf86cd799439011', {} as any))
        .rejects.toThrow();
    });
  });
});