export function getDynamicSleep(baseTime, activeVUs = 1) {
  const scaleFactor = Math.log2(activeVUs + 1);
  return Math.ceil(baseTime * scaleFactor);
}

// As VU count increases, we need to be more lenient with thresholds
export const calculateThresholds = (VUs) => {
  const scenario = __ENV.SCENARIO;

  if (!scenario) return {}

  // Base thresholds
  const baseHttpDuration = 5000;
  const baseClientCreationDuration = 3000;
  const baseNotificationProcessingDuration = 8000;
  const baseClientProcessingDuration = 10000;
  const baseErrorResponseTime = 2000;

  const scaleFactor = Math.max(1, Math.log10(VUs) * 1.5);

  const thresholds = {
    http_req_duration: [`p(95)<${Math.round(baseHttpDuration * scaleFactor)}`]
  };

  if (scenario === 'basic' || scenario === 'withNotification' || scenario === 'all') {
    thresholds['client_creation_duration'] =
      [`p(95)<${Math.round(baseClientCreationDuration * scaleFactor)}`];
  }

  if (scenario === 'withNotification' || scenario === 'all') {
    thresholds['notification_processing_duration'] =
      [`p(95)<${Math.round(baseNotificationProcessingDuration * scaleFactor)}`];
    thresholds['client_processing_duration'] =
      [`p(95)<${Math.round(baseClientProcessingDuration * scaleFactor)}`];
  }

  if (scenario === 'failure' || scenario === 'all') {
    thresholds['error_response_time'] = [`p(95)<${Math.round(baseErrorResponseTime * scaleFactor)}`];
  }

  return thresholds;
};