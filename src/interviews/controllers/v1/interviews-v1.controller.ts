import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  UseInterceptors,
  UploadedFiles,
  Res,
  Put,
  HttpException,
  HttpStatus,
  Query,
  VERSION_NEUTRAL,
} from '@nestjs/common';
import { Response } from 'express';
import { InterviewsService } from '../../interviews.service';
import { CreateInterviewDto } from '../../dto/v1/create-interview.dto';
import { UpdateInterviewDto } from '../../dto/v1/update-interview.dto';
import { AuthGuard } from '@nestjs/passport';
import { OrganisationGuard } from 'src/shared/guards/organisation.guard';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { FilesInterceptor } from '@nestjs/platform-express';
import { UploadDocumentDto } from '../../dto/v1/upload-document.dto';
import { MAX_FILE_SIZE } from 'src/shared/types/general/asset-types.enum';
import { GetInterviewQueryDto } from '../../dto/v1/get-interview-query.dto';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { InterviewViewDto } from '../../dto/v1/view-interview.dto';
import { plainToInstance } from 'class-transformer';
import { CacheInterceptor, CacheTTL } from '@nestjs/cache-manager';

@ApiAuthProtectedRoutes()
@Controller({ path: 'interviews', version: ['1', VERSION_NEUTRAL] })
@UseInterceptors(CacheInterceptor)
@ApiTags('Interviews V1')
export class InterviewsV1Controller {
  constructor(private readonly interviewsService: InterviewsService) {}

  @Post()
  @UseGuards(AuthGuard('jwt'), OrganisationGuard)
  async create(@Body() createInterviewDto: CreateInterviewDto) {
    return this.interviewsService.create(createInterviewDto);
  }
  
  @CacheTTL(500)
  @Get(':interviewId')
  @ApiResponse({ type: InterviewViewDto })
  async findOne(@Param('interviewId') interviewId: string) {
    const interview = await this.interviewsService.findById(interviewId, undefined, true);
    return plainToInstance(InterviewViewDto, interview, {
      excludeExtraneousValues: true,
      enableImplicitConversion: true,
    });
  }

  @Get('/client/:clientId')
  async findByClientId(
    @Param('clientId') clientId: string,
    @Query() query: GetInterviewQueryDto,
  ) {
    const interview = await this.interviewsService.findOne({
      client: clientId,
      isPrimary: query.relatesTo ? query.relatesTo === 'Primary' : true,
    });

    if (!interview) {
      throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);
    }

    return interview;
  }

  @Patch(':interviewId')
  async update(
    @Param('interviewId') interviewId: string,
    @Body() updateInterviewDto: UpdateInterviewDto,
  ) {
    return this.interviewsService.enqueueFlow(interviewId, updateInterviewDto);
  }

  @Delete(':interviewId')
  async remove(@Param('interviewId') interviewId: string) {
    return this.interviewsService.remove(interviewId);
  }

  @Put(':interviewId/upload')
  @UseInterceptors(
    FilesInterceptor('files', 1, {
      limits: { fileSize: MAX_FILE_SIZE },
    }),
  )
  async upload(
    @Param('interviewId') interviewId: string,
    @UploadedFiles() files: Express.Multer.File[],
    @Body() dto: UploadDocumentDto,
    @Res() res: Response,
  ) {
    res.setTimeout(60000 * 10);
    const result = await this.interviewsService.upload(interviewId, files, dto);
    return res.send(result);
  }

  @Post(':interviewId/finish')
  async finish(@Param('interviewId') interviewId: string) {
    return this.interviewsService.complete(interviewId);
  }

  @Get(':interviewId/sendDesktopInterviewEmail')
  async sendDesktopInterviewEmail(@Param('interviewId') interviewId: string) {
    return this.interviewsService.sendDesktopInterviewEmail(interviewId);
  }

  @Get(':interviewId/sendNonCitizenEmail')
  async sendNonCitizenEmail(@Param('interviewId') interviewId: string) {
    return this.interviewsService.sendNonCitizenEmail(interviewId);
  }
}