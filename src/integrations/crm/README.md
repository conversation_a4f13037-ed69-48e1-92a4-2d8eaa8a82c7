
# CRM Integration Overview

Programming design pattern: Factory

# Interview Page Update in CRM

Programming design pattern: Adapter

# CRM Service and Factory Overview

`CRMService` is a service class annotated with `@Injectable()`, making it a candidate for dependency injection within the NestJS ecosystem. This service is responsible for handling business logic related to CRM operations.

`CRMFactory` is a factory class with a static method `createCRM` that is responsible for creating instances of specific CRM implementations based on the provided type. This pattern allows for the dynamic creation of CRM instances without tightly coupling the code to specific CRM implementations.

## CrmAdapter Interface

The `CrmAdapter` interface is designed to standardize the process of converting CRM-specific data formats into a generic format that can be used uniformly across the application. This abstraction facilitates easier integration of multiple CRM systems by providing a common interface for data operations. The interface includes methods for mapping addresses, accounts, phone numbers, and email addresses from CRM-specific formats to generic formats.

- **`mapAddress`**: Converts a CRM-specific address format to a `GenericAddress` format. This method can accept additional arguments for more complex mapping scenarios.
- **`mapAccount`**: Transforms a CRM-specific account representation into a `GenericCrmAccount`, making it easier to handle accounts across different CRM systems in a uniform manner.
- **`mapPhone`**: Converts a CRM-specific phone number into a `GenericPhone` format, standardizing phone number handling.
- **`mapEmail`**: Transforms a CRM-specific email address into a `GenericEmail` format, facilitating uniform email address handling across different CRMs.

## CRM Interface

The `CRM` interface outlines the core functionalities expected from a CRM implementation within the application. It includes methods for initializing the CRM, authentication, managing contacts, updating page information, and logging communications. This interface ensures that any CRM implementation provides a consistent set of functionalities.

- **`initCrm`, `authenticate`, `createContact`, `updateContact`, `getContact`, and `getContacts`**: Cover initialization, authentication, and basic CRUD operations for contacts within the CRM.
- **`getAllUsers`, `getType`, `getMainEmailOfContact`, `getPhoneNumbersOfContact`, `getMainMobileOfContact`**: Provide additional utility methods for retrieving user information, CRM type, and contact details.
- **`updatePageInfo`, `getAuthenticatedUserId`, `initMappers`, `logCommunication`, `sendEmail`**: Include methods for updating page information based on interviews and client data, logging communications, and sending emails.
