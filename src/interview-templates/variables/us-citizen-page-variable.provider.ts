import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

export class UsCitizenPageVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      isUsCitizen: {
        description: 'US citizenship status (true/false)',
        type: VariableType.BOOLEAN,
        example: true,
      },
      citizenshipType: {
        description: 'Type of citizenship (born, naturalized, etc.)',
        type: VariableType.STRING,
        example: 'born',
        isConditional: true,
      },
      countryOfBirth: {
        description: 'Country where client was born',
        type: VariableType.STRING,
        example: 'United States',
      },
      immigrationStatus: {
        description: 'Immigration status if not a US citizen',
        type: VariableType.STRING,
        example: 'Permanent Resident',
        isConditional: true,
      },
    };
  }
}