import { TransitionTypeEnum } from './transition-type.enum';
import { TransitionDto, ContactDto } from '../dto/create-transitions.dto';
import { ICsvRow } from 'src/csv/csv.interface';

export class TransitionCsvRow implements ICsvRow {
  // Common properties
  firstName: string;
  lastName: string;
  mobile: string;
  primaryAdvisorEmail: string;
  primaryCSAEmail: string;
  assetsValue: number;

  // Properties to store Advisor and CSA ids after lookup
  primaryAdvisor: string;
  primaryCSA: string;

  // Properties for Protocol transitions
  email?: string;

  // Properties for Coapplicant
  coAppFirstName?: string;
  coAppLastName?: string;
  coAppMobile?: string;
  coAppEmail?: string;

  static fromCsvObject(csvObject: any): ICsvRow {
    const instance = new TransitionCsvRow();

    // Common assignments
    instance.firstName = csvObject['First Name'];
    instance.lastName = csvObject['Last Name'];
    instance.mobile = csvObject.Mobile;
    instance.primaryAdvisorEmail = csvObject['Primary Advisor Email'];
    instance.primaryCSAEmail = csvObject['Primary CSA Email'];
    instance.assetsValue = Number(csvObject['Asset Value']);

    // Check for Protocol or Non-Protocol
    if (csvObject.Email !== undefined) {
      // Protocol
      instance.email = csvObject.Email;
      instance.coAppEmail = csvObject['Coapplicant Email'];
    }

    // Coapplicant common assignments
    instance.coAppFirstName = csvObject['Coapplicant First Name'];
    instance.coAppLastName = csvObject['Coapplicant Last Name'];
    instance.coAppMobile = csvObject['Coapplicant Mobile'];

    if (!instance.isValid()) {
      throw new Error('Primary contact details are missing');
    }

    return instance;
  }

  isValid(): boolean {
    // check if there is any field for the primary contact
    if (!this.firstName && !this.lastName && !this.mobile) {
      return false;
    }
    return true;
  }

  toCreateDto(transitionType: TransitionTypeEnum): TransitionDto {
    const primaryContact = new ContactDto();
    primaryContact.firstName = this.firstName;
    primaryContact.lastName = this.lastName;
    primaryContact.mobile = this.mobile;
    primaryContact.email =
      transitionType === TransitionTypeEnum.Protocol ? this.email : undefined;

    let secondaryContact: ContactDto | undefined;
    if (this.coAppFirstName) {
      secondaryContact = new ContactDto();
      secondaryContact.firstName = this.coAppFirstName;
      secondaryContact.lastName = this.coAppLastName;
      secondaryContact.mobile = this.coAppMobile;
      secondaryContact.email =
        transitionType === TransitionTypeEnum.Protocol
          ? this.coAppEmail
          : undefined;
    }

    return {
      assetsValue: this.assetsValue,
      primaryContact,
      secondaryContact,
      primaryAdvisor: this.primaryAdvisor,
      primaryCSA: this.primaryCSA,
      transitionType,
    };
  }
}
