import { isEmpty } from 'lodash';
import { GenericCrmBeneficiaryEnum } from 'src/integrations/crm/types/accounts/crm-account.type';
import { GenericEmploymentStatusEnum } from 'src/integrations/crm/types/generic/employment.status.enum';
import { JobDescription } from 'src/integrations/crm/types/job-description.enum';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';
import { AccountOwnershipEnum } from 'src/shared/types/accounts/account-ownership.enum';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { getAccountByFileName } from 'src/utils/pdf-mappers/utils/getAccountByFileName';

export const accountBooleanMappers = {
  'account.isIndividual': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return account.ownership === AccountOwnershipEnum.Individual;
    },
  },
  'account.isJtwros': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return account.ownership === AccountOwnershipEnum.JTWROS;
    },
  },
  'account.isJointTenantsInCommon': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return account.ownership === AccountOwnershipEnum.JTIC;
    },
  },
  'account.isTenantsByEntirety': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return account.ownership === AccountOwnershipEnum.entirety;
    },
  },
  'account.isRothIra': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return account.type === AccountTypeEnum.RothIra;
    },
  },
  'account.isIra': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return account.type === AccountTypeEnum.Ira;
    },
  },
  'account.beneficiaries#.isPrimary': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return (
        account.beneficiaries[beneficiaryIndex].type ===
        GenericCrmBeneficiaryEnum.Primary
      );
    },
  },
  'account.beneficiaries#.isContingent': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return (
        account.beneficiaries[beneficiaryIndex].type ===
        GenericCrmBeneficiaryEnum.Contingent
      );
    },
  },

  // Applicant
  'account.applicant.isEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return (
        applicant?.additionalInfo?.employmentStatus ===
        GenericEmploymentStatusEnum.EMPLOYED
      );
    },
  },
  'account.applicant.isBusinessOwner': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.BUSINESS_OWNER,
      );
    },
  },
  'account.applicant.isBusinessOwnerSelfEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const jobDescription = applicant?.additionalInfo.jobDescription;
      return (
        jobDescription.includes(JobDescription.BUSINESS_OWNER) ||
        jobDescription.includes(JobDescription.SELF_EMPLOYED)
      );
    },
  },
  'account.applicant.isClerical': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.CLERICAL,
      ) || applicant?.additionalInfo.jobDescription.includes(
        JobDescription.ADMINISTRATIVE_SERVICES,
      );
    },
  },
  'account.applicant.isConsultant': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.CONSULTANT,
      );
    },
  },
  'account.applicant.isEducator': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.EDUCATOR,
      );
    },
  },
  'account.applicant.isExecutive': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.EXECUTIVE,
      );
    },
  },
  'account.applicant.isFinancialServices': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.FINANCIAL_SERVICES,
      );
    },
  },
  'account.applicant.isForeignGovernmentEmployee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.FOREIGN_GOVERNMENT_EMPLOYEE,
      );
    },
  },
  'account.applicant.isHomemaker': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.employmentStatus.includes(
        JobDescription.HOMEMAKER,
      );
    },
  },
  'account.applicant.isInformationTechnologyProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.INFORMATION_TECHNOLOGY_PROFESSIONAL,
      ) || applicant?.additionalInfo.jobDescription.includes(
        JobDescription.IT_PROFESSIONAL,
      );
    },
  },
  'account.applicant.isLegalProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.LEGAL_PROFESSIONAL,
      );
    },
  },
  'account.applicant.isMedicalProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.MEDICAL_PROFESSIONAL,
      );
    },
  },
  'account.applicant.isMilitary': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.MILITARY,
      );
    },
  },
  'account.applicant.isNotEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return (
        applicant?.additionalInfo?.employmentStatus ===
        GenericEmploymentStatusEnum.NOTEMPLOYED
      );
    },
  },
  'account.applicant.isOtherProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.OTHER_PROFESSIONAL,
      );
    },
  },
  'account.applicant.isRetired': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return (
        applicant?.additionalInfo?.employmentStatus ===
        GenericEmploymentStatusEnum.RETIRED
      );
    },
  },
  'account.applicant.isSalesMarketing': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.SALES_MARKETING,
      ) || applicant?.additionalInfo.jobDescription.includes(
        JobDescription.SALES_MARKETING_2,
      );
    },
  },
  'account.applicant.isSelfEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const jobDescription = applicant?.additionalInfo.employmentStatus;
      return (
        jobDescription.includes(JobDescription.BUSINESS_OWNER) ||
        jobDescription.includes(JobDescription.SELF_EMPLOYED)
      );
    },
  },
  'account.applicant.isStudent': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.STUDENT,
      );
    },
  },
  'account.applicant.isTradeServiceCareer': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.jobDescription.includes(
        JobDescription.TRADE_SERVICE_CAREER,
      );
    },
  },
  'account.applicant.isUSGovernmentEmployee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const jobDescription = applicant?.additionalInfo.jobDescription;
      return jobDescription.includes(JobDescription.US_GOVERNMENT_EMPLOYEE);
    },
  },
  'account.applicant.isAccountingProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const jobDescription = applicant?.additionalInfo.jobDescription;
      return jobDescription.includes(JobDescription.ACCOUNTING_PROFESSIONAL);
    },
  },
  'account.applicant.isUSCitizen': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return true;
    },
  },
  'account.applicant.isUSResident': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      // While we don't store the residence in the CRM
      return true;
    },
  },
  'account.applicant.hasPublicCompanyAssociation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return !isEmpty(applicant.additionalInfo.companyAssociation);
    },
  },
  'account.applicant.hasIndustryAffiliation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return !isEmpty(applicant.additionalInfo.industryAffiliation);
    },
  },
  'account.applicant.isNotUScitizen': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return false;
    },
  },
  'account.applicant.isNotUSresident': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return false;
    },
  },
  'account.applicant.hasNoPublicCompanyAssociation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return isEmpty(applicant.additionalInfo.companyAssociation);
    },
  },
  'account.applicant.hasNoIndustryAffiliation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return isEmpty(applicant.additionalInfo.industryAffiliation);
    },
  },

  // Co Applicant
  'account.coapplicant.isEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return (
        coapplicant?.additionalInfo?.employmentStatus ===
        GenericEmploymentStatusEnum.EMPLOYED
      );
    },
  },
  'account.coapplicant.isBusinessOwner': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.BUSINESS_OWNER,
      );
    },
  },
  'account.coapplicant.isBusinessOwnerSelfEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      const jobDescription = coapplicant?.additionalInfo.jobDescription;
      return (
        jobDescription.includes(JobDescription.BUSINESS_OWNER) ||
        jobDescription.includes(JobDescription.SELF_EMPLOYED)
      );
    },
  },
  'account.coapplicant.isClerical': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.CLERICAL,
      ) || applicant?.additionalInfo.jobDescription.includes(
        JobDescription.ADMINISTRATIVE_SERVICES,
      );
    },
  },
  'account.coapplicant.isConsultant': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.CONSULTANT,
      );
    },
  },
  'account.coapplicant.isEducator': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.EDUCATOR,
      );
    },
  },
  'account.coapplicant.isExecutive': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.EXECUTIVE,
      );
    },
  },
  'account.coapplicant.isFinancialServices': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.FINANCIAL_SERVICES,
      );
    },
  },
  'account.coapplicant.isForeignGovernmentEmployee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.FOREIGN_GOVERNMENT_EMPLOYEE,
      );
    },
  },
  'account.coapplicant.isHomemaker': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return coapplicant?.additionalInfo.employmentStatus.includes(
        JobDescription.HOMEMAKER,
      );
    },
  },
  'account.coapplicant.isAccountingProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      const jobDescription = coapplicant?.additionalInfo.jobDescription;
      return jobDescription.includes(JobDescription.ACCOUNTING_PROFESSIONAL);
    },
  },
  'account.coapplicant.isInformationTechnologyProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.INFORMATION_TECHNOLOGY_PROFESSIONAL,
      ) || applicant?.additionalInfo.jobDescription.includes(
        JobDescription.IT_PROFESSIONAL,
      );
    },
  },
  'account.coapplicant.isLegalProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.LEGAL_PROFESSIONAL,
      );
    },
  },
  'account.coapplicant.isMedicalProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.MEDICAL_PROFESSIONAL,
      );
    },
  },
  'account.coapplicant.isMilitary': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.MILITARY,
      );
    },
  },
  'account.coapplicant.isNotEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return (
        coapplicant?.additionalInfo?.employmentStatus ===
        GenericEmploymentStatusEnum.NOTEMPLOYED
      );
    },
  },
  'account.coapplicant.isOtherProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.OTHER_PROFESSIONAL,
      );
    },
  },
  'account.coapplicant.isRetired': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return (
        coapplicant?.additionalInfo?.employmentStatus ===
        GenericEmploymentStatusEnum.RETIRED
      );
    },
  },
  'account.coapplicant.isSalesMarketing': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.SALES_MARKETING,
      ) || applicant?.additionalInfo.jobDescription.includes(
        JobDescription.SALES_MARKETING_2,
      );
    },
  },
  'account.coapplicant.isSelfEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      const jobDescription = coapplicant?.additionalInfo.employmentStatus;
      return jobDescription.includes(JobDescription.SELF_EMPLOYED);
    },
  },
  'account.coapplicant.isStudent': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.STUDENT,
      );
    },
  },
  'account.coapplicant.isTradeServiceCareer': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return coapplicant?.additionalInfo.jobDescription.includes(
        JobDescription.TRADE_SERVICE_CAREER,
      );
    },
  },
  'account.coapplicant.isUSGovernmentEmployee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      const jobDescription = coapplicant?.additionalInfo.jobDescription;
      return jobDescription.includes(JobDescription.US_GOVERNMENT_EMPLOYEE);
    },
  },
  'account.coapplicant.isUSCitizen': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      if (coapplicant) {
        return true;
      }
    },
  },
  'account.coapplicant.isUSResident': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      if (coapplicant) {
        return true;
      }
    },
  },
  'account.coapplicant.isNotUSresident': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      if (coapplicant) {
        return false;
      }
    },
  },
  'account.coapplicant.isNotUScitizen': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      if (coapplicant) {
        return false;
      }
    },
  },
  'account.coapplicant.hasPublicCompanyAssociation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );
      return !isEmpty(coapplicant.additionalInfo.companyAssociation);
    },
  },
  'account.coapplicant.hasIndustryAffiliation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return !isEmpty(coapplicant.additionalInfo.industryAffiliation);
    },
  },
  'account.coapplicant.hasNoPublicCompanyAssociation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return isEmpty(coapplicant.additionalInfo.companyAssociation);
    },
  },
  'account.coapplicant.hasNoIndustryAffiliation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return isEmpty(coapplicant.additionalInfo.companyAssociation);
    },
  },
};
