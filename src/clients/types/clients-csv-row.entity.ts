import { ICsvRow } from 'src/csv/csv.interface';
import { CreateClientDto } from '../dto/v1/create-client.dto';
import { NotificationTypeEnum } from './notification-types.enum';

export class ClientCsvRow implements ICsvRow {
  firstName: string;
  lastName: string;
  mobile: string;
  email: string;
  coAppFirstName: string;
  coAppLastName: string;
  coAppMobile: string;
  coAppEmail: string;

  // This method will be used to create a new instance from a CSV object
  static fromCsvObject(csvObject: any): ICsvRow {
    const instance = new ClientCsvRow();
    instance.firstName = csvObject['First Name'];
    instance.lastName = csvObject['Last Name'];
    instance.mobile = csvObject.Mobile;
    instance.email = csvObject.Email;
    instance.coAppFirstName = csvObject['Coapplicant First Name'];
    instance.coAppLastName = csvObject['Coapplicant Last Name'];
    instance.coAppMobile = csvObject['Coapplicant Mobile'];
    instance.coAppEmail = csvObject['Coapplicant Email'];

    return instance;
  }

  // This method will transform the CsvRow instance into a CreateClientDto object
  toCreateDto(team: any): CreateClientDto {
    const primaryContact = {
      firstName: this.firstName,
      lastName: this.lastName,
      mobile: this.mobile,
      email: this.email,
      skipContactInterview: false,
      accounts: [],
      crmClientId: undefined,
    };

    let secondaryContact;
    if (this.coAppFirstName) {
      secondaryContact = {
        firstName: this.coAppFirstName,
        lastName: this.coAppLastName,
        mobile: this.coAppMobile,
        email: this.coAppEmail,
        skipContactInterview: false,
        accounts: [],
        crmClientId: undefined,
      };
    }

    return {
      doClientProfiling: true,
      addAccountsSelected: false,
      isAlreadyOnbord: false,
      shouldUpdateCrm: true,
      readyToSend: false,
      featuresSelected: true,
      primaryContact,
      secondaryContact,
      primaryAdvisor: team.primaryAdvisor,
      primaryCSA: team.primaryCSA,
      secondaryAdvisor: team.secondaryAdvisor || [],
      secondaryCSA: team.secondaryCSA || [],
      customTemplates: [],
      docusignSelected: false,
      sendNow: false,
      notificationMethods: [NotificationTypeEnum.EMAIL, NotificationTypeEnum.SMS]
    };
  }
}
