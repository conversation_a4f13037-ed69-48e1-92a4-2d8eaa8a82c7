export enum DBAccountType {
  joint = 'Joint-Name Brokerage Account',
  ira = 'Traditional IRA',
  roth = 'Roth IRA',
  brokerage = 'Single-Name Brokerage Account',
  offline = 'Offline',
  offline_trust = 'Trust (offline)',
  offline_inherited_ira = 'Inherited IRA (offline)',
  offline_simple_ira = 'Simple IRA (offline)',
  offline_corporate = 'Corporate (offline)',
  offline_other = 'Other (offline)',
}
