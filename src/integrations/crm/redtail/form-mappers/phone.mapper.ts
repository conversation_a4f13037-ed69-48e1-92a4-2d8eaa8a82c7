import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { PhoneMapperRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/phone-request.dto';
import { RedtailPhoneTypeEnum } from 'src/integrations/crm/redtail/types/enums';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

export class RedtailPhoneMapper implements FormUpdateMapper {
  constructor(private redtailCrmService: RedtailCRM) {}

  /**
   * Maps page data to create mobile numbers in Redtail CRM for a given client and organisation.
   * @param crmClientId The ID of the client in Redtail CRM.
   * @param organisation The organisation associated with the client.
   * @param pageData The page data containing the alternate phone numbers to create.
   * @returns A Promise that resolves when all mobile numbers have been created in Redtail CRM.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: PhoneMapperRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    const { alternatePhones } = pageData;
    if (!alternatePhones) return;
    const promises = alternatePhones.map(({ number, type }) =>
      this.redtailCrmService.redtailPhoneNumberService.upsertPhone(
        contact.crmClientId,
        number,
        RedtailPhoneTypeEnum[type.toUpperCase()],
        false,
        false,
      ),
    );
    await Promise.all(promises);
  }
}
