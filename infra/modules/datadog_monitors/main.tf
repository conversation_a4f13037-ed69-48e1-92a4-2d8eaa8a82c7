locals {
  query_threshold = var.healthy_host_count_threshold > 1 ? var.healthy_host_count_threshold - 1 : var.healthy_host_count_threshold
}

terraform {
  required_providers {
    datadog = {
      source = "DataDog/datadog"
    }
  }
}

# Infrastructure Metrics: CPU Utilization
resource "datadog_monitor" "cpu_utilization" {
  name               = "High CPU Utilization"
  type               = "metric alert"
  message            = "CPU usage is too high on host {{host.name}}"
  escalation_message = "CPU usage is still high on host {{host.name}}"
  tags               = ["Environment:${var.environment}", "Service:backend"]

  query = "avg(last_5m):avg:aws.ec2.cpuutilization{Environment:${var.environment},Service:backend} by {host} > 80"

  monitor_thresholds {
    warning           = "75"
    critical          = "80"
    warning_recovery  = "70"
    critical_recovery = "75"
  }

  notify_no_data    = true
  renotify_interval = 20
}

# Elastic Beanstalk Metrics: Environment Health
resource "datadog_monitor" "beanstalk_env_health" {
  name               = "Elastic Beanstalk Environment Health"
  type               = "metric alert"
  message            = "Elastic Beanstalk environment {{environment.name}} is unhealthy"
  escalation_message = "Elastic Beanstalk environment {{environment.name}} is still unhealthy"
  tags               = ["Environment:${var.environment}", "Service:backend"]

  query = "avg(last_5m):avg:aws.elasticbeanstalk.EnvironmentHealth{Environment:${var.environment},Service:backend} by {environment} > 90"
  monitor_thresholds {
    warning           = "85"
    critical          = "90"
    warning_recovery  = "80"
    critical_recovery = "85"
  }

  notify_no_data    = true
  renotify_interval = 20
}

# Elastic Beanstalk Metrics: Latency
resource "datadog_monitor" "latency" {
  name    = "High Latency"
  type    = "metric alert"
  message = "The latency has exceeded the threshold. Take action."

  query = "avg(last_5m):avg:aws.elasticbeanstalk.Latency{${var.environment}} > ${var.latency_threshold}"

  monitor_thresholds {
    warning  = var.latency_threshold - 50
    critical = var.latency_threshold
  }

  notify_no_data    = false
  renotify_interval = 60
}

# Elastic Beanstalk Metrics: Request Count
resource "datadog_monitor" "request_count" {
  name    = "High Request Count"
  type    = "metric alert"
  message = "The number of requests has exceeded the threshold. Take action."

  query = "avg(last_5m):sum:aws.elasticbeanstalk.RequestCount{${var.environment}} > ${var.request_count_threshold}"

  monitor_thresholds {
    critical = 5000
    warning  = 4000
  }

  notify_no_data    = false
  renotify_interval = 60
}

# Elastic Beanstalk Metrics: Healthy Host Count
resource "datadog_monitor" "healthy_host_count" {
  count  = var.healthy_host_count_threshold > 1 ? 1 : 0

  name    = "Low Healthy Host Count"
  type    = "metric alert"
  message = "The number of healthy hosts has dropped below the threshold. Take action."

  query = "avg(last_5m):avg:aws.elasticbeanstalk.HealthyHostCount{${var.environment}} < ${var.healthy_host_count_threshold - 2}"

  monitor_thresholds {
    critical = var.healthy_host_count_threshold - 2
    warning  = var.healthy_host_count_threshold - 1
  }

  notify_no_data    = false
  renotify_interval = 60
}