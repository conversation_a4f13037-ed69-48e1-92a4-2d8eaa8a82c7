#!/bin/bash

# OnBord API Testing Base Script
# This script provides authentication and base functions for API testing
# Usage: source scripts/api-testing/api-base.sh

set -e

# Configuration
API_BASE="http://localhost:3000"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
AUTH_FILE="${SCRIPT_DIR}/auth.json"
TOKEN_FILE="${SCRIPT_DIR}/token.txt"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# Authentication function
authenticate() {
    log "Authenticating with OnBord API..."
    
    # Create login payload
    cat > "${SCRIPT_DIR}/login_payload.json" << EOF
{
  "email": "<EMAIL>",
  "password": "Hello123!"
}
EOF

    # Perform login and extract token
    RESPONSE=$(curl -s -v "${API_BASE}/auth/login" \
        -H 'Content-Type: application/json' \
        -d @"${SCRIPT_DIR}/login_payload.json" 2>&1)
    
    # Extract access token from Set-Cookie header
    ACCESS_TOKEN=$(echo "$RESPONSE" | grep "Set-Cookie: access_token" | sed 's/.*access_token=\([^;]*\).*/\1/' || echo "")
    
    if [ -z "$ACCESS_TOKEN" ]; then
        error "Failed to extract access token from response"
        echo "$RESPONSE"
        return 1
    fi
    
    # Extract advisor and organization info from response body
    AUTH_INFO=$(echo "$RESPONSE" | tail -1 | jq . 2>/dev/null || echo "{}")
    
    # Store authentication info
    echo "$AUTH_INFO" > "$AUTH_FILE"
    echo "$ACCESS_TOKEN" > "$TOKEN_FILE"
    
    ADVISOR_ID=$(echo "$AUTH_INFO" | jq -r '.advisorId // "unknown"')
    ORG_ID=$(echo "$AUTH_INFO" | jq -r '.organisationId // "unknown"')
    
    success "Authentication successful"
    log "Advisor ID: $ADVISOR_ID"
    log "Organization ID: $ORG_ID"
    
    # Clean up temporary login file
    rm -f "${SCRIPT_DIR}/login_payload.json"
    
    return 0
}

# Function to make authenticated API requests
api_request() {
    local method="$1"
    local endpoint="$2"
    local data="$3"
    local content_type="${4:-application/json}"
    
    if [ ! -f "$TOKEN_FILE" ]; then
        error "No access token found. Please authenticate first."
        return 1
    fi
    
    local token=$(cat "$TOKEN_FILE")
    local url="${API_BASE}${endpoint}"
    
    log "Making $method request to $endpoint"
    
    if [ -n "$data" ]; then
        curl -s -X "$method" "$url" \
            -H "Cookie: access_token=$token" \
            -H "Content-Type: $content_type" \
            -d "$data"
    else
        curl -s -X "$method" "$url" \
            -H "Cookie: access_token=$token"
    fi
}

# Get organization ID from auth file
get_org_id() {
    if [ ! -f "$AUTH_FILE" ]; then
        error "Authentication file not found. Please authenticate first."
        return 1
    fi
    jq -r '.organisationId' "$AUTH_FILE"
}

# Get advisor ID from auth file
get_advisor_id() {
    if [ ! -f "$AUTH_FILE" ]; then
        error "Authentication file not found. Please authenticate first."
        return 1
    fi
    jq -r '.advisorId' "$AUTH_FILE"
}

# Check authentication status
check_auth() {
    if [ -f "$AUTH_FILE" ] && [ -f "$TOKEN_FILE" ]; then
        local advisor_id=$(get_advisor_id)
        local org_id=$(get_org_id)
        success "Authenticated as Advisor: $advisor_id, Organization: $org_id"
        return 0
    else
        warn "Not authenticated. Please authenticate first."
        return 1
    fi
}

# Show script status and auth info
show_status() {
    log "API Testing Base Configuration:"
    echo "  API Base: $API_BASE"
    echo "  Script Directory: $SCRIPT_DIR"
    echo "  Auth File: $AUTH_FILE"
    echo "  Token File: $TOKEN_FILE"
    echo ""
    check_auth
}

# Clean up authentication files
cleanup_auth() {
    log "Cleaning up authentication files..."
    rm -f "$AUTH_FILE" "$TOKEN_FILE"
    success "Authentication files removed"
}

# Ensure script directory exists
mkdir -p "$SCRIPT_DIR"

# If script is run directly (not sourced), show help
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    case "${1:-help}" in
        "auth")
            authenticate
            ;;
        "status")
            show_status
            ;;
        "cleanup")
            cleanup_auth
            ;;
        "help"|*)
            cat << EOF
OnBord API Testing Base Script

This script provides authentication and base functions for API testing.
It can be sourced by other scripts or run directly for basic operations.

Usage: $0 [command]

Commands:
  auth                          - Authenticate with the API
  status                        - Show authentication status and configuration
  cleanup                       - Remove authentication files
  help                          - Show this help

When sourced by other scripts:
  source scripts/api-testing/api-base.sh

Available functions after sourcing:
  authenticate()                - Authenticate with API
  api_request(method, endpoint, [data], [content-type]) - Make authenticated requests
  get_org_id()                  - Get organization ID from auth
  get_advisor_id()              - Get advisor ID from auth
  check_auth()                  - Check if authenticated
  show_status()                 - Show configuration and auth status
  log(), error(), success(), warn() - Logging functions

Files created in script directory:
  auth.json                     - Stores authentication response
  token.txt                     - Stores access token

Note: This script requires 'jq' for JSON processing and 'curl' for API requests.
EOF
            ;;
    esac
fi