import { Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { Interview } from '../../../schemas/v1/interview.schema';
import { ClientsV2Service } from 'src/clients/services/v2/clients-v2.service';
import { InterviewPageStatusEnum } from 'src/interview-templates/types/interview-page-status.enum';
import { UpdateInterviewDto } from '../../../dto/v1/update-interview.dto';
import { getPageHandler } from 'src/interviews/utils/page-updates/handlers.mapper';
import { PageUpdateContext } from 'src/interviews/types/page-update-context.type';
import { EnrichedInterview } from '../../../dto/v1/enriched-interview-dto';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { isEmpty } from 'lodash';

@Injectable()
export class InterviewPagesService {
  constructor(
    @InjectModel(Interview.name) private readonly interviewModel: Model<Interview>,
    private readonly clientsService: ClientsV2Service,
  ) {}

  updatePages(pages: any[], dto: UpdateInterviewDto, status = InterviewPageStatusEnum.SYNCED) {
    const context: PageUpdateContext = { fillContingentBeneficiaries: false };
    return pages.map(page => {
      const handler = getPageHandler(page.name, context, dto, status);
      return handler.handle(page);
    });
  }

  calculateCompletion(interview: EnrichedInterview, client: EnrichedClient) {
    if (interview.isComplete) return 100;

    const hasAccounts = interview.isPrimary
      ? !isEmpty(client.primaryContact.accounts)
      : !isEmpty(client.secondaryContact?.accounts);

    const pagesToFill = hasAccounts
      ? interview.template.pages
      : interview.template.pages.filter(p => p.name !== PagesEnum.PRIMARY_BENEFICIARIES && p.name !== PagesEnum.CONTINGENT_BENEFICIARIES);

    const total = pagesToFill.length;
    const filled = interview.template.pages.filter(p => p.status === InterviewPageStatusEnum.SYNCED).length;
    return Math.ceil((filled / total) * 100);
  }

  async validateAllSynced(interviewId: string): Promise<boolean> {
    const interview = await this.interviewModel.findById(interviewId);
    if (!interview) throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);
    return interview.template.pages.every(p => p.status === InterviewPageStatusEnum.SYNCED);
  }

  async updateInterviewPages(
    interviewId: string,
    dto: UpdateInterviewDto,
    pageStatus: InterviewPageStatusEnum,
    session?: ClientSession,
  ) {
    const interview = await this.interviewModel.findById(interviewId).session(session);
    if (!interview) throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);

    const client = await this.clientsService.findOne({ _id: interview.client }, session, false, true);

    const updatedPages = this.updatePages(interview.template.pages, dto, pageStatus);

    const percentage = this.calculateCompletion(interview.toObject() as any, client as any);

    const now = new Date();
    await this.clientsService.updateClientCompletionPercentage(interview.client.toString(), percentage, now, session);
    await this.clientsService.updateLastContactActivityTimestamp(interview.client.toString(), interview.isPrimary, session);

    return this.interviewModel.findByIdAndUpdate(
      interviewId,
      { $set: { 'template.pages': updatedPages } },
      { new: true, session },
    );
  }

  async addPage(interviewId: string, pageName: PagesEnum) {
    await this.interviewModel.updateOne(
      { _id: interviewId, 'template.pages.name': { $ne: pageName } },
      {
        $push: {
          'template.pages': { name: pageName, order: 0, elements: [], filled: false, data: {} },
        },
      },
      { runValidators: false, new: true },
    );
  }

  async removePage(interviewId: string, pageName: string) {
    const result = await this.interviewModel.updateOne(
      { _id: interviewId },
      { $pull: { 'template.pages': { name: pageName } } },
    );
    if (result.modifiedCount === 0) throw new HttpException('Page not found', HttpStatus.NOT_FOUND);
  }
}