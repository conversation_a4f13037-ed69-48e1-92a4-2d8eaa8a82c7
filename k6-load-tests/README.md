# OnBord K6 - Api Load Testing Suite
A comprehensive load testing suite for the OnBord API using k6.

## Overview
This project provides a set of load testing scenarios to verify the performance and reliability of the OnBord API. It focuses on critical user flows including client creation, interview processing, and document handling.

## Features

- **Client Creation Tests**: Create clients with and without notifications
- **Interview Flow Tests**: Verify IRA account requirements and interview completion
- **Scalable Scenarios**: Adjust virtual users (VUs) for different load patterns
- **Performance Metrics**: Track response times, error rates, and custom trends
- **Dynamic Test Parameters**: Configure test runs with environment variables

## Prerequisites
- [k6](https://k6.io/docs/getting-started/installation/) installed on your system
- Node.js and npm/yarn for dependency management
- Access credentials for the OnBord API

## Installation
1. Install dependencies:
   ```bash
   npm i
   ```

2. Set up environment variables by copying `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

3. Edit the `.env` file with your API credentials:
   ```
   USER_EMAIL=<EMAIL>
   USER_PASSWORD=your-password
   BASE_URL=api-url
   ```

## Project Structure
(If you need to add new tests. Or you add within some scope already existing or 
create new scope inside `tests` folder)
```
k6-load-tests/
├── src/
│   ├── config/
│   │   └── constants/
│   │   │   ├── k6.js                   # K6 specific constants
│   │   │   └── shared.js               # Onbord related and shared constants
│   ├── test-data/                      # Test data generators
│   │   └── e2e-client-interview.js     
│   └── utils/                          # Utility functions
│   │   ├── auth.js     
│   │   ├── clients.js     
│   │   ├── interviews.js     
│   │   └── k6-utilities.js     
├── tests/
│   ├── clients.js                      # Client creation tests
│   └── interviews.js                   # Interview flow tests
├── .env                                # Environment variables (not in git)
├── .env.example                        # Example environment variables
├── .gitignore                      
├── package.json                        # Dependencies and scripts
└── README.md                           # This very file
```

## Running Tests
The project includes several predefined test scenarios that can be run with npm scripts:

### Client Creation Tests
```bash
# Basic client creation without notifications
npm run test:client-creation-basic

# Client creation with notifications
npm run test:client-creation-notification

# Client creation failure scenario
npm run test:client-creation-failure

# Run all client creation scenarios in sequence
npm run test:client-all
```

### Interview Flow Tests
```bash
# Test IRA account requirements
npm run test:interview-ira

# Test interview completion
npm run test:interview-completion

# Run all interview flow tests in sequence
npm run test:interview-all
```

## Customizing Test Runs
You can customize test runs using environment variables:

```bash
# Run with custom VUs and duration
VUS=10 DURATION=30s npm run test:client-creation-basic

# Run with specific thresholds
THRESHOLD_P95=3000 npm run test:interview-ira
```

## Understanding Test Scenarios
Check this **[document](https://terrific-yacht-679.notion.site/Async-Refactor-Test-Plan-189d9846f84480089573cc5a1d4e6825)** to see the full test plan.

## Troubleshooting
### Common Issues
1. **Authentication Errors**:
    - Verify your API credentials in the `.env` file
    - Check that your user has the required permissions

2. **Connection Timeouts**:
    - Check network connectivity to the API endpoint
    - Verify the API_BASE_URL is correct

3. **Test Failures**:
    - Check the console output for detailed error messages
    - Verify that the API responses match expected formats

### Debugging
For detailed debug information, run with the `DEBUG` environment variable:

```bash
DEBUG=k6* npm run test:interview-ira
```

## Best Practices
- **Start Small**: Begin with a small number of VUs to validate your test
- **Incremental Load**: Gradually increase VUs to identify performance thresholds
- **Isolate Tests**: Run one test scenario at a time for clean results
- **Monitor Logs**: Keep an eye on k6 output and server logs during tests