export type RedtailActivity = {
  id: number;
  category_id: number;
  activity_code_id: number;
  subject: string;
  all_day: boolean;
  start_date: string;
  end_date: string;
  description: string;
  location: string;
  importance: number;
  priority: number;
  percentdone: number;
  share_with_client: boolean;
  background_color: string;
  text_color: string;
  added_by: number;
  deleted: boolean;
  created_at: string;
  updated_at: string;
  ical: string;
  is_private: boolean;
  completed: boolean;
  completed_at: string;
  privacy_settings: {
    option_id: number;
    team_id: number;
  };
  current_user_time_zone: string;
  repeat_ends: string;
  repeat_ends_on: string;
  repeats: string;
  repeats_every_n_days: number;
  repeats_every_n_weeks: number;
  repeats_weekly_each_days_of_the_week: string[];
  repeats_every_n_months: number;
  repeats_monthly: string;
  repeats_monthly_each_days_of_the_month: number[];
  repeats_monthly_on_days_of_the_week: string[];
  repeats_monthly_on_ordinals: string[];
  repeats_every_n_years: number;
  repeats_yearly_each_months_of_the_year: number[];
  repeats_yearly_on: string;
  repeats_yearly_on_days_of_the_week: string[];
  repeats_yearly_on_ordinals: string[];
  exception_times: string[];
  recurrence_period: string;
  next_occurrence: string;
  linked_contacts: {
    contact_id: number;
  }[];
  attendees: {
    id?: number;
    type?: string;
    name?: string;
    user_id?: number;
    email?: string;
  }[];
};

export enum ActivityAttendeeTypeEnum {
  USER = 'Crm::Activity::Attendee::User',
}

export enum ActivityRepeatEnum {
  NEVER = 'never',
}
