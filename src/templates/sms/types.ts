export enum TextMessageTemplateNameEnum {
  WelcomeText = 'WelcomeText',
  ClientIncomplete = 'ClientIncomplete',
  ClientStart = 'ClientStart',
  ReadyToSign = 'ReadyToSign',
  ReminderToSign = 'ReminderToSign',
  NonProtocolTransitionRequestEmail = 'NonProtocolTransitionRequestEmail',
  TransitionStatement = 'TransitionStatement',
}

export interface ITextMessageContext {}

export type TextMessageTemplates = {
  [Property in TextMessageTemplateNameEnum]: (
    ctx: ITextMessageContext,
  ) => string;
};
