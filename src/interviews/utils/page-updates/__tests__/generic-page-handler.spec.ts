import { InterviewPage } from 'src/interview-templates/schemas/v1/interview.template';
import { InterviewPageStatusEnum } from 'src/interview-templates/types/interview-page-status.enum';
import { UpdateInterviewDto } from 'src/interviews/dto/v1/update-interview.dto';
import { PageUpdateContext } from 'src/interviews/types/page-update-context.type';
import { GenericPageHandler } from '../generic-page-handler';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';

describe('GenericPageHandler', () => {
  let handler: GenericPageHandler;
  let context: PageUpdateContext;
  let updateDto: UpdateInterviewDto;
  let page: InterviewPage;

  beforeEach(() => {
    // Create a basic context with default values
    context = { fillContingentBeneficiaries: false };

    // Create a basic page with common structure
    page = {
      name: PagesEnum.ADDRESS,
      order: 1,
      filled: false,
      status: InterviewPageStatusEnum.UNANSWERED,
      data: {
        address: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zip: '12345'
      }
    } as InterviewPage;

    // Create a basic update DTO
    updateDto = {
      page: {
        name: PagesEnum.ADDRESS,
        data: {
          address: '456 New St',
          city: 'New City',
          state: 'NS',
          zip: '67890'
        }
      }
    };

    // Create a handler with SYNCED status
    handler = new GenericPageHandler(context, updateDto, InterviewPageStatusEnum.SYNCED);
  });

  describe('constructor', () => {
    it('should correctly initialize with provided parameters', () => {
      // Verify the handler has the correct properties
      expect((handler as any).context).toBe(context);
      expect((handler as any).updateDto).toBe(updateDto);
      expect((handler as any).pageStatus).toBe(InterviewPageStatusEnum.SYNCED);
    });

    it('should work with different page statuses', () => {
      // Create handlers with different statuses
      const handlerInProgress = new GenericPageHandler(
        context, 
        updateDto, 
        InterviewPageStatusEnum.IN_PROGRESS
      );
      
      const handlerFailed = new GenericPageHandler(
        context, 
        updateDto, 
        InterviewPageStatusEnum.FAILED
      );
      
      // Verify the handlers have the correct statuses
      expect((handlerInProgress as any).pageStatus).toBe(InterviewPageStatusEnum.IN_PROGRESS);
      expect((handlerFailed as any).pageStatus).toBe(InterviewPageStatusEnum.FAILED);
    });
  });

  describe('shouldUpdate', () => {
    it('should return true when page names match', () => {
      // Both the page and the updateDto have the same name (ADDRESS)
      const result = (handler as any).shouldUpdate(page);
      expect(result).toBe(true);
    });

    it('should return false when page names do not match', () => {
      // Change the page name so it doesn't match the updateDto
      page.name = PagesEnum.CUSTOM_QUESTIONS;
      
      const result = (handler as any).shouldUpdate(page);
      expect(result).toBe(false);
    });

    it('should work with any page type', () => {
      // Test with various page types
      const pageTypes = [
        PagesEnum.NAME,
        PagesEnum.PHONE,
        PagesEnum.SSN,
        PagesEnum.DOB,
        PagesEnum.EMPLOYMENT,
        PagesEnum.CUSTOM_QUESTIONS
      ];
      
      for (const pageType of pageTypes) {
        // Set both page and DTO to the same type
        page.name = pageType;
        updateDto.page.name = pageType;
        
        const result = (handler as any).shouldUpdate(page);
        expect(result).toBe(true);
      }
    });
  });

  describe('handle', () => {
    it('should mark the page as filled and set status when shouldUpdate returns true', () => {
      // Page name matches updateDto name
      const result = handler.handle(page);
      
      // Page should be updated
      expect(result.filled).toBe(true);
      expect(result.status).toBe(InterviewPageStatusEnum.SYNCED);
    });

    it('should not update the page when shouldUpdate returns false', () => {
      // Change the page name so it doesn't match
      page.name = PagesEnum.PHONE;
      
      const result = handler.handle(page);
      
      // Page should remain unchanged
      expect(result.filled).toBe(false);
      expect(result.status).toBe(InterviewPageStatusEnum.UNANSWERED);
    });

    it('should apply the status that was provided in constructor', () => {
      // Create handlers with different statuses
      const statusValues = [
        InterviewPageStatusEnum.SYNCED,
        InterviewPageStatusEnum.IN_PROGRESS,
        InterviewPageStatusEnum.FAILED,
        InterviewPageStatusEnum.UNANSWERED
      ];
      
      for (const status of statusValues) {
        const testHandler = new GenericPageHandler(context, updateDto, status);
        const result = testHandler.handle(page);
        
        // Page should have the status provided to the handler
        expect(result.filled).toBe(true);
        expect(result.status).toBe(status);
      }
    });

    it('should return the original page object after updating it', () => {
      // Verify that handle() is returning the same object that was passed in
      const originalPage = { ...page };
      const result = handler.handle(page);
      
      // Verify it's the same object (references match)
      expect(result).toBe(page);
      
      // Verify the page was modified
      expect(result.filled).toBe(true);
      expect(result.status).toBe(InterviewPageStatusEnum.SYNCED);
      
      // Verify other properties were not changed
      expect(result.name).toBe(originalPage.name);
      expect(result.order).toBe(originalPage.order);
      expect(result.data).toBe(originalPage.data);
    });
  });
}); 