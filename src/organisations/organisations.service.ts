import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import * as AWS from 'aws-sdk';
import mongoose, { ClientSession, Model } from 'mongoose';
import { AssetsService } from 'src/assets/assets.service';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import {
  BATCH_SIZE,
  DocusignAccountOwnershipEnum,
  REFRESH_THRESHOLD,
} from 'src/integrations/docusign/docusign.types';
import { LinkDocusignDto } from 'src/organisations/dto/link-docusign.dto';
import { Asset } from 'src/shared/schemas/asset.schema';
import { Integration } from 'src/shared/schemas/integration.schema';
import { AssetTypeEnum } from 'src/shared/types/general/asset-types.enum';
import {
  CRMEnum,
  DocumentSigningEnum,
  IntegrationConfig,
  IntegrationEnum,
} from 'src/shared/types/integrations';
import { addIntegration } from 'src/utils';
import { encrypt } from 'src/utils/encryption';
import { AddMasterAccountDto } from './dto/add-master-account.dto';
import { CreateOrganisationDto } from './dto/create-organisation.dto';
import { UpdateDocusignAccountOwnershipDto } from './dto/update-docusign-account-ownership.dto';
import { UpdateDocusignAdvisoryDocumentsDto } from './dto/update-docusign-advisory-documents.dto';
import { UpdateOrganisationDto } from './dto/update-organisation.dto';
import { CustomQuestion, Organisation } from './schemas/organisation.schema';
import { FileUploadResponseDto } from 'src/assets/dto/file-upload-response.dto';
import { DownloadDocumentRequestDto } from 'src/organisations/dto/download-document.dto';
import { CreateCustomQuestionDto } from 'src/organisations/dto/custom-questions/create-custom-question.dto';
import { UpdateCustomQuestionDto } from 'src/organisations/dto/custom-questions/update-custom-question.dto';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import {
  PaginationQueryDto,
  PaginationResponseDto,
} from 'src/shared/dto/pagination.dto';
import { OrgAdvisorOnbordCount } from './aggregations/users-and-clients.aggregation';
import { DateRange } from 'src/shared/types/general/date-range.type';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { ToggleFeatureDto } from 'src/organisations/dto/toggle-feature.dto';
import { OrganisationStatusEnum } from 'src/organisations/types/organisation-status.enum';
import { autoConvertMapToObject } from 'src/utils/convertMapToObject';

@Injectable()
export class OrganisationsService {
  constructor(
    @InjectModel(Organisation.name)
    private organisationModel: Model<Organisation>,
    @InjectModel(Asset.name)
    private assetModel: Model<Asset>,
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => DocusignService))
    private readonly docusignService: DocusignService,
    @Inject(forwardRef(() => AdvisorsCrudService))
    private readonly advisorCrudService: AdvisorsCrudService,
    private readonly assetsService: AssetsService,
    private transactionManager: TransactionManager,
    @Inject(WINSTON_MODULE_PROVIDER) private logger: Logger,
  ) {}

  // #######################
  // #         CRUD        #
  // #######################

  /**
   * Creates a new organisation.
   * @param createOrganisationDto - The DTO (Data Transfer Object) containing the details of the organisation to be created.
   * @param session - Optional MongoDB session to use for the database operations.
   * @returns An object containing a success message and the created organisation.
   */
  async create(
    createOrganisationDto: CreateOrganisationDto,
    session?: ClientSession,
  ) {
    const created = await this.organisationModel.create(
      [
        {
          name: createOrganisationDto.name,
          riaName: createOrganisationDto.riaName,
          email: createOrganisationDto.email,
          phone: createOrganisationDto.phone,
          externalAccounts: [],
          assets: [],
          address: createOrganisationDto.address,
          docuSign: createOrganisationDto.docusign,
          selectedCRM: createOrganisationDto.selectedCRM,
          transitions: createOrganisationDto.transitions,
        },
      ],
      { session },
    );

    const organisation = created[0];

    await this.advisorCrudService.create(
      {
        integrations: [],
        organisationId: organisation._id,
        personalInfo: createOrganisationDto.personalInfo,
        role: RolesEnum.CompanyAdmin,
      },
      session,
    );

    return { message: 'New organisation created!', organisation };
  }

  /**
   * Retrieves a paginated list of organisations based on the provided filters and pagination options.
   *
   * @param paginationOptions - The pagination options for the query.
   * @param filters - The filters to apply to the query.
   * @param dateRange - The optional date range to filter the results.
   * @returns A promise that resolves to a `PaginationResponseDto` containing the paginated list of organisations.
   */
  async findAll(
    paginationOptions: PaginationQueryDto,
    filters: mongoose.FilterQuery<Organisation>,
    dateRange?: DateRange,
  ): Promise<PaginationResponseDto<Organisation[]>> {
    const { limit = 5, page = 1 } = paginationOptions;
    const skip = limit * (page - 1);

    const totalResults = await this.organisationModel.countDocuments();
    const result = await this.organisationModel
      .aggregate(OrgAdvisorOnbordCount(filters, dateRange))
      .skip(skip)
      .limit(limit)
      .exec();

    return {
      result,
      totalResults,
    };
  }

  /**
   * Finds an organisation by its ID.
   * @param id - The ID of the organisation to find.
   * @param session - Optional MongoDB session to use for the operation.
   * @returns A Promise that resolves to the found organisation.
   * @throws HttpException with status HttpStatus.NOT_FOUND if the organisation is not found.
   */
  async findOne(id: string, session?: ClientSession): Promise<Organisation> {
    const organisation = await this.organisationModel
      .findById(id)
      .session(session);

    if (!organisation) {
      throw new HttpException('Organisation not found', HttpStatus.NOT_FOUND);
    }

    return organisation;
  }

  /**
   * Updates an organisation with the specified ID.
   *
   * @param id - The ID of the organisation to update.
   * @param updateOrganisationDto - The data to update the organisation with.
   * @param session - Optional MongoDB session to use for the update operation.
   * @returns A Promise that resolves to an object with a message indicating the success of the update operation.
   * @throws HttpException if the CRM is already initialized and a selected CRM is provided in the update DTO.
   * @throws HttpException if an error occurs while updating the organisation.
   */
  async update(
    id: string,
    updateOrganisationDto: UpdateOrganisationDto,
    session?: ClientSession,
  ): Promise<{ message: string }> {
    const found = await this.findOne(id, session);

    if (found.crmInitialized && updateOrganisationDto.selectedCRM) {
      throw new HttpException(
        'Prohibited change: CRM already initialized',
        HttpStatus.BAD_REQUEST,
      );
    }

    const organisation = await this.organisationModel
      .findByIdAndUpdate(id, updateOrganisationDto, { new: true })
      .session(session);

    if (organisation) {
      organisation.updatedAt = new Date();
      return {
        message: 'Organisation updated successfully.',
      };
    }

    throw new HttpException(
      'An error occurred while updating the organisation.',
      HttpStatus.BAD_REQUEST,
    );
  }

  /**
   * Removes an organisation by its ID.
   * @param id - The ID of the organisation to remove.
   * @param session - Optional MongoDB session to use for the operation.
   * @returns A promise that resolves to the removed organisation.
   */
  async remove(id: string, session?: ClientSession): Promise<Organisation> {
    return this.organisationModel.findByIdAndRemove(id).session(session);
  }

  /**
   * Retrieves the available CRMs.
   *
   * @returns An array of available CRMs.
   */
  async getAvailableCRMs() {
    return Object.values(CRMEnum).map((v) => v);
  }

  // #######################
  // #   Master Accounts   #
  // #######################

  /**
   * Adds a master account to an organisation.
   * @param id - The ID of the organisation.
   * @param addMasterAccountDto - The DTO containing the master account to be added.
   * @returns A Promise that resolves to the updated organisation.
   * @throws HttpException if the master account already exists.
   */
  async addMasterAccount(id: string, addMasterAccountDto: AddMasterAccountDto) {
    const organisation = await this.findOne(id);

    if (
      organisation.masterAccounts.includes(addMasterAccountDto.masterAccount)
    ) {
      throw new HttpException(
        'Master account already exists',
        HttpStatus.BAD_REQUEST,
      );
    }
    organisation.masterAccounts.push(addMasterAccountDto.masterAccount);

    return organisation.save();
  }

  /**
   * Retrieves the master accounts of an organisation.
   * @param id - The ID of the organisation.
   * @returns An array of master accounts.
   */
  async getMasterAccounts(id: string) {
    const organisation = await this.findOne(id);

    return organisation.masterAccounts;
  }

  /**
   * Deletes a master account from an organisation.
   *
   * @param id - The ID of the organisation.
   * @param masterAccountId - The ID of the master account to delete.
   * @returns A promise that resolves to the updated organisation after the master account is deleted.
   */
  async deleteMasterAccount(id: string, masterAccountId: string) {
    const organisation = await this.findOne(id);

    organisation.masterAccounts = organisation.masterAccounts.filter(
      (masterAccount) => masterAccount !== masterAccountId,
    );

    return organisation.save();
  }

  // #######################
  // #      DocuSign       #
  // #######################

  /**
   * Links a Docusign integration to an organisation.
   * @param id - The ID of the organisation.
   * @param linkDocusignDto - The Docusign integration data.
   * @param isRefresh - Optional flag indicating if it's a refresh operation.
   * @returns A message indicating the successful linking of Docusign.
   * @throws HttpException if access token or refresh token is not found.
   */
  async linkDocusign(
    id: string,
    linkDocusignDto: LinkDocusignDto,
    isRefresh = false,
  ) {
    const { accessToken, refreshToken, expiresIn, lastRefresh } =
      linkDocusignDto;

    if (!accessToken || !refreshToken) {
      throw new HttpException(
        'Access token or refresh token not found.',
        HttpStatus.BAD_REQUEST,
      );
    }

    const encryptedAccessToken = await encrypt(
      JSON.stringify({ accessToken, refreshToken }),
    );

    const integration: Integration = {
      integrationType: IntegrationEnum.DocumentSigning,
      integrationConfig: {
        name: DocumentSigningEnum.Docusign,
        credentials: encryptedAccessToken,
        lastRefresh,
        expiresIn,
        accountOwnership: DocusignAccountOwnershipEnum.Firm,
        enabled: true,
      },
    };

    await addIntegration<Model<Organisation>>(
      { _id: id },
      integration,
      this.organisationModel,
      isRefresh,
    );

    const accountInfo = await this.docusignService.getAccountInfo({
      organisationId: id,
    });

  
    const schwabTemplateId = await this.docusignService.getSchwabEnvelopeTemplate(accountInfo.accountId, { organisationId: id });
      

    const { externalAccounts } = await this.findOne(id);

    await this.organisationModel.updateOne(
      { _id: id },
      {
        externalAccounts: [
          ...externalAccounts.filter(
            (integration) =>
              integration.integrationType !== IntegrationEnum.DocumentSigning,
          ),
          {
            ...integration,
            integrationConfig: {
              ...integration.integrationConfig,
              ...accountInfo,
              ...(schwabTemplateId && { schwabTemplateId }),
            },
          },
        ],
        updatedAt: new Date(),
      },
    );

    return { message: `Docusign linked successfully.` };
  }

  /**
   * Unlinks the Docusign integration from an organisation.
   * Removes the Docusign integration from the externalAccounts array of the organisation.
   * @param id - The ID of the organisation.
   * @returns A message indicating the successful unlinking of Docusign.
   */
  async unlinkDocusign(id: string) {
    const { externalAccounts } = await this.findOne(id);

    await this.update(id, {
      externalAccounts: externalAccounts.filter(
        ({ integrationType }) =>
          integrationType !== IntegrationEnum.DocumentSigning,
      ),
    });

    return { message: `Docusign unlinked successfully.` };
  }

  /**
   * Finds organisations that are candidates for refreshing their Docusign integration token.
   *
   * @param page - The page number for pagination.
   * @returns A promise that resolves to an array of Organisation objects that are candidates for refreshing their Docusign integration token.
   */
  async findDocusignRefreshCandidates(page: number): Promise<Organisation[]> {
    // Find organisations with docusign integration
    const limit = BATCH_SIZE;
    const skip = limit * (page - 1);

    const found = await this.organisationModel
      .find({
        'integrations.integrationConfig.name': DocumentSigningEnum.Docusign,
      })
      .skip(skip)
      .limit(limit)
      .exec();

    // Filter out advisors without a docusign integration setup or who have refreshed their token
    // within the last REFRESH_THRESHOLD days
    const refreshCandidates = found.filter((organisation) => {
      const { integrationConfig } =
        organisation.externalAccounts.find(
          ({ integrationType }: Integration) =>
            integrationType === IntegrationEnum.DocumentSigning,
        ) || {};

      if (!integrationConfig) {
        return false;
      }

      const {
        lastRefresh,
      }: Partial<IntegrationConfig & { lastRefresh: Date }> = integrationConfig;

      const now = new Date();
      const threshold = new Date(
        lastRefresh.getTime() + REFRESH_THRESHOLD * 24 * 60 * 60 * 1000,
      );

      return now >= threshold;
    });

    return refreshCandidates;
  }

  /**
   * Finds the Docusign integration configuration for a given organisation ID.
   * @param id - The ID of the organisation.
   * @returns The Docusign integration configuration if found, otherwise null.
   */
  async findDocusignIntegration(id: string) {
    if (!id) {
      return null;
    }

    const organisation = await this.organisationModel.findOne({
      _id: id,
      'externalAccounts.integrationConfig.name': DocumentSigningEnum.Docusign,
    });

    if (!organisation) {
      return null;
    }

    const { integrationConfig } = organisation.externalAccounts.find(
      ({ integrationType }: Integration) =>
        integrationType === IntegrationEnum.DocumentSigning,
    );

    return integrationConfig;
  }

  /**
   * Updates the ownership of a DocuSign account for a specific organisation.
   * @param id - The ID of the organisation.
   * @param updateAccountOwnership - The DTO containing the updated account ownership information.
   * @returns A string indicating the success of the update operation.
   */
  async updateDocusignAccountOwnership(
    id: string,
    updateAccountOwnership: UpdateDocusignAccountOwnershipDto,
  ) {
    const { accountOwnership } = updateAccountOwnership;

    await this.organisationModel.updateOne(
      {
        _id: id,
        'externalAccounts.integrationType': IntegrationEnum.DocumentSigning,
      },
      {
        $set: {
          'externalAccounts.$.integrationConfig.accountOwnership':
            accountOwnership,
          updatedAt: new Date(),
        },
      },
    );

    return 'DocuSign account ownership updated successfully.';
  }

  /**
   * Sets the DocuSign account ID for an organisation.
   *
   * @param id - The ID of the organisation.
   * @param accountId - The ID of the DocuSign account.
   * @returns A success message indicating that the DocuSign account ID has been set.
   */
  async setDocusignAccount(id: string, accountId: string) {
    const account = await this.docusignService.getAccountInfoById(accountId, {
      organisationId: id,
    });

    await this.organisationModel.updateOne(
      {
        _id: id,
        'externalAccounts.integrationConfig.name': DocumentSigningEnum.Docusign,
      },
      {
        $set: {
          'externalAccounts.$.integrationConfig.accountId': account.accountId,
          'externalAccounts.$.integrationConfig.accountName':
            account.accountName,
          updatedAt: new Date(),
        },
      },
    );

    return 'DocuSign account ID set successfully.';
  }

  /**
   * Upserts Docusign advisory documents.
   *
   * @param upsertDocusignAdvisoryDocumentsDto - The DTO containing the information for upserting Docusign advisory documents.
   * @returns A string indicating the success of the document update.
   * @throws HttpException if an error occurs while updating the advisory documents.
   */
  async upsertDocusignAdvisoryDocuments(
    upsertDocusignAdvisoryDocumentsDto: UpdateDocusignAdvisoryDocumentsDto,
  ) {
    const { fileName, file } = upsertDocusignAdvisoryDocumentsDto;

    try {
      if (!file) {
        throw new HttpException(
          'No file was uploaded.',
          HttpStatus.BAD_REQUEST,
        );
      }

      const s3 = new AWS.S3();

      const params = {
        Bucket:
          this.configService.get<string>('ORG_DOCS_BUCKET_NAME') ||
          'onbord-docusign-advisory-documents',
        Key: `global/${fileName}`,
        Body: file.buffer,
      };

      const response = await s3.upload(params).promise();

      await this.assetModel.updateOne(
        { assetId: fileName },
        {
          assetId: fileName,
          assetType: AssetTypeEnum.Document,
          assetLocation: response.Location,
          isPublic: true,
          updatedAt: new Date(),
        },
        { upsert: true },
      );

      return 'Document updated successfully.';
    } catch (error) {
      throw new HttpException(
        'An error occurred while updating the advisory documents.',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * Retrieves Docusign advisory documents for a given ID.
   * @param id - The ID of the document.
   * @returns A promise that resolves to an array of Docusign advisory documents.
   */
  async getDocusignAdvisoryDocuments(id: string) {
    return this.assetModel.find();
  }

  /**
   * Upserts the configuration for an organisation.
   *
   * @param id - The ID of the organisation.
   * @param config - The configuration to be upserted, represented as a Map of key-value pairs.
   * @returns A Promise that resolves to the updated organisation.
   */
  async upsertConfig(id: string, config: Map<string, string | number>) {
    const organisation = await this.findOne(id);

    organisation.configuration = {
      ...organisation.configuration,
      ...autoConvertMapToObject(config),
    };

    return organisation.save();
  }

  /**
   * Uploads a logo file for the specified organisation.
   * @param organisationId - The ID of the organisation.
   * @param logo - The logo file to upload.
   * @returns The location of the uploaded logo file.
   */
  async uploadLogo(organisationId: string, logo: Express.Multer.File) {
    const assetName = 'logo';
    const { location, key } = await this.uploadPublicAsset(
      organisationId,
      logo,
      assetName,
    );

    await this.organisationModel.updateOne(
      { _id: organisationId },
      {
        $pull: { assets: { assetType: AssetTypeEnum.Logo } },
      },
    );

    await this.organisationModel.updateOne(
      { _id: organisationId },
      {
        $push: {
          assets: {
            assetId: assetName,
            assetLocation: location,
            assetType: AssetTypeEnum.Logo,
            isPublic: true,
            assetKey: key,
          },
        },
      },
    );

    return location;
  }

  /**
   * Uploads a public asset for the specified organisation.
   *
   * @param organisationId - The ID of the organisation.
   * @param file - The file to be uploaded.
   * @param fileName - The name of the file (optional, defaults to the original name of the file).
   * @returns A promise that resolves to a `FileUploadResponseDto` containing the location and key of the uploaded file.
   */
  async uploadPublicAsset(
    organisationId: string,
    file: Express.Multer.File,
    fileName: string = file.originalname,
  ): Promise<FileUploadResponseDto> {
    const prefix = `organisation/${organisationId}/assets`;
    const { location, key } = await this.assetsService.uploadPublicFile(
      file.buffer,
      fileName,
      file.mimetype,
      prefix,
    );

    return {
      location,
      key,
    };
  }

  /**
   * Uploads a private document for an organisation.
   *
   * @param organisationId - The ID of the organisation.
   * @param file - The file to be uploaded.
   * @param fileName - The name of the file (optional, defaults to the original name of the file).
   * @param assetType - The type of the asset.
   * @returns A Promise that resolves to the presigned URL of the uploaded file.
   */
  async uploadPrivateDocument(
    organisationId: string,
    file: Express.Multer.File,
    fileName: string = file.originalname,
    assetType: AssetTypeEnum,
  ): Promise<string> {
    const prefix = `organisation/${organisationId}/documents`;

    const { key, location } = await this.assetsService.uploadPrivateFile(
      file.buffer,
      fileName,
      file.mimetype,
      prefix,
    );

    await this.organisationModel.updateOne(
      { _id: organisationId },
      {
        $pull: {
          assets: { assetType },
        },
      },
    );

    await this.organisationModel.updateOne(
      { _id: organisationId },
      {
        $push: {
          assets: {
            assetId: fileName,
            assetType,
            isPublic: false,
            assetKey: key,
          },
        },
      },
    );
    const presignedUrl = await this.assetsService.generatePresignedUrl(key);

    return presignedUrl;
  }

  /**
   * Downloads a private document for a given organisation.
   * @param organisationId - The ID of the organisation.
   * @param dto - The DownloadDocumentRequestDto containing assetType and assetId.
   * @returns The presigned URL of the downloaded document.
   * @throws BadRequestException if neither assetType nor assetId is provided.
   * @throws HttpException if the asset is not found.
   */
  async downloadPrivateDocument(
    organisationId: string,
    dto: DownloadDocumentRequestDto,
  ) {
    const { assetType, assetId } = dto;
    let predicate;
    const organisation = await this.findOne(organisationId);

    if (!assetType && !assetId) {
      throw new BadRequestException(
        'Either assetType or assetId should be provided.',
      );
    }

    if (assetType) {
      predicate = (asset) => asset.assetType === assetType;
    }

    if (assetId) {
      predicate = (asset) => asset.assetId === assetId;
    }

    const asset = organisation.assets.find(predicate);

    if (!asset) {
      throw new HttpException('Asset not found', HttpStatus.NOT_FOUND);
    }

    const { assetKey } = asset;

    const presignedUrl = await this.assetsService.generatePresignedUrl(
      assetKey,
    );

    return presignedUrl;
  }

  // ###################################
  // #         Custom Questions        #
  // ###################################

  /**
   * Returns an array of custom questions for a given organization.
   * @param orgId - The ID of the organization to retrieve custom questions for.
   * @returns An array of CustomQuestion objects.
   */
  async getCustomQuestions(
    orgId: string,
    { page, limit, search }: PaginationQueryDto,
  ): Promise<PaginationResponseDto<CustomQuestion[]>> {
    const organisation = await this.findOne(orgId);

    const filteredQuestions = search
      ? organisation.customQuestions.filter((question) =>
          question.question.toLowerCase().startsWith(search.toLowerCase()),
        )
      : organisation.customQuestions;

    const skip = (page - 1) * limit;
    const paginatedQuestions = filteredQuestions.slice(skip, skip + limit);

    return {
      result: paginatedQuestions,
      totalResults: filteredQuestions.length,
    };
  }

  /**
   * Retrieves an array of custom questions by their IDs for a given organisation.
   * @param orgId - The ID of the organisation to retrieve the custom questions from.
   * @param questionIds - An array of IDs of the custom questions to retrieve.
   * @returns An array of CustomQuestion objects that match the provided IDs.
   * @throws HttpException if any of the provided question IDs do not exist.
   */
  async getCustomQuestionsByIds(
    orgId: string,
    questionIds: string[],
    session?: ClientSession,
  ): Promise<CustomQuestion[]> {
    const organisation = await this.findOne(orgId, session);

    const filteredQuestions = organisation.customQuestions.filter((q) =>
      questionIds.includes(q._id.toString()),
    );

    if (filteredQuestions.length !== questionIds.length) {
      throw new HttpException(
        'One or more questions do not exist',
        HttpStatus.BAD_REQUEST,
      );
    }

    const sortedQuestions = questionIds.map((id) =>
      filteredQuestions.find((q) => q._id.toString() === id),
    );

    return sortedQuestions as CustomQuestion[];
  }

  /**
   * Adds a custom question to an organisation's list of custom questions.
   * @param orgId - The ID of the organisation to add the custom question to.
   * @param questionContent - The content of the custom question to add.
   * @returns A Promise that resolves to an array of CustomQuestion objects representing the updated list of custom questions for the organisation.
   * @throws HttpException with a 400 status code if the question already exists in the organisation's list of custom questions.
   */
  async addCustomQuestion(
    orgId: string,
    questionContent: CreateCustomQuestionDto,
  ): Promise<CustomQuestion[]> {
    const existingOrganisation = await this.findOne(orgId);

    const questionExists = existingOrganisation.customQuestions.some(
      (q) => q.question.trim().toLowerCase() === questionContent.question.trim().toLowerCase(),
    );

    if (questionExists) {
      throw new HttpException(
        'Question already exists',
        HttpStatus.BAD_REQUEST,
      );
    }
    

    const updatedOrganisation = await this.organisationModel
      .findByIdAndUpdate(
        orgId,
        {
          $push: { customQuestions: questionContent },
        },
        { new: true },
      )
      .exec();

    return updatedOrganisation.customQuestions;
  }

  /**
   * Updates a custom question for an organization.
   * @param orgId The ID of the organization.
   * @param questionId The ID of the question to update.
   * @param updateCustomQuestionDto The DTO containing the updated question data.
   * @returns A Promise that resolves to an array of CustomQuestion objects.
   * @throws HttpException if the question does not exist.
   */
  async updateCustomQuestion(
    orgId: string,
    questionId: string,
    updateCustomQuestionDto: UpdateCustomQuestionDto,
  ): Promise<CustomQuestion[]> {
    const existingOrganisation = await this.findOne(orgId);

    const questionExists = existingOrganisation.customQuestions.some((q) =>
      q._id.equals(questionId),
    );

    if (!questionExists) {
      throw new HttpException(
        'Question does not exist',
        HttpStatus.BAD_REQUEST,
      );
    }

    const updatedOrganisation = await this.organisationModel
      .findByIdAndUpdate(
        orgId,
        {
          $set: {
            'customQuestions.$[question].question':
              updateCustomQuestionDto.question,
          },
        },
        {
          arrayFilters: [{ 'question._id': questionId }],
          new: true,
        },
      )
      .exec();

    return updatedOrganisation.customQuestions;
  }

  /**
   * Deletes a custom question from an organisation.
   * @param orgId The ID of the organisation.
   * @param questionId The ID of the custom question to delete.
   * @returns The updated organisation.
   * @throws HttpException if the question does not exist.
   */
  async deleteCustomQuestion(
    orgId: string,
    questionId: string,
  ): Promise<Organisation> {
    const existingOrganisation = await this.findOne(orgId);

    const questionExists = existingOrganisation.customQuestions.some((q) =>
      q._id.equals(questionId),
    );

    if (!questionExists) {
      throw new HttpException(
        'Question does not exist',
        HttpStatus.BAD_REQUEST,
      );
    }

    const updatedOrganisation = await this.organisationModel
      .findByIdAndUpdate(
        orgId,
        {
          $pull: { customQuestions: { _id: questionId } },
        },
        { new: true },
      )
      .exec();

    return updatedOrganisation;
  }

  /**
   * This method toggles the status of an organisation.
   *
   * @param orgId - The ID of the organisation whose status is to be toggled.
   * @param status - The new status to be set for the organisation.
   *
   * @returns - Returns the updated organisation with the new status.
   */
  async toggleStatus(orgId: string, status: OrganisationStatusEnum) {
    const updatedOrganisation = await this.organisationModel
      .findByIdAndUpdate(
        orgId,
        {
          $set: { status },
        },
        { new: true },
      )
      .exec();

    if (!updatedOrganisation) {
      throw new HttpException(
        'An error occurred while updating the organisation status.',
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      message: `Organisation status updated to ${updatedOrganisation.status}.`,
    };
  }

  /**
   * Toggles the specified feature for the given organisation.
   *
   * @param {string} organisationId - The ID of the organisation.
   * @param {ToggleFeatureDto} dto - The data transfer object containing the feature to toggle.
   * @returns {Promise<{ message: string }>} - A promise that resolves to an object with a success message.
   * @throws {HttpException} - If an error occurs while updating the organisation status.
   */
  async toggleFeature(organisationId: string, dto: ToggleFeatureDto) {
    const { feature } = dto;

    const organisation = await this.findOne(organisationId);
    const set = {
      docusign: { docuSign: !organisation.docuSign },
      transitions: { transitions: !organisation.transitions },
      transitionNotifications: {
        allowSendTransitionNotifications:
          !organisation.allowSendTransitionNotifications,
      },
      smsNotificationsEnabled: { smsNotificationsEnabled: !organisation.smsNotificationsEnabled },
    }[feature];

    const updatedOrganisation = await this.organisationModel
      .findByIdAndUpdate(organisationId, {
        $set: set,
      })
      .exec();

    if (!updatedOrganisation) {
      throw new HttpException(
        'An error occurred while updating the organisation status.',
        HttpStatus.BAD_REQUEST,
      );
    }

    return updatedOrganisation;
  }
}
