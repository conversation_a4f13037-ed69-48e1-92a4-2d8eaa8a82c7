import { Injectable } from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/mongoose';
import mongoose, { Model } from 'mongoose';
import { Seeder } from 'nestjs-seeder';
import { Advisor } from 'src/advisors/schemas/advisors.schema';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { Transition } from '../schemas/transitions.schema';
import { faker } from '@faker-js/faker';
import {
  TransitionStatusEnum,
  TransitionTypeEnum,
} from 'src/transitions/types/transition-type.enum';

@Injectable()
export class TransitionSeeder implements Seeder {
  constructor(
    @InjectModel(Transition.name)
    private readonly transitionModel: Model<Transition>,
    @InjectModel(Advisor.name) private readonly advisorModel: Model<Advisor>,
    @InjectModel(Organisation.name)
    private readonly organisationModel: Model<Organisation>,
    @InjectConnection() private readonly connection: mongoose.Connection,
  ) {}

  async seed(): Promise<any> {
    const session = await this.connection.startSession();
    session.startTransaction();
    try {
      const organisations = await this.organisationModel.find().exec();
      if (organisations.length === 0) {
        throw new Error('No organisations found in the database.');
      }

      const advisors = await this.advisorModel.find().exec();

      const transitions = Array.from({ length: 20 }, (_, index) => {
        const primaryContact = {
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          // email: faker.internet.email(),
          mobile: faker.phone.number(),
        };
        const secondaryContact = {
          firstName: faker.person.firstName(),
          lastName: faker.person.lastName(),
          // email: faker.internet.email(),
          mobile: faker.phone.number(),
        };

        return new this.transitionModel({
          organisationId: organisations[0]._id,
          assetsValue: faker.number.int({ min: 1000, max: 10000000 }),
          primaryContact,
          secondaryContact: index % 2 === 0 ? secondaryContact : null,
          primaryAdvisor: advisors[index % advisors.length]._id,
          primaryCSA: advisors[(index + 1) % advisors.length]._id,
          status: TransitionStatusEnum.Draft,
          transitionType: TransitionTypeEnum.Protocol,
        });
      });

      await this.transitionModel.insertMany(transitions);
    } catch (error) {
      console.error(error);
      session.abortTransaction();
    } finally {
      console.log('Seeding TransitionSeeder completed.');
      session.endSession();
    }
  }

  async drop(): Promise<any> {
    return this.transitionModel.deleteMany({});
  }
}
