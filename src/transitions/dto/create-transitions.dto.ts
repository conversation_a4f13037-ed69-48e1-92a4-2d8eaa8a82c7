import {
  IsNotEmpty,
  IsString,
  IsEmail,
  ValidateNested,
  IsNumber,
  IsOptional,
  IsArray,
  IsEnum,
  IsBoolean,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import { TransitionTypeEnum } from '../types/transition-type.enum';

export class ContactDto {
  @ApiProperty({ required: true })
  @IsString()
  firstName: string;

  @ApiProperty({ required: true })
  @IsString()
  lastName: string;

  @ApiProperty({ required: true })
  @IsString()
  @IsNotEmpty()
  mobile: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({ required: false })
  crmId?: string;
}

export class TransitionDto {
  @ApiProperty({ required: true })
  @IsNumber()
  assetsValue: number;

  @ApiProperty({ type: () => ContactDto, required: true })
  @ValidateNested()
  @Type(() => ContactDto)
  primaryContact: ContactDto;

  @ApiProperty({ type: () => ContactDto, required: false })
  @ValidateNested()
  @Type(() => ContactDto)
  @IsOptional()
  secondaryContact?: ContactDto;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  primaryAdvisor: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  primaryCSA: string;

  @ApiProperty({ required: false, type: [String] })
  @IsOptional()
  secondaryAdvisor?: string[];

  @ApiProperty({ required: false, type: [String] })
  @IsOptional()
  secondaryCSA?: string[];

  @ApiProperty({ required: true })
  @IsEnum(TransitionTypeEnum)
  transitionType: TransitionTypeEnum;
}

export class CreateTransitionsDto {
  @ApiProperty({ type: () => TransitionDto, required: true })
  @ValidateNested({ each: true })
  @Type(() => TransitionDto)
  @IsArray()
  transitions: TransitionDto[];

  @ApiProperty({ required: false, default: false })
  @IsBoolean()
  @IsOptional()
  sendNow?: boolean;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  smsPayload?: string;
}
