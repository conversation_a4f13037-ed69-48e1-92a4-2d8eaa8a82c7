import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PractifiCRM } from '../crm.practifi';

interface CustomQuestion {
  question: {
    question: string,
    type: string,
    options: [],
    required: boolean
  };
  answer: string;
}

export class PractifiCustomQuestionsMapper implements FormUpdateMapper {
  constructor(private practifiCRM: PractifiCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: CustomQuestion,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    const crmClientId = contact.crmClientId;

    const record = await this.practifiCRM.contactService.getContact(crmClientId);

    await this.practifiCRM.contactService.updateContact(crmClientId, {
      Description: `${record.Description ? record.Description : ''}\n\n Question and Answer given by ${contact.firstName} ${contact.lastName} to question ${pageData.question.question}\n Question: ${pageData.question.question}; Answer: ${pageData.answer}; Given by: ${contact.firstName} ${contact.lastName};`,
    });
  }
}