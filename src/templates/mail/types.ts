export enum EmailTemplateNameEnum {
  ClientWelcome = 'ClientWelcome',
  DesktopInterview = 'DesktopInterview',
  FailedTextMessage = 'FailedTextMessage',
  NonCitizen = 'NonCitizen',
  ReadyToSign = 'ReadyToSign',
  StatementUploaded = 'StatementUploaded',
}

export interface IEmailContext {}

export type EmailTemplates = {
  [Property in EmailTemplateNameEnum]: () => {
    subject: string;
    html: string;
  };
};
