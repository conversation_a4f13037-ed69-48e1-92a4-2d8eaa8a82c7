import { TransitionStatementContext } from 'src/templates/sms/transition-statement/transition-statement.context';

export function transitionStatementTextTemplate(
  ctx: TransitionStatementContext,
) {
  return `
    Hi ${ctx.clientFirstName} ${ctx.clientLastName}, 
    Please upload your brokerage statement using the following link: ${ctx.statementLink}
    If you have any questions, please contact ${ctx.organisationName} at ${ctx.organisationPhone}.`;
}
