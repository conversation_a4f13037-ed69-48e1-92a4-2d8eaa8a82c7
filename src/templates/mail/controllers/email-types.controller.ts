import { Controller, Get, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { EmailTypesService } from '../services/email-types.service';
import { EmailTypesResponseDto } from '../dto/email-types.dto';

@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'))
@Controller('emailTypes')
export class EmailTypesController {
  constructor(private readonly emailTypesService: EmailTypesService) {}

  @Get()
  async findAll(): Promise<EmailTypesResponseDto> {
    return this.emailTypesService.findAll();
  }
} 