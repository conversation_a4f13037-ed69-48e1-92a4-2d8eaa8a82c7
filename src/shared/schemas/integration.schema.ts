import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import {
  IntegrationConfig,
  IntegrationEnum,
} from 'src/shared/types/integrations';

@Schema({ _id: false })
export class Integration {
  @Prop({ type: String, enum: IntegrationEnum })
  integrationType: IntegrationEnum;

  @Prop({ type: Object })
  integrationConfig: IntegrationConfig;
}

export type IntegrationDocument = Integration & Document;
export const IntegrationSchema = SchemaFactory.createForClass(Integration);
