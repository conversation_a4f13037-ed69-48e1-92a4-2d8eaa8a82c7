import {
  DefaultTagsEnum,
  RedtailAccountUdfNameEnum,
  RedtailPhoneTypeEnum,
} from 'src/integrations/crm/redtail/types/enums';
import { RedtailCRM } from '../../crm.redtail';
import { userMock } from '../mocks/user.mock';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { warn } from 'console';
import { request } from 'http';
import mongoose from 'mongoose';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { HttpService } from '@nestjs/axios';
import { of } from 'rxjs';
import { AxiosRequestConfig, AxiosResponse } from 'axios';

const mockCreateDto = {
  primaryContact: {
    personalInfo: {
      firstName: 'Test',
      lastName: 'Test',
    },
    email: '<EMAIL>',
    mobile: '12345',
    accounts: [],
    crmClientId: null,
    skipContactInterview: false,
  } as any,
  secondaryContact: undefined,
  featuresSelected: true,
  primaryAdvisor: {
    id: 1,
    crmClientId: '1',
    personalInfo: {
      firstName: 'Test',
      lastName: 'Test',
    },
  },
  primaryCSA: {
    id: 1,
    crmClientId: '1',
    personalInfo: { firstName: 'Test', lastName: 'Test' },
  },
  readyToSend: true,
  sendNow: true,
};

describe.skip('RedtailCRM', () => {
  let service: RedtailCRM;
  const mockAxios = {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    request: jest.fn(),
  };
  const credentials = {
    username: 'username',
    password: 'password',
    databaseId: '123',
    userId: '123',
    userKey: 'test-user-key',
    apiKey: 'test-api-key',
  };

  const mockClsService = {
    get: jest.fn().mockReturnValue({ configuration: {} }),
    set: jest.fn(),
  };
  
  const mockOrganisation: Organisation = {
    _id: new mongoose.Types.ObjectId(),
    name: 'Test Organization',
  } as Organisation;

  const mockHttpService: SuperHttpService = {
    axiosRef: mockAxios,
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
  } as unknown as SuperHttpService;

  beforeEach(() => {
    service = new RedtailCRM(
      {} as any,
      mockOrganisation,
      credentials,
      mockHttpService,
      {} as any,
      {} as any,
      {
        warn: jest.fn(),
        error: jest.fn(),
      } as any,
    );
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('authenticate', () => {
    it('should return an authentication object', async () => {
      mockAxios.request.mockResolvedValueOnce({
        data: {
          authenticated_user: {
            database_id: '1',
            user_id: '1',
            user_key: 'key',
          },
        },
      });
      const result: any = await service.authenticate();
      expect(result).toBeDefined();
      expect(result.databaseId).toEqual('1');
      expect(result.userId).toEqual('1');
      expect(result.userKey).toEqual('key');
    });
  });

  describe('getContact', () => {
    const mockContactData = {
      additionalInfo: {
        addresses: [],
        emails: [],
        employmentStatus: null,
        phones: [],
        jobDescription: null,
        industryAffiliation: null,
        companyAssociation: null,
      },
      citizenship: 1,
      dob: '********',
      firstName: 'test',
      lastName: 'one',
      middleName: 'middle',
      residence: 'USA',
      suffix: 'Mr.',
      taxId: '1111111',
    };
    beforeEach(() => {
      jest.spyOn(service, 'getContactPersonalProfile').mockResolvedValueOnce({
        personalProfile: {
          citizenship: 1,
        } as any,
      });

      jest.spyOn(service, 'getAccounts').mockResolvedValueOnce({
        accounts: [],
      } as any);
      jest
        .spyOn(service.redtailUdfService, 'getContactUdfs')
        .mockResolvedValueOnce({
          udfs: [],
        } as any);
      jest
        .spyOn(service.redtailPhoneNumberService, 'getContactPhoneNumbers')
        .mockResolvedValueOnce({
          phones: [],
        } as any);
      jest
        .spyOn(service.redtailAddressService, 'getContactAddresses')
        .mockResolvedValueOnce({
          addresses: [],
        } as any);
      jest
        .spyOn(service.redtailEmailAddressService, 'getEmailAddresses')
        .mockResolvedValueOnce({
          emails: [],
        } as any);

      mockAxios.get.mockClear();
    });
    it('should return a contact object on success', async () => {
      mockAxios.request.mockResolvedValueOnce({
        data: {
          contact: {
            first_name: 'test',
            last_name: 'one',
            middle_name: 'middle',
            suffix: 'Mr.',
            dob: '********',
            tax_id: '1111111',
          },
        },
      });
      const result = await service.getContact('1');
      expect(result).toEqual(mockContactData);
    });
    it('should handle an error when the API call fails', async () => {
      mockAxios.request.mockRejectedValueOnce(new Error('API call failed'));
      await expect(service.getContact('1')).rejects.toThrow('API call failed');
    });
  });

  describe.skip('getAllContacts', () => {
    it('should return an array of contacts', async () => {
      mockAxios.request.mockResolvedValueOnce({
        data: { contacts: [{ id: '1' }, { id: '2' }] },
      });
      const result = await service.getContacts({ name: 'test', limit: 2 });
      expect(result).toEqual([{ id: '1' }, { id: '2' }]);
    });
  });

  describe('getAllUsers', () => {
    it('should fetch all users successfully', async () => {
      const mockResponseData = { database_users: [userMock] };

      mockAxios.get.mockResolvedValueOnce({ data: mockResponseData });
      const query = { limit: 10 };
      const result = await service.getAllUsers(query);
      expect(result).toMatchObject([
        {
          crmId: userMock.id,
          firstName: userMock.first_name,
          lastName: userMock.last_name,
        },
      ]);
    });
  });

  describe('createContact', () => {
    let createNewContactMock;
    let createFamilyMock;
    let addMemberToFamilyMock;
    let getTagByTagNameMock;
    let tagContactMock;
    let tagAdvisorMock;
    let getOrgConfigMock;
    let createAccountMock;
    let createAccountCreationTaskMock;

    beforeEach(() => {
      createNewContactMock = jest.fn();
      createFamilyMock = jest.fn();
      addMemberToFamilyMock = jest.fn();
      getTagByTagNameMock = jest.fn();
      tagContactMock = jest.fn();
      tagAdvisorMock = jest.fn();
      getOrgConfigMock = jest.fn();
      createAccountMock = jest.fn();
      createAccountCreationTaskMock = jest.fn();

      service.redtailFamilyService = {
        createFamily: createFamilyMock,
        addMemberToFamily: addMemberToFamilyMock,
      } as any;
      service.redtailTagService = {
        getTagByTagName: getTagByTagNameMock,
        tagContact: tagContactMock,
      } as any;
      service.tagAdvisor = tagAdvisorMock;
      service.redtailAccountsService = {
        createAccount: createAccountMock,
      } as any;
      service.createAccountCreationTask = createAccountCreationTaskMock;
      service.createNewContact = createNewContactMock;
    });

    it('should return a contact ID', async () => {
      createNewContactMock.mockResolvedValueOnce({ id: 1 });
      createFamilyMock.mockResolvedValueOnce({ id: 'familyId' });
      getTagByTagNameMock.mockResolvedValueOnce({
        id: 'xxx',
        name: DefaultTagsEnum.OnBord,
      });
      getOrgConfigMock.mockReturnValueOnce({
        configuration: {
          [RedtailAccountUdfNameEnum.ADVISORY_RATE]: 'advisoryRateFieldId',
          [RedtailAccountUdfNameEnum.ACCOUNT_FEATURES]:
            'accountFeaturesFieldId',
        },
      });
      createAccountMock.mockResolvedValueOnce({});

      const result = await service.createContact(mockCreateDto as any);

      expect(result).toEqual({
        primaryContactCrmId: '1',
        secondaryContactCrmId: undefined,
      });
      expect(createNewContactMock).toHaveBeenCalledWith(
        mockCreateDto.primaryContact,
      );
      expect(addMemberToFamilyMock).not.toHaveBeenCalled();
      expect(getTagByTagNameMock).toHaveBeenCalledWith(DefaultTagsEnum.OnBord);
      expect(tagContactMock).toHaveBeenCalled();
      expect(tagAdvisorMock).toHaveBeenCalled();
      expect(createAccountMock).toHaveBeenCalledTimes(
        mockCreateDto.primaryContact.accounts.length,
      );
      expect(createAccountCreationTaskMock).toHaveBeenCalledTimes(
        mockCreateDto.primaryContact.accounts.length,
      );
    });
  });

  describe.skip('updateContact', () => {
    it('should update contact successfully', async () => {
      jest.spyOn(service, 'createNewContact').mockResolvedValueOnce({
        id: 2,
      } as any);

      jest
        .spyOn(service.redtailEmailAddressService, 'getMainEmailAddress')
        .mockResolvedValueOnce(null)
        .mockResolvedValueOnce({
          address: '<EMAIL>',
          id: 1,
        } as any);

      jest
        .spyOn(service.redtailEmailAddressService, 'createEmailAddress')
        .mockResolvedValueOnce({} as any);

      jest
        .spyOn(service.redtailEmailAddressService, 'updateEmailAddress')
        .mockResolvedValueOnce({} as any);

      jest
        .spyOn(service.redtailPhoneNumberService, 'getContactPhoneNumbers')
        .mockResolvedValueOnce({
          phones: [
            { phone_type: RedtailPhoneTypeEnum.HOME, number: 'xzx' } as any,
          ],
        });

      jest
        .spyOn(service.redtailPhoneNumberService, 'createPhoneNumber')
        .mockResolvedValueOnce({} as any);

      jest.spyOn(service, 'tagAdvisor').mockResolvedValueOnce({} as any);

      jest
        .spyOn(service.redtailPhoneNumberService, 'updatePhoneNumber')
        .mockResolvedValueOnce({} as any);

      jest
        .spyOn(service.redtailFamilyService, 'upsertFamily')
        .mockResolvedValueOnce({} as any);

      jest
        .spyOn(service.redtailAccountsService, 'syncContactAccounts')
        .mockResolvedValueOnce({} as any);

      jest
        .spyOn(service, 'createAccountCreationTask')
        .mockResolvedValueOnce({} as any);

      // Assuming `updateContactName` is also a method of `service`.
      jest.spyOn(service, 'updateContactName').mockResolvedValueOnce({} as any);

      await service.updateContact({
        ...mockCreateDto,
        primaryContact: {
          ...mockCreateDto.primaryContact,
          crmClientId: '1',
        },
        secondaryAdvisor: {} as any,
        primaryAdvisor: mockCreateDto.primaryAdvisor as any,
        secondaryCSA: {} as any,
        primaryCSA: {} as any,
        secondaryContact: mockCreateDto.secondaryContact,
      });

      expect(service.updateContactName).toHaveBeenCalled();
      expect(
        service.redtailEmailAddressService.getMainEmailAddress,
      ).toHaveBeenCalled();
      expect(
        service.redtailEmailAddressService.createEmailAddress,
      ).toHaveBeenCalled();
      expect(
        service.redtailPhoneNumberService.getContactPhoneNumbers,
      ).toHaveBeenCalled();
      expect(
        service.redtailPhoneNumberService.updatePhoneNumber,
      ).toHaveBeenCalled();
    });
  });
});
