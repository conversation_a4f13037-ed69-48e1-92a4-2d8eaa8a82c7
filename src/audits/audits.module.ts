import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AuditsService } from './audits.service';
import { AuditsController } from './audits.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Audit, AuditSchema } from './schemas/audit.schema';
import { InterviewAuditV2, InterviewAuditV2Schema } from './schemas/interview-audit.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Audit.name, schema: AuditSchema },
      { name: InterviewAuditV2.name, schema: InterviewAuditV2Schema },
    ]),
  ],
  controllers: [AuditsController],
  providers: [AuditsService],
  exports: [AuditsService, MongooseModule],
})
export class AuditsModule {}
