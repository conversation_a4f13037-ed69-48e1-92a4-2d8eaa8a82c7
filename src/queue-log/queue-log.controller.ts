import { Controller, Post, Param, NotFoundException, Get, Query, BadRequestException } from '@nestjs/common';
import { QueueLogService } from './queue-log.service';
import { GetJobsDto } from './dto/get-jobs.dto';

@Controller('/organisations/:organisationId/logs')
export class QueueLogController {
  constructor(private readonly queueLogService: QueueLogService) {}

  @Post(':id/retry')
  async retryJob(@Param('id') id: string, @Param('organisationId') organisationId: string) {
    const retried = await this.queueLogService.retryJob(id, organisationId);
    if (!retried) {
      throw new NotFoundException(`Job with ID ${id} not found or is not in a failed state`);
    }
    return { message: `Job ${id} has been queued for retry` };
  }

  @Get()
  async getJobs(@Query() getJobsDto: GetJobsDto, @Param('organisationId') organisationId: string) {
    const { page, limit, filter } = getJobsDto;
    const { jobs, total } = await this.queueLogService.getJobs(getJobsDto, organisationId);

    return {
      data: jobs,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }
}
