import { HttpService } from '@nestjs/axios';
import { HttpException, Inject, Injectable, forwardRef } from '@nestjs/common';
import { Response } from 'express';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { OAuth } from 'src/auth/auth.types';
import { CRMEnum } from 'src/shared/types/integrations';
import { Logger } from 'winston';

@Injectable()
export class WealthboxService {
  constructor(
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    private readonly httpService: HttpService,
    @Inject(forwardRef(() => AdvisorsCrmService))
    private readonly advisorsCrmService: AdvisorsCrmService,
  ) {}

  /**
   * Handles the callback from the OAuth authorization process.
   * Retrieves access and refresh tokens from Wealthbox API using the provided authorization code.
   * Stores the tokens and returns them along with the advisor ID.
   * @param code The authorization code received from the OAuth callback.
   * @param advisorId The ID of the advisor.
   * @returns An object containing the access token, refresh token, and advisor ID.
   */
  async handleCallback(code: string, advisorId: string, res: Response) {
    try {
      const tokens = await this.httpService.axiosRef.post(
        `${process.env.WEALTHBOX_BASE_AUTH_URL}/oauth/token`,
        {
          code,
          grant_type: 'authorization_code',
          redirect_uri: WealthboxService.getRedirectUri(advisorId),
          client_id: process.env.WEALTHBOX_CLIENT_ID,
          client_secret: process.env.WEALTHBOX_CLIENT_SECRET,
        },
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );
      const { access_token: accessToken, refresh_token: refreshToken } =
        tokens.data;

      await this.storeAccessToken(advisorId, { accessToken, refreshToken });

      res.redirect(process.env.WEALTHBOX_CALLBACK_REDIRECT_URI);

      return {
        accessToken,
        refreshToken,
        advisorId,
      };
    } catch (error) {
      this.logger.error(
        `Error getting tokens from Wealthbox: ${error.response?.data}`,
      );
      res.redirect(process.env.WEALTHBOX_CALLBACK_ERROR_REDIRECT_URI);
      return error;
    }
  }

  /**
   * Generates the authorization URL for Wealthbox integration.
   * @returns The authorization URL.
   * @throws {HttpException} If no advisor id is provided.
   */
  async getWealthboxAuthorisationUrl(advisorId: string) {
    if (!advisorId) {
      throw new HttpException('No advisor id provided', 400);
    }

    const responseType = 'code';
    const scope = 'login+data';
    const redirectUri = WealthboxService.getRedirectUri(advisorId);

    return `${process.env.WEALTHBOX_BASE_AUTH_URL}/oauth/authorize?response_type=${responseType}&client_id=${process.env.WEALTHBOX_CLIENT_ID}&scope=${scope}&redirect_uri=${redirectUri}`;
  }

  /**
   * Stores the access token for a given advisor or organization in the database.
   * @param id - An object containing either the advisorId or the organisationId.
   * @param oAuthData - An object containing the OAuth data to be stored.
   * @param isRefresh - A boolean indicating whether the access token is being refreshed.
   * @returns A Promise that resolves to the result of linking the Docusign account to the advisor or organization.
   * @throws HttpException if neither advisorId nor organisationId is provided.
   */
  async storeAccessToken(advisorId: string, oAuthData: OAuth) {
    await this.advisorsCrmService.linkCrm(
      {
        _id: advisorId,
      },
      {
        credentials: {
          accessToken: oAuthData.accessToken,
          refreshToken: oAuthData.refreshToken,
          lastRefresh: new Date(),
        },
        crmType: CRMEnum.Wealthbox,
      },
      false,
    );
  }

  /**
   * Returns the redirect URI for Wealthbox integration.
   * @param advisor The advisor ID.
   * @returns The redirect URI with the advisor ID.
   */
  public static getRedirectUri(advisor: string) {
    return `${process.env.WEALTHBOX_OAUTH_REDIRECT_URI}?advisorId=${advisor}`;
  }
}
