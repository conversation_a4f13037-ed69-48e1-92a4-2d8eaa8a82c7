import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Put,
  Query,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { CreateInterviewTemplateDto } from './dto/v1/create-interview-template.dto';
import { UpdateInterviewTemplateDto } from './dto/v1/update-interview-template.dto';
import { InterviewTemplatesService } from 'src/interview-templates/interview-templates.service';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { OrganisationGuard } from 'src/shared/guards/organisation.guard';
import mongoose from 'mongoose';
import { Roles } from 'src/shared/guards/roles.guard';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';
import { CreateCustomQuestionsTemplateDto } from './dto/v1/create-custom-questions-template.dto';
import { UpdateCustomQuestionsTemplateDto } from './dto/v1/update-custom-questions-template.dto';
import { CustomQuestionsInterviewTemplatesService } from 'src/interview-templates/custom-question-templates.service';

@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'), OrganisationGuard)
@Controller('/organisations/:organisationId/interview-templates')
export class InterviewTemplatesController {
  constructor(
    private readonly interviewTemplatesService: InterviewTemplatesService,
    private readonly customQuestionsInterviewTemplatesService: CustomQuestionsInterviewTemplatesService,
  ) {}

  @Post()
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.SuperAdmin)
  create(
    @Param('organisationId') organisationId: string,
    @Body() createInterviewTemplateDto: CreateInterviewTemplateDto,
  ) {
    return this.interviewTemplatesService.create(
      organisationId,
      createInterviewTemplateDto,
    );
  }

  @Get()
  findAll(
    @Param('organisationId') organisationId: string,
    @Query() paginationQueryDto: PaginationQueryDto,
  ) {
    return this.interviewTemplatesService.findAll(
      { organisationId: new mongoose.Types.ObjectId(organisationId) },
      paginationQueryDto,
    );
  }

  @Get('custom-questions')
  getCustomQuestionsTemplates(
    @Param('organisationId') organisationId: string,
    @Query() paginationQueryDto: PaginationQueryDto,
  ) {
    return this.customQuestionsInterviewTemplatesService.findAllCustomQuestionsTemplates(
      organisationId,
      paginationQueryDto,
    );
  }

  @Get(':templateId')
  findOne(
    @Param('organisationId') organisationId: string,
    @Param('templateId') templateId: string,
  ) {
    return this.interviewTemplatesService.findOne({
      organisationId: new mongoose.Types.ObjectId(organisationId),
      _id: templateId,
    });
  }

  @Patch(':templateId')
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.SuperAdmin)
  update(
    @Param('templateId') templateId: string,
    @Body() updateInterviewTemplateDto: UpdateInterviewTemplateDto,
  ) {
    return this.interviewTemplatesService.update(
      templateId,
      updateInterviewTemplateDto,
    );
  }

  @Delete(':templateId')
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.SuperAdmin)
  remove(@Param('templateId') templateId: string) {
    return this.interviewTemplatesService.remove(templateId);
  }

  @Post('custom-questions')
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.SuperAdmin)
  createCustomQuestionsTemplate(
    @Param('organisationId') organisationId: string,
    @Body() createCustomQuestionsTemplateDto: CreateCustomQuestionsTemplateDto,
  ) {
    return this.customQuestionsInterviewTemplatesService.createCustomQuestionsTemplate(
      organisationId,
      createCustomQuestionsTemplateDto,
    );
  }

  @Put('custom-questions/:templateId')
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.SuperAdmin)
  updateCustomQuestionsTemplate(
    @Param('organisationId') organisationId: string,
    @Param('templateId') templateId: string,
    @Body() updateCustomQuestionsTemplate: UpdateCustomQuestionsTemplateDto,
  ) {
    return this.customQuestionsInterviewTemplatesService.updateCustomQuestionsTemplate(
      organisationId,
      templateId,
      updateCustomQuestionsTemplate,
    );
  }

  @Delete('custom-questions/:templateId')
  deleteCustomQuestionsTemplate(
    @Param('organisationId') organisationId: string,
    @Param('templateId') templateId: string,
  ) {
    return this.customQuestionsInterviewTemplatesService.deleteCustomQuestionsTemplate(
      organisationId,
      templateId,
    );
  }
}
