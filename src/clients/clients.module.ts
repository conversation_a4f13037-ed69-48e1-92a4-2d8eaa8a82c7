import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, forwardRef, OnModuleInit } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Client, ClientSchema } from './schemas/clients.schema';
import { DraftClient, DraftClientSchema } from './schemas/draft-client.schema';
import { AdvisorsModule } from 'src/advisors/advisors.module';
import { InterviewsModule } from 'src/interviews/interviews.module';
import { SmsModule } from 'src/notifications/sms/sms.module';
import { ClsModule } from 'nestjs-cls';
import { DocusignModule } from 'src/integrations/docusign/docusign.module';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { MailModule } from 'src/notifications/mail/mail.module';
import { CsvModule } from 'src/csv/csv.module';
import { TransitionsModule } from 'src/transitions/transitions.module';
import { BullModule, InjectQueue } from '@nestjs/bullmq';
import { BullBoardModule } from '@bull-board/nestjs';
import { BullMQAdapter } from "@bull-board/api/bullMQAdapter";
import { CLIENT_QUEUE } from 'src/shared/constants/client.constant';
import { ClientProcessor } from './client.processor';
import { QueueLogModule } from 'src/queue-log/queue-log.module';
import { OrganisationsModule } from 'src/organisations/organisations.module';
import { QueueRegistryService } from 'src/queue-log/queue-registry.service';
import { Queue } from 'bullmq';

// V1 Imports
import { ClientsV1Controller } from './controllers/v1/clients-v1.controller';
import { ClientContactV1Service } from './services/v1/client-contact-v1.service';

// V2 Imports
import { ClientsV2Controller } from './controllers/v2/clients-v2.controller';

// Shared Imports
import { ClientsSharedService } from './services/shared/clients-shared.service';

// Legacy imports for backward compatibility (services only)
import { ClientsV1Service, ClientsService } from './services/v1/clients-v1.service';
import { ClientContactService } from './client-contact.service';
import { ClientsCrmService } from './services/clients.crm.service';
import { ClientFlowService } from './services/client-flow.service';
import { ClientCommunicationsService } from './services/client-communications.service';
import { ClientsV2Service } from 'src/clients/services/v2/clients-v2.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Client.name, schema: ClientSchema }]),
    MongooseModule.forFeature([
      { name: DraftClient.name, schema: DraftClientSchema },
    ]),
    BullModule.registerQueue({
      name: CLIENT_QUEUE.NAME
    }),
    BullBoardModule.forFeature({
      name: CLIENT_QUEUE.NAME,
      adapter: BullMQAdapter
    }),
    forwardRef(() => QueueLogModule),
    forwardRef(() => AdvisorsModule),
    SmsModule,
    MailModule,
    ClsModule,
    forwardRef(() => InterviewsModule),
    forwardRef(() => DocusignModule),
    forwardRef(() => TransitionsModule),
    CsvModule,
    forwardRef(() => QueueLogModule),
    forwardRef(() => OrganisationsModule),
  ],
  controllers: [
    // Versioned controllers (legacy controller removed to avoid conflicts)
    // Unversioned requests will route to V1 due to defaultVersion: '1' in main.ts
    ClientsV1Controller,
    ClientsV2Controller,
  ],
  providers: [
    // Legacy services (maintain for backward compatibility)
    ClientContactService,
    ClientsCrmService,
    ClientFlowService,
    ClientCommunicationsService,
    
    // V1 services
    ClientsV1Service,
    ClientContactV1Service,
    
    // V2 services
    ClientsV2Service,
    
    // Shared services
    ClientsSharedService,
    
    // Other services
    TransactionManager,
    ClientProcessor,
    QueueRegistryService,
  ],
  exports: [
    // Export both legacy and new services for backward compatibility
    ClientsV1Service,
    ClientsService, // Legacy alias
    ClientsCrmService,
    ClientFlowService,
    ClientCommunicationsService,
    ClientsV2Service,
    ClientsSharedService,
  ],
})
export class ClientsModule implements OnModuleInit {
  private readonly logger = new Logger(ClientsModule.name);
  constructor(
    private readonly queueRegistryService: QueueRegistryService,
    @InjectQueue(CLIENT_QUEUE.NAME) private readonly clientQueue: Queue,
  ) {}

  onApplicationBootstrap() {
    // Register the queue in QueueRegistryService
    this.logger.log('ClientsModule onApplicationBootstrap triggered.');
    this.queueRegistryService.registerQueue(CLIENT_QUEUE.NAME, this.clientQueue);
  }

  onModuleInit() {
    // Register the queue when the module initializes
    this.queueRegistryService.registerQueue(CLIENT_QUEUE.NAME, this.clientQueue);
  }
}