import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsOptional, IsString } from 'class-validator';
import { EmailTemplateNameEnum } from 'src/templates/mail/types';

export class CreateEmailTemplateDto {
  @IsString()
  @ApiProperty()
  emailTemplateName: string;

  @IsEnum(EmailTemplateNameEnum)
  @ApiProperty({ enum: EmailTemplateNameEnum })
  emailTemplateType: EmailTemplateNameEnum;

  @IsString()
  @ApiProperty()
  emailTemplateContent: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ required: false })
  advisorId?: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ required: false, default: true, description: 'Whether this email template is active' })
  isActive?: boolean;
} 