import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsOptional, IsString } from 'class-validator';
import {
  Applicant,
  InterviewDataWithCrmInfo,
} from 'src/interviews/types/interview-data.type';

export class CreateEnvelopeDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  advisorId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  organisationId?: string;

  @ApiProperty()
  applicant: Applicant;

  @ApiProperty()
  @IsOptional()
  coapplicant?: Applicant;

  @ApiProperty()
  @IsString()
  primaryAdvisorEmail?: string;

  @ApiProperty()
  @IsString()
  primaryAdvisorName?: string;

  @ApiProperty()
  @IsString()
  primaryAdvisorPhone?: string;

  @ApiProperty()
  @IsString()
  envelopeId: string | undefined;

  @ApiProperty()
  @IsBoolean()
  isSchwabOwnedDocusignAccount?: boolean;

  @ApiProperty()
  @IsBoolean()
  shouldSend: boolean;

  @ApiProperty()
  @IsOptional()
  files?: Express.Multer.File[];

  @ApiProperty()
  @IsArray()
  @IsOptional()
  interviewData?: InterviewDataWithCrmInfo;

  @ApiProperty()
  @IsString()
  @IsOptional()
  webhookUrl?: string;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  addSchwabCC?: boolean;

  @ApiProperty()
  @IsString()
  @IsOptional()
  schwabTemplateId?: string;
}
