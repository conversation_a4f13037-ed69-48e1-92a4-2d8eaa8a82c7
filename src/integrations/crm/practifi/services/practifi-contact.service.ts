import { Injectable } from '@nestjs/common';
import { Connection, Record as PractifiRecord } from 'jsforce';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';
import { PractifiContactDto } from '../dto/practifi.dto';
import { PRACTIFI_CONTACT_FIELDS } from '../utils/practifi-contact-fields.constants';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { generateRequestConfigs } from '../utils/generate-request-configs.util';
import { OperationType } from 'src/super-http/enums';
import { practifiQuery, formatName } from '../utils/practifi-query.util';
import { PractifiEntityTypes } from '../types/enums';
import { commonConfigs } from '../types/types';

@Injectable()
export class PractifiContactService {
    private connection: Connection;
    private baseUrl: string;
    private commonConfigs: commonConfigs;

    constructor(
      private readonly httpService: SuperHttpService,
      private tenantId: string
    ) {}

    // Standard Practifi Contact fields
    private readonly STANDARD_FIELDS = PRACTIFI_CONTACT_FIELDS;

    setConnection(connection: Connection) {
      this.connection = connection;
      this.baseUrl = `${connection.instanceUrl}/services/data/v${connection.version}`;
      this.prepareRequestConfigs();
    }

    prepareRequestConfigs() {
      const requestConfigs = generateRequestConfigs(this.connection.accessToken);
      const { withInterceptors, ...configsWithoutInterceptors } = requestConfigs;
      this.commonConfigs = configsWithoutInterceptors;
    }

    async createContact(contactData: PractifiContactDto, externalId: string): Promise<PractifiRecord> {
      return await this.httpService.patch(
        `${this.baseUrl}/sobjects/Contact/practifi__External_Id__c/${externalId}`,
        this.tenantId,
        {
          ...contactData,
        },
        {
          rateLimiter: 'practifi',
          integrationName: 'practifi',
          operation: OperationType.PATCH,
          entityType: PractifiEntityTypes.CONTACT,
        },
        this.commonConfigs
      );
    }

    async updateContact(id: string, contactData: Partial<PractifiContactDto>): Promise<void> {
      return await this.httpService.patch(
        `${this.baseUrl}/sobjects/Contact/${id}`,
        this.tenantId,
        {
          ...contactData,
        },
        {
          rateLimiter: 'practifi',
          integrationName: 'practifi',
          operation: OperationType.PATCH,
          entityType: PractifiEntityTypes.CONTACT,
        },
        this.commonConfigs
      );
    }

    async getContact(id: string): Promise<PractifiRecord> {
      return await this.httpService.get(
        `${this.baseUrl}/sobjects/Contact/${id}`,
        this.tenantId,
        {
          rateLimiter: 'practifi',
          integrationName: 'practifi',
          operation: OperationType.GET,
          entityType: PractifiEntityTypes.CONTACT,
          entityId: id,
        },
        this.commonConfigs
      );
    }

    private getQueryFields(): string[] {
      return [
        ...this.STANDARD_FIELDS
      ];
    }

    async getContacts(pagination?: PaginationQueryDto): Promise<{records: PractifiRecord[]}> {
      try {
        const queryFields = this.getQueryFields().join(', ');
        const sanitizedSearch = formatName(`%${pagination.search}%`);
        let soql = `SELECT ${queryFields} FROM Contact WHERE Name LIKE '${sanitizedSearch}'`;

        if (pagination?.limit) {
          soql += `LIMIT ${pagination.limit}`;
        }

        if (pagination?.page) {
          const offset = (pagination.page - 1) * pagination.limit;
          soql += `OFFSET ${offset}`;
        }

        return await practifiQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, PractifiEntityTypes.CONTACT, this.tenantId);
      } catch (error) {
        throw new Error(`Error getting contacts from Practifi: ${error.message}`);
      }
    }
}