import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

export class SSNPageUpdateDto {
  ssn: string;
}
export class RedtailSSNMapper implements FormUpdateMapper {
  constructor(private redtailCrmService: RedtailCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: SSNPageUpdateDto,
  ) {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    await this.redtailCrmService.updateContactSSN(
      contact?.crmClientId?.toString(),
      pageData.ssn,
    );
  }
}
