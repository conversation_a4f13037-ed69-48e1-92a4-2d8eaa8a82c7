import { Injectable, HttpException, HttpStatus, Inject, forwardRef } from '@nestjs/common';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { ClientsV2Service } from 'src/clients/services/v2/clients-v2.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { InjectModel } from '@nestjs/mongoose';
import { Interview } from '../../../schemas/v1/interview.schema';
import { Model, ClientSession } from 'mongoose';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { AdvisorsDocusignService } from 'src/advisors/services/advisors.docusign.service';
import { Logger } from 'winston';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { isEmpty } from 'lodash';
import { DocusignAccountOwnershipEnum } from 'src/integrations/docusign/docusign.types';
import { CreateEnvelopeDto } from 'src/integrations/docusign/dto/create-envelope.dto';
import { AddAdvisorDocumentsDto } from 'src/integrations/docusign/dto/add-advisor-documents.dto';
import { EnrichedContact } from 'src/clients/dto/v1/get-clients.dto';
import { Account } from 'src/clients/schemas/clients.schema';
import { AccountAdvisoryDocumentsEnum } from 'src/shared/types/accounts/account-documents.enum';
import { InterviewDocumentsService } from '../documents/interview-documents.service';
import { ConfigService } from '@nestjs/config';
import { ClsService } from 'nestjs-cls';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { CRMEnum, CRMType } from 'src/shared/types/integrations';
import { GenericCrmAccount } from 'src/integrations/crm/types/accounts/crm-account.type';
import { AccountDto } from 'src/shared/types/accounts/account.dto';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { RedtailAccountOwnershipEnum, RedtailDBAccountType } from 'src/integrations/crm/redtail/types/enums';
import { fdob } from 'src/utils/formate-dob';
import { InterviewData, InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';
import { InterviewTemplatesV2Service } from 'src/interview-templates/services/v2/interview-templates.service';

@Injectable()
export class InterviewEnvelopeService {
  constructor(
    @Inject(forwardRef(() => DocusignService)) private readonly docusignService: DocusignService,
    @Inject(forwardRef(() => ClientsV2Service)) private readonly clientsService: ClientsV2Service,
    @Inject(forwardRef(() => OrganisationsService)) private readonly organisationsService: OrganisationsService,
    private readonly interviewTemplateService: InterviewTemplatesV2Service,
    @InjectModel(Interview.name) private readonly interviewModel: Model<Interview>,
    @Inject(forwardRef(() => AdvisorsCrudService)) private readonly advisorsCrudService: AdvisorsCrudService,
    @Inject(forwardRef(() => AdvisorsCrmService)) private readonly advisorsCrmService: AdvisorsCrmService,
    @Inject(forwardRef(() => AdvisorsDocusignService)) private readonly advisorsDocusignService: AdvisorsDocusignService,
    @Inject(forwardRef(() => InterviewDocumentsService)) private readonly documentsService: InterviewDocumentsService,
    private readonly configService: ConfigService,
    private readonly clsService: ClsService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  async createDocusignEnvelope(interviewId: string, session?: ClientSession): Promise<string> {
    const interview = await this.interviewModel.findById(interviewId).session(session);
    if (!interview) throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);

    const { client, envelopeId } = interview;
    const clientId = client._id ? client._id.toString() : client.toString();

    const { organisationId, primaryAdvisor, primaryContact, secondaryContact } =
      await this.clientsService.findOne({ _id: clientId });

    const entityIds = {
      advisorId: primaryAdvisor.id.toString(),
      organisationId: organisationId.toString(),
    };

    const { accountOwnership } = await this.docusignService.getAccountInfo(entityIds);

    const isSchwabOwnedDocusignAccount =
      accountOwnership === DocusignAccountOwnershipEnum.Schwab;

    const schwabAdvisoryFiles = [];

    const advisorIntegrationConfig = await this.advisorsDocusignService.findDocusignIntegration(entityIds.advisorId);
    const organisationIntegrationConfig = await this.organisationsService.findDocusignIntegration(entityIds.organisationId);

    const integrationConfig = advisorIntegrationConfig ?? organisationIntegrationConfig;

    const createEnvelopeDto: CreateEnvelopeDto = {
      ...entityIds,
      primaryAdvisorName: `${primaryAdvisor.firstName} ${primaryAdvisor.lastName}`,
      primaryAdvisorEmail: primaryAdvisor.email,
      primaryAdvisorPhone: primaryAdvisor.mobile,
      applicant: {
        name: `${primaryContact.firstName} ${primaryContact.lastName}`,
        email: primaryContact.email,
        phone: primaryContact.mobile,
      },
      coapplicant: !isEmpty(secondaryContact) && {
        name: `${secondaryContact?.firstName} ${secondaryContact?.lastName}`,
        email: secondaryContact?.email,
        phone: secondaryContact?.mobile,
      },
      envelopeId,
      isSchwabOwnedDocusignAccount,
      shouldSend: false,
      webhookUrl: this.configService.get<string>('DOCUSIGN_STATUS_WEBHOOK_URI'),
      files: schwabAdvisoryFiles,
      schwabTemplateId: integrationConfig?.schwabTemplateId
    };

    const createdEnvelopeId = await this.docusignService.createEnvelope(
      createEnvelopeDto,
      session
    );

    const now = new Date();
    await this.interviewModel.updateMany(
      { client },
      {
        $set: {
          envelopeId: createdEnvelopeId,
          updatedAt: now,
        },
      },
      { session }
    );

    return createdEnvelopeId;
  }

  async prepareDocusignEnvelope(interviewId: string, session: ClientSession) {
    try {
      const interview = await this.interviewModel.findById(interviewId).session(session);

      if (!interview) {
        throw new Error(`Interview ${interviewId} not found`);
      }

      const { client } = interview;
      if (!client) {
        throw new Error(`Client not found for interview ${interviewId}`);
      }

      const clientData = await this.clientsService.findOne({ _id: interview.client }, session);
      const { primaryContact, secondaryContact, primaryAdvisor } = clientData;

      const clientId = interview.client._id ? interview.client._id.toString() : interview.client.toString();
      const organisationId = clientData.organisationId.toString();
      const advisorId = primaryAdvisor.id.toString();

      let envelopeId = interview.envelopeId;

      if (interview.docusignSelected) {
        const envelopeExists = !!envelopeId;
        if (!envelopeExists) {
          try {
            envelopeId = await this.createDocusignEnvelope(interviewId, session);
          } catch (error) {
            this.logger.error(`Failed to create DocuSign envelope: ${error.message}`);
            throw error;
          }
        }

        const interviews = await this.interviewModel.find(
          { client: clientId },
          null,
          { session },
        );

        const bothInterviewsFinished = interviews.every(
          (interview) => interview.isComplete,
        );

        if (bothInterviewsFinished) {
          const { accountOwnership } = await this.docusignService.getAccountInfo({
            organisationId,
            advisorId,
          });

          const interviewData = await this.getInterviewDataWithCrmInfo(
            {
              interview: interview.toObject(),
              primaryContact,
              secondaryContact,
              primaryAdvisor,
            },
            advisorId,
            session
          );

          const existingEnvelopFiles = await this.docusignService.getEnvelopDocuments({
            envelopeId,
            organisationId,
            advisorId
          }) || { envelopeDocuments: [] };

          const existingFilenames = existingEnvelopFiles.envelopeDocuments?.map(doc =>
            typeof doc === 'string' ? doc : doc?.name
          ).filter(Boolean) || [];

          const files: Express.Multer.File[] = await this.getEnvelopeFiles(
            accountOwnership,
            organisationId,
            primaryContact,
            secondaryContact,
          ) || [];

          const filesToUpload = files.filter(file =>
            file?.originalname && !existingFilenames.includes(file.originalname)
          );

          const addAdvisorDocumentsDto: AddAdvisorDocumentsDto = {
            advisorId,
            organisationId,
            envelopeId,
            files: filesToUpload,
            interviewData,
            previousDocumentUploadNo: clientData?.fileUploadsNo || 0,
          };
          await this.docusignService.addAdvisorDocuments(addAdvisorDocumentsDto);
          this.logger.info(
            `Added advisor documents to envelope for client ${clientId}`,
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error in prepareDocusignEnvelope: ${error.message}`);
      throw error;
    }
  }

  async getEnvelopeFiles(
    accountOwnership: DocusignAccountOwnershipEnum,
    organisationId: string,
    primaryContact: EnrichedContact,
    secondaryContact: EnrichedContact,
  ): Promise<Express.Multer.File[]> {
    const advisoryFiles: Express.Multer.File[] = [];

    // Get the account opening advisory files for the applicant and co-applicant
    const applicantAccountFiles = await this.documentsService.getAccountOpeningAdvisoryFiles(
      primaryContact?.accounts || [],
      organisationId,
    );

    const coApplicantAccountFiles = await this.documentsService.getAccountOpeningAdvisoryFiles(
      secondaryContact?.accounts || [],
      organisationId,
    );

    advisoryFiles.push(
      ...(await this.documentsService.getFilesFromS3(
        [AccountAdvisoryDocumentsEnum.AdvisoryAgreement],
        organisationId,
      )),
    );

    return [
      ...applicantAccountFiles,
      ...coApplicantAccountFiles,
      ...advisoryFiles,
    ];
  }

  private async getInterviewDataWithCrmInfo(
    interviewData: InterviewData,
    advisorId: string,
    session?: ClientSession,
  ): Promise<InterviewDataWithCrmInfo> {
    let organisation = this.clsService.get(ClsDataEnum.Organisation);
    const advisor = await this.advisorsCrudService.findOne({
      _id: advisorId,
    }, session);

    if (!organisation) {
      organisation = await this.organisationsService.findOne(
        advisor.organisation?._id?.toString(),
        session,
      );
    }

    const crmInstance = await this.advisorsCrmService.getCrmInstance(advisorId);
    const crmType = await crmInstance.getType();

    const primaryContactCrmInfo = await crmInstance.getContact(
      interviewData.primaryContact.crmClientId.toString(),
      true,
      true,
    );
    primaryContactCrmInfo.dob = fdob(primaryContactCrmInfo?.dob);

    const secondaryContactCrmInfo = !isEmpty(interviewData.secondaryContact)
      ? await crmInstance.getContact(
        interviewData.secondaryContact.crmClientId.toString(),
        true,
        true,
      )
      : null;
    secondaryContactCrmInfo &&
      (secondaryContactCrmInfo.dob = fdob(secondaryContactCrmInfo?.dob));

    const accounts = await Promise.all([
      this.mergeAccounts(
        primaryContactCrmInfo.accounts,
        interviewData.primaryContact.accounts,
        crmType
      ),
      !isEmpty(interviewData.secondaryContact)
        ? this.mergeAccounts(
          secondaryContactCrmInfo.accounts,
          interviewData.secondaryContact.accounts,
          crmType
        )
        : Promise.resolve([]),
    ]);
    return {
      ...interviewData,
      organisation,
      primaryAdvisor: interviewData.primaryAdvisor,
      primaryContact: {
        ...interviewData.primaryContact,
        ...primaryContactCrmInfo,
        accounts: accounts[0],
      },
      secondaryContact: !isEmpty(interviewData.secondaryContact)
        ? {
          ...interviewData.secondaryContact,
          ...secondaryContactCrmInfo,
          accounts: accounts[1],
        }
        : null,
    };
  }

  public async mergeAccounts(
    crmAccounts: GenericCrmAccount[],
    dbAccounts: AccountDto[],
    crmType: CRMType
  ): Promise<((AccountDto & GenericCrmAccount) | null)[]> {
    return dbAccounts.map((dbAccount): AccountDto & GenericCrmAccount => {
      const crmAccount = crmAccounts.find((acc) => {
        if (dbAccount.type === AccountTypeEnum.JointNameBrokerage) {
          return acc.name.includes(
            RedtailAccountOwnershipEnum[dbAccount.ownership],
          );
        }

        if (crmType === CRMEnum.Practifi || crmType === CRMEnum.Salesforce) {
          return acc.name === dbAccount.label;
        }

        return acc.name.includes(RedtailDBAccountType[dbAccount.type]);
      });

      return { ...dbAccount, ...(crmAccount || {}) } as AccountDto & GenericCrmAccount;
    });
  }
}