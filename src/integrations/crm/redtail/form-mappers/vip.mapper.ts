import { HttpException } from '@nestjs/common';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { VipRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/vip-request.dto';
import { RedtailContactUdfNameEnum } from 'src/integrations/crm/redtail/types/enums';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

export class RedtailVIPMapper implements FormUpdateMapper {
  constructor(private redtailCrm: RedtailCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: VipRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const contactUdfFieldId =
      organisation.configuration[RedtailContactUdfNameEnum.COMPANY_ASSOCIATION];

    if (!contactUdfFieldId) {
      throw new HttpException('Job description UDF not configured', 500);
    }

    if (pageData.companyName) {
      await this.redtailCrm.redtailUdfService.upsertContactUdf(
        contact.crmClientId,
        {
          contactUdfFieldId,
          fieldValue: `${pageData.tickerSymbol}: ${pageData.companyName}`,
        },
      );
      return;
    }
    await this.redtailCrm.redtailUdfService.deleteContactUdf(
      contact.crmClientId,
      contactUdfFieldId,
    );
  }
}
