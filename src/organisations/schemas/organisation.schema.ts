import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { DataFactory, Factory } from 'nestjs-seeder';
import { Address, AddressSchema } from 'src/shared/schemas/address.schema';
import { Asset, AssetSchema } from 'src/shared/schemas/asset.schema';
import {
  Integration,
  IntegrationSchema,
} from 'src/shared/schemas/integration.schema';
import { OrganisationStatusEnum } from '../types/organisation-status.enum';
import { CRMEnum } from 'src/shared/types/integrations';
import {
  NotificationTemplate,
  NotificationTemplateSchema,
} from 'src/organisations/schemas/notification.template';
import { QuestionTypeEnum } from 'src/shared/types/interview-questions/question-types.enum';

@Schema({ timestamps: true })
export class CustomQuestion extends Document {
  @Prop()
  question: string;

  @Prop({ enum: QuestionTypeEnum, default: QuestionTypeEnum.Text })
  type: QuestionTypeEnum;

  @Prop({ type: [String], default: null })
  options: string[] | null;

  @Prop({ default: false })
  required: boolean;

  @Prop({ required: false })
  title?: string;

  @Prop({ required: false })
  description?: string;
}

const CustomQuestionSchema = SchemaFactory.createForClass(CustomQuestion);
@Schema()
export class Organisation extends Document {
  @Factory(() => 'Onbord')
  @Prop({ required: true })
  name: string;

  @Factory(() => '<EMAIL>')
  @Prop()
  riaName: string;

  @Prop()
  email: string;

  @Factory((faker) => faker.phone.number())
  @Prop()
  phone: string;

  @Factory(true)
  @Prop()
  docuSign: boolean;

  @Factory(true)
  @Prop()
  transitions: boolean;

  @Factory(true)
  @Prop({ default: false })
  allowSendTransitionNotifications: boolean;

  @Factory(() => {
    return DataFactory.createForClass(Address).generate(1)[0];
  })
  @Prop({ type: AddressSchema })
  address: Address;

  @Factory([])
  @Prop({ type: [IntegrationSchema] })
  externalAccounts: Integration[];

  @Factory([])
  @Prop({ type: [AssetSchema] })
  assets: Asset[];

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;

  @Prop({ type: [String], default: [], match: /^\d{4}-\d{4}$/ })
  masterAccounts: string[];

  @Prop({ type: Object, of: String, default: {} })
  configuration?: Record<string, any>;

  @Factory(false)
  @Prop({ default: false })
  crmInitialized: boolean;

  @Prop({ type: [CustomQuestionSchema], default: [] })
  customQuestions: CustomQuestion[];

  @Prop({
    type: [NotificationTemplateSchema],
    required: false,
    default: [],
  })
  notificationTemplates: NotificationTemplate[];

  @Factory(OrganisationStatusEnum.Active)
  @Prop({
    default: OrganisationStatusEnum.Active,
  })
  status: OrganisationStatusEnum;

  @Prop({ enum: CRMEnum })
  selectedCRM: CRMEnum;

  @Factory(true)
  @Prop({ default: true })
  smsNotificationsEnabled: boolean;
}

export const OrganisationSchema = SchemaFactory.createForClass(Organisation);
