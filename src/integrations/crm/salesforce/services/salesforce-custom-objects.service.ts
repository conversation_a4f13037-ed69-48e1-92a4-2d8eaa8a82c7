import { Injectable } from '@nestjs/common';
import { Connection, Record as SalesforceRecord } from 'jsforce';
import { SalesforceCustomAccountDto, SalesforceBeneficiaryDto, SalesforceCustomQuestion } from 'src/integrations/crm/salesforce/dto/salesforce.dto';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { generateRequestConfigs } from '../utils/generate-request-configs.util';
import { OperationType } from 'src/super-http/enums';
import { salesforceQuery } from '../utils/salesforce-query.util';
import { SalesforceEntityTypes, BeneficiaryEnum } from '../types/enums';
import { commonConfigs } from '../types/metadata.types';

@Injectable()
export class SalesforceCustomObjectsService {
    private connection: Connection;
    private baseUrl: string;
    private commonConfigs: commonConfigs;

    constructor(
      private readonly httpService: SuperHttpService,
      private tenantId: string
    ) {}

    setConnection(connection: Connection) {
      this.connection = connection;
      this.baseUrl = `${connection.instanceUrl}/services/data/v${connection.version}`;
      this.prepareRequestConfigs();
    }

    prepareRequestConfigs() {
      const requestConfigs = generateRequestConfigs(this.connection.accessToken);
      const { withInterceptors, ...configsWithoutInterceptors } = requestConfigs;
      this.commonConfigs = configsWithoutInterceptors;
    }

    async createCustomAccount(customAccountData: SalesforceCustomAccountDto, contactId: string): Promise<SalesforceRecord> {
      return {
        response: await this.httpService.patch(
          `${this.baseUrl}/sobjects/Onbord_Account__c/External_Id__c/${encodeURIComponent(customAccountData.label__c)}-${customAccountData.Idempotency_key__c}`,
          this.tenantId,
          customAccountData,
          {
            rateLimiter: 'salesforce',
            integrationName: 'salesforce',
            operation: OperationType.PATCH,
            entityType: SalesforceEntityTypes.ACCOUNT
          },
          this.commonConfigs
        ),
        contactId,
        type: customAccountData.type__c,
      };
    }

    async linkOnbordAccountToContact(contactId: string, onbordAccountId: string) {
      return await this.httpService.patch(
        `${this.baseUrl}/sobjects/ContactOAccountRelation__c/External_Id__c/${contactId}-${onbordAccountId}`,
        this.tenantId,
        {
          "Contact__c" : contactId,
          "Onbord_Account__c" : onbordAccountId
        },
        {
          rateLimiter: 'salesforce',
          integrationName: 'salesforce',
          operation: OperationType.PATCH,
          entityType: SalesforceEntityTypes.RELATION
        },
        this.commonConfigs
      );
    }

    async linkAccountToPrimaryBeneficiary(benId: string, contactId: string) {
      return await this.httpService.patch(
        `${this.baseUrl}/sobjects/BeneficiaryOAccountRelation__c/External_Id__c/${benId}-${contactId}`,
        this.tenantId,
        {
          "Primary_Beneficiary__c" : benId,
          "Onbord_Account__c" : contactId
        },
        {
          rateLimiter: 'salesforce',
          integrationName: 'salesforce',
          operation: OperationType.PATCH,
          entityType: SalesforceEntityTypes.RELATION
        },
        this.commonConfigs
      );
  }

  async linkAccountToContBeneficiary(benId: string, contactId: string) {
    // TEST UPSERT

    return await this.httpService.patch(
      `${this.baseUrl}/sobjects/ContingentBenOAccountRelation__c/External_Id__c/${benId}-${contactId}`,
      this.tenantId,
      {
        "Contingent_Beneficiary__c" : benId,
        "Onbord_Account__c" : contactId
      },
      {
        rateLimiter: 'salesforce',
        integrationName: 'salesforce',
        operation: OperationType.PATCH,
        entityType: SalesforceEntityTypes.RELATION
      },
      this.commonConfigs
    );
  }

  async getLinkedAccounts(contactId: string) {
    const soql = `SELECT Id, Name, type__c FROM Onbord_Account__c WHERE Idempotency_key__c = '${contactId}'`;
    const result = await salesforceQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, SalesforceEntityTypes.ACCOUNT, this.tenantId);
    const recordsExist = result.totalSize > 0;

    if (recordsExist) {
      return result.records.map(el => ({
        id: el.Id,
        name: el.Name,
        type: el.type__c || '',
      }));
    }
    return [];
  }

  async createPrimaryBeneficiary(primaryBeneficiaryData: SalesforceBeneficiaryDto): Promise<SalesforceRecord> {
    return await this.httpService.patch(
      `${this.baseUrl}/sobjects/Primary_Beneficiary__c/External_Id__c/${encodeURIComponent(primaryBeneficiaryData.Name)}-${encodeURIComponent(primaryBeneficiaryData.Date_of_Birth__c)}`,
      this.tenantId,
      primaryBeneficiaryData,
      {
        rateLimiter: 'salesforce',
        integrationName: 'salesforce',
        operation: OperationType.PATCH,
        entityType: SalesforceEntityTypes.BENEFICIARY
      },
      this.commonConfigs
    );
  }

  async getLinkedBeneficiaries(accountId: string) {
    return await this.httpService.get(
      `${this.baseUrl}/ui-api/related-list-records/${accountId}/BeneficiaryOAccountRelations__r`,
      this.tenantId,
      {
        rateLimiter: 'salesforce',
        integrationName: 'salesforce',
        operation: OperationType.GET,
        entityType: SalesforceEntityTypes.BENEFICIARY
      },
      this.commonConfigs
    );
  }

  async deleteLinkedBeneficiaries(recordId: string) {
    return await this.httpService.delete(
      `${this.baseUrl}/sobjects/BeneficiaryOAccountRelation__c/${recordId}`,
      this.tenantId,
      {
        rateLimiter: 'salesforce',
        integrationName: 'salesforce',
        operation: OperationType.DELETE,
        entityType: SalesforceEntityTypes.BENEFICIARY
      },
      this.commonConfigs
    );
  }

  async createContingentBeneficiary(contBeneficiaryData: SalesforceBeneficiaryDto): Promise<SalesforceRecord> {
    return await this.httpService.patch(
      `${this.baseUrl}/sobjects/Contingent_Beneficiary__c/External_Id__c/${encodeURIComponent(contBeneficiaryData.Name)}-${encodeURIComponent(contBeneficiaryData.Date_of_Birth__c)}`,
      this.tenantId,
      contBeneficiaryData,
      {
        rateLimiter: 'salesforce',
        integrationName: 'salesforce',
        operation: OperationType.PATCH,
        entityType: SalesforceEntityTypes.BENEFICIARY
      },
      this.commonConfigs
    );
  }

  async getLinkedContBeneficiaries(accountId: string) {
    return await this.httpService.get(
      `${this.baseUrl}/ui-api/related-list-records/${accountId}/ContingentBenContactRelations__r`,
      this.tenantId,
      {
        rateLimiter: 'salesforce',
        integrationName: 'salesforce',
        operation: OperationType.GET,
        entityType: SalesforceEntityTypes.BENEFICIARY
      },
      this.commonConfigs
    );
  }

  async deleteLinkedContBeneficiaries(recordId: string) {
    return await this.httpService.delete(
      `${this.baseUrl}/sobjects/ContingentBenOAccountRelation__c/${recordId}`,
      this.tenantId,
      {
        rateLimiter: 'salesforce',
        integrationName: 'salesforce',
        operation: OperationType.DELETE,
        entityType: SalesforceEntityTypes.BENEFICIARY
      },
      this.commonConfigs
    );
  }

  async createCustomQuestion(customQuestionData: SalesforceCustomQuestion): Promise<SalesforceRecord> {
    return await this.httpService.patch(
      `${this.baseUrl}/sobjects/Custom_Question__c/External_Id__c/${encodeURIComponent(customQuestionData.Answer__c.slice(0,15))}`,
      this.tenantId,
      customQuestionData,
      {
        rateLimiter: 'salesforce',
        integrationName: 'salesforce',
        operation: OperationType.CREATE,
        entityType: SalesforceEntityTypes.QUESTION
      },
      this.commonConfigs
    );
  }

  async updateCustomQuestion(customQuestionData: SalesforceCustomQuestion, recordId: string): Promise<SalesforceRecord> {
    return await this.httpService.patch(
      `${this.baseUrl}/sobjects/Custom_Question__c/${recordId}`,
      this.tenantId,
      customQuestionData,
      {
        rateLimiter: 'salesforce',
        integrationName: 'salesforce',
        operation: OperationType.PATCH,
        entityType: SalesforceEntityTypes.QUESTION
      },
      this.commonConfigs
    );
  }

  async linkQuestionToContact(questionId: string, contactId: string) {
    return await this.httpService.patch(
      `${this.baseUrl}/sobjects/CustomQContactRelation__c/External_Id__c/${questionId}-${contactId}`,
      this.tenantId,
      {
        "Custom_Question__c" : questionId,
        "Contact__c" : contactId
      },
      {
        rateLimiter: 'salesforce',
        integrationName: 'salesforce',
        operation: OperationType.PATCH,
        entityType: SalesforceEntityTypes.RELATION
      },
      this.commonConfigs
    );
  }

  async getLinkedCustomQuestions(contactId: string) {
    return await this.httpService.get(
      `${this.baseUrl}/ui-api/related-list-records/${contactId}/CustomQContactRelations__r`,
      this.tenantId,
      {
        rateLimiter: 'salesforce',
        integrationName: 'salesforce',
        operation: OperationType.GET,
        entityType: SalesforceEntityTypes.QUESTION
      },
      this.commonConfigs
    );
  }

  async getAllLinkedBeneficiaries(accountId: string) {
    const beneficiaries = [];

    const linkedPrimBeneficiaries = await this.getLinkedBeneficiaries(accountId);


    if (linkedPrimBeneficiaries.count !== 0) {
      const linkedBeneficiariesIds = linkedPrimBeneficiaries.records.map(el => el.id);

      for await (const el of linkedBeneficiariesIds) {
        const soql = `SELECT Primary_Beneficiary__c FROM BeneficiaryOAccountRelation__c WHERE Id = '${el}'`;
        const result = await salesforceQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, SalesforceEntityTypes.BENEFICIARY, this.tenantId);
        const recordExists = result.totalSize > 0;

        if (recordExists) {
          const soql2 = `SELECT Name, Percentage__c, Date_of_Birth__c FROM Primary_Beneficiary__c WHERE Id = '${result.records[0].Primary_Beneficiary__c}'`;
          const result2 = await salesforceQuery(soql2, this.baseUrl, this.httpService, this.commonConfigs, SalesforceEntityTypes.BENEFICIARY, this.tenantId);
          const recordExists2 = result2.totalSize > 0;

          if (recordExists2) {
            const benRecord = {
              fullName: result2.records[0].Name,
              firstName: result2.records[0].Name.split(' ')[0],
              lastName: result2.records[0].Name.split(' ')[1],
              percentage: result2.records[0].Percentage__c,
              type: BeneficiaryEnum.PBEN,
              dob: result2.records[0].Date_of_Birth__c,
            }
            beneficiaries.push(benRecord);
          }
        }
      }
    }

    const linkedContBeneficiaries = await this.getLinkedContBeneficiaries(accountId);

    if (linkedContBeneficiaries.count !== 0) {
      const linkedContBeneficiariesIds = linkedContBeneficiaries.records.map(el => el.id);
      for await (const el of linkedContBeneficiariesIds) {
        const soql = `SELECT Contingent_Beneficiary__c FROM ContingentBenOAccountRelation__c WHERE Id = '${el}'`;
        const result = await salesforceQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, SalesforceEntityTypes.BENEFICIARY, this.tenantId);
        const recordExists = result.totalSize > 0;

        if (recordExists) {
          const soql2 = `SELECT Name, Percentage__c, Date_of_Birth__c FROM Contingent_Beneficiary__c WHERE Id = '${result.records[0].Contingent_Beneficiary__c}'`;
          const result2 = await salesforceQuery(soql2, this.baseUrl, this.httpService, this.commonConfigs, SalesforceEntityTypes.BENEFICIARY, this.tenantId);
          const recordExists2 = result2.totalSize > 0;

          if (recordExists2) {
            const benRecord = {
              fullName: result2.records[0].Name,
              firstName: result2.records[0].Name.split(' ')[0],
              lastName: result2.records[0].Name.split(' ')[1],
              percentage: result2.records[0].Percentage__c,
              type: BeneficiaryEnum.CBEN,
              dob: result2.records[0].Date_of_Birth__c,
            }
            beneficiaries.push(benRecord);
          }
        }
      }
    }

    return beneficiaries;
  }
}