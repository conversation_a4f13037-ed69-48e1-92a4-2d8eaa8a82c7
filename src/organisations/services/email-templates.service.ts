import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { ClientSession, Model } from 'mongoose';
import { EmailTemplate } from '../schemas/email-template.schema';
import { CreateEmailTemplateDto } from '../dto/email-template/create-email-template.dto';
import { GetEmailTemplatesQueryDto } from '../dto/email-template/get-email-templates.dto';
import { PaginationResponseDto } from 'src/shared/dto/pagination.dto';
import * as cheerio from 'cheerio';

@Injectable()
export class EmailTemplatesService {
  constructor(
    @InjectModel(EmailTemplate.name)
    private readonly emailTemplateModel: Model<EmailTemplate>,
  ) {}

  /**
   * Sanitizes HTML content for email templates by:
   * - Setting maximum width for images using both HTML attributes and inline styles
   * - Ensuring proper image formatting across all email clients
   * - Using email-safe styling approaches
   * @param rawHtml - The raw HTML content to be sanitized
   * @returns The sanitized HTML content with email-optimized formatting
   */
  private sanitizeEmailTemplateContent(rawHtml: string): string {
    const $ = cheerio.load(rawHtml);

    // Process images for email client compatibility
    $('img').each((_, img) => {
      // Set dimensions using HTML attributes (widest support)
      $(img).attr('width', '200');
      $(img).attr('height', 'auto');
      
      // Add inline styles (for clients that support CSS)
      const safeInlineStyle = 'display: block; max-width: 200px; width: auto; height: auto; border: 0; outline: none; text-decoration: none; -ms-interpolation-mode: bicubic;';
      $(img).attr('style', safeInlineStyle);
      
      // Add additional attributes for Outlook compatibility
      $(img).attr('border', '0');
      $(img).attr('align', 'middle');
      
      // Ensure images have alt text for accessibility
      if (!$(img).attr('alt')) {
        $(img).attr('alt', 'Logo');
      }
    });
    
    return $.html();
  }

  /**
   * Creates a new email template for an organization
   * @param organisationId - The ID of the organization
   * @param createEmailTemplateDto - The DTO containing template details
   * @param session - Optional MongoDB session for transactions
   * @returns The created email template
   */
  async create(
    organisationId: string,
    createEmailTemplateDto: CreateEmailTemplateDto,
    session?: ClientSession,
  ): Promise<EmailTemplate> {
    // Before saving, sanitize the HTML in emailTemplateContent
    const sanitizedHtml = this.sanitizeEmailTemplateContent(
      createEmailTemplateDto.emailTemplateContent,
    );
    console.log(sanitizedHtml);

    const emailTemplate = new this.emailTemplateModel({
      ...createEmailTemplateDto,
      emailTemplateContent: sanitizedHtml,
      organisationId,
    });
    return emailTemplate.save({ session });
  }

  /**
   * Retrieves all email templates for an organization with pagination and filtering
   * @param organisationId - The ID of the organization
   * @param query - Query parameters for filtering and pagination
   * @returns Paginated list of email templates
   */
  async findAll(
    organisationId: string,
    query: GetEmailTemplatesQueryDto,
  ): Promise<PaginationResponseDto<EmailTemplate[]>> {
    const { page = 1, limit = 10, emailTemplateType, isActive, search } = query;
    const skip = (page - 1) * limit;

    const filter: any = { organisationId };
    if (emailTemplateType) {
      filter.emailTemplateType = emailTemplateType;
    }
    
    if (isActive !== undefined) {
      filter.isActive = isActive;
    }

    if (search) {
      filter.$or = [
        { emailTemplateName: { $regex: search, $options: 'i' } },
        { emailTemplateContent: { $regex: search, $options: 'i' } },
      ];
    }

    const [result, totalResults] = await Promise.all([
      this.emailTemplateModel
        .find(filter)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.emailTemplateModel.countDocuments(filter).exec(),
    ]);

    return {
      result,
      totalResults,
    };
  }

  /**
   * Finds a specific email template by ID for an organization
   * @param organisationId - The ID of the organization
   * @param id - The ID of the email template
   * @returns The found email template
   * @throws NotFoundException if template is not found
   */
  async findOne(
    organisationId: string,
    id: string,
  ): Promise<EmailTemplate> {
    const emailTemplate = await this.emailTemplateModel.findOne({
      _id: id,
      organisationId,
    });

    if (!emailTemplate) {
      throw new NotFoundException(`Email template with ID ${id} not found`);
    }

    return emailTemplate;
  }

  /**
   * Finds an active email template by type for an organization
   * @param organisationId - The ID of the organization
   * @param emailTemplateType - The type of email template to find
   * @returns The found email template or null if not found
   */
  async findByType(
    organisationId: string,
    emailTemplateType: string,
  ): Promise<EmailTemplate | null> {
    return this.emailTemplateModel.findOne({
      organisationId,
      emailTemplateType,
      isActive: true,
    });
  }

  /**
   * Updates an existing email template
   * @param organisationId - The ID of the organization
   * @param id - The ID of the email template to update
   * @param updateEmailTemplateDto - The update DTO containing new template details
   * @param session - Optional MongoDB session for transactions
   * @returns The updated email template
   * @throws NotFoundException if template is not found
   */
  async update(
    organisationId: string,
    id: string,
    updateEmailTemplateDto: CreateEmailTemplateDto,
    session?: ClientSession,
  ): Promise<EmailTemplate> {
    // Sanitize HTML content if it's being updated
    if (updateEmailTemplateDto.emailTemplateContent) {
      updateEmailTemplateDto.emailTemplateContent = this.sanitizeEmailTemplateContent(
        updateEmailTemplateDto.emailTemplateContent,
      );
    }

    const emailTemplate = await this.emailTemplateModel
      .findOneAndUpdate(
        { _id: id, organisationId },
        { $set: updateEmailTemplateDto },
        { new: true, session },
      )
      .exec();

    if (!emailTemplate) {
      throw new NotFoundException(`Email template with ID ${id} not found`);
    }

    return emailTemplate;
  }

  /**
   * Removes an email template
   * @param organisationId - The ID of the organization
   * @param id - The ID of the email template to remove
   * @param session - Optional MongoDB session for transactions
   * @throws NotFoundException if template is not found
   */
  async remove(
    organisationId: string,
    id: string,
    session?: ClientSession,
  ): Promise<void> {
    const result = await this.emailTemplateModel
      .deleteOne({ _id: id, organisationId })
      .session(session)
      .exec();

    if (result.deletedCount === 0) {
      throw new NotFoundException(`Email template with ID ${id} not found`);
    }
  }

  /**
   * Toggles the active status of an email template
   * @param organisationId - The ID of the organization
   * @param id - The ID of the email template to toggle
   * @param session - Optional MongoDB session for transactions
   * @returns The updated email template
   * @throws NotFoundException if template is not found
   */
  async toggleActive(
    organisationId: string,
    id: string,
    session?: ClientSession,
  ): Promise<EmailTemplate> {
    const emailTemplate = await this.findOne(organisationId, id);
    
    const updatedTemplate = await this.emailTemplateModel
      .findOneAndUpdate(
        { _id: id, organisationId },
        { $set: { isActive: !emailTemplate.isActive } },
        { new: true, session },
      )
      .exec();

    if (!updatedTemplate) {
      throw new NotFoundException(`Email template with ID ${id} not found`);
    }

    return updatedTemplate;
  }
} 
