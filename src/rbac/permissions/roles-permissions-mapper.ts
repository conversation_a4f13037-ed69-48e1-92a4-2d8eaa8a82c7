import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import {
  Permission,
  PermissionEnum,
} from 'src/shared/types/rbac/permissions.enum';
import { Permissions } from './permissions';

type PermissionMapper = {
  [key in RolesEnum]: Permission[];
};

export const rolesPermissionsMapper: PermissionMapper = {
  CompanyAdmin: [
    Permissions[PermissionEnum.CanInterview],
    Permissions[PermissionEnum.CanCreateCompanyUsers],
    Permissions[PermissionEnum.CanAddClient],
    Permissions[PermissionEnum.CanAddUser],
    Permissions[PermissionEnum.CanManageConfig],
    Permissions[PermissionEnum.CanDeleteAnyClients],
    Permissions[PermissionEnum.CanDisableUser],
    Permissions[PermissionEnum.CanListAnyClients],
    Permissions[PermissionEnum.CanListUsers],
    Permissions[PermissionEnum.CanManageUserProfile],
    Permissions[PermissionEnum.CanModifyAnyClients],
    Permissions[PermissionEnum.CanUpdateSelf],
  ],
  Representative: [
    Permissions[PermissionEnum.CanInterview],
    Permissions[PermissionEnum.CanAddClient],
    Permissions[PermissionEnum.ListOwnClients],
    Permissions[PermissionEnum.ModifyOwnClients],
    Permissions[PermissionEnum.DeleteOwnClients],
    Permissions[PermissionEnum.CanUpdateSelf],
  ],
  SuperAdmin: Object.values(Permissions),
};
