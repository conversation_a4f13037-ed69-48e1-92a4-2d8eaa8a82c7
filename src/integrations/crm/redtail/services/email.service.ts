import { Logger, NotFoundException } from '@nestjs/common';
import {
  GetEmailsResponse,
  GetEmailsResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/email/get-emails-response.dto';
import {
  CreateEmailResponse,
  CreateEmailResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/email/create-email-response.dto';
import {
  UpdateEmailResponse,
  UpdateEmailResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/update-email-response.dto';
import { camelize } from 'src/utils';
import {
  RedtailCRMEndpointsEnum,
  RedtailEmailTypeEnum,
} from 'src/integrations/crm/redtail/types/enums';
import { Camelize } from 'src/shared/types/monads';
import { RedtailEmail } from 'src/integrations/crm/redtail/types/email.type';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { snakeify } from 'src/utils/snakeify';
import { OperationType } from 'src/super-http/enums';

export class RedtailEmailAddressService {
  private readonly baseUrl: string;
  private readonly apiKey: string;
  private logger: Logger;

  constructor(
    private httpService: SuperHttpService,
    private authHeader: object,
    private tenantId: string
  ) {
    this.baseUrl = process.env.REDTAIL_BASE_URL;
    this.apiKey = process.env.REDTAIL_API_KEY;
    this.logger = new Logger(RedtailEmailAddressService.name);
  }

  /**
   * Creates a new email address for a given CRM client.
   * @param crmClientId The ID of the CRM client to create the email address for.
   * @param email The email address to create.
   * @returns A Promise that resolves to a CreateEmailResponse object.
   */
  public async createEmailAddress(
    crmClientId: string,
    email: string,
  ): Promise<CreateEmailResponse> {
    const emailRequestBody = {
      emailType: RedtailEmailTypeEnum.HOME,
      address: email,
      isPrimary: true,
      isPreferred: false,
    };

    const response = await this.httpService.post<CreateEmailResponseDto>(
      `${this.baseUrl}/contacts/${crmClientId}/emails`,
      this.tenantId,
      snakeify(emailRequestBody),
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.CREATE,
        entityType: 'email',
        entityId: crmClientId,
        wideCacheInvalidation: true
      },
      { headers: this.authHeader },
    );
    return camelize(response);
  }

  /**
   * Retrieves the email addresses associated with a given CRM client ID.
   * @param crmClientId The ID of the CRM client to retrieve email addresses for.
   * @returns A promise that resolves to a `GetEmailsResponse` object containing the email addresses.
   */
  public async getEmailAddresses(
    crmClientId: string,
  ): Promise<GetEmailsResponse> {
    const url = `${this.baseUrl}/contacts/${crmClientId}/emails`;

    const response = await this.httpService.get<GetEmailsResponseDto>(
      url,
      this.tenantId,
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.GET,
        entityType: 'email',
        entityId: crmClientId,
        forceFresh: false,
        cacheKeysObject: { crmClientId }
      },
      { headers: this.authHeader },
    );
    return camelize(response);
  }

  /**
   * Retrieves the main email address for a given CRM client ID.
   * @param crmClientId The ID of the CRM client to retrieve the email address for.
   * @returns A Promise that resolves with an Email object representing the main email address for the given CRM client ID.
   */
  public async getMainEmailAddress(
    crmClientId: string,
  ): Promise<Camelize<RedtailEmail>> {
    const url = `${this.baseUrl}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/emails`;
    const response = await this.httpService.get<GetEmailsResponseDto>(
      url,
      this.tenantId,
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.GET,
        entityType: 'email',
        entityId: crmClientId,
        forceFresh: false,
        cacheKeysObject: { crmClientId }
      },
      { headers: this.authHeader },
    );
    if (!response)
      throw new NotFoundException(
        `Email not found for contact with id ${crmClientId}`,
      );
    return camelize(
      response.emails?.find(
        (email) =>
          email.is_primary && email.email_type === RedtailEmailTypeEnum.HOME,
      ) || response?.emails[0],
    );
  }

  /**
   * Updates the email address of a contact in Redtail CRM.
   * @param crmClientId The ID of the contact in Redtail CRM.
   * @param emailId The ID of the email address to update.
   * @param email The new email address.
   * @returns A Promise that resolves to an UpdateEmailResponse object.
   */
  public async updateEmailAddress(
    crmClientId: string,
    emailId: string,
    email: string,
  ): Promise<UpdateEmailResponse> {
    const emailRequestBody = {
      address: email,
    };

    const response = await this.httpService.put<UpdateEmailResponseDto>(
      `${this.baseUrl}/contacts/${crmClientId}/emails/${emailId}`,
      this.tenantId,
      snakeify(emailRequestBody),
      {
        rateLimiter: 'redtail',
        integrationName: 'redtail',
        operation: OperationType.UPDATE,
        entityType: 'email',
        entityId: crmClientId,
        wideCacheInvalidation: true
      },
      { headers: this.authHeader },
    );

    return camelize(response);
  }
}
