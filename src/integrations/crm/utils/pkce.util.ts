import * as crypto from 'crypto';

export class PKCEUtil {
  static generateCodeVerifier(): string {
    // Generate exactly 96 characters for consistency
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const length = 96;
    let verifier = '';
    
    const values = crypto.randomBytes(length);
    for (let i = 0; i < length; i++) {
      verifier += charset[values[i] % charset.length];
    }

    return verifier;
  }

  static generateCodeChallenge(verifier: string): string {
    const hash = crypto.createHash('sha256')
      .update(verifier)
      .digest();
    
    return Buffer.from(hash)
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  static encodeState(data: Record<string, any>): string {
    return Buffer.from(JSON.stringify(data))
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  }

  static decodeState(state: string): Record<string, any> {
    // Add back padding if needed
    let safeState = state;
    while (safeState.length % 4) {
      safeState += '=';
    }
    
    // Convert URL-safe characters back
    safeState = safeState
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    return JSON.parse(
      Buffer.from(safeState, 'base64').toString('utf-8')
    );
  }
}