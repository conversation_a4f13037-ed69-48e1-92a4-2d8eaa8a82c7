variable "app_name" {
  description = "The name of the Elastic Beanstalk application"
  type        = string
}

variable "region" {
  description = "The AWS region where the resources will be created"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "The environment to use (populated by TF_VAR_environment)"
  type        = string
  default     = "production"
}

variable "commit_sha" {
  description = "The commit SHA to use for the Elastic Beanstalk application version."
  type        = string
}

variable "solution_stack_name" {
  description = "The name of the Elastic Beanstalk solution stack"
  type        = string
}

variable "instance_type" {
  description = "The instance type for the Elastic Beanstalk environment"
  type        = string
}

variable "healthcheck_url" {
  description = "The URL path for the Elastic Beanstalk environment health check"
  type        = string
  default     = "/health"
}

variable "environment_variables" {
  description = "A map of environment variables to pass to the Elastic Beanstalk environment"
  type        = map(string)
  default = {
    NODE_ENV = "production"
  }
}

variable "min_instances" {
  description = "The minimum number of instances for the Elastic Beanstalk environment"
  type        = number
}

variable "max_instances" {
  description = "The maximum number of instances for the Elastic Beanstalk environment"
  type        = number
}

variable "source_bundle_bucket" {
  description = "The S3 bucket containing the source bundle for the application"
  type        = string
}

variable "domain_name" {
  description = "The domain name for the Route53 record"
  type        = string
}

variable "app_port" {
  description = "The port the application is listening on"
  type        = string
  default     = "3000"
}

variable "request_count_threshold" {
  description = "The threshold for the number of requests."
  type        = number
  default     = 5000
}

variable "healthy_host_count_threshold" {
  description = "The threshold for the number of healthy hosts."
  type        = number
  default     = 3
}

variable "latency_threshold" {
  description = "The threshold for the latency."
  type        = number
  default     = 200
}

variable "dd_app_key_ssm" {
  description = "The SSM parameter name for the Datadog app key"
  type        = string
}

variable "dd_api_key_ssm" {
  description = "The SSM parameter name for the Datadog API key"
  type        = string
}

variable "cluster_name" {
  description = "The name of the MongoDB Atlas cluster"
  type        = string
}

variable "instance_size" {
  description = "The instance size of the MongoDB Atlas cluster: M0, M2 or M5"
  type        = string
}

variable "mongodb_atlas_org_id_ssm" {
  description = "The SSM parameter name for the MongoDB Atlas organization ID"
  type        = string
}

variable "mongodb_atlas_api_pub_key_ssm" {
  description = "The SSM parameter name for the MongoDB Atlas API public key"
  type        = string
}

variable "mongodb_atlas_api_pri_key_ssm" {
  description = "The SSM parameter name for the MongoDB Atlas API private key"
  type        = string
}

variable "mongodb_atlas_database_user_username_ssm" {
  description = "The SSM parameter name for the MongoDB Atlas database user username"
  type        = string
}

variable "mongodb_atlas_database_user_password_ssm" {
  description = "The SSM parameter name for the MongoDB Atlas database user password"
  type        = string
}

variable "origin" {
  description = "The origin for the API"
}

variable "work_env" {
  description = "The environment (staging, production)."
  type        = string
  default     = "production"
}

