variable "allocated_storage" {
  default     = 20
  description = "The allocated storage in GBs"
}

variable "storage_type" {
  default     = "gp2"
  description = "The type of storage"
}

variable "engine" {
  default     = "postgres"
  description = "The database engine"
}

variable "engine_version" {
  default     = "15.3"
  description = "The database engine version"
}

variable "instance_class" {
  default     = "db.t3.micro"
  description = "The instance type"
}

variable "name" {
  description = "The database name"
}

variable "parameter_group_name" {
  default     = "default.postgres15"
  description = "The name of the parameter group to associate with this instance"
}

variable "publicly_accessible" {
  default     = false
  description = "Whether the database should have a public IP address"
}

variable "db_username_ssm" {
  description = "The SSM parameter name for the database username"
}

variable "db_password_ssm" {
  description = "The SSM parameter name for the database password"
}

variable "multi_az" {
  description = "Whether to create a multi-AZ RDS instance"
  type        = bool
  default     = false
}

variable "eb_ec2_sg_id" {
  description = "The ID of the security group for the Elastic Beanstalk EC2 instances"
}

variable "environment" {
  description = "The environment name"
}

variable "backup_retention_period" {
  description = "The number of days to retain backups for."
  default     = 7
}

variable "backup_window" {
  description = "The daily time range during which automated backups are created if they are enabled."
  default     = "04:00-06:00"
}
variable "enable_backup" {
  description = "Flag to enable or disable backup."
  default     = true
}