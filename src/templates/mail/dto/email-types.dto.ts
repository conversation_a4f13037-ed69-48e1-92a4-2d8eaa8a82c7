import { ApiProperty } from '@nestjs/swagger';
import { EmailTemplateNameEnum } from '../types';

export class EmailTypeDto {
  @ApiProperty({ enum: EmailTemplateNameEnum })
  emailType: EmailTemplateNameEnum;

  @ApiProperty({ type: String })
  label: string;

  @ApiProperty({ type: String, description: 'A description of the email template purpose for tooltips' })
  description: string;

  @ApiProperty({ type: [String] })
  availablePlaceholders: string[];
  
  @ApiProperty({ type: String, description: 'The default HTML template content' })
  defaultTemplate: string;
}

export class EmailTypesResponseDto {
  @ApiProperty({ type: [EmailTypeDto] })
  emailTypes: EmailTypeDto[];
} 