import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AdvisorsModule } from 'src/advisors/advisors.module';
import { CsvModule } from 'src/csv/csv.module';
import { SmsModule } from 'src/notifications/sms/sms.module';
import { OrganisationsModule } from 'src/organisations/organisations.module';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { Transition, TransitionSchema } from './schemas/transitions.schema';
import { TransitionsController } from './transitions.controller';
import { TransitionsService } from './transitions.service';
import { DocusignModule } from 'src/integrations/docusign/docusign.module';
import { ConfigModule } from '@nestjs/config';
import { MailModule } from 'src/notifications/mail/mail.module';
import { TransitionsPublicController } from 'src/transitions/transitions.public.controller';
import { AuditsModule } from 'src/audits/audits.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Transition.name, schema: TransitionSchema },
    ]),
    ConfigModule,
    SmsModule,
    AuditsModule,
    MailModule,
    forwardRef(() => OrganisationsModule),
    CsvModule,
    forwardRef(() => AdvisorsModule),
    forwardRef(() => DocusignModule),
  ],
  controllers: [TransitionsController, TransitionsPublicController],
  providers: [TransitionsService, TransactionManager],
  exports: [TransitionsService],
})
export class TransitionsModule {}
