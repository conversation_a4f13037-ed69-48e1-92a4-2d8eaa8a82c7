import { Job, JobNode } from 'bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { SaveJobsDto } from './dto/save-jobs.dto';
import { GetJobsDto } from './dto/get-jobs.dto';
import { QueueRegistryService } from './queue-registry.service';
import { QueueLog } from './schemas/queue-log.schema';
import { QueueLogStatusEnum } from './enums/queue-log-status.enum';

@Injectable()
export class QueueLogService {
  private readonly logger = new Logger(QueueLogService.name);

  constructor(
    @InjectModel(QueueLog.name) private readonly queueLogModel: Model<QueueLog>,
    private readonly queueRegistry: QueueRegistryService,
  ) {}

  async save(payload: SaveJobsDto): Promise<QueueLog> {
    const { jobId, resource, queueName, status, result, error, organisationId } = payload;
    const update = {
      jobId,
      resource,
      queueName,
      status,
      result: status === QueueLogStatusEnum.COMPLETED ? result : undefined,
      error: status === QueueLogStatusEnum.FAILED ? error : undefined,
      timestamp: new Date(),
      organisationId
    };

    const options = {
      upsert: true,
      new: true,
      setDefaultsOnInsert: true
    };

    return this.queueLogModel.findOneAndUpdate({ jobId }, update, options);
  }

  async getFailedJob(id: string, organisationId: string): Promise<QueueLog | null> {
    try {
      const query: any = { organisationId };

      if (Types.ObjectId.isValid(id)) {
        query._id = new Types.ObjectId(id);
      } else {
        query.jobId = id;
      }

      query.status = QueueLogStatusEnum.FAILED;

      const job = await this.queueLogModel.findOne(query);
      if (!job) {
        this.logger.warn(`Failed job not found with id: ${id} and organisationId: ${organisationId}`);
      }
      return job;
    } catch (error) {
      this.logger.error(`Error finding failed job: ${error.message}`);
      return null;
    }
  }

  async retryJob(id: string, organisationId: string): Promise<QueueLog | null> {
    const failedJob = await this.getFailedJob(id, organisationId);

    if (!failedJob) {
      this.logger.warn(`Job ${id} not found for organisation ${organisationId}`);
      return null;
    }

    if (failedJob.status !== QueueLogStatusEnum.FAILED) {
      this.logger.warn(`Job ${id} is not in a failed state. Current status: ${failedJob.status}`);
      return null;
    }

    const { queueName, jobId } = failedJob;
    const queue = this.queueRegistry.getQueue(queueName);

    if (!queue) {
      this.logger.error(`Queue "${queueName}" not found in registry`);
      return null;
    }

    try {
      const job = await queue.getJob(jobId);

      if (!job) {
        this.logger.error(
          `Cannot retry job ${jobId}: Job not found in Redis and original context is lost. ` +
          `Manual intervention may be required.`
        );
        return null;
      }

      await this.retryOrRunJob(job);
      return await this.updateFailedJob(failedJob);
    } catch (error) {
      this.logger.error(`Failed to retry job ${failedJob.jobId}:`, error.stack);
      throw error;
    }
  }

  private async retryOrRunJob(job: Job): Promise<void> {
    const jobState = await job.getState();
    if (jobState === 'failed') {
      this.logger.log(`Retrying failed job ${job.id} in queue ${job.queueName}`);
      await job.retry();
    } else if (['waiting', 'delayed'].includes(jobState)) {
      this.logger.log(`Job ${job.id} in queue ${job.queueName} is already scheduled to run`);
    } else {
      this.logger.warn(`Job ${job.id} is in state "${jobState}" and does not need to be retried`);
    }
  }

  private async updateFailedJob(failedJob: QueueLog): Promise<QueueLog> {
    failedJob.status = QueueLogStatusEnum.RETRYING;
    failedJob.retryCount = (failedJob.retryCount || 0) + 1;
    failedJob.lastRetryTimestamp = new Date();
    return failedJob.save();
  }

  private async retryFlowChildren(jobNode: JobNode): Promise<void> {
    if (!jobNode.children) return;

    for (const child of jobNode.children) {
      const childQueue = this.queueRegistry.getQueue(child.job.queueName);
      if (!childQueue) {
        this.logger.warn(`Queue ${child.job.queueName} not found for child job ${child.job.id}`);
        continue;
      }

      const childJob = await childQueue.getJob(child.job.id);
      if (childJob) {
        await this.retryOrRunJob(childJob);
      } else {
        this.logger.warn(`Child job ${child.job.id} not found in Redis - skipping retry`);
      }

      if (child.children && child.children.length > 0) {
        await this.retryFlowChildren(child);
      }
    }
  }

  async getJobs(options: GetJobsDto, organisationId: string): Promise<{ jobs: QueueLog[]; total: number }> {
    const { page = 1, limit = 10, filter } = options;
    const query: any = this.buildQuery(filter, organisationId);

    const total = await this.queueLogModel.countDocuments(query);
    const jobs = await this.queueLogModel.find(query)
      .sort({ timestamp: -1 })
      .skip((page - 1) * limit)
      .limit(limit)
      .exec();

    return { jobs, total };
  }

  private buildQuery(filter: any, organisationId: string): any {
    const query: any = {};
    if (filter?.status) query.status = filter.status;
    if (filter?.startDate || filter?.endDate) {
      query.timestamp = {};
      if (filter.startDate) query.timestamp.$gte = filter.startDate;
      if (filter.endDate) query.timestamp.$lte = filter.endDate;
    }
    query.organisationId = organisationId;
    return query;
  }
}