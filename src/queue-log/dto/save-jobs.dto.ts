import { IsEnum, <PERSON>NotEmpty, <PERSON>Optional, IsString } from "class-validator";
import { QueueLogStatusEnum } from "../enums/queue-log-status.enum";
import { ResourceOptionsEnum } from "src/shared/types/queue-log/queue";

export class SaveJobsDto {
  @IsString()
  @IsNotEmpty()
  jobId: string;

  @IsEnum(ResourceOptionsEnum)
  @IsNotEmpty()
  resource: ResourceOptionsEnum; 

  @IsString()
  @IsNotEmpty()
  queueName?: string;

  @IsEnum(QueueLogStatusEnum)
  @IsNotEmpty()
  status: QueueLogStatusEnum;

  @IsString()
  @IsOptional()
  result?: string;

  @IsString()
  @IsOptional()
  error?: string;

  @IsString()
  @IsNotEmpty()
  organisationId: string;
}
