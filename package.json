{"name": "onbord-backend", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "docker compose up -d --build", "start:dev-watch": "nest start --watch", "start:debug": "nest start --debug 0.0.0.0:9229 --watch", "start:prod": "node --experimental-loader=newrelic/esm-loader.mjs dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --runInBand", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:memory": "NODE_OPTIONS=\"--max-old-space-size=8192 --expose-gc\" node --expose-gc --v8-pool-size=1 --trace-warnings node_modules/.bin/jest --config ./.jest.gc.config.js --detectLeaks --detectOpenHandles --runInBand --logHeapUsage --no-cache", "seed": "docker compose -f seeder.docker-compose.yml up onbord-dev-seeder", "seed:refresh": "npm run build && node dist/src/seeder --refresh", "db:init": "ts-node -r tsconfig-paths/register --project tsconfig.json scripts/seeding/init.ts"}, "dependencies": {"@aws-sdk/client-cloudwatch-logs": "^3.732.0", "@bull-board/api": "^6.0.0", "@bull-board/express": "^6.0.0", "@bull-board/nestjs": "^6.0.0", "@keyv/redis": "^4.2.0", "@maselious/bottleneck": "^2.19.8", "@nestjs-modules/mailer": "^1.8.1", "@nestjs/axios": "^3.1.2", "@nestjs/bullmq": "^10.2.1", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.3.2", "@nestjs/core": "^9.0.0", "@nestjs/devtools-integration": "^0.1.6", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.1.0", "@nestjs/mapped-types": "*", "@nestjs/mongoose": "^10.0.0", "@nestjs/passport": "^9.0.3", "@nestjs/platform-express": "^9.4.3", "@nestjs/schedule": "^3.0.3", "@nestjs/swagger": "^7.1.1", "@nestjs/terminus": "^10.0.1", "@nestjs/throttler": "^4.2.1", "@types/js-yaml": "^4.0.9", "@types/jsforce": "^1.11.5", "amazon-cognito-identity-js": "^6.3.1", "aws-sdk": "^2.1418.0", "axios": "^1.7.7", "bullmq": "^5.20.0", "cache-manager": "^5.2.3", "cache-manager-redis-store": "^3.0.1", "cache-manager-redis-yet": "^5.1.5", "camel-case": "^4.1.2", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cookie-parser": "^1.4.6", "cron-parser": "^4.9.0", "data-guardian": "^1.1.2", "date-fns": "^2.30.0", "dd-trace": "^4.3.0", "docusign-esign": "^6.2.0", "express-session": "^1.17.3", "handlebars": "^4.7.8", "helmet": "^7.1.0", "ioredis": "^5.4.2", "ioredis-mock": "^8.9.0", "js-yaml": "^4.1.0", "jsforce": "^3.6.3", "jwks-rsa": "^3.0.1", "keyv": "^5.2.3", "libphonenumber-js": "^1.10.55", "lodash": "^4.17.21", "mongoose": "^7.3.1", "multer": "^1.4.5-lts.1", "nest-csv-parser": "^2.0.4", "nest-winston": "^1.9.4", "nestjs-cls": "^3.5.0", "nestjs-pino": "^3.3.0", "nestjs-twilio": "4.1.1", "newrelic": "^12.11.2", "nock": "^13.5.6", "nodemailer": "^6.9.2", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdfjs-dist": "^3.11.174", "reflect-metadata": "^0.1.13", "retry-axios": "^2.6.0", "rxjs": "^7.2.0", "sharp": "0.32.6", "streamifier": "^0.1.1", "twilio": "4.23.0", "uuid": "^11.0.5", "winston": "^3.11.0", "winston-cloudwatch": "^6.2.0", "zlib": "^1.0.5"}, "devDependencies": {"@faker-js/faker": "^9.4.0", "@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@playwright/test": "^1.51.1", "@types/cookie-parser": "^1.4.3", "@types/docusign-esign": "^5.19.0", "@types/express": "^4.17.13", "@types/express-session": "^1.17.7", "@types/jest": "29.5.1", "@types/lodash": "^4.14.195", "@types/multer": "^1.4.7", "@types/node": "18.16.12", "@types/nodemailer": "^6.4.7", "@types/passport-jwt": "^3.0.8", "@types/passport-local": "^1.0.35", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.59.11", "@typescript-eslint/parser": "^5.59.11", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.5.0", "lefthook": "^1.11.5", "mongodb-memory-server": "^8.14.0", "nestjs-seeder": "^0.3.2", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.1.0", "ts-loader": "^9.2.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "roots": ["<rootDir>/src/", "<rootDir>/scripts/"], "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "transformIgnorePatterns": ["/node_modules/(?!camelcase/)"], "moduleNameMapper": {"^src/(.*)$": "<rootDir>/src/$1"}, "collectCoverageFrom": ["**/*.(t|j)s", "!**/*.module.ts", "!**/main.ts", "!**/tests/**", "!**/mocks/**"], "collectCoverage": true, "coverageReporters": ["text"], "coverageDirectory": "./coverage", "testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/src/test-setup.ts"]}}