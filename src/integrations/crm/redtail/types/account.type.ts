import { RedtailAccountDetail } from 'src/integrations/crm/redtail/types/account-detail.type';
import { RedtailSAM } from 'src/integrations/crm/redtail/types/sam.type';

export type RedtailAccount = {
  id: number;
  contact_id: number;
  account_type_id: number;
  account_type: string;
  is_linked?: boolean;
  status_id: number;
  status: string;
  number: string;
  balance: string;
  feed_aggregator?: string;
  feed_account_number?: string;
  registration?: string;
  company: string;
  product?: string;
  taxqualified: boolean;
  taxqualified_type_id?: string;
  taxqualified_type?: string;
  discretionary: boolean;
  held_away: boolean;
  managed: boolean;
  deleted: boolean;
  created_at: string;
  updated_at: string;
  assets?: any[];
  beneficiaries?: any[];
  insureds?: any[];
  owners?: any[];
  udfs?: any[];
  detail: RedtailAccountDetail;
  sam: RedtailSAM;
};
