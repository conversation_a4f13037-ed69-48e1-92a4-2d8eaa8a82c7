import { Model, connect, model } from 'mongoose';
import { RoleSchema, Role } from 'src/rbac/schemas/role.schema';
import { Advisor, AdvisorSchema } from 'src/advisors/schemas/advisors.schema';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { rolesPermissionsMapper } from 'src/rbac/permissions/roles-permissions-mapper';
import * as superadmins from './superadmins.json';
import {
  Organisation,
  OrganisationSchema,
} from 'src/organisations/schemas/organisation.schema';
import {
  InterviewTemplate,
  InterviewTemplateSchema,
} from 'src/interview-templates/schemas/v1/interview.template';
import { defaultInterviewTemplateSeed } from 'scripts/seeding/interviewTemplate';

// TODO: Add template for seeding

type AdvisorInfo = {
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
};

export async function cleanUpMongo() {
  const AdvisorModel = model(Advisor.name, AdvisorSchema);
  const RoleModel = model(Role.name, RoleSchema);
  const OrganisationModel = model(Organisation.name, OrganisationSchema);
  await AdvisorModel.deleteMany({});
  await RoleModel.deleteMany({});
  await OrganisationModel.deleteMany({});
}

export async function seed() {
  await createRoles();
  const org = await createOnbordOrg();
  for (const superAdmin of superadmins[process.env.ENV || 'staging']) {
    await createSuperAdmin(superAdmin, org);
  }
  await createInterviewTemplate();
}

export async function createRoles() {
  const RoleModel = model(Role.name, RoleSchema);
  for (const role of Object.keys(RolesEnum)) {
    const dbRole = await RoleModel.findOne({
      name: role,
    });
    if (!dbRole) {
      await RoleModel.create({
        name: role,
        permissions: rolesPermissionsMapper[role],
      });
    }
  }
}

export async function createSuperAdmin(
  adviserInfo: AdvisorInfo,
  org: Organisation,
) {
  const advisorModel = model(Advisor.name, AdvisorSchema);
  const RoleModel = model(Role.name, RoleSchema);
  const role = await RoleModel.findOne({
    name: RolesEnum.SuperAdmin,
  });
  // Create the SuperAdmin user if not present
  let superAdminUser = await advisorModel.findOne({
    'personalInfo.email': adviserInfo.email,
  });
  if (!superAdminUser) {
    const superAdminAdvisor = {
      personalInfo: {
        ...adviserInfo,
      },
      role: role.name,
      integrations: [],
    };

    superAdminUser = await advisorModel.create({
      ...superAdminAdvisor,
      organisation: {
        organisationId: org._id,
        name: org.name,
        phone: org.phone,
        address: org.address,
        email: org.email,
        setupComplete: false,
      },
      role: role.name,
    });

    console.log('SuperAdmin user created successfully');
    return superAdminUser;
  }
  console.log('SuperAdmin user already exists');
}

export async function createOnbordOrg() {
  const organisationModel = model(Organisation.name, OrganisationSchema);
  let organisation = await organisationModel.findOne({
    name: 'Onbord',
  });

  if (!organisation) {
    organisation = await organisationModel.create({
      name: 'Onbord',
      address: {
        street: '123 Fake Street',
        city: 'Brisbane',
        state: 'QLD',
        postcode: '4000',
        country: 'USA',
      },
      externalAccounts: [],
      assets: [],
      docuSign: true,
      email: 'changeme',
      phone: 'changeme',
      selectedCRM: 'Redtail',
    });
  }

  return organisation;
}

export async function createInterviewTemplate() {
  const InterviewTemplateModel = model(
    InterviewTemplate.name,
    InterviewTemplateSchema,
  );
  let interviewTemplate = await InterviewTemplateModel.findOne({
    default: true,
  });

  if (!interviewTemplate) {
    interviewTemplate = await InterviewTemplateModel.create(
      defaultInterviewTemplateSeed,
    );
  }

  return interviewTemplate;
}
