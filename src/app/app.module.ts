import { CacheStore, MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HealthModule } from 'src/health/health.module';
import { MongooseModule } from '@nestjs/mongoose';
import { AdvisorsModule } from 'src/advisors/advisors.module';
import { AuthModule } from 'src/auth/auth.module';
import { ThrottlerModule } from '@nestjs/throttler';
import { OrganisationsModule } from 'src/organisations/organisations.module';
import { RbacModule } from 'src/rbac/rbac.module';
import { AuditsModule } from 'src/audits/audits.module';
import { LoggerMiddleware } from 'src/shared/middleware/logger.middleware';
import { ClsModule } from 'nestjs-cls';
import { clsSetup } from 'src/shared/middleware/cls-setup.middleware';
import { ClientsModule } from 'src/clients/clients.module';
import { MutexModule } from 'src/mutex/mutex.module';
import { DocusignModule } from 'src/integrations/docusign/docusign.module';
import { InterviewsModule } from 'src/interviews/interviews.module';
import { TransitionsModule } from 'src/transitions/transitions.module';
import { WinstonModule } from 'nest-winston';
import winstonConfig from 'src/winston.config';
import { NotificationsModule } from 'src/notifications/notifications.module';
import { InterviewTemplatesModule } from 'src/interview-templates/interview-templates.module';
import { APP_FILTER } from '@nestjs/core';
import { AllExceptionsFilter } from 'src/utils/filters/exceptions.filter';
import { BullModule, BullRootModuleOptions } from '@nestjs/bullmq';
import { BullBoardModule } from '@bull-board/nestjs';
import { ExpressAdapter } from "@bull-board/express";
import { CacheModule } from '@nestjs/cache-manager';
import { redisStore } from 'cache-manager-redis-yet';
import { MailTemplatesModule } from 'src/templates/mail/mail-templates.module';

@Module({
  imports: [
    WinstonModule.forRoot(winstonConfig),
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    HealthModule,
    ClsModule.forRoot({
      middleware: {
        mount: true,
        setup: clsSetup,
      },
    }),
    ThrottlerModule.forRoot({
      ttl: 60,
      limit: 100,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('DATABASE_URL'),
        auth: {
          username: configService.get<string>('DB_USERNAME'),
          password: configService.get<string>('DB_PASSWORD'),
        },
        connectionFactory: (connection) => {
          connection.on('error', (error) => {
            console.error('MongoDB connection error:', error);
            // Force process exit on critical DB errors
            if (error.message.includes('ECONNREFUSED') || error.message.includes('failed to connect')) {
              process.exit(1);
            }
          });
          
          connection.on('disconnected', () => {
            console.error('MongoDB disconnected');
          });
          
          connection.on('connected', () => {
            console.log('MongoDB connected successfully');
          });
          
          return connection;
        },
        serverSelectionTimeoutMS: 5000,
        connectTimeoutMS: 10000,
        socketTimeoutMS: 45000,
        waitQueueTimeoutMS: 10000,
        retryWrites: true,
        retryReads: true
      }),
      inject: [ConfigService],
    }),
    CacheModule.registerAsync({
      useFactory: async () => {
        const store = await redisStore({
          socket: {
            host: process.env.REDIS_HOST,
            port: Number(process.env.REDIS_PORT),
            tls: process.env.WORK_ENV !== 'dev',
            timeout: 10000,
          },
          password: process.env.REDIS_PASSWORD,
          ttl: 1000,
        });
        return {
          store: store as unknown as CacheStore,
          url: process.env.REDIS_HOST,
          host: process.env.REDIS_HOST,
          port: Number(process.env.REDIS_PORT),
          ttl: 1000,
          isGlobal: true,
        };
      },
    }),
    BullModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService): BullRootModuleOptions => ({
        connection: {
          host: config.get<string>('REDIS_HOST'),
          port: config.get<number>('REDIS_PORT'),
          password: config.get<string>('REDIS_PASSWORD'),
          connectTimeout: 3 * 60 * 1000,
          tls: process.env.WORK_ENV !== 'dev' ? {
            rejectUnauthorized: false,
          } : undefined,
          maxRetriesPerRequest: 5,
          enableReadyCheck: true,
          reconnectOnError: (err: Error) => {
            const targetErrors = ['READONLY', 'ETIMEDOUT', 'Connection timeout'];
            console.error('Redis connection error:', err);
            
            if (err.message.includes('ECONNREFUSED')) {
              return true;
            }

            return targetErrors.some(targetError => err.message.includes(targetError));
          },
        },
        defaultJobOptions: {
          attempts: 5,
          backoff: {
            type: 'exponential',
            delay: 4000,
          },
        },
      }),
    }),
    BullBoardModule.forRoot({
      route: '/queues',
      adapter: ExpressAdapter,
      middleware: (req, res, next) => {
        if (process.env.WORK_ENV === 'production') {
          return res.status(404).send('Not found');
        }
        next();
      }
    }),
    AdvisorsModule,
    InterviewsModule,
    InterviewTemplatesModule,
    AuditsModule,
    AuthModule,
    OrganisationsModule,
    RbacModule,
    ClientsModule,
    TransitionsModule,
    DocusignModule,
    MutexModule,
    NotificationsModule,
    MailTemplatesModule,
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).forRoutes('*');
  }
}