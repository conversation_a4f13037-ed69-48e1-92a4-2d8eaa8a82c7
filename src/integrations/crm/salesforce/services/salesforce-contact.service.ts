import { Injectable } from '@nestjs/common';
import { Connection, Record as SalesforceRecord } from 'jsforce';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { SalesforceContactDto } from 'src/integrations/crm/salesforce/dto/salesforce.dto';
import { SALESFORCE_CONTACT_FIELDS } from '../utils/salesforce-contact-fields.constants';
import { generateRequestConfigs } from '../utils/generate-request-configs.util';
import { salesforceQuery, formatName } from '../utils/salesforce-query.util';
import { OperationType } from 'src/super-http/enums';
import { SalesforceEntityTypes } from '../types/enums';
import { commonConfigs } from '../types/metadata.types';

@Injectable()
export class SalesforceContactService {
    private connection: Connection;
    private baseUrl: string;
    private commonConfigs: commonConfigs;

    constructor(
      private readonly httpService: SuperHttpService,
      private tenantId: string
    ) {}

    // Standard Salesforce Contact fields
    private readonly STANDARD_FIELDS = SALESFORCE_CONTACT_FIELDS;

    setConnection(connection: Connection) {
      this.connection = connection;
      this.baseUrl = `${connection.instanceUrl}/services/data/v${connection.version}`;
      this.prepareRequestConfigs();
    }

    prepareRequestConfigs() {
      const requestConfigs = generateRequestConfigs(this.connection.accessToken);
      const { withInterceptors, ...configsWithoutInterceptors } = requestConfigs;
      this.commonConfigs = configsWithoutInterceptors;
    }

    async createContact(contactData: SalesforceContactDto, externalId: string): Promise<SalesforceRecord> {
      return await this.httpService.patch(
        `${this.baseUrl}/sobjects/Contact/External_Id__c/${externalId}`,
        this.tenantId,
        {
          ...contactData,
        },
        {
          rateLimiter: 'salesforce',
          integrationName: 'salesforce',
          operation: OperationType.PATCH,
          entityType: SalesforceEntityTypes.CONTACT,
        },
        this.commonConfigs
      );
    }

    async updateContact(id: string, contactData: Partial<SalesforceContactDto>): Promise<void> {
      return await this.httpService.patch(
        `${this.baseUrl}/sobjects/Contact/${id}`,
        this.tenantId,
        {
          ...contactData,
        },
        {
          rateLimiter: 'salesforce',
          integrationName: 'salesforce',
          operation: OperationType.PATCH,
          entityType: SalesforceEntityTypes.CONTACT,
        },
        this.commonConfigs
      );
    }

    async getContact(id: string): Promise<SalesforceRecord> {
      return await this.httpService.get(
        `${this.baseUrl}/sobjects/Contact/${id}`,
        this.tenantId,
        {
          rateLimiter: 'salesforce',
          integrationName: 'salesforce',
          operation: OperationType.GET,
          entityType: SalesforceEntityTypes.CONTACT,
          entityId: id,
        },
        this.commonConfigs
      );
    }

    private getQueryFields(): string[] {
      return [
        ...this.STANDARD_FIELDS,
      ];
    }

    async getContacts(pagination?: PaginationQueryDto): Promise<{records: SalesforceRecord[]}> {
      try {
        const queryFields = this.getQueryFields().join(', ');
        const sanitizedSearch = formatName(`%${pagination.search}%`);
        let soql = `SELECT ${queryFields} FROM Contact WHERE Name LIKE '${sanitizedSearch}'`;

        if (pagination?.limit) {
          soql += `LIMIT ${pagination.limit}`;
        }

        if (pagination?.page) {
          const offset = (pagination.page - 1) * pagination.limit;
          soql += `OFFSET ${offset}`;
        }

        return await salesforceQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, SalesforceEntityTypes.CONTACT, this.tenantId);
      } catch (error) {
        throw new Error(`Error getting contacts from Salesforce: ${error.message}`);
      }
    }
}