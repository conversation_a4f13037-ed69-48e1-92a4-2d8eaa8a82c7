# V2 Account Templates Implementation Summary

## Overview

We have successfully implemented account-specific templates for the V2 interview system. This feature allows different templates to be used for different account types (IRA, Roth IRA, Brokerage, etc.) and composes them together with the base client onboarding template during interview creation.

## Key Features Implemented

### 1. Schema Updates

#### InterviewTemplateV2 Schema
- Added `templateType` field: `'client_onboarding' | 'account' | 'transition' | 'custom'`
- Added `accountType` field: Required when `templateType === 'account'`
- Added `isDefaultForType` field: Marks default template for each type/account combination

#### Client & Account Schemas
- Added `templateId` to Account schema for account-specific template overrides
- Added `templateConfiguration` to Client schema for tracking template usage

#### InterviewV2 Schema
- Added `compositionInfo` field to track which templates were used:
  ```typescript
  compositionInfo: {
    baseTemplateId: string;
    accountTemplates: Array<{
      accountId: string;
      templateId: string;
      accountType: string;
    }>;
    composedAt: Date;
  }
  ```

### 2. Template Composition Service

Created `InterviewComposerService` that:
- Combines base client onboarding template with account-specific templates
- Generates unique page IDs for account pages
- Updates page flows to maintain proper navigation
- Handles page reference resolution
- Supports template-per-account override

### 3. Enhanced Interview Creation Flow

The `InterviewCoreService` now:
- Automatically detects client accounts
- Composes templates based on account types
- Creates page instances from the composed template
- Maintains proper navigation flow between base and account pages

### 4. API Endpoints

#### GET `/v2/organisations/:orgId/interview-templates`
- Filter by `templateType`, `accountType`, `status`
- Pagination support

#### GET `/v2/organisations/:orgId/interview-templates/defaults`
- Get default template by type
- Optional account type filter

#### GET `/v2/organisations/:orgId/interview-templates/account-templates`
- Get all account-specific templates for an organization

#### PUT `/v2/organisations/:orgId/interview-templates/:templateId/set-default`
- Set template as default for its type/account combination

### 5. Template Seeds

Created default templates for:
- **Client Onboarding**: Standard 9-page flow with branching
- **IRA/Roth IRA**: Primary and contingent beneficiary pages
- **Brokerage**: Investment objectives and risk tolerance
- **Joint Brokerage**: Joint account agreement

## How It Works

1. **Template Creation**: Organizations can create templates of different types
2. **Default Templates**: Each account type can have a default template
3. **Interview Creation**: When creating an interview:
   - System loads base client onboarding template
   - Detects client's accounts
   - Loads appropriate account templates
   - Composes them into a single flow
   - Account pages are inserted after base pages
   - Page IDs are regenerated to ensure uniqueness
   - Navigation flows are updated to connect pages properly

## Example Composed Interview Flow

For a client with an IRA and a Brokerage account:

1. Personal Information (base)
2. Date of Birth (base)
3. SSN (base)
4. Address (base)
5. Phone (base)
6. Employment (base)
7. Job Details (base - skipped if retired)
8. Citizenship (base)
9. IRA - Primary Beneficiaries (account)
10. IRA - Contingent Beneficiaries (account)
11. Brokerage - Investment Objectives (account)
12. Brokerage - Risk Tolerance (account)

## Benefits

1. **Flexibility**: Different account types can have completely different questions
2. **Reusability**: Templates can be shared across organizations
3. **Maintainability**: Changes to account templates don't affect base flow
4. **Scalability**: Easy to add new account types
5. **Customization**: Accounts can override default templates

## Next Steps

1. **UI Implementation**: Create template management interface
2. **Template Builder**: Visual template creation tool
3. **Analytics**: Track which templates perform best
4. **A/B Testing**: Test different template variations
5. **Conditional Account Pages**: Skip account pages based on criteria

## Technical Considerations

1. **Performance**: Templates are composed once during interview creation
2. **Storage**: Composed pages stored in page instances, not duplicated
3. **Versioning**: Published templates are immutable
4. **Migration**: Existing interviews continue using their original templates

## API Usage Examples

### Get Account Templates
```bash
curl -X GET "http://localhost:3000/v2/organisations/:orgId/interview-templates/account-templates" \
  -H "Authorization: Bearer TOKEN"
```

### Set Default Template
```bash
curl -X PUT "http://localhost:3000/v2/organisations/:orgId/interview-templates/:templateId/set-default" \
  -H "Authorization: Bearer TOKEN"
```

### Create Interview (Automatic Composition)
```bash
curl -X POST "http://localhost:3000/v2/clients/:clientId/interviews" \
  -H "Authorization: Bearer TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "contactType": "primary",
    "templateId": "optional-override"
  }'
```

The system will automatically detect the client's accounts and compose the appropriate templates.