import { parsePhoneNumber } from 'libphonenumber-js';
import { isEmpty } from 'lodash';
import {
  getBeneficaryFirstName,
  getBeneficaryLastName,
  getDobFromBeneficiaryName,
} from 'src/integrations/crm/redtail/utils/beneficiary-name-parser';
import {
  getCompanyNameFromCompanyAssociation,
  getTickerSymbolFromCompanyAssociation,
} from 'src/integrations/crm/redtail/utils/company-association-parser';
import { AddressTypeEnum } from 'src/integrations/crm/types/generic/address.type';
import { EmailTypeEnum } from 'src/integrations/crm/types/generic/email.type';
import { PhoneTypeEnum } from 'src/integrations/crm/types/generic/phone.type';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';

export const secondaryContactGeneralMappers = {
  // General Mappers
  'secondaryContact.fullName': {
    field: ['secondaryContact.firstName', 'secondaryContact.lastName'],
  },
  'secondaryContact.firstName': {
    field: ['secondaryContact.firstName'],
  },
  'secondaryContact.lastName': {
    field: ['secondaryContact.lastName'],
  },
  'secondaryContact.middleName': {
    field: ['secondaryContact.middleName'],
  },
  'secondaryContact.suffix': {
    field: ['secondaryContact.suffix'],
  },
  'secondaryContact.taxId': {
    field: ['secondaryContact.taxId'],
  },
  'secondaryContact.dob': {
    field: ['secondaryContact.dob'],
  },
  'secondaryContact.citizenship': {
    field: ['secondaryContact.citizenship'],
  },
  'secondaryContact.residence': {
    staticValue: 'USA',
  },
  'secondaryContact.employer': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const workAddress =
        interviewData.secondaryContact?.additionalInfo?.addresses.find(
          (address) => address.type === AddressTypeEnum.WORK,
        );
      return workAddress?.title || '';
    },
  },
  'secondaryContact.employmentStatus': {
    field: ['secondaryContact.additionalInfo.employmentStatus'],
  },
  'secondaryContact.industryAffiliation.companyName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const industryAffiliation =
        interviewData.secondaryContact?.additionalInfo.industryAffiliation;

      if (isEmpty(industryAffiliation)) return '';

      return industryAffiliation;
    },
  },
  'secondaryContact.companyAssociation.name': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const companyAssociationValue =
        interviewData.secondaryContact?.additionalInfo.companyAssociation;

      if (isEmpty(companyAssociationValue)) return '';

      return getCompanyNameFromCompanyAssociation(companyAssociationValue);
    },
  },
  'secondaryContact.companyAssociation.tickerSymbol': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const companyAssociationValue =
        interviewData.secondaryContact.additionalInfo.companyAssociation;

      if (isEmpty(companyAssociationValue)) return '';

      return getTickerSymbolFromCompanyAssociation(companyAssociationValue);
    },
  },

  // Phone Mappers
  'secondaryContact.phones.work.number': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.secondaryContact?.additionalInfo.phones) return '';

      const phone =
        interviewData.secondaryContact.additionalInfo.phones.find(
          (phone) => phone.type === PhoneTypeEnum.WORK,
        )?.number || '';
      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'secondaryContact.phones.home.number': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.secondaryContact?.additionalInfo.phones) return '';

      const phone =
        interviewData.secondaryContact.additionalInfo.phones.find(
          (phone) => phone.type === PhoneTypeEnum.HOME,
        )?.number || '';
      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'secondaryContact.phones.mobile.number': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.secondaryContact?.additionalInfo.phones) return '';

      const phone =
        interviewData.secondaryContact.additionalInfo.phones.find(
          (phone) => phone.type === PhoneTypeEnum.MOBILE,
        )?.number || '';
      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'secondaryContact.emails.work.address': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.secondaryContact?.additionalInfo.emails) return '';

      return (
        interviewData.secondaryContact.additionalInfo.emails.find(
          (email) => email.type === EmailTypeEnum.WORK,
        ).address || ''
      );
    },
  },
  'secondaryContact.emails.home.address': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.secondaryContact?.additionalInfo.emails.find(
          (email) => email.type === EmailTypeEnum.HOME,
        )?.address || ''
      );
    },
  },

  // Address Mappers
  'secondaryContact.addresses.home.street': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.secondaryContact?.additionalInfo.addresses) return '';

      const addresses =
        interviewData.secondaryContact?.additionalInfo?.addresses;

      return (
        addresses.find((address) => address.type === AddressTypeEnum.HOME)
          ?.street || ''
      );
    },
  },
  'secondaryContact.addresses.home.city': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.secondaryContact?.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        )?.city || ''
      );
    },
  },
  'secondaryContact.addresses.home.state': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.secondaryContact?.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        )?.state || ''
      );
    },
  },
  'secondaryContact.addresses.home.zip': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.secondaryContact?.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        )?.zip || ''
      );
    },
  },
  'secondaryContact.addresses.home.country': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.secondaryContact?.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        )?.country || ''
      );
    },
  },
  'secondaryContact.addresses.work.street': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const addresses =
        interviewData.secondaryContact?.additionalInfo?.addresses;
      return (
        addresses.find((address) => address.type === AddressTypeEnum.WORK)
          ?.street || ''
      );
    },
  },
  'secondaryContact.addresses.work.city': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.secondaryContact?.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.WORK,
        )?.city || ''
      );
    },
  },
  'secondaryContact.addresses.work.state': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.secondaryContact?.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.WORK,
        )?.state || ''
      );
    },
  },
  'secondaryContact.addresses.work.zip': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.secondaryContact?.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.WORK,
        )?.zip || ''
      );
    },
  },
  'secondaryContact.addresses.work.country': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.secondaryContact?.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.WORK,
        )?.country || ''
      );
    },
  },
  'secondaryContact.addresses.mailing.street': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.secondaryContact?.additionalInfo.addresses) return '';

      const addresses =
        interviewData.secondaryContact?.additionalInfo?.addresses;

      return (
        addresses.find((address) => address.type === AddressTypeEnum.MAILING)
          ?.street || ''
      );
    },
  },
  'secondaryContact.addresses.mailing.city': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.secondaryContact?.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.MAILING,
        )?.city || ''
      );
    },
  },
  'secondaryContact.addresses.mailing.state': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return interviewData.secondaryContact?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.MAILING,
      )?.state;
    },
  },
  'secondaryContact.addresses.mailing.zip': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.MAILING,
      )?.zip,
  },
  'secondaryContact.addresses.mailing.country': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.MAILING,
      )?.country,
  },

  // Account Mappers
  'secondaryContact.account#.custodian': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const account = interviewData.secondaryContact.accounts[accountIndex];
      if (account) return 'Charles Schwab';
    },
  },
  'secondaryContact.account#.type': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => interviewData.secondaryContact?.accounts[accountIndex]?.type || '',
  },
  'secondaryContact.account#.advisoryFee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.accounts[accountIndex]?.advisoryRate ||
      '',
  },
  'secondaryContact.account#.label': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => interviewData.secondaryContact?.accounts[accountIndex]?.label || '',
  },
  'secondaryContact.account#.ownership': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.accounts[accountIndex]?.ownership || '',
  },
  'secondaryContact.account#.masterAccountNumber': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.accounts[accountIndex]
        ?.masterAccountNumber || '',
  },
  'secondaryContact.account#.beneficiaries#.name': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) =>
      interviewData.secondaryContact?.accounts[accountIndex]?.beneficiaries[
        beneficiaryIndex
      ]?.fullName || '',
  },
  'secondaryContact.account#.beneficiaries#.percentage': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) =>
      interviewData.secondaryContact?.accounts[accountIndex]?.beneficiaries[
        beneficiaryIndex
      ]?.percentage || '',
  },
  'secondaryContact.account#.beneficiaries#.type': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) =>
      interviewData.secondaryContact?.accounts[accountIndex]?.beneficiaries[
        beneficiaryIndex
      ]?.type || '',
  },
  'secondaryContact.account#.beneficiaries#.dob': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.secondaryContact.accounts[accountIndex];
      if (!account || !account.beneficiaries[beneficiaryIndex]) return '';
      return account.beneficiaries[beneficiaryIndex]?.dob;
    },
  },
  'secondaryContact.account#.beneficiaries#.firstName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.secondaryContact.accounts[accountIndex];
      if (!account || !account.beneficiaries[beneficiaryIndex]) return '';
      const name = account.beneficiaries[beneficiaryIndex]?.firstName;
      return name;
    },
  },
  'secondaryContact.account#.beneficiaries#.lastName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.secondaryContact.accounts[accountIndex];
      if (!account || !account.beneficiaries[beneficiaryIndex]) return '';
      const name = account.beneficiaries[beneficiaryIndex]?.lastName;
      return name;
    },
  },
  'secondaryContact.account#.beneficiaries#.fullName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.secondaryContact.accounts[accountIndex];
      if (!account || !account.beneficiaries[beneficiaryIndex]) return '';
      const name = account.beneficiaries[beneficiaryIndex]?.fullName;
      return name;
    },
  },
};

function getInterviewApplicant(interviewData: InterviewDataWithCrmInfo) {
  return interviewData.interview.isPrimary
    ? interviewData.primaryContact
    : interviewData.secondaryContact;
}
