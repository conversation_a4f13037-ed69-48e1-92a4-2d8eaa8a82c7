import { Logger } from '@nestjs/common';
import { CreateContactAddressesResponseDto } from 'src/integrations/crm/redtail/dto/responses/address/create-contact-address-response.dto';
import { GetContactAddressesResponseDto } from 'src/integrations/crm/redtail/dto/responses/address/get-contact-addresses-response.dto';
import { RedtailAddress } from 'src/integrations/crm/redtail/types/address.type';
import { RedtailAddressTypeEnum, RedtailCRMEndpointsEnum } from '../types/enums';
import { AddressUpdateRequest } from 'src/integrations/crm/redtail/dto/requests/update-address.dto';
import { camelize } from 'src/utils';
import { Camelize } from 'src/shared/types/monads';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { OperationType } from 'src/super-http/enums';

export class RedtailAddressService {
  private logger: Logger;

  constructor(private httpService: SuperHttpService, private authHeader: object, private tenantId: string) {
    this.logger = new Logger('RedtailCRM');
  }

  public async getContactAddresses(
    crmClientId: string,
  ): Promise<Camelize<GetContactAddressesResponseDto>> {
    const endpoint = `${process.env.REDTAIL_BASE_URL}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/addresses`;
    const addresses = await this.httpService.get<GetContactAddressesResponseDto>(
      endpoint,
      this.tenantId,
      { 
        rateLimiter: 'redtail', 
        integrationName: 'redtail', 
        operation: OperationType.LIST, 
        entityType: 'address',
        entityId: crmClientId,
        ttl: 3600,
        cacheKeysObject: { crmClientId }
      },
      { headers: { ...this.authHeader } }
    );
    return camelize(addresses);
  }

  public async deleteContactAddress(
    crmClientId: string,
    addressId: number,
  ): Promise<void> {
    const endpoint = `${process.env.REDTAIL_BASE_URL}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/addresses/${addressId}`;
    return this.httpService.delete<void>(
      endpoint,
      this.tenantId,
      { 
        rateLimiter: 'redtail', 
        integrationName: 'redtail', 
        operation: OperationType.DELETE, 
        entityType: 'address',
        entityId: `${crmClientId}_${addressId}`,
      },
      { headers: { ...this.authHeader } }
    );
  }

  public async addContactAddress(
    crmClientId: string,
    addressType: RedtailAddressTypeEnum,
    address: RedtailAddress,
  ): Promise<Camelize<CreateContactAddressesResponseDto>> {
    const endpoint = `${process.env.REDTAIL_BASE_URL}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/addresses`;
    const res = await this.httpService.post<CreateContactAddressesResponseDto>(
      endpoint,
      this.tenantId,
      address,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.CREATE, entityType: 'address', entityId: crmClientId, wideCacheInvalidation: true },
      { headers: { ... this.authHeader} }
    );
    return camelize(res);
  }

  /**
   * Updates the contact addresses in Redtail CRM.
   * @param crmClientId The ID of the client in Redtail CRM.
   * @param addressUpdates An array of AddressUpdate objects specifying the address type and address data to update.
   * @returns A Promise that resolves to a boolean indicating success.
   */
  public async updateContactAddresses(
    crmClientId: string,
    addressUpdates: AddressUpdateRequest[],
  ): Promise<boolean> {
    try {
      // Step 1: Fetch existing addresses
      const { addresses: existingAddresses } = await this.getContactAddresses(
        crmClientId,
      );

      // Step 2: Delete existing addresses of specified types
      for (const existingAddress of existingAddresses) {
        for (const update of addressUpdates) {
          if (existingAddress.addressType === update.addressType) {
            await this.deleteContactAddress(crmClientId, existingAddress.id);
            break;
          }
        }
      }

      // Step 3: Insert new addresses
      for (const update of addressUpdates) {
        if (update.address) {
          await this.addContactAddress(crmClientId, update.addressType, {
            address_type: update.addressType,
            is_primary: update.address.isPrimary,
            street_address: update.address.streetAddress,
            city: update.address.city,
            state: update.address.state,
            zip: update.address.zip,
            address_type_description: update.address.addressTypeDescription,
            country: update.address.country,
            is_preferred: update.address.isPreferred,
            is_shared: update.address.isShared,
            secondary_address: update.address.secondaryAddress,
            addressable_id: update.address.addressableId,
            addressable_type: update.address.addressableType,
            custom_type_title: update.address.customTypeTitle,
            description: update.address.description,
          });
        }
      }
      return true;
    } catch (error) {
      this.logger.error('Failed to update contact addresses:', error);
      return false;
    }
  }
}
