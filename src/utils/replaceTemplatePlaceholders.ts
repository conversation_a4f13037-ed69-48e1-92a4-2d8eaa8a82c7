export function replaceTemplatePlaceholders(
  template: string,
  data: object
): string {
  return template.replace(
    /\[(.*?)\]/g,
    (_, key) => {
      const updatedKey = updateNaming(key.trim());
      let value = data[updatedKey] || "";
      value = value.replace(/[\r\n]+/g, '\n');
      return updatedKey.toLowerCase().includes('link') ? `${value} ` : value;
    }
  );
}


function updateNaming(param: string) {
  if (param === "organizationName") {
    return "organisationName";
  }
  if (param === "organizationPhone") {
    return "organisationPhone";
  }
  return param;
}
