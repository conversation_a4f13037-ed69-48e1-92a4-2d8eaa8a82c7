import { v4 as uuidv4 } from 'uuid';
import { TemplateTypeEnum } from '../types/template-type.enum';

export const DEFAULT_ACCOUNT_TEMPLATES = [
  {
    templateName: 'Standard IRA Template',
    templateType: TemplateTypeEnum.ACCOUNT,
    accountType: 'ira',
    description: 'Standard template for Traditional IRA accounts',
    isDefaultForType: true,
    status: 'published',
    version: 1,
    pages: [
      {
        pageId: uuidv4(),
        pageName: 'primary_beneficiaries',
        pageTitle: 'Primary Beneficiaries',
        pageType: 'primary-beneficiaries',
        isActive: true,
        isRequired: true,
        defaultOrder: 1,
        isTerminal: false,
        flow: {
          rules: [],
          defaultNext: {
            pageId: '', // Will be resolved by composer
            label: 'Continue to Contingent Beneficiaries'
          },
          allowBack: true
        },
        questions: [],
        validation: {
          rules: []
        }
      },
      {
        pageId: uuidv4(),
        pageName: 'contingent_beneficiaries',
        pageTitle: 'Contingent Beneficiaries',
        pageType: 'contingent-beneficiaries',
        isActive: true,
        isRequired: false,
        defaultOrder: 2,
        isTerminal: false,
        flow: {
          rules: [],
          defaultNext: {
            pageId: '', // Will be resolved by composer
            label: 'Continue'
          },
          allowBack: true
        },
        questions: [],
        validation: {
          rules: []
        }
      }
    ]
  },
  {
    templateName: 'Standard Roth IRA Template',
    templateType: TemplateTypeEnum.ACCOUNT,
    accountType: 'roth',
    description: 'Standard template for Roth IRA accounts',
    isDefaultForType: true,
    status: 'published',
    version: 1,
    pages: [
      {
        pageId: uuidv4(),
        pageName: 'primary_beneficiaries',
        pageTitle: 'Primary Beneficiaries',
        pageType: 'primary-beneficiaries',
        isActive: true,
        isRequired: true,
        defaultOrder: 1,
        isTerminal: false,
        flow: {
          rules: [],
          defaultNext: {
            pageId: '',
            label: 'Continue to Contingent Beneficiaries'
          },
          allowBack: true
        },
        questions: [],
        validation: {
          rules: []
        }
      },
      {
        pageId: uuidv4(),
        pageName: 'contingent_beneficiaries',
        pageTitle: 'Contingent Beneficiaries',
        pageType: 'contingent-beneficiaries',
        isActive: true,
        isRequired: false,
        defaultOrder: 2,
        isTerminal: false,
        flow: {
          rules: [],
          defaultNext: {
            pageId: '',
            label: 'Continue'
          },
          allowBack: true
        },
        questions: [],
        validation: {
          rules: []
        }
      }
    ]
  },
  {
    templateName: 'Standard Brokerage Template',
    templateType: TemplateTypeEnum.ACCOUNT,
    accountType: 'brokerage',
    description: 'Standard template for individual brokerage accounts',
    isDefaultForType: true,
    status: 'published',
    version: 1,
    pages: [
      {
        pageId: uuidv4(),
        pageName: 'investment_objectives',
        pageTitle: 'Investment Objectives',
        pageType: 'custom-questions',
        isActive: true,
        isRequired: true,
        defaultOrder: 1,
        isTerminal: false,
        flow: {
          rules: [],
          defaultNext: {
            pageId: '',
            label: 'Continue'
          },
          allowBack: true
        },
        questions: [
          {
            questionId: uuidv4(),
            questionText: 'What is your primary investment objective?',
            questionType: 'select',
            required: true,
            options: [
              { value: 'growth', label: 'Capital Growth' },
              { value: 'income', label: 'Income Generation' },
              { value: 'preservation', label: 'Capital Preservation' },
              { value: 'speculation', label: 'Speculation' }
            ]
          },
          {
            questionId: uuidv4(),
            questionText: 'What is your investment time horizon?',
            questionType: 'select',
            required: true,
            options: [
              { value: 'short', label: 'Less than 3 years' },
              { value: 'medium', label: '3-10 years' },
              { value: 'long', label: 'More than 10 years' }
            ]
          }
        ],
        validation: {
          rules: []
        }
      },
      {
        pageId: uuidv4(),
        pageName: 'risk_tolerance',
        pageTitle: 'Risk Tolerance',
        pageType: 'custom-questions',
        isActive: true,
        isRequired: true,
        defaultOrder: 2,
        isTerminal: false,
        flow: {
          rules: [],
          defaultNext: {
            pageId: '',
            label: 'Continue'
          },
          allowBack: true
        },
        questions: [
          {
            questionId: uuidv4(),
            questionText: 'How would you describe your risk tolerance?',
            questionType: 'select',
            required: true,
            options: [
              { value: 'conservative', label: 'Conservative - I prefer stable investments with minimal risk' },
              { value: 'moderate', label: 'Moderate - I can accept some risk for potential growth' },
              { value: 'aggressive', label: 'Aggressive - I am comfortable with significant risk for higher returns' }
            ]
          }
        ],
        validation: {
          rules: []
        }
      }
    ]
  },
  {
    templateName: 'Standard Joint Brokerage Template',
    templateType: TemplateTypeEnum.ACCOUNT,
    accountType: 'joint',
    description: 'Standard template for joint brokerage accounts',
    isDefaultForType: true,
    status: 'published',
    version: 1,
    pages: [
      {
        pageId: uuidv4(),
        pageName: 'joint_account_agreement',
        pageTitle: 'Joint Account Agreement',
        pageType: 'custom-questions',
        isActive: true,
        isRequired: true,
        defaultOrder: 1,
        isTerminal: false,
        flow: {
          rules: [],
          defaultNext: {
            pageId: '',
            label: 'Continue'
          },
          allowBack: true
        },
        questions: [
          {
            questionId: uuidv4(),
            questionText: 'Rights of Survivorship',
            questionType: 'checkbox',
            required: true,
            options: [
              { 
                value: 'ros', 
                label: 'We agree that this account shall have rights of survivorship'
              }
            ]
          }
        ],
        validation: {
          rules: []
        }
      }
    ]
  }
];

/**
 * Create a default client onboarding template
 */
export const DEFAULT_CLIENT_ONBOARDING_TEMPLATE = {
  templateName: 'Standard Client Onboarding',
  templateType: TemplateTypeEnum.CLIENT_ONBOARDING,
  description: 'Standard client onboarding template',
  isDefaultForType: true,
  status: 'published',
  version: 1,
  pages: [
    {
      pageId: uuidv4(),
      pageName: 'personal_information',
      pageTitle: 'Personal Information',
      pageType: 'name',
      isActive: true,
      isRequired: true,
      defaultOrder: 1,
      isTerminal: false,
      flow: {
        rules: [],
        defaultNext: {
          pageId: '',
          label: 'Continue'
        },
        allowBack: false
      }
    },
    {
      pageId: uuidv4(),
      pageName: 'date_of_birth',
      pageTitle: 'Date of Birth',
      pageType: 'dob',
      isActive: true,
      isRequired: true,
      defaultOrder: 2,
      isTerminal: false,
      flow: {
        rules: [],
        defaultNext: {
          pageId: '',
          label: 'Continue'
        },
        allowBack: true
      }
    },
    {
      pageId: uuidv4(),
      pageName: 'social_security',
      pageTitle: 'Social Security Number',
      pageType: 'ssn',
      isActive: true,
      isRequired: true,
      defaultOrder: 3,
      isTerminal: false,
      flow: {
        rules: [],
        defaultNext: {
          pageId: '',
          label: 'Continue'
        },
        allowBack: true
      }
    },
    {
      pageId: uuidv4(),
      pageName: 'address',
      pageTitle: 'Address',
      pageType: 'address',
      isActive: true,
      isRequired: true,
      defaultOrder: 4,
      isTerminal: false,
      flow: {
        rules: [],
        defaultNext: {
          pageId: '',
          label: 'Continue'
        },
        allowBack: true
      }
    },
    {
      pageId: uuidv4(),
      pageName: 'phone',
      pageTitle: 'Phone Number',
      pageType: 'phone',
      isActive: true,
      isRequired: true,
      defaultOrder: 5,
      isTerminal: false,
      flow: {
        rules: [],
        defaultNext: {
          pageId: '',
          label: 'Continue'
        },
        allowBack: true
      }
    },
    {
      pageId: uuidv4(),
      pageName: 'employment',
      pageTitle: 'Employment Information',
      pageType: 'employment',
      isActive: true,
      isRequired: true,
      defaultOrder: 6,
      isTerminal: false,
      flow: {
        rules: [
          {
            ruleId: uuidv4(),
            ruleName: 'Skip job details for retirees',
            priority: 1,
            when: {
              conditions: [
                {
                  field: 'employmentStatus',
                  operator: 'equals',
                  value: 'retired'
                }
              ]
            },
            goToPageId: 'citizenship', // Will be resolved to proper UUID
            isActive: true
          }
        ],
        defaultNext: {
          pageId: '',
          label: 'Continue'
        },
        allowBack: true
      }
    },
    {
      pageId: uuidv4(),
      pageName: 'job_details',
      pageTitle: 'Job Details',
      pageType: 'job',
      isActive: true,
      isRequired: true,
      defaultOrder: 7,
      isTerminal: false,
      flow: {
        rules: [],
        defaultNext: {
          pageId: '',
          label: 'Continue'
        },
        allowBack: true
      }
    },
    {
      pageId: uuidv4(),
      pageName: 'citizenship',
      pageTitle: 'Citizenship Status',
      pageType: 'us-citizen',
      isActive: true,
      isRequired: true,
      defaultOrder: 8,
      isTerminal: false,
      flow: {
        rules: [
          {
            ruleId: uuidv4(),
            ruleName: 'Non-citizen flow',
            priority: 1,
            when: {
              conditions: [
                {
                  field: 'citizenship',
                  operator: 'not_equals',
                  value: 'us_citizen'
                }
              ]
            },
            goToPageId: 'non_citizen_info',
            isActive: true
          }
        ],
        defaultNext: {
          pageId: '', // To account pages or completion
          label: 'Continue'
        },
        allowBack: true
      }
    },
    {
      pageId: uuidv4(),
      pageName: 'non_citizen_info',
      pageTitle: 'Non-Citizen Information',
      pageType: 'custom-questions',
      isActive: true,
      isRequired: false,
      defaultOrder: 9,
      isTerminal: true, // Ends here for non-citizens
      flow: {
        rules: [],
        defaultNext: {
          pageId: '',
          label: 'Complete'
        },
        allowBack: true
      },
      questions: [
        {
          questionId: uuidv4(),
          questionText: 'Visa Type',
          questionType: 'text',
          required: true
        },
        {
          questionId: uuidv4(),
          questionText: 'Country of Citizenship',
          questionType: 'text',
          required: true
        }
      ]
    }
  ]
};