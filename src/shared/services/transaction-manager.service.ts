import { InjectConnection } from '@nestjs/mongoose';
import { Connection, ClientSession } from 'mongoose';
import { Inject, Injectable } from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';

@Injectable()
export class TransactionManager {
  constructor(
    @InjectConnection() private readonly connection: Connection,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  async runTransaction<T>(
    work: (session: ClientSession) => Promise<T>,
  ): Promise<T> {
    const session = await this.connection.startSession();
    session.startTransaction({
      maxTimeMS: 300000, // 5 minutes
    });
    try {
      const result = await work(session);
      await session.commitTransaction();
      return result;
    } catch (e) {
      this.logger.error(e);
      await session.abortTransaction();
      throw e;
    } finally {
      session.endSession();
    }
  }
}
