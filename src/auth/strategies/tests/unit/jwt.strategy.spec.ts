import { UnauthorizedException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ClsService } from 'nestjs-cls';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { JwtStrategy } from 'src/auth/strategies/jwt.strategy';
import { OrganisationsService } from 'src/organisations/organisations.service';

jest.mock('passport-jwt', () => ({
  ExtractJwt: {
    fromAuthHeaderAsBearerToken: jest.fn(),
  },
  Strategy: class MockStrategy {
    _verify: any;
    constructor(name: string, options: any, verify: any) {
      this._verify = verify;
    }
  },
}));

describe('JwtStrategy', () => {
  let strategy: JwtStrategy;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JwtStrategy,
        {
          provide: ClsService,
          useValue: {
            get: jest.fn().mockReturnValue({}),
            set: jest.fn(),
          },
        },
        {
          provide: OrganisationsService,
          useValue: {
            findOne: jest.fn().mockReturnValue({
              configuration: {
                EXAMPLE: '',
              },
            }),
          },
        },
        {
          provide: AdvisorsCrudService,
          useValue: {
            findOne: jest.fn().mockResolvedValue({
              userId: 'test',
              organisation: {
                _id: '23',
              },
              toObject: jest.fn(() => {
                return { userId: 'test' };
              }),
            }),
          },
        },
      ],
    }).compile();

    strategy = module.get<JwtStrategy>(JwtStrategy);
  });

  it('should be defined', () => {
    expect(strategy).toBeDefined();
  });

  describe('validate', () => {
    it('should return user object when payload is valid', async () => {
      const result = await strategy.validate({
        sub: 'test',
        username: '<EMAIL>',
      });
      expect(result).toHaveProperty('userId', 'test');
    });

    it('should throw UnauthorizedException when payload is invalid', async () => {
      await expect(strategy.validate(undefined)).rejects.toThrow(
        UnauthorizedException,
      );
    });
  });
});
