import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { ClientSession, Model } from 'mongoose';
import { CreateCustomQuestionsTemplateDto } from './dto/v1/create-custom-questions-template.dto';
import { UpdateCustomQuestionsTemplateDto } from './dto/v1/update-custom-questions-template.dto';
import { InterviewTemplateTypeEnum } from 'src/interview-templates/types/interview-template-type.enum';
import { OrganisationsService } from 'src/organisations/organisations.service';
import {
  PaginationQueryDto,
  PaginationResponseDto,
} from 'src/shared/dto/pagination.dto';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { InterviewTemplate } from './schemas/v1/interview.template';
import { QuestionTypeEnum } from 'src/shared/types/interview-questions/question-types.enum';

@Injectable()
export class CustomQuestionsInterviewTemplatesService {
  constructor(
    @InjectModel(InterviewTemplate.name)
    private model: Model<InterviewTemplate>,
    private readonly organisationService: OrganisationsService,
  ) {}

  /**
   * Retrieves all custom question templates based on the provided organization ID and pagination query.
   * @param organisationId - The ID of the organization.
   * @param paginationQueryDto - The pagination query parameters.
   * @param session - Optional MongoDB session for transactional operations.
   * @returns A promise that resolves to a pagination response containing the custom question templates and total results.
   */
  async findAllCustomQuestionsTemplates(
    organisationId: string,
    paginationQueryDto: PaginationQueryDto,
    session?: ClientSession,
  ): Promise<PaginationResponseDto<InterviewTemplate[]>> {
    const { page, limit, search } = paginationQueryDto;

    // Calculate the amount to skip for pagination
    const skip = (page - 1) * limit;

    // Build the query with an optional search condition
    const queryConditions = {
      organisationId: new mongoose.Types.ObjectId(organisationId),
      type: InterviewTemplateTypeEnum.CUSTOM_QUESTIONS,
    };

    if (search) {
      // eslint-disable-next-line dot-notation
      queryConditions['templateName'] = { $regex: search, $options: 'i' }; // assuming search is by templateName
    }

    const query = this.model
      .find(queryConditions)
      .skip(skip)
      .limit(limit)
      .session(session);

    const result = await query.exec();
    const totalResults = await this.model.countDocuments(query.getFilter());

    return {
      result,
      totalResults,
    };
  }

  /**
   * Creates a custom questions template.
   *
   * @param organisationId - The ID of the organisation.
   * @param createCustomQuestionsTemplateDto - The DTO containing the custom questions template data.
   * @param session - Optional MongoDB session for transactional operations.
   * @returns The created custom questions template.
   */
  async createCustomQuestionsTemplate(
    organisationId: string,
    createCustomQuestionsTemplateDto: CreateCustomQuestionsTemplateDto,
    session?: ClientSession,
  ) {
    const { customQuestionIds, templateName } =
      createCustomQuestionsTemplateDto;

    const customQuestions =
      await this.organisationService.getCustomQuestionsByIds(
        organisationId,
        customQuestionIds,
      );

    

    const pages = customQuestions.map((customQuestion, index) => {
      const {
        _id,
        question,
        type,
        required,
        options,
        title,
        description,
      } = customQuestion;
    
      return {
        name: PagesEnum.CUSTOM_QUESTIONS,
        order: index + 1,
        data: {
          templateName,
          question: {
            _id,
            question,
            type,
            required,
            options,
            title,
            description,
          },
        },
      };
    });

    const created = await this.model.create(
      [
        {
          organisationId: new mongoose.Types.ObjectId(organisationId),
          templateName,
          pages,
          type: InterviewTemplateTypeEnum.CUSTOM_QUESTIONS,
        },
      ],
      { session },
    );

    return created[0];
  }

  /**
   * Updates a custom questions template.
   *
   * @param organisationId - The ID of the organisation.
   * @param templateId - The ID of the template to update.
   * @param updateCustomQuestionsTemplateDto - The DTO containing the new data for the custom questions template.
   * @param session - Optional MongoDB session for transactional operations.
   * @returns The updated custom questions template.
   */
  async updateCustomQuestionsTemplate(
    organisationId: string,
    templateId: string,
    updateCustomQuestionsTemplateDto: UpdateCustomQuestionsTemplateDto,
    session?: ClientSession,
  ) {
    const { customQuestionIds, templateName } =
      updateCustomQuestionsTemplateDto;

    const customQuestions =
      await this.organisationService.getCustomQuestionsByIds(
        organisationId,
        customQuestionIds,
      );

    const pages = customQuestions.map((customQuestion, index) => ({
      name: PagesEnum.CUSTOM_QUESTIONS,
      elements: [
        {
          elementType: 'text',
          elementLabel: 'Answer',
          required: true,
        },
      ],
      order: index + 1,
      data: {
        templateName,
        question: {
          _id: customQuestion._id,
          question: customQuestion.question,
          type: customQuestion?.type,
          required: customQuestion?.required,
          options: customQuestion?.options,
          title: customQuestion?.title,
          description: customQuestion?.description,
        },
      },
    }));

    const updatedTemplate = await this.model.findByIdAndUpdate(
      templateId,
      {
        organisationId: new mongoose.Types.ObjectId(organisationId),
        templateName,
        pages,
        type: InterviewTemplateTypeEnum.CUSTOM_QUESTIONS,
      },
      { new: true, session },
    );

    if (!updatedTemplate) {
      throw new NotFoundException(`Template with ID ${templateId} not found.`);
    }

    return updatedTemplate;
  }

  /**
   * Deletes a custom questions template.
   *
   * @param organisationId - The ID of the organisation.
   * @param templateId - The ID of the template to delete.
   * @param session - Optional MongoDB session for transactional operations.
   * @returns A promise indicating the success of the operation.
   */
  async deleteCustomQuestionsTemplate(
    organisationId: string,
    templateId: string,
    session?: ClientSession,
  ): Promise<void> {
    const result = await this.model
      .findOneAndDelete({
        _id: new mongoose.Types.ObjectId(templateId),
        organisationId: new mongoose.Types.ObjectId(organisationId),
      })
      .session(session);

    if (!result) {
      throw new NotFoundException(
        `Custom Questions Template with ID ${templateId} not found.`,
      );
    }
  }
}
