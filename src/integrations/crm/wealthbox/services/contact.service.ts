import { HttpService } from '@nestjs/axios';
import { CreateContactRequestDto } from 'src/integrations/crm/wealthbox/dto/contacts/create-contact.dto';
import {
  GetContactsRequestDto,
  GetContactsResponseDto,
} from 'src/integrations/crm/wealthbox/dto/contacts/get-contacts.dto';
import { ContactUpdateRequestDto } from 'src/integrations/crm/wealthbox/dto/contacts/update-contact.dto';
import { WealthboxContact } from 'src/integrations/crm/wealthbox/types/contact.type';
import { WealthboxPaginationDto } from 'src/integrations/crm/wealthbox/types/pagination.type';
import { HttpMethodsEnum } from 'src/shared/types/general/http-methods.enum';
import { makeRequest } from 'src/utils/makeRequest';
import { snakeify } from 'src/utils/snakeify';
import { snakeifyUrlParams } from 'src/utils/urlParams';

import { Logger } from 'winston';
import { ListCustomizableCategoriesResponseDto } from '../dto/contacts/customizable-category.dto';
import { WealthboxCustomizableCategoryType } from '../types/customizable-category-type.enum';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { OperationType } from 'src/super-http/enums';

export class WealthboxContactService {
  private readonly baseUrl: string;

  constructor(
    private httpService: SuperHttpService,
    private authHeader: object,
    private logger: Logger,
    private tenantId: string
  ) {
    this.baseUrl = process.env.WEALTHBOX_BASE_URL;
  }

  /**
   * Creates a new contact in Wealthbox CRM.
   * @param contactData - The data for the new contact.
   * @returns A promise that resolves when the contact has been created.
   */
  public async createContact(
    contactData: CreateContactRequestDto,
  ): Promise<WealthboxContact> {
    const url = `${this.baseUrl}/contacts`;
    return this.httpService.post<WealthboxContact>(
      url,
      this.tenantId,
      contactData,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.CREATE, entityType: 'contact' },
      { headers: this.authHeader }
    );
  }

  /**
   * Updates an existing contact in Wealthbox CRM.
   * @param contactId - The ID of the contact to update.
   * @param contactData - The data to update the contact with.
   * @returns A promise that resolves when the contact has been updated.
   */
  public async updateContact(
    contactId: string,
    contactData: ContactUpdateRequestDto,
  ): Promise<WealthboxContact> {
    const url = `${this.baseUrl}/contacts/${contactId}`;
    return this.httpService.put<WealthboxContact>(
      url,
      this.tenantId,
      snakeify(contactData),
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.UPDATE, entityType: 'contact', entityId: contactId },
      { headers: this.authHeader }
    );
  }

  /**
   * Retrieves a list of contacts from Wealthbox CRM.
   * @param queryParams - Optional query parameters for filtering the contacts.
   * @returns A promise that resolves with a list of contacts.
   */
  public async listContacts(
    queryParams?: GetContactsRequestDto,
    pagination: WealthboxPaginationDto = { page: 1, per_page: 25 },
  ): Promise<GetContactsResponseDto> {
    const query = snakeifyUrlParams({
      ...queryParams,
      ...pagination,
    }).toString();
    const url = `${this.baseUrl}/contacts${query ? `?${query}` : ''}`;
    
    return this.httpService.get<GetContactsResponseDto>(
      url,
      this.tenantId,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.LIST, entityType: 'contact', cacheKeysObject: { queryParams, pagination } },
      { headers: this.authHeader }
    );
  }

  /**
   * Retrieves a specific contact from Wealthbox CRM.
   * @param contactId - The ID of the contact to retrieve.
   * @returns A promise that resolves with the retrieved contact.
   */
  public async getContact(contactId: string): Promise<WealthboxContact> {
    const url = `${this.baseUrl}/contacts/${contactId}`;
    return this.httpService.get<WealthboxContact>(
      url,
      this.tenantId,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.GET, entityType: 'contact', entityId: contactId },
      { headers: this.authHeader }
    );
  }

  /**
   * Deletes a specific contact in Wealthbox CRM.
   * @param contactId - The ID of the contact to be deleted.
   * @returns A promise that resolves when the contact has been deleted.
   */
  public async deleteContact(contactId: string): Promise<void> {
    const url = `${this.baseUrl}/contacts/${contactId}`;
    return this.httpService.delete<void>(
      url,
      this.tenantId,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.DELETE, entityType: 'contact', entityId: contactId },
      { headers: this.authHeader }
    );
  }

  /**
   * Lists customizable categories of a specified type from Wealthbox CRM.
   * @param type - The type of category to fetch.
   * @returns A promise that resolves with the list of customizable categories.
   */
  public async listCustomizableCategories(
    type: WealthboxCustomizableCategoryType,
  ): Promise<ListCustomizableCategoriesResponseDto> {
    const url = `${this.baseUrl}/categories/${type}`;
    return this.httpService.get<ListCustomizableCategoriesResponseDto>(
      url,
      this.tenantId,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.LIST, entityType: 'contact' },
      { headers: this.authHeader }
    );
  }
}
