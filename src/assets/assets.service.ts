import { Injectable, NotFoundException } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import { FileUploadResponseDto } from 'src/assets/dto/file-upload-response.dto';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class AssetsService {
  private s3;

  constructor() {
    this.s3 = new AWS.S3({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION,
    });
  }

  /**
   * Uploads a public file to an S3 bucket.
   * @param dataBuffer - The buffer containing the file data.
   * @param filename - The name of the file.
   * @param mimetype - The MIME type of the file.
   * @param prefix - The prefix to use for the S3 object key.
   * @returns A Promise that resolves to the URL of the uploaded file.
   */
  async uploadPublicFile(
    dataBuffer: Buffer,
    filename: string,
    mimetype: string,
    prefix: string,
  ): Promise<FileUploadResponseDto> {
    const path = `${prefix}/${uuidv4()}-${filename}`;
    const uploadResult = await this.s3
      .upload({
        Bucket: process.env.ORG_DOCS_BUCKET_NAME,
        Body: dataBuffer,
        Key: path,
        ACL: 'public-read',
        ContentType: mimetype,
      })
      .promise();

    return {
      key: path,
      location: uploadResult.Location,
    };
  }

  /**
   * Uploads a private file to an S3 bucket.
   * @param dataBuffer - The buffer containing the file data.
   * @param filename - The name of the file.
   * @param mimetype - The MIME type of the file.
   * @param prefix - The prefix to use for the S3 object key.
   * @returns A Promise that resolves to the URL of the uploaded file.
   */
  async uploadPrivateFile(
    dataBuffer: Buffer,
    filename: string,
    mimetype: string,
    prefix: string,
  ): Promise<FileUploadResponseDto> {
    const path = `${prefix}/${uuidv4()}-${filename}`;

    const uploadResult = await this.s3
      .upload({
        Bucket: process.env.ORG_DOCS_BUCKET_NAME,
        Body: dataBuffer,
        Key: path,
        ACL: 'private',
        ContentType: mimetype,
      })
      .promise();

    return {
      location: uploadResult.Location,
      key: path,
    };
  }

  /**
   * Generates a presigned URL for the specified key in the AWS S3 bucket.
   * @param key - The key of the object in the S3 bucket.
   * @returns The presigned URL for the specified object.
   */
  async generatePresignedUrl(key: string): Promise<string> {
    const params = {
      Bucket: process.env.ORG_DOCS_BUCKET_NAME,
      Key: key,
      Expires: 60 * 15,
    };

    return this.s3.getSignedUrl('getObject', params);
  }

  async deleteFile(path: string): Promise<void> {
    await this.s3
      .deleteObject({
        Bucket: process.env.ORG_DOCS_BUCKET_NAME,
        Key: path,
      })
      .promise();
  }
}
