import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { Factory } from 'nestjs-seeder';
import { Client } from 'src/clients/schemas/clients.schema';
import {
  InterviewTemplate,
  InterviewTemplateSchema,
} from 'src/interview-templates/schemas/v1/interview.template';
import { AccountDocument } from 'src/shared/types/accounts/account-documents.type';
import { ObjectId, SchemaObjectID } from 'src/shared/types/mongoose';

@Schema()
export class Interview extends Document {
  @Factory((_, { clientId }) => clientId)
  @Prop({ type: SchemaObjectID, ref: 'Client' })
  client: ObjectId | Client;

  @Prop()
  envelopeId: string;

  @Prop()
  docusignSelected: boolean;

  @Prop({ type: [Object] })
  documents: AccountDocument[];

  @Factory((_, { template }) => template)
  @Prop({ type: InterviewTemplateSchema })
  template: InterviewTemplate;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;

  @Prop({ type: Boolean, default: false })
  isPrimary: boolean;

  @Prop({ type: Boolean, default: false })
  isComplete: boolean;
}

export const InterviewSchema = SchemaFactory.createForClass(Interview);

// Add index for improving query performance
InterviewSchema.index({ client: 1 });
