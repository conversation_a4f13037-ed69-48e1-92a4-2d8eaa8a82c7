import { Logger } from '@nestjs/common';

import { HttpService } from '@nestjs/axios';
import {
  CreateAccountUdfRequest,
  CreateAccountUdfRequestDto,
} from 'src/integrations/crm/redtail/dto/requests/udfs/create-account-udf-request.dto';
import { CreateUdfRequest } from 'src/integrations/crm/redtail/dto/requests/udfs/create-contact-udf-request.dto';
import { CreateUdfDefinitionRequestDto } from 'src/integrations/crm/redtail/dto/requests/udfs/create-udf-definition-request.dto';
import {
  UpdateAccountUdfRequest,
  UpdateAccountUdfRequestDto,
} from 'src/integrations/crm/redtail/dto/requests/udfs/update-account-udf-request.dto';
import { UpdateContactUdfRequest } from 'src/integrations/crm/redtail/dto/requests/udfs/update-contact-udf-request.dto';
import {
  CreateAccountUdfResponse,
  CreateAccountUdfResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/udfs/create-account-udf-response.dto';
import {
  CreateUdfDefinitionResponse,
  CreateUdfDefinitionResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/udfs/create-udf-definition-response.dto';
import {
  GetContactUdfsResponse,
  GetContactUdfsResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/udfs/get-contact-udf-response.dto';
import {
  GetUdfDefinitionsResponse,
  GetUdfDefinitionsResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/udfs/get-udf-definitions-response.dto';
import {
  UpdateAccountUdfResponse,
  UpdateAccountUdfResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/udfs/update-account-udf-response.dto';
import { RedtailContactUdfDefinition } from 'src/integrations/crm/redtail/types/contact-udf-definition.type';
import { HttpMethodsEnum } from 'src/shared/types/general/http-methods.enum';
import { camelize, snakeify } from 'src/utils';
import { makeRequest } from 'src/utils/makeRequest';
import {
  RedtailCRMEndpointsEnum,
  RedtailContactUdfNameEnum,
  RedtailContactUdfTypeMap,
} from '../types/enums';
import {
  GetAccountUdfResponse,
  GetAccountUdfResponseDto,
} from 'src/integrations/crm/redtail/dto/responses/udfs/get-account-udf-response.dto';
import { Camelize } from 'src/shared/types/monads';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { OperationType } from 'src/super-http/enums';

export class RedtailUdfService {
  private logger: Logger;

  constructor(
    private httpService: SuperHttpService,
    private authHeader: object,
    private tenantId?: string
  ) {
    this.logger = new Logger('RedtailCRM');
  }

  /**
   * Initializes contact user-defined fields (UDFs) by retrieving their definitions from Redtail CRM.
   * If a UDF definition is not found, it is added to Redtail CRM and returned.
   * @returns An array of ContactUdfDefinition objects representing the UDF definitions.
   */
  public async initContactUdfs() {
    const { contactUdfs } = await this.getContactUdfDefinition();
    const redtailUdfNames = Object.values(RedtailContactUdfNameEnum);
    let udfDefinition;
    const definitions: RedtailContactUdfDefinition[] = [];
    for (const redtailUdfName of redtailUdfNames) {
      udfDefinition = contactUdfs.find((udf) => udf.name === redtailUdfName);
      if (!udfDefinition) {
        const res = await this.addContactUdfDefintion({
          field_type: RedtailContactUdfTypeMap[redtailUdfName],
          name: redtailUdfName,
        });
        if (!res?.contactUdf) {
          this.logger.error(
            `Failed to add UDF definition for ${redtailUdfName} to Redtail CRM.`,
          );
          continue;
        }
        udfDefinition = res?.contactUdf;
      }
      definitions.push(udfDefinition);
    }
    return definitions;
  }

  /**
   * Retrieves the user-defined fields (UDFs) for a given contact in Redtail CRM.
   * @param crmClientId The ID of the contact in Redtail CRM.
   * @returns A Promise that resolves to a GetContactUdfsResponseDto object containing the UDFs for the contact.
   */
  public async getContactUdfs(
    crmClientId: string,
  ): Promise<GetContactUdfsResponse> {
    const endpoint = `${process.env.REDTAIL_BASE_URL}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/udfs`;
    const response = await this.httpService.get<GetContactUdfsResponseDto>(
      endpoint,
      this.tenantId,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.LIST, entityType: 'contact_udf', forceFresh: true },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }

  /**
   * Adds a user-defined field (UDF) to a contact in Redtail CRM.
   * If the UDF already exists for the contact, updates the existing UDF with the new field value.
   * @param crmClientId The ID of the contact in Redtail CRM.
   * @param addUdfRequestDto The DTO containing the UDF field ID and field value to add or update.
   * @returns A DTO containing the ID of the created or updated UDF.
   */
  public async upsertContactUdf(
    crmClientId: string,
    addUdfRequestDto: CreateUdfRequest,
  ): Promise<CreateAccountUdfResponse> {
    const endpoint = `${process.env.REDTAIL_BASE_URL}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/udfs`;
    let response: CreateAccountUdfResponse;

    const udfs = await this.getContactUdfs(crmClientId);
    const existingUdf = udfs.contactUdfs.find(
      (udf) => udf.contactUdfFieldId === addUdfRequestDto.contactUdfFieldId,
    );
    if (existingUdf) {
      response = await this.updateContactUdf(crmClientId, existingUdf.id, {
        fieldValue: addUdfRequestDto.fieldValue,
      });
      return camelize(response);
    }

    response = await this.httpService.post<CreateAccountUdfResponse>(
      endpoint,
      this.tenantId,
      {
        contact_udf_field_id: addUdfRequestDto.contactUdfFieldId,
        field_value: addUdfRequestDto.fieldValue,
      },
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.CREATE, entityType: 'contact_udf', forceFresh: true },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }

  /**
   * Updates a contact's user-defined field (UDF) in Redtail CRM.
   * @param crmClientId The ID of the contact in Redtail CRM.
   * @param udfId The ID of the UDF to update.
   * @param updateUdfRequestDto The DTO containing the updated UDF data.
   * @returns A Promise that resolves to a DTO containing the updated UDF data.
   */
  public async updateContactUdf(
    crmClientId: string,
    udfId: number,
    updateUdfRequestDto: UpdateContactUdfRequest,
  ): Promise<CreateAccountUdfResponse> {
    const endpoint = `${process.env.REDTAIL_BASE_URL}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/udfs/${udfId}`;
    const response = await this.httpService.put<CreateAccountUdfResponseDto>(
      endpoint,
      this.tenantId,
      snakeify(updateUdfRequestDto),
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.UPDATE, entityType: 'contact_udf', forceFresh: true },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }

  /**
   * Deletes a user-defined field (UDF) for a contact in Redtail CRM.
   * @param crmClientId The ID of the contact in Redtail CRM.
   * @param udfDefinitionId The ID of the UDF to delete.
   * @returns A Promise that resolves with no value upon successful deletion.
   */
  public async deleteContactUdf(
    crmClientId: string,
    udfDefinitionId: number,
  ): Promise<void> {
    const { contactUdfs } = await this.getContactUdfs(crmClientId);
    const existingContactUdf = contactUdfs.find(
      (udf) => udf.contactUdfFieldId === udfDefinitionId,
    );

    if (!existingContactUdf) {
      return;
    }

    const endpoint = `${process.env.REDTAIL_BASE_URL}${RedtailCRMEndpointsEnum.Contacts}/${crmClientId}/udfs/${existingContactUdf.id}`;
    await this.httpService.delete(
      endpoint,
      this.tenantId,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.DELETE, entityType: 'contact_udf', forceFresh: true },
      { headers: { ...this.authHeader } }
    );
  }

  /**
   * Adds a new user-defined field (UDF) definition for a contact in Redtail CRM.
   * @param udfDefinition - The UDF definition to be created.
   * @returns A promise that resolves to the newly created UDF definition.
   */
  public async addContactUdfDefintion(
    udfDefinition: CreateUdfDefinitionRequestDto,
  ): Promise<CreateUdfDefinitionResponse> {
    const endpoint = `${process.env.REDTAIL_BASE_URL}/lists/contact_udfs`;
    const response = await this.httpService.post<CreateUdfDefinitionResponseDto>(
      endpoint,
      this.tenantId,
      udfDefinition,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.CREATE, entityType: 'contact_udf', forceFresh: true },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }

  /**
   * Retrieves the user-defined fields (UDFs) for a contact from Redtail CRM.
   * @returns A Promise that resolves to a GetUdfDefinitionsResponseDto object containing the UDF definitions.
   */
  public async getContactUdfDefinition(): Promise<GetUdfDefinitionsResponse> {
    const endpoint = `${process.env.REDTAIL_BASE_URL}/lists/contact_udfs`;
    const response = await this.httpService.get<GetUdfDefinitionsResponseDto>(
      endpoint,
      this.tenantId,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.LIST, entityType: 'contact_udf', forceFresh: true },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }

  /**
   * Retrieves the user-defined fields (UDFs) for accounts from Redtail CRM.
   * @returns A promise that resolves to an object containing the UDF definitions.
   */
  public async getAccountUdfDefinitions(): Promise<GetAccountUdfResponse> {
    const endpoint = `${process.env.REDTAIL_BASE_URL}/lists/account_udfs`;
    const response = await this.httpService.get<GetAccountUdfResponseDto>(
      endpoint,
      this.tenantId,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.LIST, entityType: 'account_udf', forceFresh: true },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }

  /**
   * Retrieves the user-defined fields (UDFs) for a given Redtail account.
   * @param redtailAccountId The ID of the Redtail account to retrieve UDFs for.
   * @returns A Promise that resolves to the UDFs for the specified account.
   */
  public async getAccountUdfs(
    redtailAccountId: number,
  ): Promise<Camelize<GetAccountUdfResponse>> {
    const endpoint = `${process.env.REDTAIL_BASE_URL}/accounts/${redtailAccountId}/udfs`;
    const response = await this.httpService.get<GetAccountUdfResponseDto>(
      endpoint,
      this.tenantId,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.LIST, entityType: 'account_udf', forceFresh: true },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }

  /**
   * Upserts an account UDF (User Defined Field) in Redtail CRM.
   * If the UDF already exists, it updates it. Otherwise, it creates a new one.
   * @param redtailAccountId The ID of the Redtail account to upsert the UDF for.
   * @param createAccountUdfDto The DTO (Data Transfer Object) containing the UDF data to upsert.
   * @returns A Promise that resolves to either a CreateAccountUdfResponse or an UpdateAccountUdfRequest.
   */
  async upsertAccountUdf(
    redtailAccountId: number,
    createAccountUdfDto: CreateAccountUdfRequest,
  ): Promise<CreateAccountUdfResponse | UpdateAccountUdfResponse> {
    const accUdfs = await this.getAccountUdfs(redtailAccountId);
    const existingUdf = accUdfs.accountUdfs.find(
      (udf) => udf.accountUdfFieldId === createAccountUdfDto.accountUdfFieldId,
    );

    if (existingUdf) {
      return this.updateAccountUdf(
        redtailAccountId,
        existingUdf.id,
        createAccountUdfDto,
      );
    }

    return this.addAccountUdf(redtailAccountId, createAccountUdfDto);
  }

  /**
   * Adds a user-defined field (UDF) to a Redtail account.
   * @param redtailAccountId The ID of the Redtail account to add the UDF to.
   * @param body The request body containing the UDF data to add.
   * @returns A Promise that resolves to the response data from the Redtail API.
   */
  async addAccountUdf(
    redtailAccountId: number,
    createAccountUdfDto: CreateAccountUdfRequest,
  ): Promise<CreateAccountUdfResponse> {
    const endpoint = `${process.env.REDTAIL_BASE_URL}/accounts/${redtailAccountId}/udfs`;
    const body: CreateAccountUdfRequestDto = {
      account_udf_field_id: createAccountUdfDto.accountUdfFieldId,
      field_value: createAccountUdfDto.fieldValue,
    };
    const response = await this.httpService.post<CreateAccountUdfResponseDto>(
      endpoint,
      this.tenantId,
      body,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.CREATE, entityType: 'account_udf', forceFresh: true },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }

  /**
   * Updates a user-defined field (UDF) for a Redtail account.
   * @param redtailAccountId - The ID of the Redtail account to update the UDF for.
   * @param udfId - The ID of the UDF to update.
   * @param body - The request body containing the updated UDF data.
   * @returns A Promise that resolves to an object containing the updated UDF data.
   */
  async updateAccountUdf(
    redtailAccountId: number,
    udfId: number,
    updateAccountDto: UpdateAccountUdfRequest,
  ): Promise<UpdateAccountUdfResponse> {
    const endpoint = `${process.env.REDTAIL_BASE_URL}/accounts/${redtailAccountId}/udfs/${udfId}`;
    const body: UpdateAccountUdfRequestDto = {
      field_value: updateAccountDto.fieldValue,
    };
    const response = await this.httpService.put<UpdateAccountUdfResponseDto>(
      endpoint,
      this.tenantId,
      body,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.UPDATE, entityType: 'account_udf', forceFresh: true },
      { headers: { ...this.authHeader } }
    );
    return camelize(response);
  }

  /**
   * Deletes a user-defined field (UDF) for a Redtail account.
   * @param redtailAccountId - The ID of the Redtail account.
   * @param udfId - The ID of the UDF to delete.
   * @returns A Promise that resolves with no value when the UDF is deleted.
   */
  async deleteAccountUdf(
    redtailAccountId: number,
    udfId: number,
  ): Promise<void> {
    const endpoint = `${process.env.REDTAIL_BASE_URL}/accounts/${redtailAccountId}/udfs/${udfId}`;
    await this.httpService.delete(
      endpoint,
      this.tenantId,
      { rateLimiter: 'redtail', integrationName: 'redtail', operation: OperationType.DELETE, entityType: 'account_udf', forceFresh: true },
      { headers: { ...this.authHeader } }
    );
  }
}
