import parsePhoneNumber from 'libphonenumber-js';
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TwilioService } from 'nestjs-twilio';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { CommunicationTypeEnum } from 'src/integrations/crm/types/communications/communication-type.enum';
import { isEmpty } from 'lodash';
import { ClientContact } from 'src/clients/schemas/clients.schema';
import { ClsService } from 'nestjs-cls';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { Twilio } from "twilio";

@Injectable()
export class SmsService {
  private readonly twilioClient: Twilio;

  constructor(
    private configService: ConfigService,
    private twilioService: TwilioService,
    private readonly cls: ClsService,
    @Inject(forwardRef(() => AdvisorsCrmService))
    private advisorsCrmService: AdvisorsCrmService,
  ) {
    this.twilioClient = this.twilioService.client;
  }

  /**
   * Sends a text message.
   *
   * @param to - The recipient's phone number.
   * @param body - The content of the text message.
   * @param from - The sender's phone number (optional).
   * @param contact - The client contact associated with the text message (optional).
   * @param advisorId - The ID of the advisor sending the text message (optional).
   * @returns A Promise that resolves to the Twilio message object.
   * @throws Error if SMS notifications are disabled for the organisation
   */
  async sendText(
    to: string,
    body: string,
    from?: string,
    contact?: ClientContact,
    advisorId?: string,
  ) {
    if (isEmpty(to)) {
      throw new Error('Valid recipient phone number is required');
    }

    const organisation = this.cls.get<Organisation>(ClsDataEnum.Organisation);
    if (!organisation?.smsNotificationsEnabled) {
      throw new Error('SMS notifications are disabled for this organisation');
    }

    const defaultFrom = this.configService.get<string>('TWILIO_PHONE_NUMBER');
    const twilioMessage = await this.twilioClient.messages.create({
      body,
      from: from || defaultFrom,
      to: parsePhoneNumber(to, 'US').number,
    });

    if (contact && advisorId) {
      await this.advisorsCrmService.logCommunication(
        contact.crmClientId.toString(),
        {
          communicationType: CommunicationTypeEnum.Sms,
          communicationDetails: `SMS sent to ${to}, body: ${body}`,
        },
        advisorId,
      );
    }

    return twilioMessage;
  }
}
