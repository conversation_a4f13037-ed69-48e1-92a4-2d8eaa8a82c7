import { AddressTypeEnum } from 'src/integrations/crm/types/generic/address.type';
import { WealthboxAddressKind } from 'src/integrations/crm/wealthbox/types';

export function addressKindToAddressType(
  addressKind: WealthboxAddressKind,
): AddressTypeEnum {
  switch (addressKind) {
    case WealthboxAddressKind.Home:
      return AddressTypeEnum.HOME;
    case WealthboxAddressKind.Work:
      return AddressTypeEnum.WORK;
    case WealthboxAddressKind.Other:
      return AddressTypeEnum.MAILING;
    default:
      return AddressTypeEnum.OTHER;
  }
}

export function addressTypeToAddressKind(
  addressType: AddressTypeEnum,
): WealthboxAddressKind {
  switch (addressType) {
    case AddressTypeEnum.HOME:
      return WealthboxAddressKind.Home;
    case AddressTypeEnum.WORK:
      return WealthboxAddressKind.Work;
    case AddressTypeEnum.MAILING:
      return WealthboxAddressKind.Other;
    default:
      return WealthboxAddressKind.Other;
  }
}
