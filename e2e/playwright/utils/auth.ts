import { Page } from '@playwright/test';
import { TEST_CONFIG } from '../config/environment';

export async function login(page: Page, email?: string, password?: string) {
    await page.goto(TEST_CONFIG.baseUrl);
    await page.fill('input[type="email"]', email || TEST_CONFIG.testUser.email);
    await page.fill('input[type="password"]', password || TEST_CONFIG.testUser.password);
    await page.click('input[type="submit"]');
} 