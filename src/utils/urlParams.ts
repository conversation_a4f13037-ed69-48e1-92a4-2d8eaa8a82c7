import { snakeCase } from 'lodash';
import { URLSearchParams } from 'url';

export function snakeifyUrlParams(params: Record<string, any>): string {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    const snakeKey = snakeCase(key);

    if (Array.isArray(value)) {
      // For array values, append each item individually
      value.forEach((item) => searchParams.append(snakeKey, String(item)));
    } else if (value !== undefined && value !== null) {
      // For non-array values, directly set the parameter
      searchParams.set(snakeKey, String(value));
    }
  });

  return searchParams.toString();
}
