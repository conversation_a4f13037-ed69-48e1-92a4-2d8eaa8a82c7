import {
  Body,
  Controller,
  Delete,
  Get,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  VERSION_NEUTRAL,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { ClientSession } from 'mongoose';
import { ClsService } from 'nestjs-cls';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { Transaction } from 'src/shared/decorators/transaction.decorator';
import { OrganisationGuard } from 'src/shared/guards/organisation.guard';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { MAX_FILE_SIZE } from 'src/shared/types/general/asset-types.enum';
import { fileFilter } from 'src/utils/filters/files.filter';
import { ClientsV1Service } from 'src/clients/services/v1/clients-v1.service';
import { CreateClientDto } from '../../dto/v1/create-client.dto';
import { GetClientsQueryDto } from '../../dto/v1/get-clients.dto';
import { UpdateClientDto } from '../../dto/v1/update-client.dto';
import { ClientContactV1Service } from '../../services/v1/client-contact-v1.service';
import {
  ClientBatchArchiveRequestDto,
  ClientBatchArchiveResponseDto,
} from '../../dto/v1/batch-archive-client.dto';
import {
  ClientBatchSendTextRequestDto,
  ClientBatchSendTextResponseDto,
} from '../../dto/v1/batch-send-text.dto';
import { ClientUrl } from '../../types/get-client-urls';
import { ParseObjectIdPipe } from 'src/utils/isMongoObjectId';
import { ApiTags } from '@nestjs/swagger';

@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'), OrganisationGuard)
@Controller({ path: '/organisations/:organisationId/clients', version: ['1', VERSION_NEUTRAL] })
@ApiTags('Clients V1')
export class ClientsV1Controller {
  constructor(
    private readonly clientsService: ClientsV1Service,
    private readonly clientContactService: ClientContactV1Service,
    private readonly transactionManager: TransactionManager,
    private readonly clsService: ClsService,
  ) {}

  @Post()
  @Transaction()
  create(
    @Param('organisationId') organisationId,
    @Body() createClientDto: CreateClientDto,
    session?: ClientSession,
  ) {
    return this.clientsService.create(organisationId, createClientDto, session);
  }

  @Post('batch')
  @UseInterceptors(
    AnyFilesInterceptor({
      fileFilter,
      limits: { files: 2, fileSize: MAX_FILE_SIZE },
    }),
  )
  async createBatch(
    @UploadedFiles() files: Express.Multer.File[],
    @Param('organisationId') organisationId,
    session?: ClientSession,
  ) {
    return this.clientsService.handleFileUpload(files, organisationId, session);
  }

  @Post('batch/archive')
  @Transaction()
  async batchArchive(
    @Param('organisationId') organisationId,
    @Body() body: ClientBatchArchiveRequestDto,
    session?: ClientSession,
  ): Promise<ClientBatchArchiveResponseDto> {
    return this.clientsService.batchArchiveClients(body, session);
  }

  @Post('batch/sms')
  @Transaction()
  async batchSms(
    @Param('organisationId') organisationId,
    @Body() body: ClientBatchSendTextRequestDto,
    session?: ClientSession,
  ): Promise<ClientBatchSendTextResponseDto> {
    return this.clientsService.batchSendSms(body, session);
  }

  @Get()
  findAllByAdvisor(
    @Param('organisationId') organisationId,
    @Query() query: GetClientsQueryDto,
  ) {
    return this.clientsService.findAllByLoggedInAdvisor(query, organisationId);
  }

  @Get('/contacts')
  findAllContactsByAdvisor(
    @Param('organisationId') organisationId,
    @Query() query: GetClientsQueryDto,
  ) {
    return this.clientContactService.findAllContactsByLoggedInAdvisor(
      query,
      organisationId,
    );
  }

  @Delete(':clientId/:contactType/contacts')
  deleteContact(
    @Param('organisationId') organisationId,
    @Param('clientId') clientId,
    @Param('contactType') contactType,
  ) {
    return this.clientContactService.deleteContactFromClient(
      organisationId,
      clientId,
      contactType,
    );
  }

  @Get('statuses')
  async getClientStatuses(
    @Param('organisationId') organisationId: string,
    @Query('clientIds') clientIds: string,
  ) {
    const clientIdArray = clientIds ? clientIds.split(',') : [];
    return this.clientsService.getClientStatuses(organisationId, clientIdArray);
  }

  @Get(':clientId')
  async findOne(@Param('clientId') clientId: string) {
    try {
      return await this.clientsService.findOne({ _id: clientId }, null, false);
    } catch (err) {
      if (err instanceof NotFoundException) {
        return await this.clientsService.findOneDraft({ _id: clientId });
      }
      throw err;
    }
  }

  @Patch(':clientId')
  @Transaction()
  async update(
    @Param('clientId') clientId: string,
    @Body() updateClientDto: UpdateClientDto,
  ) {
    try {
      return await this.clientsService.update(clientId, updateClientDto);
    } catch (err) {
      if (err instanceof NotFoundException) {
        return await this.clientsService.updateDraftClient(
          clientId,
          updateClientDto,
        );
      }
      throw err;
    }
  }

  @Patch(':clientId/resend-text')
  @Transaction()
  async resendText(
    @Param('clientId') clientId: string,
  ) {
    return await this.clientsService.resendText(clientId);
  }

  @Delete(':clientId')
  @Transaction()
  remove(@Param('clientId') clientId: string, session?: ClientSession) {
    return this.clientsService.remove(clientId, session);
  }

  @Get(':clientId/interview/sendEnvelope')
  sendEnvelope(@Param('clientId') clientId: string) {
    return this.clientsService.sendEnvelope(clientId);
  }

  @Get(':clientId/interview/reviewEnvelope')
  reviewEnvelope(@Param('clientId') clientId: string) {
    return this.clientsService.reviewEnvelope(clientId);
  }

  @Patch(':clientId/archive')
  @Transaction()
  archive(@Param('clientId') clientId: string, session?: ClientSession) {
    return this.clientsService.archiveClient(clientId, session);
  }

  @Patch(':clientId/unarchive')
  @Transaction()
  unarchive(@Param('clientId') clientId: string, session?: ClientSession) {
    return this.clientsService.unarchiveClient(clientId, session);
  }

  @Get(':clientId/url')
  async getClientInterviewUrl(
    @Param('organisationId', ParseObjectIdPipe) organisationId: string,
    @Param('clientId', ParseObjectIdPipe) clientId: string,
  ): Promise<ClientUrl> {
    return this.clientsService.generateClientUrl(organisationId, clientId);
  }

  @Patch(':clientId/retry')
  async retryFailedClient(
    @Param('organisationId', ParseObjectIdPipe) organisationId: string,
    @Param('clientId', ParseObjectIdPipe) clientId: string,
  ) {
    return this.clientsService.retryFailedClient(clientId);
  }
}