import { randomString } from 'https://jslib.k6.io/k6-utils/1.2.0/index.js';
import { check, sleep } from 'k6';
import http from 'k6/http';

import { VUs } from "../config/constants/k6.js";
import { BASE_URL } from "../config/constants/shared.js";
import { getDynamicSleep } from "./k6-utilities.js";

export const responseVerifier = (response) => check(response, {
  'client created successfully': (r) => r.status === 201,
  'client has ID': (r) => {
    try {
      const body = JSON.parse(r.body);
      return body._id !== undefined;
    } catch (e) {
      console.error('Failed to parse response:', r.body);
      return false;
    }
  }
})

export function generateClientData(data) {
  const firstName = `Test${randomString(5)}`;
  const lastName = `User${randomString(5)}`;
  const email = '<EMAIL>';
  console.log(data);
  return {
    primaryContact: {
      firstName,
      lastName,
      email,
      mobile: "+***********",
      accounts: [],
      skipContactInterview: false,
    },
    primaryAdvisor: { id: data.advisorId },
    secondaryAdvisor: [],
    primaryCSA: { id: data.advisorId },
    secondaryCSA: [],
    notificationMethods: ["email", 'sms'],
    sendNow: true,
    readyToSend: true,
    doClientProfiling: true,
    fixedRate: false,
    docusignSelected: true,
    addAccountsSelected: false,
    featuresSelected: false,
    customTemplates: [],
  };
}

export function waitForClientProcessing(clientId, headers, organisationId) {
  let maxAttempts = 20;
  let attempts = 0;

  const sleepTime = getDynamicSleep(5, VUs['e2e-client-interview']);

  while (attempts < maxAttempts) {
    const statusResponse = http.get(
      `${BASE_URL}/organisations/${organisationId}/clients/${clientId}`,
      { headers }
    );

    if (statusResponse.status !== 200) {
      console.log(`Failed to get client status: ${statusResponse.body}`);
      sleep(3);
      attempts++;
      continue;
    }

    try {
      const body = JSON.parse(statusResponse.body);
      console.log(`Client status: ${body.status} (attempt ${attempts + 1})`);  // Added logging

      if (['Ready', 'Sent'].includes(body.status)) {
        return true;
      }
    } catch (e) {
      console.log(`Failed to parse client status response: ${e.message}`);
    }

    sleep(sleepTime);
    maxAttempts++;
    attempts++;
  }

  return false;
}