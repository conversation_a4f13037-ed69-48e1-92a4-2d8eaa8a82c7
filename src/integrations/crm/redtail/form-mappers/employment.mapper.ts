import { HttpException } from '@nestjs/common';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { EmploymentStatusRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/employment-status-request.dto';
import { RedtailContactUdfNameEnum } from 'src/integrations/crm/redtail/types/enums';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';

export class RedtailEmploymentMapper implements FormUpdateMapper {
  constructor(private redtailService: RedtailCRM) {}

  /**
   * Maps employment status page data to Redtail CRM.
   * @param clientCrmId The client's CRM ID.
   * @param organisation The organisation object.
   * @param pageData The employment status request DTO.
   * @returns A promise that resolves with the mapped data.
   * @throws HttpException if the employment status UDF is not configured.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: EmploymentStatusRequestDto,
  ) {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    const employmentStatusUdfId =
      organisation.configuration[RedtailContactUdfNameEnum.EMPLOYMENT_STATUS];

    if (!employmentStatusUdfId) {
      throw new HttpException('Employment status UDF not configured', 500);
    }

    // Remove company and job pages if the user is retired, a student, or a homemaker
    if (
      ['retired', 'student', 'home-maker', 'homemaker'].includes(
        pageData.status.toLowerCase(),
      )
    ) {
      await this.redtailService.interviewsService.removePageFromInterview(
        interview._id.toString(),
        PagesEnum.COMPANY,
      );
      await this.redtailService.interviewsService.removePageFromInterview(
        interview._id.toString(),
        PagesEnum.JOB,
      );
    } else {
      // Add company and job pages if the user is not retired, a student, or a homemaker
      await this.redtailService.interviewsService.addPageToInterview(
        interview._id.toString(),
        PagesEnum.COMPANY,
      );
      await this.redtailService.interviewsService.addPageToInterview(
        interview._id.toString(),
        PagesEnum.JOB,
      );
    }

    await this.redtailService.redtailUdfService.upsertContactUdf(
      contact.crmClientId,
      {
        contactUdfFieldId: employmentStatusUdfId,
        fieldValue: pageData.status,
      },
    );
  }
}
