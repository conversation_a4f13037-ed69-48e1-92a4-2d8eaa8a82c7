import { Test, TestingModule } from '@nestjs/testing';
import { AuthService } from 'src/auth/auth.service';
import { PasswordAlreadySetException } from 'src/auth/exceptions/invalid-new-password.exception';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { CognitoService } from 'src/shared/services/cognito.service';
import { mockCognitoService } from 'src/shared/services/tests/mocks/cognito.service.mock';
import { RbacService } from 'src/rbac/rbac.service';
import { mockRbacService } from 'src/rbac/mocks/rbac.service.mock';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { mockAdvisorsCrudService } from 'src/advisors/tests/mocks/advisors.service.mock';
import { ClsService } from 'nestjs-cls';
import { ConfigService } from '@nestjs/config';
import { WorkEnvironmentsEnum } from 'src/shared/types/general/env';

describe.skip('AuthService', () => {
  let service: AuthService;
  let configService: ConfigService;

  beforeEach(async () => {
    Object.values(mockCognitoService).forEach((mockFn) => mockFn.mockClear());

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        { provide: CognitoService, useValue: mockCognitoService },
        { provide: RbacService, useValue: mockRbacService },
        { provide: AdvisorsCrudService, useValue: mockAdvisorsCrudService },
        {
          provide: ClsService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue(WorkEnvironmentsEnum.Staging),
          },
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    configService = module.get<ConfigService>(ConfigService);
  });

  describe('newUserPassword', () => {
    it('should throw PasswordAlreadySetException', async () => {
      const mockResponse = {
        ChallengeName: 'SOME_OTHER_CHALLENGE',
      };
      mockCognitoService.initiateAuth.mockReturnValueOnce(mockResponse);

      await expect(
        service.newUserPassword({
          email: '<EMAIL>',
          oldPassword: 'oldPassword',
          newPassword: 'newPassword',
        }),
      ).rejects.toThrowError(PasswordAlreadySetException);
    });

    it('should successfully change the password', async () => {
      const mockResponse = {
        ChallengeName: 'NEW_PASSWORD_REQUIRED',
        Session: 'sessionString',
      };
      mockCognitoService.initiateAuth.mockReturnValueOnce(mockResponse);
      mockCognitoService.respondToAuthChallenge.mockReturnValueOnce({});

      await expect(
        service.newUserPassword({
          email: '<EMAIL>',
          oldPassword: 'oldPassword',
          newPassword: 'newPassword',
        }),
      ).resolves.toBeUndefined();
    });
  });

  describe('resetPassword', () => {
    it('should reset password successfully', async () => {
      mockCognitoService.adminResetUserPassword.mockReturnValueOnce({});

      await expect(
        service.resetPassword({
          email: '<EMAIL>',
        }),
      ).resolves.toBeDefined();
    });
  });

  describe('setPassword', () => {
    it('should set password successfully', async () => {
      mockCognitoService.confirmForgotPassword.mockReturnValueOnce({});

      await expect(
        service.setPassword({
          email: '<EMAIL>',
          confirmationCode: 'code',
          password: 'password',
        }),
      ).resolves.toBeUndefined();
    });
  });

  describe('respondToMfaChallenge', () => {
    let response: any;

    beforeEach(() => {
      response = {
        cookie: jest.fn(),
      };
    });

    it('should handle MFA challenge correctly', async () => {
      const mockResponse = {
        AuthenticationResult: {
          AccessToken: 'newAccessToken',
          RefreshToken: 'newRefreshToken',
        },
      };

      mockAdvisorsCrudService.findOne.mockReturnValueOnce({
        _id: 'xyz',
        organisation: {
          organisationId: '123',
        },
      });

      mockCognitoService.respondToAuthChallenge.mockReturnValueOnce(
        mockResponse,
      );

      await service.respondToMfaChallenge(
        {
          email: '<EMAIL>',
          mfaCode: '123456',
          session: 'sessionString',
        },
        response,
      );

      expect(response.cookie).toHaveBeenCalledTimes(2);
      expect(response.cookie).toHaveBeenNthCalledWith(
        1,
        'access_token',
        'newAccessToken',
        { httpOnly: true },
      );
      expect(response.cookie).toHaveBeenNthCalledWith(
        2,
        'refresh_token',
        'newRefreshToken',
        { httpOnly: true },
      );
    });
  });
});
