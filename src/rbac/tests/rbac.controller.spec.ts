import { Test, TestingModule } from '@nestjs/testing';
import { PermissionEnum } from 'src/shared/types/rbac/permissions.enum';
import { AddPermissionDto } from '../dto/add-permission.dto';
import { CreateRoleDto } from '../dto/create-role.dto';
import { DeletePermissionDto } from '../dto/delete-permission.dto';
import { RbacController } from '../rbac.controller';
import { RbacService } from '../rbac.service';

describe('RbacController', () => {
  let controller: RbacController;
  let service: RbacService;

  const mockRbacService = {
    findAllRoles: jest.fn().mockResolvedValue([]),
    findOneRole: jest.fn().mockResolvedValue({}),
    createRole: jest.fn().mockResolvedValue({}),
    deleteRole: jest.fn().mockResolvedValue({}),
    addPermission: jest.fn().mockResolvedValue({}),
    deletePermission: jest.fn().mockResolvedValue({}),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [RbacController],
      providers: [
        {
          provide: RbacService,
          useValue: mockRbacService,
        },
      ],
    }).compile();

    controller = module.get<RbacController>(RbacController);
    service = module.get<RbacService>(RbacService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should get all roles', async () => {
    await controller.findAll();
    expect(mockRbacService.findAllRoles).toHaveBeenCalled();
  });

  it('should get one role', async () => {
    await controller.findOne('12345');
    expect(mockRbacService.findOneRole).toHaveBeenCalled();
  });

  it('should create a role', async () => {
    const createRoleDto: CreateRoleDto = {
      name: 'Admin',
      permissions: [PermissionEnum.CanCreateCompanyUsers],
    };
    await controller.create(createRoleDto);
    expect(mockRbacService.createRole).toHaveBeenCalled();
  });

  it('should delete a role', async () => {
    await controller.delete('12345');
    expect(mockRbacService.deleteRole).toHaveBeenCalled();
  });

  it('should add a permission', async () => {
    const addPermissionDto: AddPermissionDto = { permissionId: 'Read' };
    await controller.addPermission('12345', addPermissionDto);
    expect(mockRbacService.addPermission).toHaveBeenCalled();
  });

  it('should delete a permission', async () => {
    const deletePermissionDto: DeletePermissionDto = { permissionId: 'Read' };
    await controller.removePermission('12345', deletePermissionDto);
    expect(mockRbacService.deletePermission).toHaveBeenCalled();
  });
});
