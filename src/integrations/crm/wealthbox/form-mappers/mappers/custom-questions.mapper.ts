import { isEmpty } from 'lodash';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';
import { CustomQuestionPayloadDto } from 'src/integrations/crm/redtail/form-mappers/dto/custom-question-request.dto';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';

export class WealthboxCustomQuestionsMapper implements FormUpdateMapper {
  constructor(private WealthboxCrm: WealthboxCRM) {}

  /**
   * Updates the contact company and address in Wealthbox CRM.
   * @param userId The ID of the user.
   * @param WealthboxContactId The ID of the contact in Wealthbox CRM.
   * @param companyName The name of the company.
   * @param address The address to update.
   * @returns A Promise that resolves to a boolean indicating success.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    payload: CustomQuestionPayloadDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const crmContact = await this.WealthboxCrm.contactService.getContact(
      contact.crmClientId,
    );

    const existingInfo = crmContact.important_information;

    const newContent = this.updateImportantInfoContent(
      existingInfo,
      payload.question.question,
      payload.answer,
    );

    await this.WealthboxCrm.contactService.updateContact(contact.crmClientId, {
      important_information: newContent,
    });
  }

  private updateImportantInfoContent(
    existingContent: string,
    newQuestion: string,
    newAnswer: string,
  ): string {
    const questionAnswerPattern = `Question: ${newQuestion} - Answer: `;
    const newQuestionAnswerPair = `${questionAnswerPattern}${newAnswer}<br>`;

    if (isEmpty(existingContent)) {
      return newQuestionAnswerPair;
    }

    const lines = existingContent.split('<br>');

    let updated = false;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].startsWith(questionAnswerPattern)) {
        lines[i] = newQuestionAnswerPair.trim(); // Replace existing line
        updated = true;
        break;
      }
    }

    if (!updated) {
      // If not updated, append new question-answer pair
      if (!existingContent.endsWith('<br>') && existingContent.length > 0) {
        existingContent += '<br>';
      }
      existingContent += newQuestionAnswerPair;
    } else {
      existingContent = lines.join('<br>');
    }

    return existingContent;
  }
}
