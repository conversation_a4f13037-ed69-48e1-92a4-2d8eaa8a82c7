import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

export class NotificationViewDto {
  @ApiProperty()
  @Expose()
  @Type(() => String)
  _id: string;

  @ApiProperty()
  @Expose()
  templateName: string;

  @ApiProperty()
  @Expose()
  templateType: string;

  @ApiProperty()
  @Expose()
  subject: string;

  @ApiProperty()
  @Expose()
  template: string;

  @ApiProperty()
  @Expose()
  default: boolean;
}
