import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import { EmailTemplateNameEnum } from 'src/templates/mail/types';
import { TextMessageTemplateNameEnum } from 'src/templates/sms/types';

@Schema({ _id: false, timestamps: true })
export class NotificationTemplate extends Document {
  @Prop({ required: true })
  templateName: EmailTemplateNameEnum | TextMessageTemplateNameEnum;

  @Prop({ required: true })
  templateType: 'email' | 'sms';

  @Prop()
  subject?: string;

  @Prop()
  template?: string;

  @Prop({ type: Boolean, default: false })
  default: boolean;
}

export const NotificationTemplateSchema =
  SchemaFactory.createForClass(NotificationTemplate);
