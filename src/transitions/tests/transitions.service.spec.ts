import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { TransitionsService } from '../transitions.service';
import { Transition } from '../schemas/transitions.schema';
import { TransitionTypeEnum } from '../types/transition-type.enum';
import { TransitionStatusEnum } from '../types/transition-status.enum';
import {
  CreateTransitionsDto,
  TransitionDto,
} from '../dto/create-transitions.dto';
import { SmsService } from 'src/notifications/sms/sms.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';

describe.skip('TransitionsService', () => {
  let service: TransitionsService;
  let transitionModel: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TransitionsService,
        {
          provide: getModelToken(Transition.name),
          useValue: {
            create: jest.fn(),
            insertMany: jest.fn(),
            find: jest.fn(),
            findOne: jest.fn(),
            findByIdAndUpdate: jest.fn(),
            findByIdAndDelete: jest.fn(),
          },
        },
        {
          provide: SmsService,
          useValue: {},
        },
        {
          provide: OrganisationsService,
          useValue: {},
        },
        {
          provide: AdvisorsCrudService,
          useValue: {},
        },
        {
          provide: 'FilesService',
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<TransitionsService>(TransitionsService);
    transitionModel = module.get(getModelToken(Transition.name));
  });

  describe('create', () => {
    it('should create a batch of transitions from an array of transitions', async () => {
      const organisationId = 'some-org-id';
      const createTransitionsDto: CreateTransitionsDto = {
        transitions: [
          {
            assetsValue: 0,
            primaryContact: {
              firstName: '',
              lastName: '',
              email: '',
              mobile: '',
              crmId: '',
            },
            primaryAdvisor: '',
            primaryCSA: '',
            transitionType: TransitionTypeEnum.Protocol,
          },
        ],
      };

      const transitionsToBeCreated = createTransitionsDto.transitions.map(
        (transition) => ({
          organisationId,
          status: TransitionStatusEnum.Draft, // assuming default status
          ...transition,
        }),
      );

      const expectedResult = transitionsToBeCreated.map(
        (transition, index) => ({
          id: `some-id-${index}`,
          ...transition,
        }),
      );

      jest
        .spyOn(transitionModel, 'insertMany')
        .mockResolvedValue(expectedResult);

      const result = await service.createBatchFromArray(
        organisationId,
        createTransitionsDto,
      );

      expect(result).toEqual(expectedResult);
      expect(transitionModel.insertMany).toHaveBeenCalledWith(
        transitionsToBeCreated,
        { session: undefined },
      );
    });
  });
});
