import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';

@Schema({ timestamps: true, collection: 'interviewaudits_v2' })
export class InterviewAuditV2 extends Document {
  @Prop({ 
    type: SchemaTypes.ObjectId, 
    ref: 'InterviewV2', 
    required: true,
    index: true 
  })
  interviewId!: string;

  @Prop({ 
    type: SchemaTypes.ObjectId, 
    ref: 'Client', 
    required: true,
    index: true 
  })
  clientId!: string;

  @Prop({ 
    type: String,
    required: true,
    enum: ['page_visited', 'page_completed', 'data_synced', 'interview_started', 'interview_completed', 'interview_abandoned', 'navigation_branched']
  })
  eventType!: string;

  @Prop({ 
    type: String,
    default: null
  })
  pageId?: string;

  @Prop({ 
    type: String,
    default: null
  })
  pageName?: string;

  @Prop({ 
    type: Object,
    default: {}
  })
  eventData?: {
    fromPage?: string;
    toPage?: string;
    branchCondition?: string;
    syncJobId?: string;
    errorMessage?: string;
    duration?: number;  // milliseconds
  };

  @Prop({ 
    type: String,
    default: null
  })
  sessionId?: string;

  @Prop({ 
    type: String,
    default: null
  })
  ipAddress?: string;

  @Prop({ 
    type: String,
    default: null
  })
  userAgent?: string;

  // NO PII or answer data stored!
}

export const InterviewAuditV2Schema = SchemaFactory.createForClass(InterviewAuditV2);

// Indexes
InterviewAuditV2Schema.index({ interviewId: 1, createdAt: -1 });
InterviewAuditV2Schema.index({ clientId: 1, eventType: 1 });
InterviewAuditV2Schema.index({ createdAt: -1 });