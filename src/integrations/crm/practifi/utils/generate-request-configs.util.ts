export const generateRequestConfigs = (accessToken: string) => ({
  headers: {
    Authorization: `Bear<PERSON> ${accessToken}`,
    'Content-Type': 'application/json',
  },
  timeout: 120000,
  withInterceptors: {
    type: 'response',
    successCallback: (response) => {
      const limitInfo = response.headers['sforce-limit-info'];
      if (limitInfo) {
        const apiUsageHeader = response.headers['sforce-limit-info'].split('=')[1].split('/');
          if (apiUsageHeader[0] === apiUsageHeader[1]) {
            throw new Error('Daily requests limit is exceeded. Please contact your admin');
        }
      }

      return response;
    },
    errorCallback: (error) => {
      return Promise.reject(error);
    }
  }
});
