# DocuSign Integration in NestJS

This documentation provides an overview of the DocuSign integration within a NestJS application, focusing on the `DocusignController` and `DocusignService` components. These components facilitate authorization with DocuSign, handle callbacks and webhooks, and manage document signing workflows.

## When is Docusign used?
When a client finishes an interview. OnBord's system will insert all necessary documents (based on account selection) into an envelope and send it for signing to the client. After going through the advisor's review.
Once the client signs, the status of the client should change in <PERSON>Bord, via webhook execution.

## DocusignController

The `DocusignController` is responsible for handling HTTP requests related to DocuSign operations.

### Endpoints

- **POST `/integrations/docusign/authorization-uri`**: Generates and returns the authorization URI for DocuSign, which is used to start the OAuth flow.

- **GET `/integrations/docusign/callback`**: Handles the OAuth callback from DocuSign. It exchanges the authorization code for an access token and stores it.

- **POST `/integrations/docusign/status/webhook`**: Receives webhook notifications from DocuSign regarding envelope status changes and processes them accordingly.

## DocusignService

The `DocusignService` encapsulates the business logic for interacting with the DocuSign API, including authentication, envelope management, and webhook processing.

### Key Methods

- **`getAuthorizationUri`**: Generates the authorization URI based on provided parameters, initiating the OAuth flow.

- **`getAccessToken`**: Exchanges an authorization code for an access token and refresh token.

- **`storeAccessToken`**: Saves the obtained access token and refresh token to the database, associating them with either an advisor or an organization.

- **`processWebhook`**: Processes incoming webhook payloads from DocuSign, updating the application state based on envelope status changes.

- **`createEnvelope`**: Prepares and sends a new envelope for document signing, including setting up recipients and documents.

- **`sendEnvelope`**: Marks an envelope as sent, triggering emails to recipients to sign the documents.

- **`reviewEnvelope`**: Generates a URL for the sender view, allowing the sender to review the envelope before sending it.

- **`addAdvisorDocuments`**: Adds documents to an existing envelope, typically used for adding advisory documents after the initial envelope creation.

- **`addClientDocuments`**: Similar to `addAdvisorDocuments`, but focused on adding client documents to the envelope.

### Authentication and Authorization

The service uses the DocuSign eSignature API client for Node.js. It handles OAuth flows, token management, and API requests. Integration with the `ConfigService` allows for dynamic configuration based on environment variables or configuration files.

### Error Handling

Both the controller and service include robust error handling, ensuring that any issues during the OAuth process, document management, or webhook processing are logged and appropriately managed.

### Webhook Processing

The webhook handling mechanism is designed to parse and process notifications from DocuSign about envelope status changes, allowing the application to update its state accordingly. This includes parsing the webhook payload, identifying the envelope and its status, and taking appropriate actions based on the application's business logic.

## Configuration

This integration requires setting up environment variables or configuration options for the DocuSign API base URL, client ID, client secret, and OAuth redirect URIs.

## Security Considerations

The service includes security measures such as encrypting stored tokens and verifying the authenticity of webhook payloads from DocuSign.

## Dependencies

- **docusign-esign**: The official DocuSign eSignature Node.js client.
- **NestJS modules**: `HttpModule` for making HTTP requests, `ConfigService` for configuration management, and custom services for application-specific business logic.

## Conclusion

The DocuSign integration in NestJS applications facilitates electronic document signing processes, streamlining workflows that require secure and verifiable signatures. By encapsulating the integration logic within dedicated controller and service components, the application maintains a clean architecture and separates concerns effectively.
