region               = "us-east-1"
app_name             = "onbord-backend"
solution_stack_name  = "64bit Amazon Linux 2 v5.8.2 running Node.js 18"
instance_type        = "t3.micro"
source_bundle_bucket = "onbord-backend-staging-bundle"
domain_name          = "api-staging.onbord.io"

app_port             = "3000"
origin               = "^https://([a-zA-Z0-9-]+.cloudfront.net|staging.onbord.io|beta.onbord.io)/?$"
work_env             = "staging"

instance_size                            = "M0"
cluster_name                             = "onbord-db-staging"
mongodb_atlas_org_id_ssm                 = "/onbord/backend/MONGODB_ATLAS_ORG_ID"
mongodb_atlas_api_pub_key_ssm            = "/onbord/backend/MONGODB_ATLAS_API_PUB_KEY"
mongodb_atlas_api_pri_key_ssm            = "/onbord/backend/MONGODB_ATLAS_API_PRI_KEY"
mongodb_atlas_database_user_username_ssm = "/onbord/backend/staging/MONGODB_ATLAS_DB_USERNAME"
mongodb_atlas_database_user_password_ssm = "/onbord/backend/staging/MONGODB_ATLAS_DB_PASSWORD"


dd_app_key_ssm       = "/onbord/backend/DD_APP_KEY"
dd_api_key_ssm       = "/onbord/backend/DD_API_KEY"

request_count_threshold      = 5000
healthy_host_count_threshold = 1
latency_threshold            = 200