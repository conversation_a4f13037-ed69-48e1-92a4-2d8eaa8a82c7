# Terraform Infrastructure Setup for NestJS Application on AWS Elastic Beanstalk

This repository contains the Terraform configurations required to set up a NestJS application on AWS Elastic Beanstalk.

## Prerequisites

Before using these configurations, ensure that you have the following installed and configured:

- [Terraform](https://www.terraform.io/downloads.html) (Version 1.4.6)

Also, ensure that you have an AWS account and have configured your AWS credentials either in `~/.aws/credentials` or by setting the `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY` environment variables.

## Directory Structure

The `environments` directory contains separate Terraform configurations for each environment (i.e., `production`, `staging`, and `ephemeral`). The `modules` directory contains reusable modules that define different parts of the infrastructure, such as the Elastic Beanstalk application and environment. Each module contains `main.tf`, `output.tf` and `variables.tf` files. The `scripts` directory contains helper shell scripts for setting up Terraform backend and environment variables.

The Terraform configurations used in this repository are:

| File | Description |
| ---- | ----------- |
| `backend.tf` | Defines the backend for Terraform, where the Terraform state is stored. |
| `main.tf` | Contains the primary set of configuration files where resources are created. |
| `variables.tf` | Contains the variables used by the configurations. |
| `output.tf` | Contains the output configuration that Terraform will return after applying the plan. |
| `[environment].auto.tfvars` | Contains the environment-specific variables that Terraform will use during configuration. |

### Terraform Resources

| Resource                                | Technical Description                                            | Non-Technical Description                                                                                                                                 |
| --------------------------------------- | ---------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------- |
| aws_acm_certificate                     | This resource creates an AWS Certificate Manager certificate for the specified domain. | It's like getting an ID card for your website, ensuring secure connections for your users.                                                              |
| aws_route53_record (cert_validation_record) | This creates DNS validation records for the certificate.        | It's like making an announcement that your website's ID is valid and can be trusted.                                                                     |
| aws_acm_certificate_validation          | This resource validates the certificate using the DNS validation records. | It's like confirming that your website's ID card is valid and properly registered.                                                                    |
| aws_iam_role (elastic_beanstalk_role)   | This creates an IAM role for the EC2 instances used by Elastic Beanstalk. | It's a set of permissions for your web application to access needed resources within AWS.                                                             |
| aws_iam_role_policy (ssm_policy)        | This attaches an IAM policy to the role, allowing it to get parameters from AWS SSM. | It's the detailed permissions that allow your application to retrieve sensitive data like database credentials safely.                                |
| aws_iam_instance_profile                 | This creates an IAM instance profile that can be associated with the EC2 instances. | It's a way to pass the permissions (role) to your web application running on AWS.                                                                      |
| aws_iam_role_policy_attachment           | This attaches a predefined AWS Elastic Beanstalk policy to the IAM role. | It gives your web application the standard permissions it needs to run and be managed on AWS.                                                         |
| aws_s3_bucket                           | This creates an Amazon S3 bucket for storing your application code. | It's your cloud storage where you keep your application's code for deployment.                                                                          |
| aws_s3_bucket_object                    | This uploads your application code to the S3 bucket.             | It's the action of putting your web application's code into your cloud storage.                                                                          |
| aws_elastic_beanstalk_application        | This creates an AWS Elastic Beanstalk application.               | It's setting up your application's home within AWS's platform for deploying and scaling web applications.                                                |
| aws_elastic_beanstalk_application_version | This creates a version of your Beanstalk application.            | It's like marking a specific version of your application for deployment.                                                                                 |
| aws_elastic_beanstalk_environment        | This creates an AWS Elastic Beanstalk environment to run your application. | It's like setting up the stage where your web application is going to perform.                                                                         |
| aws_elb (data source)                    | This fetches data about your Elastic Load Balancer.              | It's like asking for details about your website's traffic director to use that info elsewhere.                                                         |
| aws_security_group_rule (allow_https)    | This allows incoming HTTPS traffic to your load balancer.        | It's like setting up a rule that allows users to visit your website securely.                                                                             |
| aws_route53_record (record)              | This creates a DNS record to route traffic to your Beanstalk application. | It's like setting up a signpost directing users to your website.                                                                                          |
| aws_route53_zone                        | This creates a Route 53 hosted zone for your domain.              | It's like registering your website's address book on AWS.                                                                                                 |

## Scripts

- `set-environment.sh` - Determines the environment (production, staging, ephemeral) based on the current Git branch and exports the `TF_VAR_environment` environment variable.
- `provision-terraform-backend.sh` - Provisions the necessary AWS resources for Terraform to work. Takes environment name as a command-line argument.
- `destroy-terraform-backend.sh` - Destroys the Terraform backend resources. Takes environment name as a command-line argument.


## Terraform

Terraform is used to provision and manage AWS resources. Each environment (staging, production, ephemeral) has its own Terraform configuration.

## GitHub Actions

This repository uses GitHub Actions to automate the deployment of the application and infrastructure. The `production.yml` and `staging.yml` workflow are triggered on `push` events to the `main` branch.

The pipeline is essentially a series of automated tasks (CI/CD) that triggers every time changes are pushed to the main branch of your GitHub repository. It's designed to safely and reliably deploy your application to a staging environment.

At a high level, the pipeline takes care of the following:

- Setup: It sets up the environment necessary for the application to run. This includes Java, Node.js, AWS credentials, and caching dependencies to speed up the process.

- Building and Testing: The pipeline then installs all necessary dependencies, and it builds and tests the application to ensure the recent changes didn't break anything.

- Packaging: Once the application passes the tests, the pipeline bundles the application into a zip file ready for deployment.

- Infrastructure Setup: It prepares the AWS infrastructure for deployment using Terraform, a popular Infrastructure as Code (IaC) tool. The pipeline runs several Terraform commands to ensure that the infrastructure configuration is correct.

- Approval: The pipeline then waits for manual approval from one of the designated approvers before proceeding. This is an extra measure of safety to ensure that only approved changes are deployed.

- Deployment: Once approved, the pipeline applies the Terraform configurations, effectively deploying or updating the application in the staging environment on AWS.

Overall, this pipeline automates the process of taking the code from your main branch, ensuring it's tested and working correctly, and then deploying it to your staging environment. This way, you can focus on writing code, while the pipeline handles the heavy lifting of getting that code safely deployed.


# Frequently Asked Questions

**Q: What is the purpose of this infrastructure code?**

A: This infrastructure code provisions and configures an AWS environment for a web application using Infrastructure as Code (IaC) principles. It sets up necessary resources like SSL certificate, DNS records, IAM roles, S3 bucket, Elastic Beanstalk application and environment, load balancer, and more.

**Q: What technologies or services are used in this infrastructure?**

A: The infrastructure utilizes various AWS services, including AWS Certificate Manager (ACM), Amazon Route 53, IAM (Identity and Access Management), S3 (Simple Storage Service), Elastic Beanstalk, and Elastic Load Balancer (ELB). It also leverages Terraform as the IaC tool for provisioning and managing the infrastructure.

**Q: How does the SSL certificate validation work?**

A: The infrastructure uses DNS validation to verify domain ownership for the SSL certificate. DNS validation records are created in Route 53, and ACM validates the certificate by checking these records. Once validated, the certificate can be used to establish secure connections for the web application.

**Q: What is the role of IAM in this infrastructure?**

A: IAM (Identity and Access Management) is responsible for managing access and permissions. In this infrastructure, IAM is used to create an IAM role for EC2 instances used by Elastic Beanstalk, attach policies for accessing sensitive data from AWS SSM, and configure an IAM instance profile to pass the permissions to the web application.

**Q: How is the application code stored and deployed?**

A: The infrastructure provisions an S3 bucket for storing the application code. The code is uploaded to the S3 bucket using the aws_s3_bucket_object resource. The Elastic Beanstalk application version references the code in the S3 bucket and is deployed to the Elastic Beanstalk environment.

**Q: What is the purpose of the Elastic Beanstalk environment?**

A: The Elastic Beanstalk environment provides a platform for running the API. It includes necessary configurations like solution stack, environment variables, instance type, autoscaling settings, load balancer configuration, and more. The environment ensures the web application is deployed and scaled correctly.

**Q: How does the infrastructure handle HTTPS traffic?**

A: The infrastructure allows incoming HTTPS traffic to the load balancer using an aws_security_group_rule. It also configures the Elastic Beanstalk environment to listen on port 443, use HTTPS as the listener protocol, and specifies the SSL certificate to be used for secure connections.

**Q: How is the DNS routing set up for the web application?**

A: The infrastructure creates a DNS record in Route 53 using the aws_route53_record resource. It sets up an "A" record pointing to the load balancer's CNAME. This routing allows users to access the web application by using the specified domain name.

**Q: What is the purpose of the Route 53 hosted zone?**

A: The infrastructure creates a Route 53 hosted zone for the specified domain. This hosted zone is used to manage the DNS records for the domain, including the DNS record created for routing traffic to the web application.

**Q: Can I customize the configuration and settings of this infrastructure?**

A: Yes, you can modify the variables and parameters in the provided Terraform code to adapt the infrastructure to your specific requirements. Make sure to review the documentation and understand the impact of any changes before applying them.

**Q: How do I deploy this infrastructure?**

A: You should not deploy the infrastructure from your personal machine. Instead, you should use a CI/CD pipeline to deploy the infrastructure. The provided GitHub Actions workflow can be used to deploy the infrastructure to a staging environment. You can also use the workflow as a template to create a similar workflow for deploying to a production environment.

## Connecting to Private AWS VPC resources

- Setup AWS CLI so you can authenticate to Onbord AWS. Awsume is a 3rd party tool which helps with this if you are working with multiple AWS accounts.
- Add AWS CLI SSM plugin
- Optionally Download and install Redis Insight Browser from Redis (it seems to be free and a bit easier than redis-cli or nc, telnet etc)
- Run the SSM command (these examples show connections to ElasticCache Redis)
Staging:
```
aws ssm start-session \
    --target i-03356ad41ec4b0456 \
    --document-name AWS-StartPortForwardingSessionToRemoteHost \
    --parameters '{ "host":["onbord-redis-staging-001.onbord-redis-staging.qf351q.use1.cache.amazonaws.com"], "portNumber":["6379"], "localPortNumber":["6379"] }'
```
Production:
```
aws ssm start-session \
    --target i-01c103990d31d3001 \
    --document-name AWS-StartPortForwardingSessionToRemoteHost \
    --parameters '{ "host":["master.onbord-redis-production.qf351q.use1.cache.amazonaws.com"], "portNumber":["6379"], "localPortNumber":["6379"] }'
```

- Added a redis DB to Redis Insight Browser - note the host is localhost as you are using the SSM to proxy. Also if your laptop is running Redis locally you might find that Port 6379  to be in use. in which case you will need to change it to something else i.e 6380
