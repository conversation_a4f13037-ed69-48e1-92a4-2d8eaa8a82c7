import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Observable } from 'rxjs';
import { RolesEnum } from '../types/rbac/roles.enum';
import { OrganisationStatusEnum } from 'src/organisations/types/organisation-status.enum';

/**
 * Organisation Guard
 *
 * This guard is used to check if the user is trying to access a resource belonging
 * to their organisation.
 */
@Injectable()
export class OrganisationGuard implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (user.role.name === RolesEnum.SuperAdmin) return true;

    if (!request.params.organisationId) return true;

    const orgId = request.params.organisationId;

    const organisationId = user.organisation._id.toString();

    return (
      organisationId === orgId &&
      user.organisation.status === OrganisationStatusEnum.Active
    );
  }
}
