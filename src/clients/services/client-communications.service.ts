import {
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { ClsService } from 'nestjs-cls';
import { S3Attachment } from 'src/notifications/mail/mail.types';
import { MailService } from 'src/notifications/mail/mail.service';
import { SmsService } from 'src/notifications/sms/sms.service';
import { Interview } from 'src/interviews/schemas/v1/interview.schema';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Client } from '../schemas/clients.schema';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { AssetTypeEnum } from 'src/shared/types/general/asset-types.enum';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { Logger } from 'winston';
import { ClientSession } from 'mongoose';
import { ClientWelcomeEmailContext } from 'src/templates/mail/client-welcome/client-welcome.context';
import { EmailTemplateNameEnum } from 'src/templates/mail/types';
import { FailedTextMessagesEmailContext } from 'src/templates/mail/failed-text-messages/failed-text-messages.context';
import { isEmpty } from 'lodash';
import { NotificationTypeEnum } from '../types/notification-types.enum';
import { smsTemplateMapper } from 'src/templates/sms';
import { TextMessageTemplateNameEnum } from 'src/templates/sms/types';
import { getFrontendDomain } from 'src/utils/getFrontendDomain';
import { LogoTypesUrlEnum } from '../types/logo-types-url.enum';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { Asset } from 'src/shared/schemas/asset.schema';

@Injectable()
export class ClientCommunicationsService {
  constructor(
    private readonly smsService: SmsService,
    private readonly mailService: MailService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
    @Inject(forwardRef(() => OrganisationsService))
    private readonly organisationsService: OrganisationsService,
    @Inject(forwardRef(() => AdvisorsCrudService))
    private readonly advisorCrudService: AdvisorsCrudService,
  ) {}

  /**
   * Sends welcome notifications (email and/or SMS) to a client based on notification methods
   *
   * @param clientId - The ID of the client
   * @param client - The client object
   * @param primaryInterview - The primary interview associated with the client
   * @param secondaryInterview - The secondary interview associated with the client, if any
   * @param notificationMethods - Array of notification types to send
   * @returns A promise that resolves when all notifications have been sent
   */
  async sendWelcomeNotification(
    clientId: string,
    client: Client,
    primaryInterview: Interview,
    secondaryInterview: Interview | undefined,
    notificationMethods: NotificationTypeEnum[],
  ) {
    if (!primaryInterview) {
      this.logger.error(`No primary interview found for client ${clientId}. They should have been previously created`);
      throw new Error(`No primary interview found`);
    }

    const args = [client, primaryInterview, secondaryInterview] as const;

    const shouldSendEmail = notificationMethods?.includes(NotificationTypeEnum.EMAIL);
    const shouldSendSms = notificationMethods?.includes(NotificationTypeEnum.SMS);

    if (shouldSendEmail) {
      await this.sendWelcomeEmail(...args);
    }

    if (shouldSendSms) {
      await this.sendWelcomeText(...args);
    }
  }

  /**
   * Sends a welcome text message to the primary and secondary contacts of a client.
   * @param client - The client to send the welcome text to.
   * @param primaryInterview - The primary interview associated with the client.
   * @param secondaryInterview - The secondary interview associated with the client, if any.
   * @returns A Promise that resolves to the updated client object with status set to "Sent".
   */
  async sendWelcomeText(
    client: Client,
    primaryInterview: Interview | EnrichedInterview,
    secondaryInterview: Interview | EnrichedInterview | undefined = undefined,
    session?: ClientSession,
  ) {
    try {
      // Directly fetch the organization without using CLS
      const organisation = await this.organisationsService.findOne(client.organisationId.toString());
      
      // Fetch the primary advisor
      const advisor = await this.advisorCrudService.findOne({_id: client.primaryAdvisor.id});
      
      // Send for primary interview
      await this.smsService.sendText(
        client.primaryContact.mobile,
        this.generateWelcomeText(primaryInterview, organisation.name),
        undefined,
        client.primaryContact,
        advisor._id.toString()
      );
      
      // Send for secondary interview, if applicable
      if (!isEmpty(secondaryInterview) && !isEmpty(client.secondaryContact)) {
        await this.smsService.sendText(
          client?.secondaryContact?.mobile,
          this.generateWelcomeText(secondaryInterview, organisation.name),
          undefined,
          client?.secondaryContact,
          advisor._id.toString()
        );
      }

      return client;
    } catch (error) {
      await this.sendFailedTextMessageEmail(
        client,
        primaryInterview,
        secondaryInterview,
      );
    }
  }

  /**
   * Sends a welcome email to the client.
   *
   * @param client - The enriched client object.
   * @param primaryInterview - The primary interview object.
   * @param secondaryInterview - The secondary interview object (optional).
   * @param session - The client session (optional).
   * @returns A promise that resolves when all the emails have been sent.
   */
  async sendWelcomeEmail(
    client: Client,
    primaryInterview: Interview,
    secondaryInterview: Interview | undefined,
    session?: ClientSession,
  ) {
    // Directly fetch the organization without using CLS
    const organisation = await this.organisationsService.findOne(client.organisationId.toString());

    const primaryAdvisor = await this.advisorCrudService.findOne({
      _id: client.primaryAdvisor.id,
    });

    // TODO: Add ADV-2B of secondary advisor when applicable
    const primaryAdv2B = primaryAdvisor.assets.find(
      (asset) => asset.assetType === AssetTypeEnum.ADV2B,
    );

    const [ADV2A, CRS] = organisation.assets.filter(
      (asset: Asset) =>
        asset.assetType === AssetTypeEnum.ADV2A ||
        asset.assetType === AssetTypeEnum.CRS,
    );

    const attachments: S3Attachment[] = [
      ...(client.sendAdv2b !== false ? [{ key: primaryAdv2B.assetKey, name: AssetTypeEnum.ADV2B }] : []),
      { key: ADV2A.assetKey, name: AssetTypeEnum.ADV2A },
      { key: CRS?.assetKey, name: AssetTypeEnum.CRS },
    ].filter((attachment) => !!attachment.key);

    const organizationLogo = organisation?.assets?.filter(
      (asset) => asset.assetType === 'logo'
    )[0]?.assetLocation || LogoTypesUrlEnum.Onbord;

    const promises = [];

    if (primaryInterview && client.primaryContact) {
      promises.push(
        this.mailService.sendEmail(
          {
            to: client.primaryContact.email,
            templateName: EmailTemplateNameEnum.ClientWelcome,
            context: {
              firstName: client.primaryContact.firstName,
              csaFirstName: client.primaryCSA.firstName,
              csaLastName: client.primaryCSA.lastName,
              csaEmail: client.primaryCSA.email,
              interviewUrl: `https://${getFrontendDomain()}/interview/${primaryInterview._id}`,
              organizationLogo
            },
            attachments,
            organisation,
          },
          client.primaryContact,
          primaryAdvisor._id.toString(),
        )
      );
    }

    if (secondaryInterview && client.secondaryContact) {
      promises.push(
        this.mailService.sendEmail(
          {
            to: client.secondaryContact.email,
            templateName: EmailTemplateNameEnum.ClientWelcome,
            context: {
              firstName: client.secondaryContact.firstName,
              csaFirstName: client.primaryCSA.firstName,
              csaLastName: client.primaryCSA.lastName,
              csaEmail: client.primaryCSA.email,
              interviewUrl: `https://${getFrontendDomain()}/interview/${secondaryInterview._id}`,
              organizationLogo
            },
            attachments,
            organisation,
          },
          client.secondaryContact,
          primaryAdvisor._id.toString(),
        )
      );
    }

    if (promises.length === 0) {
      const errors = [];
      if (!primaryInterview || !client.primaryContact) {
        errors.push('Primary contact/interview configuration is invalid');
      }
      if (!secondaryInterview && !client.secondaryContact) {
        errors.push('Secondary contact/interview configuration is invalid');
      }
      throw new Error(`Cannot send welcome emails: ${errors.join(', ')}`);
    }

    return Promise.all(promises);
  }

  /**
   * Sends a failed text message email to the client's primary or secondary contact.
   *
   * @param client - The client object.
   * @param primaryInterview - The primary interview object.
   * @param secondaryInterview - The secondary interview object (optional).
   * @returns A promise that resolves when all the email sending operations are completed.
   */
  async sendFailedTextMessageEmail(
    client: Client,
    primaryInterview: Interview,
    secondaryInterview: Interview | undefined,
  ) {
    // Get the organization for the client
    const organisation = await this.organisationsService.findOne(client.organisationId.toString());
    
    // Fetch the primary advisor
    const advisor = await this.advisorCrudService.findOne({_id: client.primaryAdvisor.id});
    
    const promises = [primaryInterview, secondaryInterview].map(
      async (interview) => {
        if (isEmpty(interview)) {
          return;
        }
        const contact = interview.isPrimary
          ? client.primaryContact
          : client.secondaryContact;
        const to: string = contact.email;
        const templateName = EmailTemplateNameEnum.FailedTextMessage;
        const context: FailedTextMessagesEmailContext = {
          clientMobilePhone: contact.mobile,
          onbordMobilePhone: process.env.TWILIO_PHONE_NUMBER,
          failedTextBody: this.generateWelcomeText(interview, organisation.name),
        };

        return this.mailService.sendEmail(
          { to, templateName, context, organisation },
          contact,
          advisor._id.toString()
        );
      },
    );

    return Promise.all(promises);
  }

  /**
   * Batch sends SMS messages to multiple clients
   *
   * @param clients - Array of clients to send SMS to
   * @param primaryInterviews - Map of primary interviews by client ID
   * @param secondaryInterviews - Map of secondary interviews by client ID
   * @returns A promise that resolves when all messages have been sent
   */
  async batchSendSms(
    clients: Client[],
    primaryInterviews: Record<string, Interview>,
    secondaryInterviews: Record<string, Interview>,
    session?: ClientSession,
  ) {
    const results = await Promise.allSettled(
      clients.map(async (client) => {
        try {
          const primaryInterview = primaryInterviews[client._id.toString()];
          const secondaryInterview = secondaryInterviews[client._id.toString()];

          if (!primaryInterview) {
            this.logger.error(
              `No primary interview found for client ${client._id}. Should have been previously created`,
            );
            throw new Error(`No primary interview found`);
          }

          const args = [
            client,
            primaryInterview,
            secondaryInterview,
            session,
          ] as const;

          await Promise.all([
            this.sendWelcomeText(...args),
            this.sendWelcomeEmail(...args),
          ]);

          this.logger.info(`Sent text message to client ${client._id}`);
          return { clientId: client._id, communicated: true };
        } catch (error) {
          this.logger.error(
            `Failed to send text message to client ${client._id}: ${error.message}`,
            error.stack,
          );
          return { clientId: client._id, communicated: false };
        }
      })
    );

    return results.map(result =>
      result.status === 'fulfilled' ? result.value : { clientId: null, communicated: false }
    );
  }

  /**
   * Generates the welcome text for an interview.
   *
   * @param interview - The interview object.
   * @param organisationName - The name of the organization.
   * @returns The generated welcome text.
   */
  private generateWelcomeText(interview: Interview, organisationName: string) {
    const templateType = TextMessageTemplateNameEnum.WelcomeText;
    const context = {
      interviewId: interview._id,
      organisationName,
    };

    return smsTemplateMapper(templateType, context);
  }
}