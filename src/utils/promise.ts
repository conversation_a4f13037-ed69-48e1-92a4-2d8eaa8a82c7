declare global {
  interface PromiseConstructor {
    allLimitted<T>(promises: Promise<T>[], limit?: number): Promise<T[]>;
  }
}

const MAX_CONCURRENCY_LIMIT = 20;

Promise.allLimitted = function <T>(
  promises: Promise<T>[],
  limit: number = MAX_CONCURRENCY_LIMIT,
): Promise<T[]> {
  return new Promise((resolve, reject) => {
    let activePromiseCount = 0;
    let finishedPromiseCount = 0;

    const results: T[] = [];
    const totalPromises = promises.length;

    function nextTick() {
      if (!promises.length) {
        if (totalPromises === finishedPromiseCount) {
          resolve(results);
          return;
        }
      }

      activePromiseCount++;

      const promise = promises.shift();

      if (!promise) {
        return;
      }

      promise
        .then((fulfilled) => results.push(fulfilled))
        .catch(reject)
        .finally(() => {
          finishedPromiseCount++;
          activePromiseCount--;

          if (activePromiseCount < limit) {
            nextTick();
          }
        });
    }

    for (let i = 0; i < limit; i++) {
      nextTick();
    }
  });
};

export {};
