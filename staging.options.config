option_settings:
  aws:elasticbeanstalk:application:environment:
    AWS_COGNITO_CLIENT_ID: '{{resolve:ssm:/onbord/backend/AWS_COGNITO_CLIENT_ID:1}}'
    AWS_COGNITO_USER_POOL_ID: us-east-1_Angpi4Vtn
    AWS_KMS_KEY_ID: arn:aws:kms:us-east-1:094396706397:key/d2ea467f-3f00-41eb-83d1-41b8237c04d7
    AWS_REGION: us-east-1
    BASE_DOMAIN_NAME: api-staging.onbord.io
    CORS_ORIGIN: ^https://([a-zA-Z0-9-]+.cloudfront.net|staging.onbord.io|beta.onbord.io)/?$
    DATABASE_URL: mongodb+srv://staging.pwgnrsv.mongodb.net
    DB_PASSWORD : '{{resolve:ssm:/onbord/backend/staging/MONGODB_ATLAS_DB_PASSWORD:1}}'
    DB_USERNAME: onbord_backend_db_staging
    DD_API_KEY: '{{resolve:ssm:/onbord/backend/DD_API_KEY:1}}'
    DD_APP_KEY: '{{resolve:ssm:/onbord/backend/DD_APP_KEY:1}}'
    DD_LOGS_INJECTION: true
    DD_SITE: true
    DOCUSIGN_API_BASE_URL: https://demo.docusign.net/restapi
    DOCUSIGN_CALLBACK_ERROR_REDIRECT_URI: https://staging.onbord.io/error
    DOCUSIGN_CALLBACK_REDIRECT_URI: https://staging.onbord.io
    DOCUSIGN_CLIENT_ID: '{{resolve:ssm:/onbord/backend/staging/DOCUSIGN_CLIENT_ID:1}}'
    DOCUSIGN_CLIENT_SECRET: '{{resolve:ssm:/onbord/backend/staging/DOCUSIGN_CLIENT_SECRET:1}}'
    DOCUSIGN_OAUTH_ACCESS_TOKEN_URI: '{{resolve:ssm:/onbord/backend/staging/DOCUSIGN_OAUTH_ACCESS_TOKEN_URI:1}}'
    DOCUSIGN_OAUTH_REDIRECT_URI: '{{resolve:ssm:/onbord/backend/staging/DOCUSIGN_OAUTH_REDIRECT_URI:1}}'
    DOCUSIGN_STATUS_WEBHOOK_URI:  '{{resolve:ssm:/onbord/backend/staging/DOCUSIGN_STATUS_WEBHOOK_URI:1}}'
    MAIL_FROM: '{{resolve:ssm:/onbord/backend/MAIL_FROM:1}}'
    MAIL_HOST: '{{resolve:ssm:/onbord/backend/MAIL_HOST:1}}'
    MAIL_PASSWORD: '{{resolve:ssm:/onbord/backend/MAIL_PASSWORD:1}}'
    MAIL_PORT: '{{resolve:ssm:/onbord/backend/MAIL_PORT:1}}'
    MAIL_USER: '{{resolve:ssm:/onbord/backend/MAIL_USER:1}}'
    NODE_ENV: production
    ORG_DOCS_BUCKET_NAME: onbord-backend-staging-organisations-assets
    PORT: 3000
    PRACTIFI_CLIENT_ID: '{{resolve:ssm:/onbord/backend/staging/PRACTIFI_CLIENT_ID:1}}'
    PRACTIFI_CLIENT_SECRET: '{{resolve:ssm:/onbord/backend/staging/PRACTIFI_CLIENT_SECRET:1}}'
    PRACTIFI_OAUTH_REDIRECT_URI: https://api-staging.onbord.io/integrations/practifi/callback
    REDIS_HOST: '{{resolve:ssm:/onbord/backend/staging/REDIS_HOST:1}}'
    REDIS_PASSWORD: '{{resolve:ssm:/onbord/backend/staging/REDIS_PASSWORD:1}}'
    REDIS_PORT: '{{resolve:ssm:/onbord/backend/staging/REDIS_PORT:1}}'
    REDTAIL_API_KEY: '{{resolve:ssm:/onbord/backend/staging/REDTAIL_API_KEY:1}}'
    REDTAIL_BASE_URL: '{{resolve:ssm:/onbord/backend/staging/REDTAIL_API_KEY:1}}'
    REDTAIL_FRONTEND_URL: '{{resolve:ssm:/onbord/backend/staging/REDTAIL_FRONTEND_URL:1}}'
    SALESFORCE_API_URL: '{{resolve:ssm:/onbord/backend/staging/SALESFORCE_API_URL:1}}'
    SALESFORCE_AUTH_URL: '{{resolve:ssm:/onbord/backend/staging/SALESFORCE_AUTH_URL:1}}'
    SALESFORCE_CALLBACK_ERROR_REDIRECT_URI: '{{resolve:ssm:/onbord/backend/staging/SALESFORCE_CALLBACK_ERROR_REDIRECT_URI:1}}'
    SALESFORCE_CALLBACK_REDIRECT_URI: '{{resolve:ssm:/onbord/backend/staging/SALESFORCE_CALLBACK_REDIRECT_URI:1}}'
    SALESFORCE_CLIENT_ID: '{{resolve:ssm:/onbord/backend/staging/SALESFORCE_CLIENT_ID:1}}'
    SALESFORCE_CLIENT_SECRET: '{{resolve:ssm:/onbord/backend/staging/SALESFORCE_CLIENT_SECRET:1}}'
    SALESFORCE_OAUTH_REDIRECT_URI: '{{resolve:ssm:/onbord/backend/staging/SALESFORCE_OAUTH_REDIRECT_URI:1}}'
    SALESFORCE_TOKEN_URL: '{{resolve:ssm:/onbord/backend/staging/SALESFORCE_TOKEN_URL:1}}'
    TWILIO_ACCOUNT_SID: '{{resolve:ssm:/onbord/backend/staging/TWILIO_ACCOUNT_SID:1}}'
    TWILIO_AUTH_TOKEN: '{{resolve:ssm:/onbord/backend/staging/TWILIO_AUTH_TOKEN:1}}'
    TWILIO_PHONE_NUMBER: '{{resolve:ssm:/onbord/backend/staging/TWILIO_PHONE_NUMBER:1}}'
    WEALTHBOX_BASE_AUTH_URL: '{{resolve:ssm:/onbord/backend/staging/WEALTHBOX_BASE_AUTH_URL:1}}'
    WEALTHBOX_BASE_URL: '{{resolve:ssm:/onbord/backend/staging/WEALTHBOX_BASE_URL:1}}'
    WEALTHBOX_CALLBACK_ERROR_REDIRECT_URI: '{{resolve:ssm:/onbord/backend/staging/WEALTHBOX_CALLBACK_ERROR_REDIRECT_URI:1}}'
    WEALTHBOX_CALLBACK_REDIRECT_URI: '{{resolve:ssm:/onbord/backend/staging/WEALTHBOX_CALLBACK_ERROR_REDIRECT_URI:1}}'
    WEALTHBOX_CLIENT_ID: '{{resolve:ssm:/onbord/backend/staging/WEALTHBOX_CLIENT_ID:1}}'
    WEALTHBOX_CLIENT_SECRET: '{{resolve:ssm:/onbord/backend/staging/WEALTHBOX_CLIENT_SECRET:1}}'
    WEALTHBOX_FRONTEND_URL: '{{resolve:ssm:/onbord/backend/staging/WEALTHBOX_FRONTEND_URL:1}}'
    WEALTHBOX_OAUTH_REDIRECT_URI: '{{resolve:ssm:/onbord/backend/staging/WEALTHBOX_OAUTH_REDIRECT_URI:1}}'
    WORK_ENV: staging
