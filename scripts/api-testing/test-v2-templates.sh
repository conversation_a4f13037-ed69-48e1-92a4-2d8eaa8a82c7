#!/bin/bash

# Source the base API functions
source "$(dirname "$0")/api-base.sh"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}Testing V2 Interview Templates API${NC}\n"

# Authenticate first
authenticate

# Get organization ID
ORG_ID=$(get_org_id)
echo -e "${BLUE}Organization ID: ${ORG_ID}${NC}\n"

# Test 1: Get all templates (simple)
echo -e "${YELLOW}Test 1: Get all V2 templates${NC}"
RESPONSE=$(api_request "GET" "/v2/organisations/${ORG_ID}/interview-templates")
echo -e "Response: $RESPONSE\n"

# Test 2: Get all templates with pagination
echo -e "${YELLOW}Test 2: Get templates with pagination (page=1, limit=5)${NC}"
RESPONSE=$(api_request "GET" "/v2/organisations/${ORG_ID}/interview-templates?page=1&limit=5")
echo -e "Response: $RESPONSE\n"

# Test 3: Filter by template type
echo -e "${YELLOW}Test 3: Get only client onboarding templates${NC}"
RESPONSE=$(api_request "GET" "/v2/organisations/${ORG_ID}/interview-templates?templateType=client_onboarding")
echo -e "Response: $RESPONSE\n"

# Test 4: Filter by account type
echo -e "${YELLOW}Test 4: Get account templates for IRA${NC}"
RESPONSE=$(api_request "GET" "/v2/organisations/${ORG_ID}/interview-templates?templateType=account&accountType=ira")
echo -e "Response: $RESPONSE\n"

# Test 5: Get account-specific templates
echo -e "${YELLOW}Test 5: Get all account-specific templates${NC}"
RESPONSE=$(api_request "GET" "/v2/organisations/${ORG_ID}/interview-templates/account-templates")
echo -e "Response: $RESPONSE\n"

# Test 6: Get default templates
echo -e "${YELLOW}Test 6: Get default client onboarding template${NC}"
RESPONSE=$(api_request "GET" "/v2/organisations/${ORG_ID}/interview-templates/defaults?templateType=client_onboarding")
echo -e "Response: $RESPONSE\n"

# Test 7: Get default account template
echo -e "${YELLOW}Test 7: Get default IRA template${NC}"
RESPONSE=$(api_request "GET" "/v2/organisations/${ORG_ID}/interview-templates/defaults?templateType=account&accountType=ira")
echo -e "Response: $RESPONSE\n"

# Test 8: Get a specific template (need to get ID from previous responses)
echo -e "${YELLOW}Test 8: Get specific template by ID${NC}"
# First, let's get a template ID from the previous responses
TEMPLATE_ID=$(echo "$RESPONSE" | grep -o '"_id":"[^"]*"' | head -1 | cut -d'"' -f4)
if [ ! -z "$TEMPLATE_ID" ]; then
    echo -e "Using template ID: $TEMPLATE_ID"
    RESPONSE=$(api_request "GET" "/v2/organisations/${ORG_ID}/interview-templates/${TEMPLATE_ID}")
    echo -e "Response: $RESPONSE\n"
else
    echo -e "${RED}No template ID found in previous responses${NC}\n"
fi

echo -e "${GREEN}V2 Template API Testing Complete${NC}"