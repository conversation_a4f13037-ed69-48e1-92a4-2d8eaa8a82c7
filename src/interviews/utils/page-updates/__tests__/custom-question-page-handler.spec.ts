import { InterviewPage } from 'src/interview-templates/schemas/v1/interview.template';
import { InterviewPageStatusEnum } from 'src/interview-templates/types/interview-page-status.enum';
import { UpdateInterviewDto } from 'src/interviews/dto/v1/update-interview.dto';
import { PageUpdateContext } from 'src/interviews/types/page-update-context.type';
import { CustomQuestionsPageHandler } from '../custom-question-page-handler';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import mongoose from 'mongoose';

describe('CustomQuestionsPageHandler', () => {
  let handler: CustomQuestionsPageHandler;
  let context: PageUpdateContext;
  let updateDto: UpdateInterviewDto;
  let page: InterviewPage;

  beforeEach(() => {
    context = { fillContingentBeneficiaries: false };
    // Create a basic page structure that matches the database
    page = {
      name: PagesEnum.CUSTOM_QUESTIONS,
      order: 1,
      filled: false,
      status: InterviewPageStatusEnum.UNANSWERED,
      data: {
        templateName: 'Test Template', // Note: Page in DB has templateName
        question: {
          _id: new mongoose.Types.ObjectId('67cfb0bd93f5e37580a5e6d0'),
          question: 'test question',
          type: 'text',
          required: false,
          options: [],
          title: 'test title',
          description: 'test description'
        }
      }
    } as InterviewPage;

    // Create update DTO that matches the actual request structure
    updateDto = {
      page: {
        name: PagesEnum.CUSTOM_QUESTIONS,
        data: {
          question: {
            _id: new mongoose.Types.ObjectId('67cfb0bd93f5e37580a5e6d0'),
            question: 'test question',
            type: 'text',
            options: [],
            required: false,
            title: 'test title',
            description: 'test description'
          },
          answer: 'test answer'
        }
      }
    };

    handler = new CustomQuestionsPageHandler(context, updateDto, InterviewPageStatusEnum.SYNCED);
  });

  describe('shouldUpdate', () => {
    it('should return false if super.shouldUpdate returns false (different page names)', () => {
      // Override the page name to be different
      page.name = PagesEnum.ADDRESS;
      
      // Access the private method via any cast
      const result = (handler as any).shouldUpdate(page);
      
      expect(result).toBe(false);
    });

    it('should return false if updateDto.page.data?.question is empty', () => {
      updateDto.page.data.question = null;
      
      const result = (handler as any).shouldUpdate(page);
      
      expect(result).toBe(false);
    });

    it('should return true when question IDs match', () => {
      // IDs already set to match in beforeEach
      
      const result = (handler as any).shouldUpdate(page);
      
      expect(result).toBe(true);
    });

    it('should return true when question IDs do not match but question text matches (case-insensitive, trimmed)', () => {
      // Different ID but same question text
      updateDto.page.data.question._id = new mongoose.Types.ObjectId('67cfb0bd93f5e37580a5e6d1');
      
      const result = (handler as any).shouldUpdate(page);
      
      expect(result).toBe(true);
    });

    it('should return true when question text matches with different case and whitespace', () => {
      // Different ID and modified question text (with whitespace and different case)
      updateDto.page.data.question._id = new mongoose.Types.ObjectId('67cfb0bd93f5e37580a5e6d1');
      updateDto.page.data.question.question = '  TEST Question  ';
      
      const result = (handler as any).shouldUpdate(page);
      
      expect(result).toBe(true);
    });

    it('should return false when neither IDs nor question text match', () => {
      // Different ID and different question text
      updateDto.page.data.question._id = new mongoose.Types.ObjectId('67cfb0bd93f5e37580a5e6d1');
      updateDto.page.data.question.question = 'completely different question';
      
      const result = (handler as any).shouldUpdate(page);
      
      expect(result).toBe(false);
    });

    it('should return false when question text is missing in update DTO', () => {
      // Missing question text in update
      updateDto.page.data.question._id = new mongoose.Types.ObjectId('67cfb0bd93f5e37580a5e6d1');
      delete updateDto.page.data.question.question;
      
      const result = (handler as any).shouldUpdate(page);
      
      expect(result).toBe(false);
    });

    it('should return false when question text is missing in page data', () => {
      // Missing question text in page
      updateDto.page.data.question._id = new mongoose.Types.ObjectId('67cfb0bd93f5e37580a5e6d1');
      delete page.data.question.question;
      
      const result = (handler as any).shouldUpdate(page);
      
      expect(result).toBe(false);
    });

    it('should handle various custom question types correctly', () => {
      // Array of test cases based on the real examples
      const testCases = [
        {
          description: "multipleChoice question",
          dto: {
            page: {
              data: {
                question: {
                  _id: new mongoose.Types.ObjectId('67cfb0bd93f5e37580a5e6d0'),
                  question: "test",
                  type: "multipleChoice",
                  options: ["1"],
                  required: false,
                  title: "test",
                  description: "test"
                },
                answer: "1"
              },
              name: "custom_questions"
            }
          },
          pageQuestion: {
            _id: new mongoose.Types.ObjectId('67cfb0bd93f5e37580a5e6d0'),
            question: "test",
            type: "multipleChoice",
            options: ["1"],
            required: false,
            title: "test",
            description: "test"
          }
        },
        {
          description: "fillInBlank question",
          dto: {
            page: {
              data: {
                question: {
                  _id: new mongoose.Types.ObjectId('67cfb0d493f5e37580a5e6e6'),
                  question: "test _",
                  type: "fillInBlank",
                  options: [],
                  required: true,
                  title: "test"
                },
                answer: "2312"
              },
              name: "custom_questions"
            }
          },
          pageQuestion: {
            _id: new mongoose.Types.ObjectId('67cfb0d493f5e37580a5e6e6'),
            question: "test _",
            type: "fillInBlank",
            options: [],
            required: true,
            title: "test"
          }
        }
      ];
      
      for (const testCase of testCases) {
        updateDto = testCase.dto as UpdateInterviewDto;
        page.data.question = testCase.pageQuestion;
        
        handler = new CustomQuestionsPageHandler(context, updateDto, InterviewPageStatusEnum.SYNCED);
        
        const result = (handler as any).shouldUpdate(page);
        expect(result).toBe(true);
      }
    });
  });

  describe('handle', () => {
    it('should update the page when shouldUpdate returns true', () => {
      // shouldUpdate should return true with our default setup
      
      const result = handler.handle(page);
      
      expect(result.filled).toBe(true);
      expect(result.status).toBe(InterviewPageStatusEnum.SYNCED);
    });

    it('should not update the page when shouldUpdate returns false', () => {
      // Change page name to make shouldUpdate return false
      page.name = PagesEnum.ADDRESS;
      
      const result = handler.handle(page);
      
      // Page should remain unchanged
      expect(result.filled).toBe(false);
      expect(result.status).toBe(InterviewPageStatusEnum.UNANSWERED);
    });
  });
}); 