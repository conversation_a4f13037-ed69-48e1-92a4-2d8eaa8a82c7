output "onbord_backend_app_name" {
  description = "The name of the Elastic Beanstalk application."
  value       = aws_elastic_beanstalk_application.app_vpc.name
}

output "onbord_backend_env_name" {
  description = "The name of the Elastic Beanstalk environment."
  value       = aws_elastic_beanstalk_environment.ebs_env_vpc.name
}

output "onbord_backend_env_endpoint" {
  description = "The name of the Elastic Beanstalk environment."
  value       = aws_elastic_beanstalk_environment.ebs_env_vpc.endpoint_url
}

output "onbord_backend_env_cname" {
  description = "The CNAME of the Elastic Beanstalk environment."
  value       = aws_elastic_beanstalk_environment.ebs_env_vpc.cname
}

output "nat_gateway_ip" {
  description = "Public IP addresses of the NAT Gateway"
  value = aws_eip.nat.public_ip
}
