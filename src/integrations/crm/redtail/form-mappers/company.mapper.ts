import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { AddressUpdateRequest } from 'src/integrations/crm/redtail/dto/requests/update-address.dto';
import { AddressUpdateRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/company-request.dto';
import { RedtailAddressTypeEnum } from 'src/integrations/crm/redtail/types/enums';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { Country } from 'src/shared/types/general/country.enum';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';

export class RedtailCompanyMapper implements FormUpdateMapper {
  constructor(private redtailCrm: RedtailCRM) {}

  /**
   * Updates the contact company and address in Redtail CRM.
   * @param userId The ID of the user.
   * @param redtailContactId The ID of the contact in Redtail CRM.
   * @param companyName The name of the company.
   * @param address The address to update.
   * @returns A Promise that resolves to a boolean indicating success.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    update: AddressUpdateRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    const workAddressUpdate: AddressUpdateRequest = {
      addressType: RedtailAddressTypeEnum.WORK,
      address: {
        isPrimary: false,
        streetAddress: update.address.line1,
        city: update.address.city,
        state: update.address.state,
        zip: update.address.zip,
        customTypeTitle: update.name,
        country: Country.US,
      },
    };

    await this.redtailCrm.redtailAddressService.updateContactAddresses(
      contact.crmClientId,
      [workAddressUpdate],
    );
  }
}
