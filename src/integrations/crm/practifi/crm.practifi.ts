import { HttpException, HttpStatus } from '@nestjs/common';
import { Connection } from 'jsforce';
import { v4 as uuidv4 } from 'uuid';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import { OAuth } from 'src/auth/auth.types';
import { EnrichedClient, EnrichedContact } from 'src/clients/dto/v1/get-clients.dto';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { InterviewsService } from 'src/interviews/interviews.service';
import { MailService } from 'src/notifications/mail/mail.service';
import { SendEmailOptions } from 'src/notifications/mail/mail.types';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';
import { CrmAuthException } from 'src/shared/exceptions/crm-auth.exception';
import { CRMEnum , CRMType } from 'src/shared/types/integrations';
import { Logger } from 'winston';
import { CRM, PageUpdateMapper } from '../crm.interface';
import { CrmContact } from '../types/contacts/crm-contact.type';
import { CreateContactDto, CreateContactResponseDto, UpdateContactDto, UpdateContactResponseDto } from '../types/create-contact.dto';
import { User } from '../types/user.type';
import { PractifiAdapter } from './crm.practifi.adapter';
import { practifiMappers } from './form-mappers';
import { PractifiContactService } from './services/practifi-contact.service';
import { PractifiTaskService } from './services/practifi-task.service';
import { PractifiUserService } from './services/practifi-user.service';
import { PractifiAccountService } from './services/practifi-account.service';
import { LogCommunicationDto } from 'src/integrations/crm/types/communications/log-communication.dto';
import { GenericEmail } from 'src/integrations/crm/types/generic/email.type';
import { GenericPhone } from 'src/integrations/crm/types/generic/phone.type';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { PractifiCustomObjectsService } from './services/practifi-custom-objects.service';
import { PractifiPackageService } from './services/practifi-package.service';
import { PractifiSuperHTTPConfigsService } from './services/practifi-superhttp-configs.service';
import { handleConvertTypes } from './utils/convert-to-practifi-registration-types.util';
import { DefaultTagsEnum, AccountStage, AccountTypes } from './types/enums';
import { AccountDtoWithCRMClientId } from './types/types';

enum PractifiInitKeys {
  PRACTIFI_INSTALLED = 'practifiInstalled'
};
type InitResult = Map<PractifiInitKeys, string | number>;

export class PractifiCRM implements CRM {
  pageUpdateMapper: PageUpdateMapper;
  protected readonly type: CRMEnum = CRMEnum.Practifi;
  protected baseUrl: string;
  protected connection: Connection;
  public contactService: PractifiContactService;
  public customObjectService: PractifiCustomObjectsService;
  public taskService: PractifiTaskService;
  public userService: PractifiUserService;
  public accountService: PractifiAccountService;
  public superHTTPConfigs: PractifiSuperHTTPConfigsService;
  public practifiPackageService: PractifiPackageService;
  private readonly adapter = new PractifiAdapter();
  private tenantId: string;
  
  constructor(
    protected advisor: AdvisorWithRole,
    protected organisation: Organisation,
    protected credentials: OAuth,
    private readonly superHttp: SuperHttpService,
    public interviewsService: InterviewsService,
    protected emailService: MailService,
    protected logger: Logger,
  ) {
    this.tenantId = this.organisation._id.toString();
    this.baseUrl = credentials.instanceUrl;
    this.contactService = new PractifiContactService(this.superHttp, this.tenantId);
    this.superHTTPConfigs = new PractifiSuperHTTPConfigsService(this.superHttp, this.tenantId);
    this.customObjectService = new PractifiCustomObjectsService(this.superHttp, this.tenantId);
    this.practifiPackageService = new PractifiPackageService(this.superHttp, this.tenantId);
    this.accountService = new PractifiAccountService(this.superHttp, this.tenantId);
    this.taskService = new PractifiTaskService(this.superHttp, this.tenantId);
    this.userService = new PractifiUserService(this.superHttp, this.tenantId);
    this.initMappers();
    this.initializeConnection();
  }

  initMappers(): PageUpdateMapper {
    this.pageUpdateMapper = practifiMappers.call(this);
    return this.pageUpdateMapper;
  }

  private initializeConnection(): void {
    try {
      this.logger.debug('Initializing Practifi connection', {
        baseUrl: this.baseUrl,
        hasAccessToken: !!this.credentials?.accessToken
      });

      if (!this.credentials?.accessToken) {
        throw new Error('Access token not available');
      }

      this.connection = new Connection({
        instanceUrl: this.baseUrl,
        accessToken: this.credentials.accessToken,
        version: '57.0',
        oauth2 : {
          clientId : process.env.PRACTIFI_CLIENT_ID,
          clientSecret : process.env.PRACTIFI_CLIENT_SECRET,
          redirectUri : process.env.PRACTIFI_OAUTH_REDIRECT_URI
        }
      });

      // Initialize services with connection
      this.contactService.setConnection(this.connection);
      this.taskService.setConnection(this.connection);
      this.userService.setConnection(this.connection);
      this.superHTTPConfigs.setRequestConfigs(this.credentials.accessToken);
      this.customObjectService.setConnection(this.connection);
      this.practifiPackageService.setConnection(this.connection);
      this.accountService.setConnection(this.connection);

      this.logger.debug('Practifi connection initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Practifi connection', {
        error: error.message,
        stack: error.stack,
      });
      throw new HttpException(
        'Failed to initialize Practifi connection',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async checkUnmanagedPackageInstalled() {
    return await this.practifiPackageService.isPackageInstalled();
  }

  async authenticate(): Promise<OAuth> {
    try {
      const refreshToken = this.credentials.refreshToken;
      const lastRefresh = this.credentials.lastRefresh;
      const lastRefreshTime = new Date(lastRefresh).getTime();
      const oneHourAgo = Date.now() - 1800000;
      const advisorId = this.advisor?._id?.toString();

      if (!advisorId) {
        throw new HttpException(
          'Advisor not found',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      if (lastRefreshTime < oneHourAgo) {
        const tokens = await this.connection.oauth2.refreshToken(refreshToken);

        this.credentials = {
          ...this.credentials,
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token ? tokens.refresh_token : refreshToken,
          lastRefresh: new Date(),
        };

        // Re-initialize connection with new token
        this.initializeConnection();

        return {
          ...this.credentials,
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token ? tokens.refresh_token : refreshToken,
          lastRefresh: new Date(),
          shouldRefresh: true,
        };
      }

      return {
        ...this.credentials,
        shouldRefresh: false,
      };
    } catch (error) {
      const status = error.response?.status || error?.status || error?.code;
      const shouldUnlink = true;

      this.logger.error(
        `Error authenticating with Practifi: ${error.response?.data ? JSON.stringify(error.response?.data) : error}`,
      );

      throw new CrmAuthException(
        error.message,
        status,
        shouldUnlink,
      );
    }
  }

  async initCrm(): Promise<Map<string, string | number>> {
    const isPackageInstalled = await this.checkUnmanagedPackageInstalled();
    const result: InitResult = new Map<PractifiInitKeys, string | number>();
    result.set(PractifiInitKeys.PRACTIFI_INSTALLED, isPackageInstalled ? 1 : 0)
    return Promise.resolve(result);
  }

  public async getType(): Promise<CRMType> {
    return this.type;
  }

  async getContact(id: string): Promise<CrmContact> {
    try {
      const contact = await this.contactService.getContact(id);
      const mappedContact = this.adapter.mapContact(contact);

      const mappedAssets = await this.mapPractifiAssets(contact.AccountId);
      if (mappedAssets.length > 0) {
        mappedContact.accounts = mappedAssets;
      }

      const companyAddress = await this.getCompanyAddress(id);
      if (companyAddress) {
        mappedContact.additionalInfo.addresses = mappedContact.additionalInfo.addresses.concat(companyAddress);
      }

      return mappedContact;
    } catch (error) {
      this.logger.error('Error getting contact from Practifi', {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  async getContacts(pagination?: PaginationQueryDto): Promise<CrmContact[]> {
    try {
      const contacts = await this.contactService.getContacts(pagination);
      const mappedContacts = contacts?.records?.map(contact => this.adapter.mapContact(contact));

      return mappedContacts;
    } catch (error) {
      this.logger.error('Error getting contacts from Practifi', {
        error: error.message,
        stack: error.stack,
      });
      throw error;
    }
  }

  async getAllUsers(pagination?: PaginationQueryDto): Promise<User[]> {
    try {
      const users = await this.userService.getUsers(pagination);
      return users.map(user => ({
        id: user.Id,
        name: `${user.FirstName} ${user.LastName}`,
        email: user.Email,
        crmId: user.Id,
        firstName: user.FirstName,
        lastName: user.LastName,
      }));
    } catch (error) {
      this.logger.error('Error getting users from Practifi', {
        error: error.message,
        stack: error.stack,
      });
      throw new HttpException(
        'Failed to get users from Practifi',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createContact(createContactDto: CreateContactDto): Promise<CreateContactResponseDto> {
    try {
      const { primaryContact, secondaryContact } = createContactDto;

      // Create contacts
      let primaryContactResponse;
      let secondaryContactResponse;

      const idempotencyKeyPrimary = uuidv4();
      const idempotencyKeySecondary = uuidv4();

      primaryContactResponse = await this.contactService.createContact({
        FirstName: primaryContact.firstName,
        LastName: primaryContact.lastName,
        Email: primaryContact.email,
        MobilePhone: primaryContact.mobile,
      }, idempotencyKeyPrimary);

      if (secondaryContact) {
        if (secondaryContact.crmClientId) {
          await this.contactService.updateContact(secondaryContact.crmClientId, {
            FirstName: secondaryContact.firstName,
            LastName: secondaryContact.lastName,
            Email: secondaryContact.email,
            MobilePhone: secondaryContact.mobile,
          });

          secondaryContactResponse = {};
          secondaryContactResponse.id = secondaryContact.crmClientId;
        }
        if (!secondaryContact.crmClientId) {
          secondaryContactResponse = await this.contactService.createContact({
            FirstName: secondaryContact.firstName,
            LastName: secondaryContact.lastName,
            Email: secondaryContact.email,
            MobilePhone: secondaryContact.mobile,
          }, idempotencyKeySecondary);
        }
      }

      let household;
      const householdName = `${primaryContact.firstName} ${primaryContact.lastName} ${secondaryContact ? `& ${secondaryContact.firstName} ${secondaryContact.lastName}` : ''} Household`;

      // Create household account and contacts
      household = await this.accountService.createAccount({
        Name: householdName,
        practifi__Primary_Member__c: primaryContactResponse.id,
        ...(secondaryContactResponse && { practifi__Spouse__c: secondaryContactResponse.id }),
        Type: AccountTypes.HOUSEHOLD_PROSPECT,
        practifi__Client_Stage__c: AccountStage.PROSPECT,
      });
      if (primaryContactResponse) {
        const primaryRelationshipType = await this.customObjectService.createRelationshipType({
          name: 'Primary',
        });

        await this.customObjectService.createRelationship({
          practifi__From_Contact__c: primaryContactResponse.id,
          practifi__To_Entity__c: household.id,
          practifi__Relationship_Type__c: primaryRelationshipType.id,
        });

        await this.contactService.updateContact(primaryContactResponse.id, {
          AccountId: household.id,
        });
      }

      if (secondaryContactResponse) {
        const spouseRelationshipType = await this.customObjectService.createRelationshipType({
          name: 'Spouse',
        });
        await this.customObjectService.createRelationship({
          practifi__From_Contact__c: secondaryContactResponse.id,
          practifi__To_Entity__c: household.id,
          practifi__Relationship_Type__c: spouseRelationshipType.id,
        });

        await this.contactService.updateContact(secondaryContactResponse.id, {
          AccountId: household.id,
        });
      }

      // Create assets
      if ((primaryContact?.accounts && primaryContact.accounts?.length > 0) || (secondaryContact?.accounts && secondaryContact.accounts?.length > 0)) {
        let assets = [...primaryContact?.accounts.map(acc => ({ ...acc, crmClientId: primaryContactResponse.id }))];
        if (secondaryContact?.accounts && secondaryContact.accounts?.length > 0) {
          assets = [...assets, ...secondaryContact.accounts.map(acc => ({ ...acc, crmClientId: secondaryContactResponse.id }))];
        }
        // here we link assets only to a household account
        await this.createPractifiAssets({ assets, id: household.id });
      }

      return {
        primaryContactCrmId: primaryContactResponse.id,
        secondaryContactCrmId: secondaryContactResponse?.id,
      };
    } catch (error) {
      this.logger.error('Error creating contact in Practifi', {
        error: error.message,
        createContactDto,
      });
      throw error;
    }
  }

  async updateContact(updateContactDto: UpdateContactDto): Promise<UpdateContactResponseDto> {
    try {
      // the flow is the following
      // - Existing contact and no secondary data - Existing contact updated
      // - 2 existing contacts - both contacts updated, check if both contacts have a household - if a household already exists, no new household created, if no household - create a household
      // - Existing contact and new contact - contact updated, new contact created, household created and relationship with a household created

      const primaryData = updateContactDto.primaryContact;
      const secondaryData = updateContactDto.secondaryContact;

      const idempotencyKeySecondary = uuidv4();

      // 1. if no secondary data, first case

      await this.contactService.updateContact(primaryData.crmClientId, {
        FirstName: primaryData.firstName,
        LastName: primaryData.lastName,
        Email: primaryData.email,
        MobilePhone: primaryData.mobile,
      });

      // 2. if secondary data is present
      let secondaryContactResponse;
      if (primaryData && secondaryData) {
        if (secondaryData.crmClientId) {
          await this.contactService.updateContact(secondaryData.crmClientId, {
            FirstName: secondaryData.firstName,
            LastName: secondaryData.lastName,
            Email: secondaryData.email,
            MobilePhone: secondaryData.mobile,
          });
        }
        if (!secondaryData.crmClientId) {
          secondaryContactResponse = await this.contactService.createContact({
            FirstName: secondaryData.firstName,
            LastName: secondaryData.lastName,
            Email: secondaryData.email,
            MobilePhone: secondaryData.mobile,
          }, idempotencyKeySecondary);
          secondaryData.crmClientId = secondaryContactResponse.id;
        }
      }

      // 3. create household

      let household;
      const householdName = `${primaryData.firstName} ${primaryData.lastName} ${secondaryData ? `& ${secondaryData.firstName} ${secondaryData.lastName}` : ''} Household`;

      household = await this.accountService.createAccount({
        Name: householdName,
        practifi__Primary_Member__c: primaryData.crmClientId,
        ...(secondaryData && { practifi__Spouse__c: secondaryData.crmClientId }),
        Type: AccountTypes.HOUSEHOLD_PROSPECT,
        practifi__Client_Stage__c: AccountStage.PROSPECT,
      });

      // create relationship with the household
      const primaryRelationshipType = await this.customObjectService.createRelationshipType({
        name: 'Primary',
      });

      await this.customObjectService.createRelationship({
        practifi__From_Contact__c: primaryData.crmClientId,
        practifi__To_Entity__c: household.id,
        practifi__Relationship_Type__c: primaryRelationshipType.id,
      });

      await this.contactService.updateContact(primaryData.crmClientId, {
        AccountId: household.id,
      });

      if (secondaryData) {
        const spouseRelationshipType = await this.customObjectService.createRelationshipType({
          name: 'Spouse',
        });
        await this.customObjectService.createRelationship({
          practifi__From_Contact__c: secondaryData.crmClientId,
          practifi__To_Entity__c: household.id,
          practifi__Relationship_Type__c: spouseRelationshipType.id,
        });
        await this.contactService.updateContact(secondaryContactResponse.id, {
          AccountId: household.id,
        });
      }

      // 4. create assets
      if ((primaryData?.accounts && primaryData.accounts?.length > 0) || (secondaryData?.accounts && secondaryData.accounts?.length > 0)) {
        let assets = [...primaryData?.accounts.map(acc => ({ ...acc, crmClientId: primaryData.crmClientId }))];
        if (secondaryData?.accounts && secondaryData.accounts?.length > 0) {
          assets = [...assets, ...secondaryData.accounts.map(acc => ({ ...acc, crmClientId: secondaryData.crmClientId }))];
        }
        await this.createPractifiAssets(
          { assets, id: household.id },
        );
      }

      return {
        primaryContactCrmId: primaryData.crmClientId,
        secondaryContactCrmId: secondaryData?.crmClientId,
      };
    } catch (error) {
      this.logger.error('Error updating contact in Practifi', {
        error: error.message,
        updateContactDto,
      });
      throw error;
    }
  }

  async updatePageInfo(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageInfo: { pageName: string; pageData: unknown },
  ): Promise<void> {
    const mapper = this.pageUpdateMapper[pageInfo.pageName];
    if (!mapper) {
      this.logger.warn(`No mapper found for page ${pageInfo.pageName}`);
      return;
    }

    try {
      await mapper.mapPageData(interview, client, organisation, pageInfo.pageData);
    } catch (error) {
      this.logger.error('Error updating page info', {
        error: error.message,
        pageInfo,
        interviewId: interview._id,
      });
      throw error;
    }
  }

  async sendEmail(
    emailOptions: SendEmailOptions,
    contact?: EnrichedContact,
    advisorId?: string,
  ): Promise<void> {
    // Ensure organisation is included in emailOptions
    const emailOptionsWithOrg = {
      ...emailOptions,
      organisation: this.organisation
    };
    return this.emailService.sendEmail(emailOptionsWithOrg, contact, advisorId);
  }

  async getMainEmailOfContact(id: string | number): Promise<GenericEmail> {
    const contact = await this.contactService.getContact(id.toString());
    return this.adapter.toGenericEmail(contact, true);
  }

  async getPhoneNumbersOfContact(id: string | number): Promise<GenericPhone[]> {
    const contact = await this.contactService.getContact(id.toString());
    return this.adapter.toGenericPhones(contact, true);
  }

  async getMainMobileOfContact(id: string): Promise<string> {
    const contact = await this.contactService.getContact(id);
    return this.adapter.toMainMobilePhone(contact, true);
  }

  async getAuthenticatedUserId(): Promise<string> {
    const user = await this.userService.getCurrentUser();
    return user.id;
  }

  async logCommunication(crmClientId: string, logCommunicationDto: LogCommunicationDto): Promise<void> {
    await this.taskService.createCommunicationTask(crmClientId, logCommunicationDto);
  }

  async createPractifiAssets(
    payload: {
      assets: AccountDtoWithCRMClientId[],
      id: string,
    },
  ) {
    const assets = payload?.assets ?
      payload?.assets.map(el => this.customObjectService.createCustomAsset({
        Name: el.label,
        practifi__Client__c: payload.id,
        // advisory_rate__c: String(el.advisoryRate),
        practifi__Category__c: 'Managed funds',
        practifi__Registration_Type__c: handleConvertTypes(el.type),
        // ownership__c: el.ownership,
        practifi__Account_Number__c: el.masterAccountNumber || '',
        practifi__Source__c: DefaultTagsEnum.OnBord,
        practifi__External_Id_1__c: `${el.crmClientId}-${el.label}`,
        // ...(el.features && { features__c: el.features.join(' ') }),
      })) : [];

    await Promise.all(
      [
        ...assets,
      ]
    );
  }

  async mapPractifiAssets(accountId: string) {
    const linkedAssets: {type: string, name: string, id: string}[] = await this.customObjectService.getLinkedAssets(accountId);

    const linkedBeneficiariesRequests = linkedAssets.map(async asset => ({
      ...asset,
      beneficiaries: await this.customObjectService.getLinkedBeneficiaries(asset.id)
    }));
    const linkedBeneficiaries = await Promise.all(
      [
        ...linkedBeneficiariesRequests,
      ]
    );

    return linkedBeneficiaries;
  }

  async getCompanyAddress(contactId: string) {
    const companyAddress = await this.customObjectService.getCompanyAddress(contactId);

    if (!companyAddress) {
      return;
    }

    const mappedCompanyAddress = this.adapter.mapAddress(companyAddress);

    return mappedCompanyAddress;
  }
}