data "aws_ssm_parameter" "DB_USERNAME" {
  name = var.db_username_ssm
}

data "aws_ssm_parameter" "DB_PASSWORD" {
  name = var.db_password_ssm
}

resource "aws_db_instance" "default" {
  allocated_storage       = var.allocated_storage
  storage_type            = var.storage_type
  engine                  = var.engine
  engine_version          = var.engine_version
  instance_class          = var.instance_class
  db_name                 = "onbord_rdbms_${var.environment}"
  identifier              = var.name
  username                = data.aws_ssm_parameter.DB_USERNAME.value
  password                = data.aws_ssm_parameter.DB_PASSWORD.value
  parameter_group_name    = var.parameter_group_name
  publicly_accessible     = var.publicly_accessible
  multi_az                = var.multi_az
  vpc_security_group_ids  = [aws_security_group.rds_sg.id]
  deletion_protection     = true
  storage_encrypted       = true
  backup_retention_period = var.enable_backup ? var.backup_retention_period : 0
  backup_window           = var.enable_backup ? var.backup_window : null
  copy_tags_to_snapshot   = var.enable_backup ? true : false
  skip_final_snapshot     = true
}

resource "aws_security_group" "rds_sg" {
  name        = "rds-sg-${var.environment}"
  description = "Security group for RDS instance"

  ingress {
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [var.eb_ec2_sg_id]
  }
}
