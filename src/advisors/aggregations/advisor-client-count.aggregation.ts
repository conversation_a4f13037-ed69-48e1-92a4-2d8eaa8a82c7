import mongoose from 'mongoose';
import { Advisor } from '../schemas/advisors.schema';

export const AdvisorWithClientCountAggregation = (
  filters: mongoose.FilterQuery<Advisor> = {},
) => [
  {
    $match: {
      ...filters,
    },
  },
  {
    $lookup: {
      from: 'clients',
      let: { advisorId: '$_id' },
      pipeline: [
        {
          $match: {
            $expr: {
              $or: [
                { $eq: ['$primaryAdvisor.id', '$$advisorId'] },
                { $eq: ['$primaryCSA.id', '$$advisorId'] },
              ],
            },
          },
        },
      ],
      as: 'clients',
    },
  },
  {
    $addFields: {
      clientsCount: { $size: '$clients' },
    },
  },
];
