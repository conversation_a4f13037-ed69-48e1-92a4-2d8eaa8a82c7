import { CreateAdvisorDto } from 'src/advisors/dto/create-advisor.dto';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';

export const createAdvisorDtoMock: CreateAdvisorDto = {
  integrations: [],
  personalInfo: {
    firstName: '<PERSON>',
    lastName: 'Do<PERSON>',
    address: {
      street: '456 Park Ave',
      city: 'New York',
      state: 'NY',
      zip: '10001',
    },
    email: '<EMAIL>',
    phone: '1234567890',
  },
  organisationId: '64d3945d17737f26e8f60074',
  role: RolesEnum.CompanyAdmin,
};
