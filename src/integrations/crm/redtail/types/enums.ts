export enum RedtailCRMEndpointsEnum {
  ContactsSearch = '/contacts/search',
  Contacts = '/contacts',
  Tags = '/tags',
  Families = '/families',
  Users = '/lists/database_users',
}

export enum DefaultTagsEnum {
  OnBord = 'OnBord',
}

export enum DefaultTagDescriptionsEnum {
  OnBord = 'Contacts created by <PERSON><PERSON><PERSON>',
}

export enum RedtailUDFFieldTypeEnum {
  TEXT = 4,
  NUMBER = 6,
  LIST = 3,
}

export enum RedtailStatusEnum {
  ACTIVE_CLIENT = 2,
}

export enum RedtailEmailTypeEnum {
  HOME = 1,
  WORK = 2,
}

export enum RedtailAddressTypeEnum {
  HOME = 1,
  WORK = 2,
  MAILING = 3,
  OTHER = 4,
}

export enum RedtailCountryCodeEnum {
  US = 1,
}

export enum RedtailPhoneTypeEnum {
  HOME = 1,
  WORK = 2,
  MOBILE = 3,
  FAX = 4,
  OTHER = 5,
  DIRECT_DIAL = 6,
  TOLL_FREE = 7,
}

export enum RedtailRelationshipEnum {
  SPOUSE = 1,
}

export enum RedtailCitizenshipEnum {
  US = 1,
}

export enum RedtailAccountTypeEnum {
  BROKERAGE_ACCOUNT = 3,
}

export enum RedtailAccountTaxQualifiedTypeEnum {
  ROTH_IRA = 1,
  TRADITIONAL_IRA = 2,
}

export enum RedtailBeneficiaryEnum {
  PRIMARY = 1,
  CONTINGENT = 2,
}

// Inconsistent casing due to inherited inconsistencies
export enum RedtailAccountOwnershipEnum {
  individual = 'individual',
  JTWROS = 'JTWROS',
  JTIC = 'Joint Tenants in Common',
  entirety = 'Tenants by Entirety',
}

export enum RedtailAccountFeatureEnum {
  MONEYLINK = 'MoneyLink',
  Acat = 'Acat',
  IRADISTRIBUTION = 'IRADistribution',
}

export const REDTAIL_OWNERSHIP_FIELD = 'registration';

export enum RedtailActivityTypeEnum {
  TASK = 1,
  CATEGORY_GENERAL_INFORMATION = 2,
  IMPORTANCE_HIGH = 3,
}

export enum RedtailAccountUdfNameEnum {
  ADVISORY_RATE = 'OnBord Advisory Rate',
  ACCOUNT_FEATURES = 'OnBord Account Features',
}

export enum RedtailContactUdfNameEnum {
  EMPLOYMENT_STATUS = 'OnBord Employment Status',
  JOB_DESCRIPTION = 'OnBord Job Description',
  COMPANY_ASSOCIATION = 'OnBord Public Company Association',
  INDUSTRY_AFFILIATION = 'OnBord Industry Affiliation',
}

export const RedtailContactUdfTypeMap = {
  [RedtailContactUdfNameEnum.EMPLOYMENT_STATUS]: RedtailUDFFieldTypeEnum.TEXT,
  [RedtailContactUdfNameEnum.JOB_DESCRIPTION]: RedtailUDFFieldTypeEnum.TEXT,
  [RedtailContactUdfNameEnum.COMPANY_ASSOCIATION]: RedtailUDFFieldTypeEnum.TEXT,
  [RedtailContactUdfNameEnum.INDUSTRY_AFFILIATION]:
    RedtailUDFFieldTypeEnum.TEXT,
};

export const REDTAIL_TAG_GROUP_ONBORD = 'OnBord';

export enum RedtailCrmContactTypeEnum {
  INDIVIDUAL = 'Crm::Contact::Individual',
}

export enum RedtailDBAccountType {
  joint = 'Joint-Name Brokerage Account',
  ira = 'Traditional IRA',
  roth = 'Roth IRA',
  brokerage = 'Single-Name Brokerage Account',
  offline = 'Offline',
  offline_trust = 'Trust (offline)',
  offline_inherited_ira = 'Inherited IRA (offline)',
  offline_simple_ira = 'Simple IRA (offline)',
  offline_corporate = 'Corporate (offline)',
  offline_other = 'Other (offline)',
}
