# Account Template Implementation Plan

## Overview
This plan guides you through implementing account-specific templates in the OnBord platform. The implementation allows different templates for different account types (IRA, Brokerage, etc.) while keeping the system simple.

## Phase 1: Schema Updates (30 minutes)

### Step 1.1: Update InterviewTemplateV2 Schema
**File**: `src/interview-templates/schemas/v2/interview.template.ts`

Add the following fields to the `InterviewTemplateV2` class:

```typescript
@Prop({ 
  type: String,
  enum: ['client_onboarding', 'account', 'transition', 'custom'],
  default: 'client_onboarding',
  required: true,
  index: true
})
templateType!: 'client_onboarding' | 'account' | 'transition' | 'custom';

@Prop({ 
  type: String,
  enum: ['Ira', 'RothIra', 'SingleNameBrokerage', 'JointNameBrokerage'],
  required: function() { return this.templateType === 'account'; },
  index: true
})
accountType?: string;

@Prop({ 
  type: Boolean,
  default: false
})
isDefaultForType!: boolean;
```

### Step 1.2: Update Client Schema
**File**: `src/clients/schemas/clients.schema.ts`

Add to the `Account` class:
```typescript
@Prop({ 
  type: String,
  ref: 'InterviewTemplateV2'
})
templateId?: string;
```

Add to the `Client` class:
```typescript
@Prop({ 
  type: Object,
  default: {}
})
templateConfiguration?: {
  baseTemplateId: string;
  accountTemplates: Array<{
    accountId: string;
    templateId: string;
    appliedAt: Date;
  }>;
};
```

### Step 1.3: Update InterviewV2 Schema
**File**: `src/interviews/schemas/v2/interview.schema.ts`

Add composition tracking:
```typescript
@Prop({ 
  type: Object,
  default: {}
})
compositionInfo?: {
  baseTemplateId: string;
  accountTemplates: Array<{
    accountId: string;
    templateId: string;
    accountType: string;
  }>;
  composedAt: Date;
};

@Prop({ 
  type: [InterviewPageInstanceV2Schema],
  default: []
})
composedPages?: InterviewPageInstanceV2[];  // Store the final composed pages
```

## Phase 2: Create Types and DTOs (30 minutes)

### Step 2.1: Create Template Type Enum
**File**: `src/interview-templates/types/template-type.enum.ts` (new file)

```typescript
export enum TemplateTypeEnum {
  CLIENT_ONBOARDING = 'client_onboarding',
  ACCOUNT = 'account',
  TRANSITION = 'transition',
  CUSTOM = 'custom'
}
```

### Step 2.2: Create Composition DTOs
**File**: `src/interviews/dto/v2/compose-interview.dto.ts` (new file)

```typescript
import { IsString, IsOptional, IsArray, ValidateNested, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';

class AccountTemplateDto {
  @IsString()
  accountId!: string;

  @IsEnum(AccountTypeEnum)
  accountType!: AccountTypeEnum;

  @IsString()
  accountLabel!: string;

  @IsOptional()
  @IsString()
  templateId?: string;
}

export class ComposeInterviewDto {
  @IsString()
  clientId!: string;

  @IsString()
  organisationId!: string;

  @IsOptional()
  @IsString()
  baseTemplateId?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AccountTemplateDto)
  accounts!: AccountTemplateDto[];

  @IsString()
  contactType!: 'primary' | 'secondary';
}
```

## Phase 3: Service Implementation (2 hours)

### Step 3.1: Create Interview Composer Service
**File**: `src/interview-templates/services/interview-composer.service.ts` (new file)

```typescript
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';
import { InterviewTemplateV2 } from '../schemas/v2/interview.template';
import { InterviewV2 } from 'src/interviews/schemas/v2/interview.schema';
import { InterviewPageInstanceV2 } from 'src/interviews/schemas/v2/interview-page-instance.schema';
import { ComposeInterviewDto } from 'src/interviews/dto/v2/compose-interview.dto';
import { TemplateTypeEnum } from '../types/template-type.enum';

@Injectable()
export class InterviewComposerService {
  constructor(
    @InjectModel(InterviewTemplateV2.name)
    private templateModel: Model<InterviewTemplateV2>,
    @InjectModel(InterviewV2.name)
    private interviewModel: Model<InterviewV2>,
    @InjectModel(InterviewPageInstanceV2.name)
    private pageInstanceModel: Model<InterviewPageInstanceV2>
  ) {}

  async composeInterview(dto: ComposeInterviewDto): Promise<InterviewV2> {
    // 1. Get base template
    const baseTemplate = await this.getBaseTemplate(dto.organisationId, dto.baseTemplateId);
    
    // 2. Start with base pages
    const composedPages: any[] = baseTemplate.pages.map((page, index) => ({
      ...page.toObject(),
      visitOrder: index,
      source: 'base_template',
      sourceTemplateId: baseTemplate._id.toString()
    }));

    // 3. Track account templates used
    const accountTemplatesUsed: any[] = [];

    // 4. Add account-specific pages
    for (const account of dto.accounts) {
      const accountTemplate = await this.getAccountTemplate(
        dto.organisationId,
        account.accountType,
        account.templateId
      );

      if (!accountTemplate) {
        console.warn(`No template found for account type ${account.accountType}`);
        continue;
      }

      accountTemplatesUsed.push({
        accountId: account.accountId,
        templateId: accountTemplate._id.toString(),
        accountType: account.accountType
      });

      // Add pages with account context
      const startOrder = composedPages.length;
      const accountPages = accountTemplate.pages.map((page, index) => ({
        ...page.toObject(),
        pageId: uuidv4(),
        pageName: `${page.pageName}_${account.accountId}`,
        pageTitle: `${account.accountLabel} - ${page.pageTitle}`,
        visitOrder: startOrder + index,
        defaultOrder: 1000 + (startOrder + index) * 10,
        accountContext: {
          accountId: account.accountId,
          accountType: account.accountType,
          accountLabel: account.accountLabel
        },
        source: 'account_template',
        sourceTemplateId: accountTemplate._id.toString()
      }));

      composedPages.push(...accountPages);
    }

    // 5. Update page flows
    const finalPages = this.updatePageFlows(composedPages);

    // 6. Create interview
    const interview = await this.interviewModel.create({
      client: dto.clientId,
      organisationId: dto.organisationId,
      advisor: dto.advisorId || dto.clientId, // temporary, should come from context
      template: baseTemplate._id,
      contactType: dto.contactType,
      apiVersion: 'v2',
      status: 'pending',
      compositionInfo: {
        baseTemplateId: baseTemplate._id.toString(),
        accountTemplates: accountTemplatesUsed,
        composedAt: new Date()
      }
    });

    // 7. Create page instances
    const pageInstances = await this.createPageInstances(interview._id.toString(), finalPages);

    return interview;
  }

  private async getBaseTemplate(
    organisationId: string,
    templateId?: string
  ): Promise<InterviewTemplateV2> {
    const query = templateId
      ? { _id: templateId }
      : {
          organisationId,
          templateType: TemplateTypeEnum.CLIENT_ONBOARDING,
          isDefaultForType: true,
          status: 'published'
        };

    const template = await this.templateModel.findOne(query);
    if (!template) {
      throw new NotFoundException('Base template not found');
    }

    return template;
  }

  private async getAccountTemplate(
    organisationId: string,
    accountType: string,
    templateId?: string
  ): Promise<InterviewTemplateV2 | null> {
    const query = templateId
      ? { _id: templateId }
      : {
          organisationId,
          templateType: TemplateTypeEnum.ACCOUNT,
          accountType,
          isDefaultForType: true,
          status: 'published'
        };

    return this.templateModel.findOne(query);
  }

  private updatePageFlows(pages: any[]): any[] {
    const pageMap = new Map(pages.map(p => [p.pageId, p]));
    
    return pages.map((page, index) => {
      const nextPage = pages[index + 1];
      
      // Update default next
      if (page.flow?.defaultNext && nextPage) {
        page.flow.defaultNext.pageId = nextPage.pageId;
      }
      
      // Update rule references (if any)
      if (page.flow?.rules) {
        page.flow.rules = page.flow.rules.map(rule => {
          // Try to resolve page references
          const targetPage = Array.from(pageMap.values()).find(p => 
            p.pageName === rule.goToPageId || p.pageId === rule.goToPageId
          );
          
          return {
            ...rule,
            goToPageId: targetPage?.pageId || rule.goToPageId
          };
        });
      }
      
      return page;
    });
  }

  private async createPageInstances(
    interviewId: string,
    pages: any[]
  ): Promise<InterviewPageInstanceV2[]> {
    const pageInstances = pages.map(page => ({
      interviewId,
      pageId: page.pageId,
      pageName: page.pageName,
      visitOrder: page.visitOrder,
      status: 'pending',
      syncStatus: 'pending',
      accountContext: page.accountContext
    }));

    return this.pageInstanceModel.insertMany(pageInstances);
  }
}
```

### Step 3.2: Update Interview Templates Module
**File**: `src/interview-templates/interview-templates.module.ts`

Add the new service:
```typescript
import { InterviewComposerService } from './services/interview-composer.service';

@Module({
  // ... existing configuration
  providers: [
    InterviewTemplatesService,
    InterviewComposerService,  // Add this
    // ... other providers
  ],
  exports: [
    InterviewTemplatesService,
    InterviewComposerService,  // Add this
  ],
})
export class InterviewTemplatesModule {}
```

## Phase 4: API Endpoints (2 hours)

### Step 4.1: Update Interview Templates Controller
**File**: `src/interview-templates/interview-templates.controller.ts`

Add filtering by template type:
```typescript
@Get()
async findAll(
  @Query('organisationId') organisationId: string,
  @Query('templateType') templateType?: string,
  @Query('accountType') accountType?: string,
) {
  const filter: any = { organisationId };
  
  if (templateType) {
    filter.templateType = templateType;
  }
  
  if (accountType) {
    filter.accountType = accountType;
  }
  
  return this.interviewTemplatesService.find(filter);
}

@Get('defaults')
async getDefaults(
  @Query('organisationId') organisationId: string,
  @Query('templateType') templateType: string,
  @Query('accountType') accountType?: string,
) {
  const filter: any = {
    organisationId,
    templateType,
    isDefaultForType: true,
    status: 'published'
  };
  
  if (accountType) {
    filter.accountType = accountType;
  }
  
  return this.interviewTemplatesService.findOne(filter);
}

@Put(':id/set-default')
async setAsDefault(@Param('id') id: string) {
  return this.interviewTemplatesService.setAsDefaultForType(id);
}
```

### Step 4.2: Create Interview Composition Controller
**File**: `src/interviews/controllers/v2/interview-composer.controller.ts` (new file)

```typescript
import { Controller, Post, Body, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { InterviewComposerService } from 'src/interview-templates/services/interview-composer.service';
import { ComposeInterviewDto } from '../../dto/v2/compose-interview.dto';

@ApiTags('interviews-v2')
@Controller('v2/interviews')
export class InterviewComposerController {
  constructor(
    private readonly composerService: InterviewComposerService
  ) {}

  @Post('compose')
  @ApiOperation({ summary: 'Compose an interview from base and account templates' })
  async composeInterview(@Body() dto: ComposeInterviewDto) {
    return this.composerService.composeInterview(dto);
  }
}
```

## Phase 5: Database Seeds (1 hour)

### Step 5.1: Create Default Account Templates
**File**: `src/interview-templates/seeds/default-account-templates.seed.ts` (new file)

```typescript
import { v4 as uuidv4 } from 'uuid';
import { TemplateTypeEnum } from '../types/template-type.enum';

export const DEFAULT_ACCOUNT_TEMPLATES = [
  {
    templateName: 'Standard IRA Template',
    templateType: TemplateTypeEnum.ACCOUNT,
    accountType: 'Ira',
    description: 'Standard template for Traditional IRA accounts',
    isDefaultForType: true,
    status: 'published',
    version: 1,
    pages: [
      {
        pageId: uuidv4(),
        pageName: 'primary_beneficiaries',
        pageTitle: 'Primary Beneficiaries',
        pageType: 'primary-beneficiaries',
        isActive: true,
        defaultOrder: 1,
        isTerminal: false,
        flow: {
          rules: [],
          defaultNext: {
            pageId: '',
            label: 'Continue to Contingent Beneficiaries'
          },
          allowBack: true
        }
      },
      {
        pageId: uuidv4(),
        pageName: 'contingent_beneficiaries',
        pageTitle: 'Contingent Beneficiaries',
        pageType: 'contingent-beneficiaries',
        isActive: true,
        defaultOrder: 2,
        isTerminal: false,
        flow: {
          rules: [],
          defaultNext: {
            pageId: '',
            label: 'Continue'
          },
          allowBack: true
        }
      }
    ]
  },
  {
    templateName: 'Standard Roth IRA Template',
    templateType: TemplateTypeEnum.ACCOUNT,
    accountType: 'RothIra',
    description: 'Standard template for Roth IRA accounts',
    isDefaultForType: true,
    status: 'published',
    version: 1,
    pages: [
      // Same structure as Traditional IRA
    ]
  },
  {
    templateName: 'Standard Brokerage Template',
    templateType: TemplateTypeEnum.ACCOUNT,
    accountType: 'SingleNameBrokerage',
    description: 'Standard template for individual brokerage accounts',
    isDefaultForType: true,
    status: 'published',
    version: 1,
    pages: [
      {
        pageId: uuidv4(),
        pageName: 'investment_objectives',
        pageTitle: 'Investment Objectives',
        pageType: 'custom-questions',
        isActive: true,
        defaultOrder: 1,
        isTerminal: false,
        flow: {
          rules: [],
          defaultNext: {
            pageId: '',
            label: 'Continue'
          }
        }
      }
    ]
  }
];
```
