import { parsePhoneNumber } from 'libphonenumber-js';
import { isEmpty } from 'lodash';
import {
  getCompanyNameFromCompanyAssociation,
  getTickerSymbolFromCompanyAssociation,
} from 'src/integrations/crm/redtail/utils/company-association-parser';
import { AddressTypeEnum } from 'src/integrations/crm/types/generic/address.type';
import { EmailTypeEnum } from 'src/integrations/crm/types/generic/email.type';
import { PhoneTypeEnum } from 'src/integrations/crm/types/generic/phone.type';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';

export const primaryContactGeneralMappers = {
  // General Mappers
  'primaryContact.fullName': {
    field: ['primaryContact.firstName', 'primaryContact.lastName'],
  },
  'primaryContact.firstName': {
    field: ['primaryContact.firstName'],
  },
  'primaryContact.lastName': {
    field: ['primaryContact.lastName'],
  },
  'primaryContact.middleName': {
    field: ['primaryContact.middleName'],
  },
  'primaryContact.suffix': {
    field: ['primaryContact.suffix'],
  },
  'primaryContact.taxId': {
    field: ['primaryContact.taxId'],
  },
  'primaryContact.dob': {
    field: ['primaryContact.dob'],
  },
  'primaryContact.citizenship': {
    staticValue: 'USA',
  },
  'primaryContact.residence': {
    staticValue: 'USA',
  },
  'primaryContact.employmentStatus': {
    field: ['primaryContact.additionalInfo.employmentStatus'],
  },
  'primaryContact.employer': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const workAddress =
        interviewData.primaryContact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.WORK,
        );
      return workAddress?.title || '';
    },
  },
  'primaryContact.industryAffiliation.companyName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const industryAffiliation =
        interviewData.primaryContact.additionalInfo?.industryAffiliation;

      if (isEmpty(industryAffiliation)) return '';

      return industryAffiliation;
    },
  },
  'primaryContact.companyAssociation.name': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const companyAssociationValue =
        interviewData.primaryContact.additionalInfo.companyAssociation;

      if (isEmpty(companyAssociationValue)) return '';

      return !isEmpty(companyAssociationValue)
        ? getCompanyNameFromCompanyAssociation(companyAssociationValue)
        : '';
    },
  },
  'primaryContact.companyAssociation.tickerSymbol': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const companyAssociationValue =
        interviewData.primaryContact.additionalInfo.companyAssociation;

      return !isEmpty(companyAssociationValue)
        ? getTickerSymbolFromCompanyAssociation(companyAssociationValue)
        : '';
    },
  },
  // Phone Mappers
  'primaryContact.phones.work.number': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.primaryContact.additionalInfo.phones) return '';

      const phone = interviewData.primaryContact.additionalInfo.phones.find(
        (phone) => phone.type === PhoneTypeEnum.WORK,
      )?.number;

      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'primaryContact.phones.home.number': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (isEmpty(interviewData.primaryContact.additionalInfo.phones))
        return '';

      const phone = interviewData.primaryContact.additionalInfo.phones.find(
        (phone) => phone.type === PhoneTypeEnum.HOME,
      )?.number;

      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'primaryContact.phones.mobile.number': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.primaryContact.additionalInfo.phones) return '';

      const phone = interviewData.primaryContact.additionalInfo.phones.find(
        (phone) => phone.type === PhoneTypeEnum.MOBILE,
      )?.number;

      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'primaryContact.emails.work.address': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (isEmpty(interviewData.primaryContact.additionalInfo.emails))
        return '';

      return (
        interviewData.primaryContact.additionalInfo.emails.find(
          (email) => email.type === EmailTypeEnum.WORK,
        )?.address || ''
      );
    },
  },
  'primaryContact.emails.home.address': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (isEmpty(interviewData.primaryContact.additionalInfo.emails))
        return '';

      return (
        interviewData.primaryContact.additionalInfo.emails.find(
          (email) => email.type === EmailTypeEnum.HOME,
        )?.address || ''
      );
    },
  },

  // Address Mappers
  'primaryContact.addresses.home.street': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.primaryContact.additionalInfo?.addresses) return '';

      const addresses = interviewData.primaryContact.additionalInfo?.addresses;
      return (
        interviewData.primaryContact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        )?.street || ''
      );
    },
  },
  'primaryContact.addresses.home.city': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.primaryContact.additionalInfo?.addresses) return '';

      return (
        interviewData.primaryContact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        )?.city || ''
      );
    },
  },
  'primaryContact.addresses.home.state': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.primaryContact.additionalInfo?.addresses) return '';

      return (
        interviewData.primaryContact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        )?.state || ''
      );
    },
  },
  'primaryContact.addresses.home.zip': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.primaryContact.additionalInfo?.addresses) return '';

      return (
        interviewData.primaryContact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        )?.zip || ''
      );
    },
  },
  'primaryContact.addresses.home.country': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.primaryContact.additionalInfo?.addresses) return '';

      return (
        interviewData.primaryContact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.HOME,
        )?.country || 'USA'
      );
    },
  },
  'primaryContact.addresses.work.street': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.primaryContact.additionalInfo?.addresses) return '';

      const addresses = interviewData.primaryContact.additionalInfo?.addresses;
      return (
        addresses.find((address) => address.type === AddressTypeEnum.WORK)
          ?.street || ''
      );
    },
  },
  'primaryContact.addresses.work.city': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.primaryContact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.WORK,
        )?.city || ''
      );
    },
  },
  'primaryContact.addresses.work.state': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.primaryContact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.WORK,
        )?.state || ''
      );
    },
  },
  'primaryContact.addresses.work.zip': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.primaryContact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.WORK,
        )?.zip || ''
      );
    },
  },
  'primaryContact.addresses.work.country': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.primaryContact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.WORK,
        )?.country || 'USA'
      );
    },
  },
  'primaryContact.addresses.mailing.street': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!interviewData.primaryContact.additionalInfo?.addresses) return '';

      const addresses = interviewData.primaryContact.additionalInfo?.addresses;
      return (
        addresses.find((address) => address.type === AddressTypeEnum.MAILING)
          ?.street || ''
      );
    },
  },
  'primaryContact.addresses.mailing.city': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.primaryContact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.MAILING,
        )?.city || ''
      );
    },
  },
  'primaryContact.addresses.mailing.state': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.primaryContact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.MAILING,
        )?.state || ''
      );
    },
  },
  'primaryContact.addresses.mailing.zip': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.primaryContact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.MAILING,
        )?.zip || ''
      );
    },
  },
  'primaryContact.addresses.mailing.country': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.primaryContact.additionalInfo.addresses.find(
          (address) => address.type === AddressTypeEnum.MAILING,
        )?.country || 'USA'
      );
    },
  },
  'primaryContact.account#.advisoryFee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.accounts[accountIndex]?.advisoryRate || '',
  },
  // Account Mappers
  'primaryContact.account#.custodian': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      if (account) return 'Charles Schwab';
    },
  },
  'primaryContact.account#.type': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      if (!account) return '';
      return account.type;
    },
  },
  'primaryContact.account#.label': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      if (!account) return '';
      return account.label;
    },
  },
  'primaryContact.account#.ownership': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      if (!account) return '';
      return account.ownership;
    },
  },
  'primaryContact.account#.masterAccountNumber': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      if (!account) return '';
      return account.masterAccountNumber || '';
    },
  },
  'primaryContact.account#.beneficiaries#.percentage': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      if (!account) return '';
      return account.beneficiaries[beneficiaryIndex]?.percentage || '';
    },
  },
  'primaryContact.account#.beneficiaries#.type': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      if (!account) return '';
      return account.beneficiaries[beneficiaryIndex]?.type || '';
    },
  },
  'primaryContact.account#.beneficiaries#.dob': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      if (!account || !account.beneficiaries[beneficiaryIndex]) return '';
      return account.beneficiaries[beneficiaryIndex]?.dob;
    },
  },
  'primaryContact.account#.beneficiaries#.firstName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      if (!account || !account.beneficiaries[beneficiaryIndex]) return '';
      return account.beneficiaries[beneficiaryIndex]?.firstName;
    },
  },
  'primaryContact.account#.beneficiaries#.lastName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      if (!account || !account.beneficiaries[beneficiaryIndex]) return '';
      return account.beneficiaries[beneficiaryIndex]?.lastName;
    },
  },
  'primaryContact.account#.beneficiaries#.fullName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      if (!account || !account.beneficiaries[beneficiaryIndex]) return '';
      return account.beneficiaries[beneficiaryIndex]?.fullName;
    },
  },
};
