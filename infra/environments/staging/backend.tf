terraform {
  backend "s3" {
    bucket         = "onbord-backend-tf-state-staging"
    key            = "terraform.tfstate"
    region         = "us-east-1"
    dynamodb_table = "onbord-backend-tf-state-lock-staging"
    encrypt        = true
  }
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    datadog = {
      source = "DataDog/datadog"
    }
  }
}
