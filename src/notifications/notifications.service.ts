import { HttpException, HttpStatus, Inject, Injectable, forwardRef } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { MailService } from './mail/mail.service';
import { SmsService } from './sms/sms.service';
import { MutexService } from 'src/mutex/mutex.service';
import { ClientsV1Service } from 'src/clients/services/v1/clients-v1.service';
import { InterviewsService } from 'src/interviews/interviews.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { smsTemplateMapper } from 'src/templates/sms';
import { getFrontendDomain } from 'src/utils/getFrontendDomain';
import { TextMessageTemplateNameEnum } from 'src/templates/sms/types';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';

@Injectable()
export class NotificationsService {
  private readonly serviceName: string;

  constructor(
    @Inject(forwardRef(() => MailService))
    private readonly mailService: MailService,
    @Inject(forwardRef(() => SmsService))
    private readonly smsService: SmsService,
    private readonly mutexService: MutexService,
    @Inject(forwardRef(() => ClientsV1Service))
    private readonly clientsService: ClientsV1Service,
    @Inject(forwardRef(() => InterviewsService))
    private readonly interviewsService: InterviewsService,
    @Inject(forwardRef(() => OrganisationsService))
    private readonly organisationsService: OrganisationsService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {
    this.serviceName = this.constructor.name;
  }

  /**
   * Sends interview reminder notifications to clients with interviews scheduled for the current day.
   * @returns A Promise that resolves when all notifications have been sent.
   */
  @Cron('0 12 * * *')
  async startInterviewReminderNotification() {
    const pid = `${this.serviceName}.startInterviewReminderNotification`;
    const cronSchedule = '0 12 * * *';

    await this.sendNotification(pid, cronSchedule, async () => {
      const clientsToNotify = await this.clientsService.findAll(
        {
          status: ClientStatusEnum.Sent,
          'interviewStatus.percentageCompleted': { $eq: 0 },
        },
        false,
      ); // TODO: Batch instead of findAll using pagination

      this.logger.info(
        `PID: ${pid}, Starting interview reminder notifications. Total clients to notify: ${clientsToNotify.length}`,
      );

      const promises = clientsToNotify.map(async (client: EnrichedClient) => {
        const interviews = await this.interviewsService.find({
          client: client._id,
        });
        const organisation = await this.organisationsService.findOne(
          client.organisationId.toString(),
        );

        return Promise.all(
          interviews
            .filter(({ template }) =>
              template.pages.every(({ filled }) => filled === false),
            )
            .map(async (interview) => {
              const contact = interview.isPrimary
                ? client.primaryContact
                : client.secondaryContact;

              const templateType = TextMessageTemplateNameEnum.ClientStart;
              const context = {
                organisationName: organisation.name,
                interviewUrl: `https://${getFrontendDomain()}/interview/${interview._id.toString()}`,
              };
              const body = smsTemplateMapper(templateType, context);

              const advisorId = client.primaryAdvisor.id.toString();

              if (process.env.WORK_ENV === 'production') {
                this.logger.info(
                  `
                  PID: ${pid}, Client ID: ${client._id} - ${body}
                  Successfully would have sent SMS`,
                );
                // return this.smsService.sendText(
                //   contact.mobile,
                //   body,
                //   undefined,
                //   contact,
                //   advisorId,
                // );
              } else {
                this.logger.info(
                  `
                  PID: ${pid}, Client ID: ${client._id} - ${body}
                  Successfully would have sent SMS`,
                );
              }
            }),
        );
      });

      const results = await Promise.allSettled(promises);

      this.logPromisesErrors(pid, clientsToNotify, results);

      return results;
    });
  }

  /**
   * Sends a reminder notification to clients who have incomplete interviews.
   * @returns A Promise that resolves when all notifications have been sent.
   */
  @Cron('0 13 * * *')
  async finishInterviewReminderNotification() {
    const pid = `${this.serviceName}.finishInterviewReminderNotification`;
    const cronSchedule = '0 13 * * *';

    await this.sendNotification(pid, cronSchedule, async () => {
      // TODO: Add pagination for batch processing
      const clientsToNotify = await this.clientsService.findAll(
        {
          status: ClientStatusEnum.Sent,
          'interviewStatus.percentageCompleted': { $gt: 0 },
        },
        false,
      );

      this.logger.info(
        `PID: ${pid}, Starting interview reminder notifications. Total clients to notify: ${clientsToNotify.length}`,
      );

      const promises = clientsToNotify.map(async (client: EnrichedClient) => {
        const interviews = await this.interviewsService.find({
          client: client._id,
        });
        const organisation = await this.organisationsService.findOne(
          client.organisationId.toString(),
        );

        return Promise.all(
          interviews
            .filter(({ template }) =>
              template.pages.some(({ filled }) => filled === false),
            )
            .map(async (interview) => {
              const contact = interview.isPrimary
                ? client.primaryContact
                : client.secondaryContact;

              const templateType = TextMessageTemplateNameEnum.ClientIncomplete;
              const context = {
                organisationName: organisation.name,
                interviewUrl: `https://${getFrontendDomain()}/interview/${
                  interview._id
                }`,
              };
              const body = smsTemplateMapper(templateType, context);
              const advisorId = client.primaryAdvisor?.id?.toString();
              if (process.env.WORK_ENV === 'production') {
                this.logger.info(
                  `
                  PID: ${pid}, Client ID: ${client._id} - ${body}
                  Successfully would have sent SMS`,
                );
                // return this.smsService.sendText(
                //   contact.mobile,
                //   body,
                //   undefined,
                //   contact,
                //   advisorId,
                // );
              } else {
                this.logger.info(
                  `
                  PID: ${pid}, Client ID: ${client._id} - ${body}
                  Successfully would have sent SMS`,
                );
              }
            }),
        );
      });

      const results = await Promise.allSettled(promises);

      this.logPromisesErrors(pid, clientsToNotify, results);

      return results;
    });
  }

  /**
   * Sends a reminder notification to clients who are waiting for signature.
   * @returns A promise that resolves when all notifications have been sent.
   */
  @Cron('0 14 * * *')
  async signEnvelopeReminderNotification() {
    const pid = `${this.serviceName}.signEnvelopeReminderNotification`;
    const cronSchedule = '0 14 * * *';

    await this.sendNotification(pid, cronSchedule, async () => {
      const clientsToNotify = await this.clientsService.findAll(
        {
          status: ClientStatusEnum.WaitingSignature,
        },
        false,
      );

      this.logger.info(
        `PID: ${pid}, Clients to notify: ${clientsToNotify.length}`,
      );

      const promises = clientsToNotify.map(async (client: EnrichedClient) => {
        const organisation = await this.organisationsService.findOne(
          client.organisationId.toString(),
        );

        const contact = client.primaryContact;
        const templateType = TextMessageTemplateNameEnum.ReminderToSign;
        const context = {
          organisationName: organisation.name,
        };
        const body = smsTemplateMapper(templateType, context);
        const advisorId = client.primaryAdvisor.id.toString();
        if (process.env.WORK_ENV === 'production') {
          this.logger.info(
            `
            PID: ${pid}, Client ID: ${client._id} - ${body}
            Successfully would have sent SMS`,
          );
          // return this.smsService.sendText(
          //   contact.mobile,
          //   body,
          //   undefined,
          //   contact,
          //   advisorId,
          // );
        } else {
          this.logger.info(
            `
            PID: ${pid}, Client ID: ${client._id} - ${body}
            Successfully would have sent SMS`,
          );
        }
      });

      const results = await Promise.allSettled(promises);

      this.logPromisesErrors(pid, clientsToNotify, results);

      return results;
    });
  }

  /**
   * Sends a notification and locks the mutex for the given process ID and cron expression.
   * @param pid The process ID to lock the mutex for.
   * @param cron The cron expression to lock the mutex for.
   * @param calback The callback function to execute after locking the mutex.
   */
  private async sendNotification(
    pid: string,
    cron: string,
    callback: Function,
  ) {
    try {
      await this.mutexService.lock(pid, cron);
      await callback();
    } catch (error) {
      throw new HttpException(
        `Error in ${pid}: ${error.message} - ${JSON.stringify(
          error instanceof HttpException ? error.getResponse() : {},
        )}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    } finally {
      await this.mutexService.unlock(pid);
    }
  }

  private logPromisesErrors(
    pid: string,
    clientsToNotify: EnrichedClient[],
    results: PromiseSettledResult<unknown>[],
  ) {
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        const client = clientsToNotify[index];
        this.logger.error(
          `Error sending notification in ${pid} to client ID: ${client._id}. Error: ${result.reason}`,
        );
      }
    });
  }
}
