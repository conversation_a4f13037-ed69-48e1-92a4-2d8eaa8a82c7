import { InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';
import { AccountOwnerEnum } from 'src/shared/types/accounts/account-ownership.enum';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { parseAccountLabelFromFileName } from 'src/utils/accounts/accountFileNameSuffix';

export function getAccountByFileName(
  interviewData: InterviewDataWithCrmInfo,
  fileName: string,
) {
  const accountLabel = parseAccountLabelFromFileName(fileName);

  let account;
  account = interviewData.primaryContact.accounts?.find(
    (account) => account.label === accountLabel,
  );

  if (account) {
    return {
      account,
      applicant: interviewData.primaryContact,
      coapplicant:
        account.type === AccountTypeEnum.JointNameBrokerage
          ? interviewData.secondaryContact
          : null,
      signer: AccountOwnerEnum.PrimaryContact,
    };
  }

  account = interviewData.secondaryContact?.accounts?.find(
    (account) => account.label === accountLabel,
  );

  if (account) {
    return {
      account,
      applicant: interviewData.secondaryContact,
      coapplicant: null,
      signer: AccountOwnerEnum.SecondaryContact,
    };
  }
  throw new Error(`Account with label ${accountLabel} not found`);
}
