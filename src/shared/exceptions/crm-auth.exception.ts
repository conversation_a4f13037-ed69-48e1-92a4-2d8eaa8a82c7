import { HttpException, HttpStatus } from '@nestjs/common';

export class CrmAuthException extends HttpException {
  public readonly code: HttpStatus;
  public readonly error: string;
  public readonly shouldUnlink: boolean;
  public readonly connectionStatus: boolean;

  constructor(
    message: string,
    code: HttpStatus = HttpStatus.BAD_REQUEST,
    shouldUnlink: boolean = false,
  ) {
    super(message, code);
    this.code = code;
    this.error = message;
    this.shouldUnlink = shouldUnlink;
  }

  getResponse() {
    return {
      code: this.code,
      error: this.error,
      shouldUnlink: this.shouldUnlink,
    };
  }
}
