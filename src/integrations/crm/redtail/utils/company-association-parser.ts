import { VipRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/vip-request.dto';

export const computeCompanyAssociationValue = (
  companyAssociation: VipRequestDto,
) => {
  const { companyName, tickerSymbol } = companyAssociation;
  return `${companyName}; ${tickerSymbol}`;
};

export const getCompanyNameFromCompanyAssociation = (
  companyAssociation: string,
) => {
  if (!companyAssociation) return '';
  const name = companyAssociation.split('; ')[0];
  return name;
};

export const getTickerSymbolFromCompanyAssociation = (
  companyAssociation: string,
) => {
  if (!companyAssociation) return '';
  const tickerSymbol = companyAssociation.split('; ')[1];
  return tickerSymbol;
};
