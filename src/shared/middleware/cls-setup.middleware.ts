import { ClsService, ClsStore } from 'nestjs-cls';
import { Request } from 'express';
import { randomUUID } from 'crypto';

// This is a middleware function that will be called before every request.
// Purpose of this function is to setup the CLS store with any data that might useful
export function clsSetup(cls: ClsService<ClsStore>, req: Request) {
  cls.set(
    'ID',
    `Session ID: ${
      req.headers['x-session-id'] || 'unknown'
    } - Request ID: ${randomUUID()}`,
  );
}
