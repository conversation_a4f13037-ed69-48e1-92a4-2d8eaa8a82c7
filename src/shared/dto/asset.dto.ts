import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { AssetTypeEnum } from 'src/shared/types/general/asset-types.enum';

export class AssetViewDto {
  @Expose()
  @ApiProperty()
  @Type(() => String)
  _id: string;

  @ApiProperty()
  @Expose()
  assetId: string;

  @ApiProperty()
  @Expose()
  assetType: AssetTypeEnum;

  @ApiProperty()
  @Expose()
  assetKey: string;

  @ApiProperty()
  @Expose()
  assetLocation: string;

  @ApiProperty()
  @Expose()
  isPublic: boolean;

  @ApiProperty()
  @Expose()
  createdAt: Date;
}
