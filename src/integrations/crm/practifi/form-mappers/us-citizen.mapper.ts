import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PractifiCRM } from '../crm.practifi';

interface USCitizenPageData {
  citizen: boolean;
  resident: boolean;
}

export class PractifiUSCitizenMapper implements FormUpdateMapper {
  constructor(private practifiCRM: PractifiCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: USCitizenPageData,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    
    if (pageData.citizen && pageData.resident) {
      await this.practifiCRM.contactService.updateContact(contact.crmClientId, {
        practifi__Country_Of_Citizenship__c: 'USA',
        practifi__Country_Of_Residence__c: 'USA',
        practifi__Citizenship_Status__c: 'US Citizen',
      });
    }
  }
} 