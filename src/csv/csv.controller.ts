import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { CSVService } from './csv.service';
import { Controller, Get, Header, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { TransitionTypeEnum } from 'src/transitions/types/transition-type.enum';

@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'))
@Controller('files/csv')
export class CsvController {
  constructor(private readonly csvService: CSVService) {}

  @Get('/clients/template')
  @Header('Content-Type', 'text/csv')
  @Header('Content-Disposition', 'attachment; filename="client_template.csv"')
  downloadClientCsvTemplate() {
    return this.csvService.createClientCsvTemplate();
  }

  @Get('/transitions/protocol/template')
  @Header('Content-Type', 'text/csv')
  @Header(
    'Content-Disposition',
    'attachment; filename="protocol_transitions_template.csv"',
  )
  downloadProtocolTransitionsCsvTemplate() {
    return this.csvService.createTransitionCsvTemplate(
      TransitionTypeEnum.Protocol,
    );
  }

  @Get('/transitions/nonprotocol/template')
  @Header('Content-Type', 'text/csv')
  @Header(
    'Content-Disposition',
    'attachment; filename="nonprotocol_transitions_template.csv"',
  )
  downloadNonProtocolTransitionsCsvTemplate() {
    return this.csvService.createTransitionCsvTemplate(
      TransitionTypeEnum.NonProtocol,
    );
  }
}
