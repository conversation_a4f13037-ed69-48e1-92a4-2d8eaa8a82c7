import {
  ContactRoleValue,
  WealthboxContactSource,
  WealthboxContactStatus,
  WealthboxContactType,
  CustomField,
  DateTime,
  WealthboxDriversLicense,
  WealthboxEmailAddress,
  WealthboxGender,
  WealthboxHousehold,
  WealthboxInvestmentObjective,
  WealthboxMailingAddress,
  WealthboxMaritalStatus,
  WealthboxOccupation,
  WealthboxPhoneNumber,
  WealthboxRiskTolerance,
  WealthboxTag,
  WealthboxTimeHorizon,
  WealthboxWebsite,
} from 'src/integrations/crm/wealthbox/types';

export interface WealthboxContact {
  id: number;
  creator: number;
  created_at: DateTime;
  updated_at: DateTime;
  prefix: string;
  first_name: string;
  middle_name: string;
  last_name: string;
  suffix: string;
  nickname: string;
  job_title: string;
  twitter_name: string;
  linkedin_url: string;
  background_information: string;
  background_info?: string;
  birth_date: DateTime;
  anniversary: DateTime;
  client_since: DateTime;
  date_of_death: DateTime;
  assigned_to: number;
  referred_by: number;
  type: WealthboxContactType;
  gender: WealthboxG<PERSON>;
  contact_source: WealthboxContactSource;
  contact_type: string;
  status: WealthboxContactStatus;
  marital_status: WealthboxMaritalStatus;
  attorney: number;
  cpa: number;
  doctor: number;
  insurance: number;
  business_manager: number;
  family_officer: number;
  assistant: number;
  other: number;
  trusted_contact: number;
  important_information: string;
  personal_interests: string;
  investment_objective: WealthboxInvestmentObjective;
  time_horizon: WealthboxTimeHorizon;
  risk_tolerance: WealthboxRiskTolerance;
  mutual_fund_experience: number;
  stocks_and_bonds_experience: number;
  partnerships_experience: number;
  other_investing_experience: number;
  gross_annual_income: number;
  assets: number;
  non_liquid_assets: number;
  liabilities: number;
  adjusted_gross_income: number;
  estimated_taxes: number;
  confirmed_by_tax_return: boolean;
  tax_year: number;
  tax_bracket: number;
  birth_place: string;
  maiden_name: string;
  passport_number: string;
  green_card_number: string;
  occupation: WealthboxOccupation;
  drivers_license: WealthboxDriversLicense;
  retirement_date: DateTime;
  signed_fee_agreement_date: DateTime;
  signed_ips_agreement_date: DateTime;
  signed_fp_agreement_date: DateTime;
  last_adv_offering_date: DateTime;
  initial_crs_offering_date: DateTime;
  last_crs_offering_date: DateTime;
  last_privacy_offering_date: DateTime;
  company_name: string;
  household: WealthboxHousehold;
  image: string;
  tags: WealthboxTag[];
  street_addresses: WealthboxMailingAddress[];
  email_addresses: WealthboxEmailAddress[];
  phone_numbers: WealthboxPhoneNumber[];
  websites: WealthboxWebsite[];
  custom_fields: CustomField[];
  contact_roles: ContactRoleValue[];
}
