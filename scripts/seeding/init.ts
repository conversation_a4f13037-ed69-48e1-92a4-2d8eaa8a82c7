import { connect, model, Schema, Document } from 'mongoose';
import { cleanUpMongo, seed } from './seed';
import { getSSM } from './ssm';
import * as mongoHosts from './mongo_hosts.json';

// Define a schema for the config collection
const ConfigSchema = new Schema({
  _id: String,
  hasRun: <PERSON><PERSON>,
});

// Define a model for the config collection
const Config = model('Config', ConfigSchema);

export async function initializationScript() {
  if (!process.env.ENV) {
    console.log('Environment not set. Exiting...');
    return 1;
  }
  console.log('Running initialization script...');
  const dbUrl = await getSSM(`/onbord/backend/${process.env.ENV}/DB_URL`);
  const mongoHost = dbUrl.split('//')[1];
  const mongoPwd = await getSSM(
    `/onbord/backend/${process.env.ENV}/MONGODB_ATLAS_DB_PASSWORD`,
  );
  const mongoUser = await getSSM(
    `/onbord/backend/${process.env.ENV}/MONGODB_ATLAS_DB_USERNAME`,
  );

  // Connect to MongoDB
  await connect(
    `mongodb+srv://${mongoUser}:${mongoPwd}@${mongoHost}/?retryWrites=true&w=majority`,
  );
  console.log('Connected to MongoDB...');

  // Retrieve the initialization_status document
  const status = await Config.findById('initialization_status');

  if (!status) {
    console.log('Initialization status not found. Creating...');
    // Create the initialization_status document if it does not exist
    await Config.create({
      _id: 'initialization_status',
      hasRun: false,
    });
  }

  if (status?.hasRun) {
    console.log('Initialization script has already been run. Exiting...');
    return 0;
  }

  await cleanUpMongo();
  await seed();
  const r = await Config.findByIdAndUpdate('initialization_status', {
    hasRun: true,
  });
  return 0;
}

initializationScript().catch((error) => console.error(error));
