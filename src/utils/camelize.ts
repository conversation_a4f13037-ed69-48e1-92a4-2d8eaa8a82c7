import { camelCase } from 'camel-case';
import { Camelize } from 'src/shared/types/monads';

function camelizeObject<T>(obj: T, parentKey?: string): Camelize<T> {
  if (obj === null || obj === undefined) {
    return;
  }

  const result: any = {};
  if (Array.isArray(obj)) {
    return obj.map((item) => camelizeObject(item)) as unknown as Camelize<T>;
  }
  for (const key in obj) {
    const camelKey = camelCase(key);
    result[camelKey] =
      typeof obj[key] === 'object' && obj[key] !== null
        ? camelizeObject(obj[key], camelKey)
        : obj[key];
  }
  return result;
}

export function camelize<T>(obj: T): Camelize<T> {
  return camelizeObject(obj) as Camelize<T>;
}
