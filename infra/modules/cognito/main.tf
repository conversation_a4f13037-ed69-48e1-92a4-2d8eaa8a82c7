resource "aws_iam_role" "sns_caller_role" {
  name = "sns_caller_role_${var.user_pool_name}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [{
      Action = "sts:AssumeRole",
      Effect = "Allow",
      Principal = {
        Service = "cognito-idp.amazonaws.com"
      }
    }]
  })
}

resource "aws_iam_role_policy_attachment" "sns_policy" {
  role       = aws_iam_role.sns_caller_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonSNSFullAccess"
}

resource "aws_cognito_user_pool" "main" {
  name = var.user_pool_name

  auto_verified_attributes = ["email"]

  mfa_configuration = "OPTIONAL"

  email_configuration {
    email_sending_account  = "DEVELOPER"
    source_arn             = "arn:aws:ses:us-east-1:************:identity/<EMAIL>"
  }
  sms_configuration {
    external_id    = var.user_pool_name
    sns_caller_arn = aws_iam_role.sns_caller_role.arn
    sns_region     = "us-east-1"
  }

  # Create User config
  admin_create_user_config {
    # Invitation Message template to send in invitation email
    invite_message_template {
      email_message = var.invitation_email_message
      email_subject = var.invitation_email_subject
      sms_message   = var.invitation_email_sms_text
    }
  }
  # TODO on update, verification code to be sent out before using the new attribute values
  # user_attribute_update_settings {
  #   attributes_require_verification_before_update = ["phone_number"]
  # }
  # SMS MFA text message
  sms_authentication_message = var.mfa_SMS_text

  password_policy {
    minimum_length    = 8
    require_lowercase = true
    require_numbers   = true
    require_symbols   = true
    require_uppercase = true
  }

  schema {
    attribute_data_type      = "String"
    developer_only_attribute = false
    mutable                  = true
    name                     = "username"
  }

  schema {
    attribute_data_type      = "String"
    developer_only_attribute = false
    mutable                  = true
    name                     = "name"
    required                 = true
  }

  schema {
    attribute_data_type      = "String"
    developer_only_attribute = false
    mutable                  = true
    name                     = "phone_number"
    required                 = false
  }

  schema {
    attribute_data_type      = "String"
    developer_only_attribute = false
    mutable                  = true
    name                     = "organisationId"
  }


  schema {
    attribute_data_type      = "String"
    developer_only_attribute = false
    mutable                  = true
    name                     = "role"
    string_attribute_constraints {
      min_length = 1
      max_length = 128
    }
  }

  lifecycle {
    ignore_changes = [schema, password_policy]
  }

  account_recovery_setting {
    recovery_mechanism {
      name     = "verified_email"
      priority = 1
    }
  }
}

resource "aws_cognito_user_pool_client" "client" {
  name         = var.client_name
  user_pool_id = aws_cognito_user_pool.main.id

  explicit_auth_flows = [
    "ALLOW_REFRESH_TOKEN_AUTH",
    "ALLOW_USER_PASSWORD_AUTH",
    "ALLOW_USER_SRP_AUTH"
  ]

  read_attributes = [
    "address", "birthdate", "custom:organisationId", "custom:role", "email",
    "family_name", "gender", "given_name", "locale", "middle_name", "name",
    "nickname", "phone_number", "picture", "preferred_username", "profile",
    "updated_at", "website", "zoneinfo"
  ]

  write_attributes = [
    "address", "birthdate", "custom:organisationId", "custom:role", "email",
    "family_name", "gender", "given_name", "locale", "middle_name", "name",
    "nickname", "phone_number", "picture", "preferred_username", "profile",
    "updated_at", "website", "zoneinfo"
  ]

  generate_secret = false
}
