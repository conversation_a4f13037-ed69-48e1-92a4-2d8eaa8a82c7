import { isEmpty } from 'lodash';
import { RedtailCitizenshipEnum } from 'src/integrations/crm/redtail/types/enums';
import { getDobFromBeneficiaryName } from 'src/integrations/crm/redtail/utils/beneficiary-name-parser';
import {
  getCompanyNameFromCompanyAssociation,
  getTickerSymbolFromCompanyAssociation,
} from 'src/integrations/crm/redtail/utils/company-association-parser';
import { AddressTypeEnum } from 'src/integrations/crm/types/generic/address.type';
import { EmailTypeEnum } from 'src/integrations/crm/types/generic/email.type';
import { PhoneTypeEnum } from 'src/integrations/crm/types/generic/phone.type';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';
import { AccountTypeEnum } from 'src/shared/types/accounts/account-type.enum';
import { getAccountByFileName } from 'src/utils/pdf-mappers/utils/getAccountByFileName';
import parsePhoneNumber, { isValidPhoneNumber } from 'libphonenumber-js';

/**
 * This module exports an object containing callback functions and static values
 * that map to specific fields in an account object. These functions and values
 * are used to extract data from an interviewData object and generate a PDF document.
 *
 * @remarks
 * The object keys follow a specific pattern to indicate which field they map to.
 * The '#' character is used to indicate an array index, and the '.' character is used
 * to indicate nested fields. For example, the key 'account.beneficiaries#.name' maps
 * to the name field of the beneficiary at the specified index in the beneficiaries array.
 *
 * @example
 * // To get the name of the first beneficiary of the second account:
 * const name = accountGeneralMappers['account[1].beneficiaries[0].name'].callback(fileName, interviewData, 1, 0);
 */
export const accountGeneralMappers = {
  'account.beneficiaries#.name': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return account.beneficiaries[beneficiaryIndex]?.name || '';
    },
  },
  'account.beneficiaries#.percentage': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return account.beneficiaries[beneficiaryIndex]?.percentage || '';
    },
  },
  'account.beneficiaries#.type': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return account.beneficiaries[beneficiaryIndex]?.type || '';
    },
  },
  'account.beneficiaries#.dob': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return account.beneficiaries[beneficiaryIndex]?.dob;
    },
  },
  'account.beneficiaries#.firstName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return account.beneficiaries[beneficiaryIndex]?.firstName;
    },
  },
  'account.beneficiaries#.lastName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return account.beneficiaries[beneficiaryIndex]?.lastName;
    },
  },
  'account.beneficiaries#.fullName': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return account.beneficiaries[beneficiaryIndex]?.fullName;
    },
  },
  'account.ownership': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return account.ownership;
    },
  },
  'account.masterAccountNumber': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return account.masterAccountNumber || '';
    },
  },
  'account.name': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return account.name;
    },
  },
  'account.label': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return account.label;
    },
  },
  'account.type': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return account.type;
    },
  },
  'account.allApplicantFullNames': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const coapplicantStr = coapplicant
        ? ` and ${coapplicant.firstName} ${coapplicant.lastName}`
        : '';

      return `${applicant.firstName} ${applicant.lastName} ${coapplicantStr}`;
    },
  },
  'account.applicant.fullName': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return `${applicant.firstName} ${applicant.lastName}`;
    },
  },
  'account.applicant.firstName': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant.firstName;
    },
  },
  'account.applicant.lastName': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type === AccountTypeEnum.JointNameBrokerage) {
        return interviewData.primaryContact.lastName;
      }

      return applicant.lastName;
    },
  },
  'account.applicant.middleName': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type === AccountTypeEnum.JointNameBrokerage) {
        return interviewData.primaryContact.middleName;
      }
      return applicant.middleName;
    },
  },
  'account.applicant.suffix': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant.suffix;
    },
  },
  'account.applicant.taxId': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant.taxId;
    },
  },
  'account.applicant.dob': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant.dob;
    },
  },
  'account.applicant.citizenship': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return RedtailCitizenshipEnum[applicant.citizenship] || '';
    },
  },
  'account.applicant.residence': {
    staticValue: 'USA',
  },
  'account.applicant.employer': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (isEmpty(applicant.additionalInfo?.addresses)) {
        return '';
      }

      const workAddress = applicant?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.WORK,
      );
      return workAddress?.title || '';
    },
  },
  'account.applicant.companyAssociation.name': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const companyAssociationValue =
        applicant.additionalInfo.companyAssociation;

      if (isEmpty(companyAssociationValue)) return '';

      return !isEmpty(companyAssociationValue)
        ? getCompanyNameFromCompanyAssociation(companyAssociationValue)
        : '';
    },
  },
  'account.applicant.companyAssociation.tickerSymbol': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const companyAssociationValue =
        applicant.additionalInfo.companyAssociation;

      if (isEmpty(companyAssociationValue)) return '';

      return !isEmpty(companyAssociationValue)
        ? getTickerSymbolFromCompanyAssociation(companyAssociationValue)
        : '';
    },
  },
  'account.applicant.industryAffiliation.companyName': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const industryAffiliation = applicant.additionalInfo.industryAffiliation;

      if (isEmpty(industryAffiliation)) return '';

      return industryAffiliation;
    },
  },
  'account.applicant.employmentStatus': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo?.employmentStatus || '';
    },
  },
  'account.applicant.phones.work.number': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const phone = applicant?.additionalInfo.phones.find(
        (phone) => phone.type === PhoneTypeEnum.WORK,
      )?.number;

      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'account.applicant.phones.home.number': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const phone = applicant?.additionalInfo.phones.find(
        (phone) => phone.type === PhoneTypeEnum.HOME,
      )?.number;

      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'account.applicant.phones.mobile.number': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const phone = applicant?.additionalInfo.phones.find(
        (phone) => phone.type === PhoneTypeEnum.MOBILE,
      )?.number;

      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'account.applicant.emails.work.address': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.emails.find(
        (email) => email.type === EmailTypeEnum.WORK,
      )?.address;
    },
  },
  'account.applicant.emails.home.address': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.emails.find(
        (email) => email.type === EmailTypeEnum.HOME,
      )?.address;
    },
  },
  'account.applicant.addresses.home.street': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (isEmpty(applicant.additionalInfo?.addresses)) {
        return '';
      }

      const homeAddress = applicant?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.HOME,
      );

      return homeAddress?.street || '';
    },
  },
  'account.applicant.addresses.home.city': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.HOME,
      )?.city;
    },
  },
  'account.applicant.addresses.home.state': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.HOME,
      )?.state;
    },
  },
  'account.applicant.addresses.home.zip': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return applicant?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.HOME,
      )?.zip;
    },
  },
  'account.applicant.addresses.home.country': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const addresses = applicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find((address) => address.type === AddressTypeEnum.HOME)
        ?.country;
    },
  },
  'account.applicant.addresses.work.street': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (isEmpty(applicant.additionalInfo?.addresses)) {
        return '';
      }

      const workAddress = applicant?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.WORK,
      );

      return workAddress?.street || '';
    },
  },
  'account.applicant.addresses.work.city': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (isEmpty(applicant.additionalInfo?.addresses)) {
        return '';
      }

      const workAddress = applicant?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.WORK,
      );

      return workAddress?.city || '';
    },
  },
  'account.applicant.addresses.work.state': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (isEmpty(applicant.additionalInfo?.addresses)) {
        return '';
      }

      const workAddress = applicant?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.WORK,
      );

      return workAddress?.state || '';
    },
  },
  'account.applicant.addresses.work.zip': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (isEmpty(applicant.additionalInfo?.addresses)) {
        return '';
      }

      const workAddress = applicant?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.WORK,
      );

      return workAddress?.zip || '';
    },
  },
  'account.applicant.addresses.work.country': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const addresses = applicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find((address) => address.type === AddressTypeEnum.WORK)
        ?.country;
    },
  },
  'account.applicant.addresses.mailing.street': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (isEmpty(applicant.additionalInfo?.addresses)) {
        return '';
      }

      const address = applicant?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.MAILING,
      );

      return address?.street || '';
    },
  },
  'account.applicant.addresses.mailing.city': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (isEmpty(applicant.additionalInfo?.addresses)) {
        return '';
      }

      const address = applicant?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.MAILING,
      );

      return address?.city || '';
    },
  },
  'account.applicant.addresses.mailing.state': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (isEmpty(applicant.additionalInfo?.addresses)) {
        return '';
      }

      const address = applicant?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.MAILING,
      );

      return address?.state || '';
    },
  },
  'account.applicant.addresses.mailing.zip': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (isEmpty(applicant?.additionalInfo?.addresses)) {
        return '';
      }

      const address = applicant?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.MAILING,
      );

      return address?.zip || '';
    },
  },
  'account.applicant.addresses.mailing.country': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const addresses = applicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find(
        (address) => address.type === AddressTypeEnum.MAILING,
      )?.country;
    },
  },
  'account.coapplicant.firstName': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      return coapplicant?.firstName || '';
    },
  },
  'account.coapplicant.lastName': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      return coapplicant?.lastName || '';
    },
  },
  'account.coapplicant.fullName': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (isEmpty(coapplicant)) {
        return '';
      }

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      return `${coapplicant.firstName} ${coapplicant.lastName}`;
    },
  },
  'account.coapplicant.middleName': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      return coapplicant?.middleName || '';
    },
  },
  'account.coapplicant.suffix': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      return coapplicant?.suffix || '';
    },
  },
  'account.coapplicant.taxId': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      return coapplicant?.taxId || '';
    },
  },
  'account.coapplicant.dob': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      return coapplicant?.dob || '';
    },
  },
  'account.coapplicant.citizenship': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      return RedtailCitizenshipEnum[coapplicant.citizenship] || '';
    },
  },
  'account.coapplicant.residence': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      return coapplicant?.residence || '';
    },
  },
  'account.coapplicant.employmentStatus': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      return (
        interviewData.secondaryContact?.additionalInfo.employmentStatus || ''
      );
    },
  },
  'account.coapplicant.employer': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      if (isEmpty(applicant?.additionalInfo?.addresses)) {
        return '';
      }

      const workAddress = applicant?.additionalInfo.addresses.find(
        (address) => address.type === AddressTypeEnum.WORK,
      );

      return workAddress?.title || '';
    },
  },
  'account.coapplicant.phones.work.number': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }
      const phone = coapplicant?.additionalInfo?.phones?.find(
        (phone) => phone.type === PhoneTypeEnum.WORK,
      )?.number;

      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'account.coapplicant.phones.home.number': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }
      const phone = coapplicant?.additionalInfo?.phones?.find(
        (phone) => phone.type === PhoneTypeEnum.HOME,
      )?.number;

      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'account.coapplicant.phones.mobile.number': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }
      const phone = coapplicant?.additionalInfo?.phones?.find(
        (phone) => phone.type === PhoneTypeEnum.MOBILE,
      )?.number;

      return parsePhoneNumber(phone, 'US').nationalNumber;
    },
  },
  'account.coapplicant.emails.work.address': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }
      return coapplicant?.additionalInfo?.emails?.find(
        (email) => email.type === EmailTypeEnum.WORK,
      ).address;
    },
  },
  'account.coapplicant.emails.home.address': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      return coapplicant?.additionalInfo?.emails?.find(
        (email) => email.type === EmailTypeEnum.HOME,
      ).address;
    },
  },
  'account.coapplicant.addresses.home.street': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find((address) => address.type === AddressTypeEnum.HOME)
        ?.street;
    },
  },
  'account.coapplicant.addresses.home.city': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }
      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find((address) => address.type === AddressTypeEnum.HOME)
        ?.city;
    },
  },
  'account.coapplicant.addresses.home.state': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }
      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find((address) => address.type === AddressTypeEnum.HOME)
        ?.state;
    },
  },
  'account.coapplicant.addresses.home.zip': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }
      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find((address) => address.type === AddressTypeEnum.HOME)
        ?.zip;
    },
  },
  'account.coapplicant.addresses.home.country': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }
      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find((address) => address.type === AddressTypeEnum.HOME)
        ?.country;
    },
  },
  'account.coapplicant.addresses.work.street': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }
      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find((address) => address.type === AddressTypeEnum.WORK)
        ?.street;
    },
  },
  'account.coapplicant.addresses.work.city': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find((address) => address.type === AddressTypeEnum.WORK)
        ?.city;
    },
  },
  'account.coapplicant.addresses.work.state': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }
      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find((address) => address.type === AddressTypeEnum.WORK)
        ?.state;
    },
  },
  'account.coapplicant.addresses.work.zip': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find((address) => address.type === AddressTypeEnum.WORK)
        ?.zip;
    },
  },
  'account.coapplicant.addresses.work.country': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }
      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find((address) => address.type === AddressTypeEnum.WORK)
        ?.country;
    },
  },
  'account.coapplicant.addresses.mailing.street': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }
      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find(
        (address) => address.type === AddressTypeEnum.MAILING,
      )?.street;
    },
  },
  'account.coapplicant.addresses.mailing.city': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find(
        (address) => address.type === AddressTypeEnum.MAILING,
      )?.city;
    },
  },
  'account.coapplicant.addresses.mailing.state': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find(
        (address) => address.type === AddressTypeEnum.MAILING,
      )?.state;
    },
  },
  'account.coapplicant.addresses.mailing.zip': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }

      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find(
        (address) => address.type === AddressTypeEnum.MAILING,
      )?.zip;
    },
  },
  'account.coapplicant.addresses.mailing.country': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (account.type !== AccountTypeEnum.JointNameBrokerage) {
        return '';
      }
      const addresses = coapplicant?.additionalInfo?.addresses;

      if (isEmpty(addresses)) {
        return '';
      }

      return addresses.find(
        (address) => address.type === AddressTypeEnum.MAILING,
      )?.country;
    },
  },
  'account.coapplicant.industryAffiliation.companyName': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      const industryAffiliation =
        coapplicant.additionalInfo.industryAffiliation;

      if (isEmpty(industryAffiliation)) return '';

      return industryAffiliation;
    },
  },
  'account.coapplicant.companyAssociation.name': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (isEmpty(coapplicant)) return '';

      const companyAssociationValue =
        coapplicant.additionalInfo.companyAssociation;

      return getCompanyNameFromCompanyAssociation(companyAssociationValue);
    },
  },
  'account.coapplicant.companyAssociation.tickerSymbol': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account, applicant, coapplicant } = getAccountByFileName(
        interviewData,
        fileName,
      );

      if (isEmpty(coapplicant)) return '';

      const companyAssociationValue =
        coapplicant.additionalInfo.companyAssociation;

      return getTickerSymbolFromCompanyAssociation(companyAssociationValue);
    },
  },
  'account.custodian': {
    callback: (fileName: string, interviewData: InterviewDataWithCrmInfo) => {
      const { account } = getAccountByFileName(interviewData, fileName);

      if (isEmpty(account)) return 'Charles Schwab';
    },
  },
};
