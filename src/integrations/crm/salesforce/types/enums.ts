export enum SalesforceEntityTypes {
  ACCOUNT = 'account',
  CONTACT = 'contact',
  RELATIONSHIP = 'relationship',
  ROLE = 'role',
  TASK = 'task',
  RECORD_TYPE = 'recordType',
  RELATION = 'relation',
  BENEFICIARY = 'beneficiary',
  QUESTION = 'question',
  USER = 'user'
}

export enum TaskStatus {
  NOT_STARTED = 'Not Started',
  COMPLETED = 'Completed',
}

export enum TaskPriority {
  HIGH = 'High',
  NORMAL = 'Normal',
}

export enum BeneficiaryEnum {
  CBEN = 'Contingent',
  PBEN = 'Primary',
  ROLE = 'Beneficiary',
}