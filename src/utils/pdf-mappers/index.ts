import { tabFillingMappers as legacyMappers } from 'src/utils/pdf-mappers/general-mappers/legacyMappers';
import { primaryContactBooleanMappers } from 'src/utils/pdf-mappers/boolean-mappers/primaryContact';
import { secondaryContactBooleanMappers } from 'src/utils/pdf-mappers/boolean-mappers/secondaryContact';
import { accountBooleanMappers } from 'src/utils/pdf-mappers/boolean-mappers/account';
import { primaryContactGeneralMappers } from 'src/utils/pdf-mappers/general-mappers/new-mappers/primaryContact';
import { accountGeneralMappers } from 'src/utils/pdf-mappers/general-mappers/new-mappers/account';
import { secondaryContactGeneralMappers } from 'src/utils/pdf-mappers/general-mappers/new-mappers/secondaryContact';
import { primaryAdvisorGeneralMappers } from 'src/utils/pdf-mappers/general-mappers/new-mappers/primaryAdvisor';
import { organisationGeneralMappers } from 'src/utils/pdf-mappers/general-mappers/new-mappers/organisation';
import { contactGeneralMappers } from 'src/utils/pdf-mappers/general-mappers/new-mappers/contact';

export const pdfMappers = {
  ...primaryContactGeneralMappers,
  ...secondaryContactGeneralMappers,
  ...accountGeneralMappers,
  ...primaryContactBooleanMappers,
  ...secondaryContactBooleanMappers,
  ...accountBooleanMappers,
  ...contactGeneralMappers,
  ...primaryAdvisorGeneralMappers,
  ...organisationGeneralMappers,
  ...legacyMappers, // TODO: remove this after all mappers are migrated
};
