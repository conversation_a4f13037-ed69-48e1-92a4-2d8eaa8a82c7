import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PractifiCRM } from '../crm.practifi';

interface PhonePageData {
  alternatePhones: { type: string, number: string }[]
}

export class PractifiPhoneMapper implements FormUpdateMapper {
  constructor(private practifiCRM: PractifiCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: PhonePageData,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const updateData: any = {};

    if (pageData.alternatePhones) {
      for (const el of pageData.alternatePhones) {
        if (el.type.toLowerCase() === 'home') {
          updateData.HomePhone = el.number;
        }
        if (el.type.toLowerCase() === 'work') {
          updateData.practifi__Work_Phone__c = el.number;
        }
        if (el.type.toLowerCase() === 'other') {
          updateData.OtherPhone = el.number;
        }
      }
    }
    
    if (!pageData.alternatePhones) {
      updateData.HomePhone = null;
      updateData.practifi__Work_Phone__c = null;
      updateData.OtherPhone = null;
    }

    await this.practifiCRM.contactService.updateContact(contact.crmClientId, updateData);
  }
} 