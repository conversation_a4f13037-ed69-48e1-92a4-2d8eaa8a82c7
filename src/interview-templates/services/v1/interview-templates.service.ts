import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import mongoose, { ClientSession, FilterQuery, Model } from 'mongoose';
import { CreateInterviewTemplateDto } from '../../dto/v1/create-interview-template.dto';
import { UpdateInterviewTemplateDto } from '../../dto/v1/update-interview-template.dto';
import { InterviewTemplate } from '../../schemas/v1/interview.template';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { CreateCustomQuestionsTemplateDto } from '../../dto/v1/create-custom-questions-template.dto';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import {
  PaginationQueryDto,
  PaginationResponseDto,
} from 'src/shared/dto/pagination.dto';
import { InterviewTemplateTypeEnum } from 'src/interview-templates/types/interview-template-type.enum';
import { UpdateCustomQuestionsTemplateDto } from '../../dto/v1/update-custom-questions-template.dto';
@Injectable()
export class InterviewTemplatesV1Service {
  constructor(
    @InjectModel(InterviewTemplate.name)
    private model: Model<InterviewTemplate>,
    private readonly organisationService: OrganisationsService,
  ) {}

  /**
   * Creates a new interview template.
   *
   * @param organisationId - The ID of the organisation.
   * @param createInterviewTemplateDto - The data for creating the interview template.
   * @param session - Optional MongoDB session for transactional operations.
   * @returns A promise that resolves to the created interview template.
   */
  async create(
    organisationId: string,
    createInterviewTemplateDto: CreateInterviewTemplateDto,
    session?: ClientSession,
  ): Promise<InterviewTemplate> {
    const created = await this.model.create(
      [
        {
          organisationId: new mongoose.Types.ObjectId(organisationId),
          templateName: createInterviewTemplateDto.templateName,
          pages: createInterviewTemplateDto.pages,
        },
      ],
      { session },
    );
    return created[0];
  }

  /**
   * Retrieves all interview templates with pagination.
   * @param filter - The filter query to apply.
   * @param paginationQueryDto - The pagination query DTO.
   * @param session - Optional MongoDB session to use for the operation.
   * @returns A promise that resolves to a paginated response of interview templates.
   */
  async findAll(
    filter: FilterQuery<InterviewTemplate>,
    paginationQueryDto: PaginationQueryDto,
    session?: ClientSession,
  ): Promise<PaginationResponseDto<InterviewTemplate[]>> {
    const { page, limit } = paginationQueryDto;

    // Calculate the amount to skip for pagination
    const skip = (page - 1) * limit;

    const query = this.model
      .find(filter)
      .skip(skip)
      .limit(limit)
      .session(session);

    const result = await query.exec();
    const totalResults = await this.model.countDocuments(query.getFilter());

    return {
      result,
      totalResults,
    };
  }

  /**
   * Finds a single interview template based on the provided filter.
   *
   * @param filter - The filter query to apply when searching for the interview template.
   * @param session - Optional MongoDB session to use for the operation.
   * @returns A promise that resolves to the found interview template.
   */
  async findOne(
    filter: FilterQuery<InterviewTemplate>,
    session?: ClientSession,
  ): Promise<InterviewTemplate> {
    return this.model.findOne(filter).session(session);
  }

  /**
   * Updates an interview template by its ID.
   * @param id - The ID of the interview template to update.
   * @param updateInterviewTemplateDto - The data to update the interview template with.
   * @param session - Optional MongoDB session to use for the update operation.
   * @returns A Promise that resolves to the updated interview template.
   */
  async update(
    id: string,
    updateInterviewTemplateDto: UpdateInterviewTemplateDto,
    session?: ClientSession,
  ): Promise<InterviewTemplate> {
    return this.model
      .findByIdAndUpdate(id, updateInterviewTemplateDto, {
        new: true,
      })
      .session(session);
  }

  /**
   * Removes an interview template by its ID.
   * @param id - The ID of the interview template to remove.
   * @param session - Optional MongoDB session to use for the operation.
   * @returns A Promise that resolves to the removed interview template.
   */
  async remove(
    id: string,
    session?: ClientSession,
  ): Promise<InterviewTemplate> {
    return this.model.findByIdAndDelete(id).session(session);
  }

  /**
   * Retrieves the default interview template.
   * @returns A Promise that resolves to the default InterviewTemplate.
   */
  async getDefaultTemplate(): Promise<InterviewTemplate> {
    return this.model.findOne({ default: true });
  }

  /**
   * Merges multiple interview templates into one template object by concatenating all their pages.
   *
   * @param templateIds - Array of template IDs to merge.
   * @param session - Optional MongoDB session for transactional operations.
   * @returns A new InterviewTemplate object with merged pages.
   */
  async getMergedTemplate(
    templateIds: string[],
    session?: ClientSession,
  ): Promise<InterviewTemplate> {
    const templates = await this.model
      .find({
        _id: { $in: templateIds.map((id) => new mongoose.Types.ObjectId(id)) },
      })
      .session(session);

    if (templates.length === 0) {
      throw new NotFoundException('Templates not found.');
    }

    const questionIds = new Set<string>();
    const mergedPages = templates
      .flatMap((template) => template.pages)
      .filter((page) => {
        if (
          page.name === PagesEnum.CUSTOM_QUESTIONS &&
          page.data?.question?._id
        ) {
          const questionId = page.data.question._id.toString();
          if (questionIds.has(questionId)) {
            return false;
          }
          questionIds.add(questionId);
        }
        return true;
      });
    const mergedTemplate = new this.model({
      templateName: 'Merged Template',
      pages: mergedPages,
      organisationId:
        templates[0].organisationId || templates[1]?.organisationId,
      default: false,
      type: InterviewTemplateTypeEnum.MERGED,
    });

    return mergedTemplate;
  }
}
