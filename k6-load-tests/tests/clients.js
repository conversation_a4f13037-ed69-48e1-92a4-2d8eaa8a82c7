import http from 'k6/http';
import { sleep } from 'k6';
import { Trend } from 'k6/metrics';

import { getAuthToken } from "../src/utils/auth.js";
import {
  generateClientData,
  responseVerifier as clientResponseVerifier,
  waitForClientProcessing
} from "../src/utils/clients.js";
import { ERROR_RATE, VUs } from "../src/config/constants/k6.js";
import { BASE_URL } from "../src/config/constants/shared.js";
import {calculateThresholds, getDynamicSleep} from "../src/utils/k6-utilities.js";

// Create custom metrics
const clientProcessingDuration = new Trend('client_processing_duration');
const notificationProcessingDuration = new Trend('notification_processing_duration');
const clientCreationDuration = new Trend('client_creation_duration');
const errorResponseTime = new Trend('error_response_time');

const _VUs = VUs['client-creation'];

export const options = {
  scenarios: {
    client_creation: {
      executor: 'shared-iterations',
      vus: _VUs,
      iterations: _VUs,
      maxDuration: '120s'
    }
  },
  thresholds: calculateThresholds(_VUs),
};

const scenarios = {
  basic: (data) => {
    const clientData = generateClientData(data);
    clientData.notificationMethods = [];
    clientData.sendNow = false;

    const startTime = new Date().getTime();
    
    const response = http.post(
      `${BASE_URL}/organisations/${data.organisationId}/clients`,
      JSON.stringify(clientData),
      { headers: data.headers, tags: { type: 'createClient' } }
    );
    
    const endTime = new Date().getTime();
    clientCreationDuration.add(endTime - startTime);

    const clientCreationSuccess = clientResponseVerifier(response);

    if (!clientCreationSuccess) {
      console.log(`Failed to create client: ${response.body}`);
      ERROR_RATE.add(1);
      return;
    }

    const sleepTime = getDynamicSleep(2, _VUs);
    sleep(sleepTime);
  },

  withNotification: (data) => {
    const clientData = generateClientData(data);
    clientData.notificationMethods = ['email', 'sms'];
    clientData.sendNow = true;

    const startTime = new Date().getTime();
    
    const response = http.post(
      `${BASE_URL}/organisations/${data.organisationId}/clients`,
      JSON.stringify(clientData),
      { headers: data.headers, tags: { type: 'createClientWithNotification' } }
    );

    const createEndTime = new Date().getTime();
    clientCreationDuration.add(createEndTime - startTime);

    const clientCreationSuccess = clientResponseVerifier(response);

    if (!clientCreationSuccess) {
      console.log(`Failed to create client with notification: ${response.body}`);
      ERROR_RATE.add(1);
      return;
    }

    // Wait for notifications to be sent
    const clientId = JSON.parse(response.body)._id;
    const processingStartTime = new Date().getTime();
    const notificationSent = waitForClientProcessing(clientId, data.headers, data.organisationId);
    const processingEndTime = new Date().getTime();
    
    // Record metrics
    clientProcessingDuration.add(processingEndTime - startTime);
    notificationProcessingDuration.add(processingEndTime - processingStartTime);

    if (!notificationSent) {
      console.log(`Notification sending timed out for client: ${clientId}`);
      ERROR_RATE.add(1);
    }

    const sleepTime = getDynamicSleep(3, _VUs);
    sleep(sleepTime);
  },

  failure: (data) => {
    const invalidClientData = generateClientData(data);
    // Force a failure
    invalidClientData.primaryContact = null;

    const startTime = new Date().getTime();
    
    const response = http.post(
      `${BASE_URL}/organisations/${data.organisationId}/clients`,
      JSON.stringify(invalidClientData),
      { headers: data.headers, tags: { type: 'createClientFailure' } }
    );
    
    const endTime = new Date().getTime();
    errorResponseTime.add(endTime - startTime);

    const isExpectedFailure = response.status === 400;

    if (!isExpectedFailure) {
      console.log(`Unexpected response for failure scenario: ${response.body}`);
      ERROR_RATE.add(1);
    }

    const sleepTime = getDynamicSleep(1, _VUs);
    sleep(sleepTime);
  }
};

export function setup() {
  const body = getAuthToken();
  if (!body) {
    throw new Error('Failed to get auth token');
  }

  const headers = {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${body.token}`
  };

  console.log('Headers set:', JSON.stringify(headers, null, 2));
  return { headers, advisorId: body.advisorId, organisationId: body.organisationId  };
}

export default function (data) {
  const scenarioName = __ENV.SCENARIO;

  if (!scenarios[scenarioName]) {
    console.error(`Unknown scenario: ${scenarioName}`);
    ERROR_RATE.add(1);
    return;
  }

  scenarios[scenarioName](data);
}