import { IsS<PERSON>, IsBoolean, <PERSON><PERSON><PERSON>al, <PERSON><PERSON>num, IsUUID, IsArray, ValidateNested, IsNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateInterviewV2Dto {
  @ApiProperty({ description: 'Client ID' })
  @IsString()
  @IsNotEmpty()
  clientId!: string;

  @ApiPropertyOptional({ description: 'Template ID (uses default if not provided)' })
  @IsOptional()
  @IsString()
  templateId?: string;

  @ApiProperty({ enum: ['primary', 'secondary'], description: 'Which contact this interview is for' })
  @IsEnum(['primary', 'secondary'])
  contactType!: 'primary' | 'secondary';

  @ApiPropertyOptional({ description: 'Branch name for interview variants' })
  @IsOptional()
  @IsString()
  branch?: string;

  @ApiPropertyOptional({ description: 'Parent interview ID for branched interviews' })
  @IsOptional()
  @IsString()
  parentInterviewId?: string;

  @ApiPropertyOptional({ description: 'Interview metadata' })
  @IsOptional()
  metadata?: {
    deviceType?: string;
    userAgent?: string;
    ipAddress?: string;
    sessionId?: string;
  };
}