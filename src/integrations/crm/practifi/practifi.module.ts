import { Module, forwardRef } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { PractifiController } from './practifi.controller';
import { AdvisorsModule } from 'src/advisors/advisors.module';
import { PractifiContactService } from './services/practifi-contact.service';
import { PractifiTaskService } from './services/practifi-task.service';
import { PractifiUserService } from './services/practifi-user.service';
import { PractifiService } from './practifi.service';
import { ClsService } from 'nestjs-cls';
@Module({
  imports: [
    HttpModule,
    forwardRef(() => AdvisorsModule),
  ],
  controllers: [PractifiController],
  providers: [
    PractifiService,
    PractifiContactService,
    PractifiTaskService,
    PractifiUserService,
  ],
  exports: [PractifiService],
})
export class PractifiModule {} 