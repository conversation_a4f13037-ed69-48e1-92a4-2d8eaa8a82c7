# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

OnBord is a comprehensive financial advisor client onboarding platform consisting of three main components:

### 1. Backend API (`onbord-backend/`)
- **NestJS** TypeScript API with MongoDB and Redis
- **Purpose**: Core business logic, CRM integrations, document management, authentication
- **Port**: `http://localhost:3000` (Swagger at `/api`)

### 2. Frontend Application (`onbord-frontend/`)  
- **Vue.js 2.6** with Vuetify UI framework
- **Purpose**: Advisor and client user interfaces for onboarding workflows
- **Port**: `http://localhost:3001` (when running separately)

### 3. Schwab SDK (`schwab_docs/`)
- **TypeScript SDK** for Schwab API integrations
- **Purpose**: OAuth authentication and account management for Schwab services
- **Status**: Development library for future custodian integrations

## Quick Start Commands

### Development Environment Setup
```bash
# Backend (Full stack with Docker)
cd onbord-backend
npm run start:dev           # Start all services (MongoDB, Redis, API)

# Frontend (Separate development)
cd onbord-frontend  
npm run serve               # Development server
npm run wserve              # Windows Node >14 compatibility

# Backend watch mode only (requires external DB/Redis)
cd onbord-backend
npm run start:dev-watch     # NestJS with hot reload only
```

### Database & Testing
```bash
# Backend
npm run seed                # Seed database with test data via Docker
npm run seed:refresh        # Build and refresh seed data (direct)
npm run db:init            # Initialize database scripts
npm run test                # Unit tests with Jest (--runInBand)
npm run test:watch         # Jest watch mode
npm run test:cov           # Coverage reports
npm run test:debug         # Debug Jest tests
npm run test:e2e           # Playwright end-to-end tests
npm run test:memory        # Memory leak detection with garbage collection
npm run lint               # ESLint with auto-fix
npm run format             # Prettier formatting

# Frontend  
npm run test               # Jest unit tests
npm run lint               # ESLint with auto-fix
```

## Workflow Notes

### Logging and Build Verification
- Instead of running npm run build, please use docker tail of onbord-backend container to check latest logs of the build in docker. You can assume it's running, no need to run npm run dev

## Architecture Overview

## Credentials and Testing

- Local testing credentials: 
  - Email: <EMAIL>
  - Password: Hello123!
  - Note: These are local credentials, completely safe to use for endpoint testing

## API Testing Scripts

### Base API Testing Framework
- **Script Location**: `scripts/api-testing/api-base.sh`
- **Purpose**: Provides authentication and base functions for API testing
- **Features**: Authentication handling, token management, authenticated request helpers

**Usage:**
```bash
# Direct usage for basic operations
./scripts/api-testing/api-base.sh auth     # Authenticate
./scripts/api-testing/api-base.sh status   # Check auth status
./scripts/api-testing/api-base.sh cleanup  # Remove auth files

# Source in other scripts for extended functionality
source scripts/api-testing/api-base.sh
authenticate
api_request "GET" "/v2/organisations/$(get_org_id)/interview-templates"
```

### Manual Curl Login Commands

- Login Curl Command (for manual testing):
```bash
curl 'http://localhost:3000/auth/login' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Accept-Language: en-US,en;q=0.9' \
  -H 'Cache-Control: no-cache' \
  -H 'Connection: keep-alive' \
  -H 'Content-Type: application/json' \
  --data-raw '{"email":"<EMAIL>","password":"Hello123!"}'
```

## Development Principles

- We take types seriously, no workarounds by using any, respect hierarchy and respect well structured code.