import { Camelize } from 'src/shared/types/monads';

export class AddBeneficiaryResponseDto {
  account_beneficiary: {
    id: number;
    account_id: number;
    name: string;
    beneficiary_type: number;
    beneficiary_type_description: string;
    percentage: string;
    deleted: boolean;
    created_at: string;
    updated_at: string;
  };
}

export type AddBeneficiaryResponse = Camelize<AddBeneficiaryResponseDto>;
