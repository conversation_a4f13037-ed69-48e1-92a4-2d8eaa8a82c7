import { TransitionEmailRegisterContext } from 'src/templates/sms/transition-email-register/transition-email-register.context';

export function transitionEmailRegisterTemplate(
  ctx: TransitionEmailRegisterContext,
) {
  return `
    Hi ${ctx.clientFirstName} ${ctx.clientLastName},
    ${ctx.advisorFirstName} ${ctx.advisorLastName} is switching to ${ctx.organisationName} and would like to continue working with you. 
    If you wish, please submit your email address to receive your non solicitation agreement at the following link: ${ctx.emailLink}.
    If you have any questions, please contact ${ctx.organisationName} at ${ctx.organisationPhone}.`;
}
