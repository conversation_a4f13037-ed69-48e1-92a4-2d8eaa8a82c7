import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import { RbacService } from './rbac.service';
import { Role } from './schemas/role.schema';
import { CreateRoleDto } from './dto/create-role.dto';
import { AddPermissionDto } from './dto/add-permission.dto';
import { DeletePermissionDto } from './dto/delete-permission.dto';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { AuthGuard } from '@nestjs/passport';
@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'))
@Controller('rbac/roles')
export class RbacController {
  constructor(private readonly rolesService: RbacService) {}

  @Get()
  async findAll(): Promise<Role[]> {
    return this.rolesService.findAllRoles();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<Role> {
    return this.rolesService.findOneRole({ _id: id });
  }

  @Post()
  async create(@Body() role: CreateRoleDto): Promise<Role> {
    return this.rolesService.createRole(role);
  }

  @Delete(':id')
  async delete(@Param('id') id: string): Promise<Role> {
    return this.rolesService.deleteRole(id);
  }

  @Post(':roleId/permissions')
  async addPermission(
    @Param('roleId') roleId: string,
    @Body() addPermissionDto: AddPermissionDto,
  ): Promise<Role> {
    return this.rolesService.addPermission(roleId, addPermissionDto);
  }

  @Delete(':roleId/permissions')
  async removePermission(
    @Param('roleId') roleId: string,
    @Body() deletePermissionDto: DeletePermissionDto,
  ): Promise<Role> {
    return this.rolesService.deletePermission(roleId, deletePermissionDto);
  }
}
