import { RedtailCitizenshipEnum } from 'src/integrations/crm/redtail/types/enums';
import { Camelize } from 'src/shared/types/monads';

export class GetPersonalProfileResponseDto {
  personal_profile: {
    id: number;
    contact_id: number;
    enw_assets: string;
    enw_liabilities: string;
    enw_assets_nonliquid: string;
    enw_is_override: boolean;
    birth_place: string;
    maiden_name: string;
    height: string;
    weight: string;
    is_smoker: boolean;
    medical_conditions: string;
    review_start_date: string;
    review_end_date: string;
    next_review_at: string;
    citizenship: RedtailCitizenshipEnum;
    citizenship_description: string;
    alien_country: string;
    deleted: boolean;
    created_at: string;
    updated_at: string;
  };
}

export type GetPersonalProfileResponse =
  Camelize<GetPersonalProfileResponseDto>;
