import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { SalesforceCRM } from 'src/integrations/crm/salesforce/crm.salesforce';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

interface VIPPageData {
  companyName: string;
  tickerSymbol?: string;
}

export class SalesforceVIPMapper implements FormUpdateMapper {
  constructor(private salesforceCRM: SalesforceCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: VIPPageData,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    // Remove data in case the client changes their answer
    if (!pageData.companyName) {
    // Remove industry affiliation from Salesforce contact
      await this.salesforceCRM.contactService.updateContact(contact.crmClientId, {
        Onbord_Affiliated_Company__c: null,
        Onbord_Ticker__c: null,
      });
  
      return;
    }

    await this.salesforceCRM.contactService.updateContact(contact.crmClientId, {
      Onbord_Affiliated_Company__c: pageData.companyName,
      Onbord_Ticker__c: pageData.tickerSymbol,
    });
  }
}
