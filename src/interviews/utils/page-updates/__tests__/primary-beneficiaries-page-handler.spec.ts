import { InterviewPage } from 'src/interview-templates/schemas/v1/interview.template';
import { PrimaryBeneficiariesPageHandler } from '../primary-beneficiaries-page-handler';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { InterviewPageStatusEnum } from 'src/interview-templates/types/interview-page-status.enum';

describe('PrimaryBeneficiariesPageHandler', () => {
  let handler: PrimaryBeneficiariesPageHandler;
  let page: InterviewPage;
  let context: any;

  beforeEach(() => {
    context = {
      fillContingentBeneficiaries: false
    };

    const updateDto = {
      page: {
        name: PagesEnum.PRIMARY_BENEFICIARIES,
        data: {
          instance: {
            name: 'testInstance',
            label: 'testLabel'
          }
        }
      }
    };

    page = {
      name: PagesEnum.PRIMARY_BENEFICIARIES,
      order: 1,
      filled: false,
      status: InterviewPageStatusEnum.UNANSWERED,
      data: {
        instance: 'testInstance',
        label: 'testLabel'
      },
    } as InterviewPage;

    handler = new PrimaryBeneficiariesPageHandler(context, updateDto, InterviewPageStatusEnum.SYNCED);
  });

  describe('shouldUpdate', () => {
    it('should return false when instance data is empty', () => {
      const updateDtoWithEmptyInstance = {
        page: {
          name: PagesEnum.PRIMARY_BENEFICIARIES,
          data: {
            instance: null
          }
        }
      };
      handler = new PrimaryBeneficiariesPageHandler(context, updateDtoWithEmptyInstance, InterviewPageStatusEnum.SYNCED);
      
      const result = handler.shouldUpdate(page);
      
      expect(result).toBe(false);
    });

    it('should return true when all conditions match', () => {
      const result = handler.shouldUpdate(page);
      
      expect(result).toBe(true);
    });

    it('should return false when instance name does not match', () => {
      page.data.instance = 'differentInstance';
      
      const result = handler.shouldUpdate(page);
      
      expect(result).toBe(false);
    });

    it('should return false when label does not match', () => {
      page.data.label = 'differentLabel';
      
      const result = handler.shouldUpdate(page);
      
      expect(result).toBe(false);
    });
  });

  describe('handle', () => {
    it('should set fillContingentBeneficiaries to true when beneficiaries are empty', () => {
      const updateDtoWithEmptyBeneficiaries = {
        page: {
          name: PagesEnum.PRIMARY_BENEFICIARIES,
          data: {
            instance: {
              name: 'testInstance',
              label: 'testLabel'
            },
            beneficiaries: []
          }
        }
      };
      handler = new PrimaryBeneficiariesPageHandler(context, updateDtoWithEmptyBeneficiaries, InterviewPageStatusEnum.SYNCED);

      handler.handle(page);

      expect(context.fillContingentBeneficiaries).toBe(true);
    });

    it('should not set fillContingentBeneficiaries when beneficiaries are not empty', () => {
      const updateDtoWithBeneficiaries = {
        page: {
          name: PagesEnum.PRIMARY_BENEFICIARIES,
          data: {
            instance: {
              name: 'testInstance',
              label: 'testLabel'
            },
            beneficiaries: ['beneficiary1']
          }
        }
      };
      handler = new PrimaryBeneficiariesPageHandler(context, updateDtoWithBeneficiaries, InterviewPageStatusEnum.SYNCED);

      handler.handle(page);

      expect(context.fillContingentBeneficiaries).toBe(false);
    });

    it('should update page status and filled property', () => {
      const result = handler.handle(page);

      expect(result.status).toBe(InterviewPageStatusEnum.SYNCED);
      expect(result.filled).toBe(true);
    });

    it('should return the same page object after updating it', () => {
      const result = handler.handle(page);

      expect(result).toBe(page);
    });
  });
});