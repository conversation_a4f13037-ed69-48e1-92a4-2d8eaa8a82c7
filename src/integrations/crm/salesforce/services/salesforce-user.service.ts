import { Injectable } from '@nestjs/common';
import { Connection, Record as SalesforceRecord } from 'jsforce';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { generateRequestConfigs } from '../utils/generate-request-configs.util';
import { salesforceQuery } from '../utils/salesforce-query.util';
import { SalesforceEntityTypes } from '../types/enums';
import { commonConfigs } from '../types/metadata.types';

@Injectable()
export class SalesforceUserService {
  private connection: Connection;
  private baseUrl: string;
  private commonConfigs: commonConfigs;

  constructor(
    private readonly httpService: SuperHttpService,
    private tenantId: string
  ) {}

  setConnection(connection: Connection) {
    this.connection = connection;
    this.baseUrl = `${connection.instanceUrl}/services/data/v${connection.version}`;
    this.prepareRequestConfigs();
  }

  prepareRequestConfigs() {
    const requestConfigs = generateRequestConfigs(this.connection.accessToken);
    const { withInterceptors, ...configsWithoutInterceptors } = requestConfigs;
    this.commonConfigs = configsWithoutInterceptors;
  }

  async getUsers(pagination?: PaginationQueryDto): Promise<SalesforceRecord[]> {
    let soql = `SELECT Id, FirstName, LastName, Email FROM User WHERE IsActive = 'true'`;

    if (pagination?.limit) {
      soql += `LIMIT ${pagination.limit}`;
    }

    if (pagination?.page) {
      const offset = (pagination.page - 1) * pagination.limit;
      soql += `OFFSET ${offset}`;
    }

    const result = await salesforceQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, SalesforceEntityTypes.USER, this.tenantId);
    return Array.isArray(result?.records) ? result?.records : [result?.records];
  }

  async getCurrentUser(): Promise<{ id: string; organizationId: string }> {
    const identity = await this.connection.identity();
    return {
      id: identity.user_id,
      organizationId: identity.organization_id,
    };
  }
}