import { Test, TestingModule } from '@nestjs/testing';
import { InterviewV2Facade } from '../interview-v2.facade';
import { InterviewCoreService } from './core/interview-core.service';
import { InterviewPagesService } from './pages/interview-pages.service';
import { InterviewDocumentsService } from './documents/interview-documents.service';
import { InterviewEnvelopeService } from './envelope/interview-envelope.service';
import { InterviewNotificationService } from './notifications/interview-notification.service';
import { InterviewQueueService } from './queue/interview-queue.service';
import { CreateInterviewDto } from '../../dto/v1/create-interview.dto';
import { UpdateInterviewDto } from '../../dto/v1/update-interview.dto';
import { UploadDocumentDto } from '../../dto/v1/upload-document.dto';
import { RequiredDocumentDto } from '../../dto/v1/required-document.dto';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { EnrichedInterview } from '../../dto/v1/enriched-interview-dto';
import { InterviewCompletionParams } from '../../types/interview-data.type';
import { ClientSession } from 'mongoose';

describe('InterviewV2Facade', () => {
  let facade: InterviewV2Facade;
  let coreService: jest.Mocked<InterviewCoreService>;
  let pagesService: jest.Mocked<InterviewPagesService>;
  let docsService: jest.Mocked<InterviewDocumentsService>;
  let envelopeService: jest.Mocked<InterviewEnvelopeService>;
  let notifyService: jest.Mocked<InterviewNotificationService>;
  let queueService: jest.Mocked<InterviewQueueService>;

  const mockSession = {} as ClientSession;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        InterviewV2Facade,
        {
          provide: InterviewCoreService,
          useValue: {
            create: jest.fn(),
            find: jest.fn(),
            findAll: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            remove: jest.fn(),
          },
        },
        {
          provide: InterviewPagesService,
          useValue: {
            updatePages: jest.fn(),
            updateInterviewPages: jest.fn(),
            addPage: jest.fn(),
            removePage: jest.fn(),
            validateAllSynced: jest.fn(),
          },
        },
        {
          provide: InterviewDocumentsService,
          useValue: {
            upload: jest.fn(),
            addRequiredDocument: jest.fn(),
            removeRequiredDocument: jest.fn(),
            getAccountOpeningAdvisoryFiles: jest.fn(),
          },
        },
        {
          provide: InterviewEnvelopeService,
          useValue: {
            getEnvelopeFiles: jest.fn(),
            createDocusignEnvelope: jest.fn(),
            prepareDocusignEnvelope: jest.fn(),
          },
        },
        {
          provide: InterviewNotificationService,
          useValue: {
            sendDesktopInterviewEmail: jest.fn(),
            sendNonCitizenEmail: jest.fn(),
            sendNotification: jest.fn(),
          },
        },
        {
          provide: InterviewQueueService,
          useValue: {
            enqueuePageUpdate: jest.fn(),
            enqueueCompletionFlow: jest.fn(),
            markInterviewAsComplete: jest.fn(),
          },
        },
      ],
    }).compile();

    facade = module.get<InterviewV2Facade>(InterviewV2Facade);
    coreService = module.get(InterviewCoreService);
    pagesService = module.get(InterviewPagesService);
    docsService = module.get(InterviewDocumentsService);
    envelopeService = module.get(InterviewEnvelopeService);
    notifyService = module.get(InterviewNotificationService);
    queueService = module.get(InterviewQueueService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('CRUD operations', () => {
    describe('create', () => {
      it('should delegate to core service with correct parameters', async () => {
        const dto: CreateInterviewDto = { clientId: 'client123' } as any;
        const expected = { _id: 'interview123' };
        coreService.create.mockResolvedValue(expected as any);

        const result = await facade.create(dto, mockSession);

        expect(coreService.create).toHaveBeenCalledWith(dto, mockSession);
        expect(result).toBe(expected);
      });

      it('should handle errors from core service', async () => {
        const dto: CreateInterviewDto = { clientId: 'client123' } as any;
        const error = new Error('Creation failed');
        coreService.create.mockRejectedValue(error);

        await expect(facade.create(dto)).rejects.toThrow(error);
      });
    });

    describe('find', () => {
      it('should delegate to core service with filter', async () => {
        const filter = { clientId: 'client123' };
        const expected = [{ _id: 'interview123' }];
        coreService.find.mockResolvedValue(expected as any);

        const result = await facade.find(filter);

        expect(coreService.find).toHaveBeenCalledWith(filter);
        expect(result).toBe(expected);
      });
    });

    describe('findAll', () => {
      it('should delegate to core service', async () => {
        const expected = [{ _id: 'interview123' }];
        coreService.findAll.mockResolvedValue(expected as any);

        const result = await facade.findAll();

        expect(coreService.findAll).toHaveBeenCalledWith();
        expect(result).toBe(expected);
      });
    });

    describe('findOne', () => {
      it('should delegate to core service with all parameters', async () => {
        const filter = { _id: 'interview123' };
        const expected = { _id: 'interview123' };
        coreService.findOne.mockResolvedValue(expected as any);

        const result = await facade.findOne(filter, mockSession, true);

        expect(coreService.findOne).toHaveBeenCalledWith(filter, mockSession, true);
        expect(result).toBe(expected);
      });

      it('should use default skipEnrich value when not provided', async () => {
        const filter = { _id: 'interview123' };
        coreService.findOne.mockResolvedValue({} as any);

        await facade.findOne(filter);

        expect(coreService.findOne).toHaveBeenCalledWith(filter, undefined, false);
      });
    });

    describe('findById', () => {
      it('should delegate to core service with all parameters', async () => {
        const id = 'interview123';
        const expected = { _id: id };
        coreService.findById.mockResolvedValue(expected as any);

        const result = await facade.findById(id, mockSession, true);

        expect(coreService.findById).toHaveBeenCalledWith(id, mockSession, true);
        expect(result).toBe(expected);
      });
    });

    describe('remove', () => {
      it('should delegate to core service', async () => {
        const id = 'interview123';
        const expected = { _id: id };
        coreService.remove.mockResolvedValue(expected as any);

        const result = await facade.remove(id, mockSession);

        expect(coreService.remove).toHaveBeenCalledWith(id, mockSession);
        expect(result).toBe(expected);
      });
    });
  });

  describe('Page operations', () => {
    describe('updatePages', () => {
      it('should delegate to pages service', async () => {
        const pages = [{
          name: 'test',
          order: 1,
          filled: false,
          data: {},
          status: 'UNANSWERED',
          elements: []
        }] as any;
        const dto: UpdateInterviewDto = {} as any;
        const status = { status: 'complete' };
        const expected = [{
          name: 'test',
          order: 1,
          filled: true,
          data: {},
          status: 'SYNCED',
          elements: []
        }] as any;
        pagesService.updatePages.mockReturnValue(expected);

        const result = facade.updatePages(pages, dto, status);

        expect(pagesService.updatePages).toHaveBeenCalledWith(pages, dto, status);
        expect(result).toBe(expected);
      });
    });

    describe('updateInterviewPages', () => {
      it('should delegate to pages service', async () => {
        const interviewId = 'interview123';
        const dto: UpdateInterviewDto = {} as any;
        const pageStatus = { status: 'complete' };
        const expected = { updated: true };
        pagesService.updateInterviewPages.mockResolvedValue(expected as any);

        const result = await facade.updateInterviewPages(interviewId, dto, pageStatus, mockSession);

        expect(pagesService.updateInterviewPages).toHaveBeenCalledWith(
          interviewId,
          dto,
          pageStatus,
          mockSession,
        );
        expect(result).toBe(expected);
      });
    });

    describe('addPageToInterview', () => {
      it('should delegate to pages service addPage method', async () => {
        const id = 'interview123';
        const pageName = PagesEnum.NAME;
        const expected = { added: true };
        pagesService.addPage.mockResolvedValue(expected as any);

        const result = await facade.addPageToInterview(id, pageName);

        expect(pagesService.addPage).toHaveBeenCalledWith(id, pageName);
        expect(result).toBe(expected);
      });
    });

    describe('removePageFromInterview', () => {
      it('should delegate to pages service removePage method', async () => {
        const id = 'interview123';
        const pageName = 'test-page';
        const expected = { removed: true };
        pagesService.removePage.mockResolvedValue(expected as any);

        const result = await facade.removePageFromInterview(id, pageName);

        expect(pagesService.removePage).toHaveBeenCalledWith(id, pageName);
        expect(result).toBe(expected);
      });
    });

    describe('validateAllPagesAreSynced', () => {
      it('should delegate to pages service validateAllSynced method', async () => {
        const interviewId = 'interview123';
        pagesService.validateAllSynced.mockResolvedValue(true);

        const result = await facade.validateAllPagesAreSynced(interviewId);

        expect(pagesService.validateAllSynced).toHaveBeenCalledWith(interviewId);
        expect(result).toBe(true);
      });
    });
  });

  describe('Document operations', () => {
    describe('upload', () => {
      it('should fetch interview and delegate to docs service', async () => {
        const interviewId = 'interview123';
        const files = [{ originalname: 'test.pdf' }] as Express.Multer.File[];
        const dto: UploadDocumentDto = {} as any;
        const mockInterview = { _id: interviewId };
        const expected = { uploaded: true };

        coreService.findById.mockResolvedValue(mockInterview as any);
        docsService.upload.mockResolvedValue(expected as any);

        const result = await facade.upload(interviewId, files, dto, mockSession);

        expect(coreService.findById).toHaveBeenCalledWith(interviewId, mockSession);
        expect(docsService.upload).toHaveBeenCalledWith(mockInterview, files, dto, mockSession);
        expect(result).toBe(expected);
      });

      it('should handle errors when interview not found', async () => {
        const interviewId = 'interview123';
        const files = [] as Express.Multer.File[];
        const error = new Error('Interview not found');

        coreService.findById.mockRejectedValue(error);

        await expect(facade.upload(interviewId, files)).rejects.toThrow(error);
        expect(docsService.upload).not.toHaveBeenCalled();
      });
    });

    describe('addRequiredDocument', () => {
      it('should delegate to docs service', async () => {
        const interviewId = 'interview123';
        const dto: RequiredDocumentDto = { documentName: 'test.pdf' } as any;
        const expected = { added: true };
        docsService.addRequiredDocument.mockResolvedValue(expected as any);

        const result = await facade.addRequiredDocument(interviewId, dto);

        expect(docsService.addRequiredDocument).toHaveBeenCalledWith(interviewId, dto);
        expect(result).toBe(expected);
      });
    });

    describe('removeRequiredDocument', () => {
      it('should delegate to docs service', async () => {
        const interviewId = 'interview123';
        const dto: RequiredDocumentDto = { documentName: 'test.pdf' } as any;
        const expected = { removed: true };
        docsService.removeRequiredDocument.mockResolvedValue(expected as any);

        const result = await facade.removeRequiredDocument(interviewId, dto);

        expect(docsService.removeRequiredDocument).toHaveBeenCalledWith(interviewId, dto);
        expect(result).toBe(expected);
      });
    });

    describe('getAccountOpeningAdvisoryFiles', () => {
      it('should delegate to docs service', async () => {
        const accounts = [{ id: 'account1' }];
        const organisationId = 'org123';
        const expected = ['file1.pdf', 'file2.pdf'];
        docsService.getAccountOpeningAdvisoryFiles.mockResolvedValue(expected as any);

        const result = await facade.getAccountOpeningAdvisoryFiles(accounts, organisationId);

        expect(docsService.getAccountOpeningAdvisoryFiles).toHaveBeenCalledWith(
          accounts,
          organisationId,
        );
        expect(result).toBe(expected);
      });
    });

    describe('getEnvelopeFiles', () => {
      it('should delegate to envelope service', async () => {
        const accountOwnership = 'joint';
        const organisationId = 'org123';
        const primaryContact = { name: 'John' };
        const secondaryContact = { name: 'Jane' };
        const expected = ['file1.pdf', 'file2.pdf'];
        envelopeService.getEnvelopeFiles.mockResolvedValue(expected as any);

        const result = await facade.getEnvelopeFiles(
          accountOwnership,
          organisationId,
          primaryContact,
          secondaryContact,
        );

        expect(envelopeService.getEnvelopeFiles).toHaveBeenCalledWith(
          accountOwnership,
          organisationId,
          primaryContact,
          secondaryContact,
        );
        expect(result).toBe(expected);
      });
    });
  });

  describe('Envelope operations', () => {
    describe('createDocusignEnvelope', () => {
      it('should delegate to envelope service', async () => {
        const interviewId = 'interview123';
        const expected = { envelopeId: 'env123' };
        envelopeService.createDocusignEnvelope.mockResolvedValue(expected as any);

        const result = await facade.createDocusignEnvelope(interviewId, mockSession);

        expect(envelopeService.createDocusignEnvelope).toHaveBeenCalledWith(
          interviewId,
          mockSession,
        );
        expect(result).toBe(expected);
      });
    });

    describe('prepareDocusignEnvelope', () => {
      it('should delegate to envelope service', async () => {
        const interviewId = 'interview123';
        const expected = { prepared: true };
        envelopeService.prepareDocusignEnvelope.mockResolvedValue(expected as any);

        const result = await facade.prepareDocusignEnvelope(interviewId, mockSession);

        expect(envelopeService.prepareDocusignEnvelope).toHaveBeenCalledWith(
          interviewId,
          mockSession,
        );
        expect(result).toBe(expected);
      });
    });
  });

  describe('Notification operations', () => {
    describe('sendDesktopInterviewEmail', () => {
      it('should delegate to notify service', async () => {
        const id = 'interview123';
        const expected = { sent: true };
        notifyService.sendDesktopInterviewEmail.mockResolvedValue(expected as any);

        const result = await facade.sendDesktopInterviewEmail(id);

        expect(notifyService.sendDesktopInterviewEmail).toHaveBeenCalledWith(id);
        expect(result).toBe(expected);
      });
    });

    describe('sendNonCitizenEmail', () => {
      it('should delegate to notify service', async () => {
        const id = 'interview123';
        const expected = { sent: true };
        notifyService.sendNonCitizenEmail.mockResolvedValue(expected as any);

        const result = await facade.sendNonCitizenEmail(id);

        expect(notifyService.sendNonCitizenEmail).toHaveBeenCalledWith(id);
        expect(result).toBe(expected);
      });
    });

    describe('sendNotification', () => {
      it('should delegate to notify service', async () => {
        const interviewId = 'interview123';
        const expected = { sent: true };
        notifyService.sendNotification.mockResolvedValue(expected as any);

        const result = await facade.sendNotification(interviewId, mockSession);

        expect(notifyService.sendNotification).toHaveBeenCalledWith(interviewId, mockSession);
        expect(result).toBe(expected);
      });
    });
  });

  describe('Queue operations', () => {
    describe('enqueueFlow', () => {
      it('should delegate to queue service enqueuePageUpdate', async () => {
        const interviewId = 'interview123';
        const dto: UpdateInterviewDto = {} as any;
        const expected = { queued: true };
        queueService.enqueuePageUpdate.mockResolvedValue(expected as any);

        const result = await facade.enqueueFlow(interviewId, dto);

        expect(queueService.enqueuePageUpdate).toHaveBeenCalledWith(interviewId, dto);
        expect(result).toBe(expected);
      });
    });

    describe('enqueueInterviewCompletionFlow', () => {
      it('should delegate to queue service enqueueCompletionFlow', async () => {
        const params: InterviewCompletionParams = {
          interview: {} as any,
          clientId: 'client123',
          organisationId: 'org123',
          advisorId: 'advisor123',
          primaryContact: {} as any,
          primaryAdvisor: {} as any,
          docusignSelected: true,
          isPrimary: true,
        };
        const expected = { queued: true };
        queueService.enqueueCompletionFlow.mockResolvedValue(expected as any);

        const result = await facade.enqueueInterviewCompletionFlow(params);

        expect(queueService.enqueueCompletionFlow).toHaveBeenCalledWith(params);
        expect(result).toBe(expected);
      });
    });

    describe('markInterviewAsComplete', () => {
      it('should delegate to queue service', async () => {
        const interviewId = 'interview123';
        const expected = { marked: true };
        queueService.markInterviewAsComplete.mockResolvedValue(expected as any);

        const result = await facade.markInterviewAsComplete(interviewId, mockSession);

        expect(queueService.markInterviewAsComplete).toHaveBeenCalledWith(
          interviewId,
          mockSession,
        );
        expect(result).toBe(expected);
      });
    });
  });

  describe('Complete workflow', () => {
    const mockInterview: EnrichedInterview = {
      _id: 'interview123',
      envelopeId: 'env123',
      docusignSelected: true,
      isPrimary: true,
      client: {
        _id: 'client123',
        organisationId: 'org123',
        primaryContact: { name: 'John' } as any,
        secondaryContact: { name: 'Jane' } as any,
        primaryAdvisor: { id: 'advisor123' } as any,
      },
    } as any;

    describe('complete', () => {
      it('should execute complete workflow successfully', async () => {
        const id = 'interview123';
        pagesService.validateAllSynced.mockResolvedValue(true);
        coreService.findOne.mockResolvedValue(mockInterview);
        queueService.enqueueCompletionFlow.mockResolvedValue({} as any);

        const result = await facade.complete(id, mockSession);

        expect(pagesService.validateAllSynced).toHaveBeenCalledWith(id);
        expect(coreService.findOne).toHaveBeenCalledWith({ _id: id }, mockSession, true);
        expect(queueService.enqueueCompletionFlow).toHaveBeenCalledWith({
          interview: mockInterview,
          envelopeId: 'env123',
          organisationId: 'org123',
          advisorId: 'advisor123',
          clientId: 'client123',
          primaryContact: mockInterview.client.primaryContact,
          secondaryContact: mockInterview.client.secondaryContact,
          primaryAdvisor: mockInterview.client.primaryAdvisor,
          docusignSelected: true,
          isPrimary: true,
        });
        expect(result).toEqual(mockInterview);
      });

      it('should throw error when pages are not synced', async () => {
        const id = 'interview123';
        pagesService.validateAllSynced.mockResolvedValue(false);

        await expect(facade.complete(id)).rejects.toThrow(
          'Cannot finish interview: Some pages are not synced',
        );
        expect(coreService.findOne).not.toHaveBeenCalled();
        expect(queueService.enqueueCompletionFlow).not.toHaveBeenCalled();
      });

      it('should throw error when interview not found', async () => {
        const id = 'interview123';
        pagesService.validateAllSynced.mockResolvedValue(true);
        coreService.findOne.mockResolvedValue(null);

        await expect(facade.complete(id)).rejects.toThrow('Interview not found');
        expect(queueService.enqueueCompletionFlow).not.toHaveBeenCalled();
      });

      it('should handle interview without secondary contact', async () => {
        const id = 'interview123';
        const interviewWithoutSecondary = {
          ...mockInterview,
          client: {
            ...mockInterview.client,
            secondaryContact: undefined,
          },
        };
        pagesService.validateAllSynced.mockResolvedValue(true);
        coreService.findOne.mockResolvedValue(interviewWithoutSecondary);
        queueService.enqueueCompletionFlow.mockResolvedValue({} as any);

        const result = await facade.complete(id);

        expect(queueService.enqueueCompletionFlow).toHaveBeenCalledWith(
          expect.objectContaining({
            secondaryContact: undefined,
          }),
        );
        expect(result).toEqual(interviewWithoutSecondary);
      });

      it('should propagate errors from service calls', async () => {
        const id = 'interview123';
        const error = new Error('Service error');
        pagesService.validateAllSynced.mockRejectedValue(error);

        await expect(facade.complete(id)).rejects.toThrow(error);
      });
    });

    describe('finish', () => {
      it('should be an alias for complete method', async () => {
        const id = 'interview123';
        pagesService.validateAllSynced.mockResolvedValue(true);
        coreService.findOne.mockResolvedValue(mockInterview);
        queueService.enqueueCompletionFlow.mockResolvedValue({} as any);

        const result = await facade.finish(id, mockSession);

        expect(pagesService.validateAllSynced).toHaveBeenCalledWith(id);
        expect(coreService.findOne).toHaveBeenCalledWith({ _id: id }, mockSession, true);
        expect(result).toEqual(mockInterview);
      });
    });
  });

  describe('Async behavior and promise handling', () => {
    it('should handle concurrent calls correctly', async () => {
      const promises: Promise<any>[] = [];
      coreService.findAll.mockResolvedValue([]);
      pagesService.validateAllSynced.mockResolvedValue(true);
      notifyService.sendDesktopInterviewEmail.mockResolvedValue({} as any);

      // Make multiple concurrent calls
      promises.push(facade.findAll());
      promises.push(facade.validateAllPagesAreSynced('interview1'));
      promises.push(facade.sendDesktopInterviewEmail('interview2'));

      const results = await Promise.all(promises);

      expect(results).toHaveLength(3);
      expect(coreService.findAll).toHaveBeenCalledTimes(1);
      expect(pagesService.validateAllSynced).toHaveBeenCalledTimes(1);
      expect(notifyService.sendDesktopInterviewEmail).toHaveBeenCalledTimes(1);
    });

    it('should handle promise rejections properly', async () => {
      const error1 = new Error('Error 1');
      const error2 = new Error('Error 2');
      coreService.findAll.mockRejectedValue(error1);
      pagesService.validateAllSynced.mockRejectedValue(error2);

      await expect(facade.findAll()).rejects.toThrow(error1);
      await expect(facade.validateAllPagesAreSynced('interview1')).rejects.toThrow(error2);
    });
  });

  describe('Public API exposure', () => {
    it('should expose pages service as public property', () => {
      expect(facade.pages).toBe(pagesService);
    });
  });

  describe('Facade behavior verification', () => {
    it('should not add any business logic, only orchestration', () => {
      // Verify that the facade is purely a delegator by checking
      // that all methods simply forward calls without modification
      // This is proven by all the tests above showing direct delegation
      expect(true).toBe(true);
    });

    it('should maintain correct method signatures', () => {
      // Verify methods exist and are functions
      expect(typeof facade.create).toBe('function');
      expect(typeof facade.find).toBe('function');
      expect(typeof facade.findAll).toBe('function');
      expect(typeof facade.findOne).toBe('function');
      expect(typeof facade.findById).toBe('function');
      expect(typeof facade.remove).toBe('function');
      expect(typeof facade.updatePages).toBe('function');
      expect(typeof facade.updateInterviewPages).toBe('function');
      expect(typeof facade.addPageToInterview).toBe('function');
      expect(typeof facade.removePageFromInterview).toBe('function');
      expect(typeof facade.validateAllPagesAreSynced).toBe('function');
      expect(typeof facade.upload).toBe('function');
      expect(typeof facade.addRequiredDocument).toBe('function');
      expect(typeof facade.removeRequiredDocument).toBe('function');
      expect(typeof facade.getAccountOpeningAdvisoryFiles).toBe('function');
      expect(typeof facade.getEnvelopeFiles).toBe('function');
      expect(typeof facade.createDocusignEnvelope).toBe('function');
      expect(typeof facade.prepareDocusignEnvelope).toBe('function');
      expect(typeof facade.sendDesktopInterviewEmail).toBe('function');
      expect(typeof facade.sendNonCitizenEmail).toBe('function');
      expect(typeof facade.sendNotification).toBe('function');
      expect(typeof facade.enqueueFlow).toBe('function');
      expect(typeof facade.enqueueInterviewCompletionFlow).toBe('function');
      expect(typeof facade.markInterviewAsComplete).toBe('function');
      expect(typeof facade.complete).toBe('function');
      expect(typeof facade.finish).toBe('function');
    });
  });
});