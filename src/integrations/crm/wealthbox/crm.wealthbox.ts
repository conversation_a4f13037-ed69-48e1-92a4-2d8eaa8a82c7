import { HttpException, HttpStatus } from '@nestjs/common';
import { isEmpty } from 'lodash';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import { OAuth } from 'src/auth/auth.types';
import { ContactDto } from 'src/clients/dto/v1/create-client.dto';
import {
  EnrichedClient,
  EnrichedContact,
} from 'src/clients/dto/v1/get-clients.dto';
import { CRM, PageUpdateMapper } from 'src/integrations/crm/crm.interface';
import { GenericCrmAccount } from 'src/integrations/crm/types/accounts/crm-account.type';
import { DBAccountType } from 'src/integrations/crm/types/accounts/db-account-type.enum';
import { LogCommunicationDto } from 'src/integrations/crm/types/communications/log-communication.dto';
import { CrmContact } from 'src/integrations/crm/types/contacts/crm-contact.type';
import {
  Create<PERSON>ontactDto,
  CreateContactResponseDto,
  UpdateContactDto,
  UpdateContactResponseDto,
} from 'src/integrations/crm/types/create-contact.dto';
import { GenericEmail } from 'src/integrations/crm/types/generic/email.type';
import { GenericPhone } from 'src/integrations/crm/types/generic/phone.type';
import { CrmPageInfoUpdateDto } from 'src/integrations/crm/types/page-info-update.dto';
import { User } from 'src/integrations/crm/types/user.type';
import { buildAccountCreationTaskDetails } from 'src/integrations/crm/utils/build-account-task';
import { WealthboxAdapter } from 'src/integrations/crm/wealthbox/crm.wealthbox.adapter';
import { WealthboxEmailAddressKind } from 'src/integrations/crm/wealthbox/dto/email.dto';
import { WealthboxPhoneNumberKind } from 'src/integrations/crm/wealthbox/dto/phone.dto';
import { wealthboxMappers } from 'src/integrations/crm/wealthbox/form-mappers';
import { WealthboxContactService } from 'src/integrations/crm/wealthbox/services/contact.service';
import { HouseholdService } from 'src/integrations/crm/wealthbox/services/household.service';
import { WealthboxNotesService } from 'src/integrations/crm/wealthbox/services/notes.service';
import { WealthboxTaskService } from 'src/integrations/crm/wealthbox/services/task.service';
import { WealthboxContactType } from 'src/integrations/crm/wealthbox/types';
import { WealthboxContact } from 'src/integrations/crm/wealthbox/types/contact.type';
import {
  contactToYAML,
  parseDataNoteYAML,
} from 'src/integrations/crm/wealthbox/utils/data-parsing';
import { formatDate } from 'src/integrations/crm/wealthbox/utils/date-converter';
import { emailAddressKindToEmailAddressType } from 'src/integrations/crm/wealthbox/utils/email-address-kind-mapper';
import { phoneNumberKindToPhoneType } from 'src/integrations/crm/wealthbox/utils/phone-kind-mapper';
import { WealthboxService } from 'src/integrations/crm/wealthbox/wealthbox.service';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { InterviewsService } from 'src/interviews/interviews.service';
import { MailService } from 'src/notifications/mail/mail.service';
import { SendEmailOptions } from 'src/notifications/mail/mail.types';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';
import { CRMEnum , CRMType } from 'src/shared/types/integrations';
import { Logger } from 'winston';
import { ContactRolesNameEnum } from './types/contact-role-names.enum';
import { WealthboxCustomizableCategoryType } from './types/customizable-category-type.enum';
import { CrmAuthException } from 'src/shared/exceptions/crm-auth.exception';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { OperationType } from 'src/super-http/enums';

export class WealthboxCRM implements CRM {
  pageUpdateMapper: PageUpdateMapper;
  protected readonly type: CRMEnum = CRMEnum.Wealthbox;
  protected baseUrl: string;
  public contactService: WealthboxContactService;
  public notesService: WealthboxNotesService;
  public taskService: WealthboxTaskService;
  public householdService: HouseholdService;
  private readonly adapter = new WealthboxAdapter();
  protected tenantId: string;
  constructor(
    protected advisor: AdvisorWithRole,
    protected organisation: Organisation,
    protected credentials: OAuth,
    private readonly superHttp: SuperHttpService,
    public interviewsService: InterviewsService,
    protected emailService: MailService,
    protected logger: Logger,
  ) {
    this.baseUrl = process.env.WEALTHBOX_BASE_URL;
    this.tenantId = this.organisation._id.toString();
    this.initMappers();
  }

  initCrm(): Promise<Map<string, string | number>> {
    return Promise.resolve(new Map());
  }

  private getCredentialsHeader() {
    return {
      Authorization: `Bearer ${this.credentials.accessToken}`,
      'Content-Type': 'application/json',
    };
  }

  private intializeServices() {
    this.contactService = new WealthboxContactService(
      this.superHttp,
      this.getCredentialsHeader(),
      this.logger,
      this.tenantId,
    );

    this.notesService = new WealthboxNotesService(
      this.superHttp,
      this.getCredentialsHeader(),
      this.logger,
      this.tenantId,
    );

    this.taskService = new WealthboxTaskService(
      this.superHttp,
      this.getCredentialsHeader(),
      this.logger,
      this.tenantId,
    );

    this.householdService = new HouseholdService(
      this.superHttp,
      this.getCredentialsHeader(),
      this.logger,
      this.tenantId,
    );
  }

  async authenticate(): Promise<OAuth> {
    let shouldUnlink: boolean;
    try {
      const refreshToken = this.credentials.refreshToken;
      const lastRefresh = this.credentials.lastRefresh;
      const lastRefreshTime = new Date(lastRefresh).getTime();
      const oneHourAgo = Date.now() - 1800000;
      const advisorId = this.advisor?._id?.toString();

      if (!advisorId) {
        throw new HttpException(
          'Advisor not found',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
      if (lastRefreshTime < oneHourAgo) {
        const tokens = await this.superHttp.post<any>(
          `${process.env.WEALTHBOX_BASE_AUTH_URL}/oauth/token`,
          this.tenantId,
          {
            grant_type: 'refresh_token',
            refresh_token: refreshToken,
            redirect_uri: WealthboxService.getRedirectUri(advisorId),
            client_id: process.env.WEALTHBOX_CLIENT_ID,
            client_secret: process.env.WEALTHBOX_CLIENT_SECRET,
          },
          { rateLimiter: 'wealthbox', integrationName: 'wealthbox', entityType: 'me', operation: OperationType.GET, forceFresh: true },
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        );

        this.credentials = {
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token,
          lastRefresh: new Date(),
        };

        return {
          accessToken: tokens.access_token,
          refreshToken: tokens.refresh_token,
          lastRefresh: new Date(),
          shouldRefresh: true,
        };
      }
      return {
        ...this.credentials,
        shouldRefresh: false,
      };
    } catch (error) {
      const status = error.response?.status || error?.status || error?.code;

      shouldUnlink = true; // Might change if Wealthbox throws errors non related to Auth issues
      this.logger.error(
        `Error getting tokens from Wealthbox: ${
          error.response?.data ? JSON.stringify(error.response?.data) : error
        }`,
      );
      throw new CrmAuthException(
        error.message,
        status,
        shouldUnlink,
      );
    } finally {
      this.intializeServices();
    }
  }

  async sendEmail(
    emailOptions: SendEmailOptions,
    contact?: EnrichedContact,
    advisorId?: string,
  ) {
    // Ensure organisation is included in emailOptions
    const emailOptionsWithOrg = {
      ...emailOptions,
      organisation: this.organisation
    };
    return this.emailService.sendEmail(emailOptionsWithOrg, contact, advisorId);
  }

  async createContact(
    createContactDto: CreateContactDto,
  ): Promise<CreateContactResponseDto> {
    const { primaryContact, secondaryContact, primaryAdvisor, primaryCSA } =
      createContactDto;
    const secondaryContactFullName = secondaryContact
      ? `& ${secondaryContact.firstName} ${secondaryContact.lastName}`
      : '';
    // Append secondary contact if exists
    const householdName = `${createContactDto.primaryContact.firstName} ${createContactDto.primaryContact.lastName} ${secondaryContactFullName} Household`;
    let secondaryContactCrm: WealthboxContact;
    const primaryContactCrm = await this.createCrmContact(
      primaryContact,
      primaryAdvisor,
      primaryCSA,
      householdName,
      true,
    );

    if (secondaryContact) {
      secondaryContactCrm = await this.createCrmContact(
        secondaryContact,
        primaryAdvisor,
        primaryCSA,
        householdName,
        false,
      );
    }

    await Promise.all([
      this.createAccountCreationTasks(
        primaryContactCrm.id.toString(),
        primaryContact,
        primaryAdvisor,
        primaryCSA,
      ),
      secondaryContactCrm &&
        this.createAccountCreationTasks(
          secondaryContactCrm.id.toString(),
          secondaryContact,
          primaryAdvisor,
          primaryCSA,
        ),
    ]);

    return {
      primaryContactCrmId: primaryContactCrm.id.toString(),
      secondaryContactCrmId:
        secondaryContactCrm && secondaryContactCrm.id.toString(),
    };
  }

  async updateContact(
    contactInfo: UpdateContactDto,
  ): Promise<UpdateContactResponseDto> {
    const householdName = `${contactInfo.primaryContact.firstName} ${contactInfo.primaryContact.lastName} Household`;
    const { crmClientId: primaryContactCrmId } = contactInfo.primaryContact;
    let { crmClientId: secondaryContactCrmId } =
      contactInfo.secondaryContact || {};
    const { primaryContact, secondaryContact, primaryAdvisor, primaryCSA } =
      contactInfo;

    // Update or create primary and secondary contacts
    await this.upsertCrmContact(
      primaryContact,
      primaryContactCrmId,
      householdName,
      true,
      primaryAdvisor,
      primaryCSA,
    );

    if (secondaryContact) {
      secondaryContactCrmId =
        secondaryContactCrmId &&
        (
          await this.upsertCrmContact(
            secondaryContact,
            secondaryContactCrmId,
            householdName,
            false,
            primaryAdvisor,
            primaryCSA,
          )
        )?.id?.toString();
    }

    // Upsert notes and create account creation tasks
    await Promise.all([
      this.upsertNotesAndCreateTasks(
        primaryContactCrmId,
        primaryContact,
        primaryAdvisor,
        primaryCSA,
      ),
      secondaryContact &&
        secondaryContactCrmId &&
        this.upsertNotesAndCreateTasks(
          secondaryContactCrmId,
          secondaryContact,
          primaryAdvisor,
          primaryCSA,
        ),
    ]);

    return {
      primaryContactCrmId: primaryContactCrmId.toString(),
      secondaryContactCrmId: secondaryContactCrmId
        ? secondaryContactCrmId.toString()
        : null,
    };
  }

  private async createAccountCreationTasks(
    crmId: string,
    contact: ContactDto,
    _primaryAdvisor: AdvisorWithRole, // Unused parameter, prefixed with underscore
    primaryCSA: AdvisorWithRole = undefined,
  ) {
    if (!contact?.accounts) return;
    return Promise.all(
      contact?.accounts?.map(async (account) => {
        const taskDetails = buildAccountCreationTaskDetails(account, contact);
        return this.taskService.createTask({
          due_date: formatDate(new Date()),
          name: taskDetails.subject,
          description: taskDetails.description,
          assigned_to: Number(primaryCSA.crmId),
          linked_to: [
            {
              id: Number(crmId),
              type: 'Contact',
            },
          ],
        });
      }),
    );
  }

  private async upsertCrmContact(
    contact: ContactDto,
    crmId: string,
    householdName: string,
    isPrimary: boolean,
    primaryAdvisor: AdvisorWithRole,
    primaryCSA: AdvisorWithRole,
  ) {
    if (crmId) {
      return this.updateCrmContact(contact, householdName, isPrimary);
    } else {
      const newContact = await this.createCrmContact(
        contact,
        primaryAdvisor,
        primaryCSA,
        householdName,
        isPrimary,
      );

      if (!newContact?.id) {
        throw new HttpException('Error creating contact in Wealthbox', 500);
      }

      return newContact;
    }
  }

  private async upsertNotesAndCreateTasks(
    crmId: string,
    contact: ContactDto,
    primaryAdvisor: AdvisorWithRole,
    primaryCSA: AdvisorWithRole,
  ) {
    await this.notesService.upsertDataNote(crmId, {
      content: `${contactToYAML(contact)}`,
      linked_to: [{ id: Number(crmId), type: 'Contact' }],
      tags: ['do_not_delete'],
    });
    await this.createAccountCreationTasks(
      crmId,
      contact,
      primaryAdvisor,
      primaryCSA,
    );
  }

  public async createCrmContact(
    contact: ContactDto,
    primaryAdvisor: AdvisorWithRole,
    primaryCSA: AdvisorWithRole,
    householdName: string,
    isPrimary: boolean,
  ): Promise<WealthboxContact> {
    const advisorRole = await this.getContactRoleByName(
      ContactRolesNameEnum.Advisor,
    );
    const csaRole = await this.getContactRoleByName(ContactRolesNameEnum.CSA);

    const contactCrm = await this.contactService.createContact(
      this.adapter.mapCreateContactDtoToCrmFormat(
        contact,
        householdName,
        isPrimary,
        Number(primaryAdvisor.crmId),
        Number(primaryCSA.crmId),
        advisorRole,
        csaRole,
      ),
    );

    await this.notesService.createNote({
      content: `${contactToYAML(contact)}`,
      linked_to: [
        {
          id: contactCrm.id,
          type: 'Contact',
        },
      ],
      tags: ['do_not_delete'],
    });
    return contactCrm;
  }

  public async updateCrmContact(
    contact: ContactDto,
    _householdName: string, // Unused parameter, prefixed with underscore
    _isPrimary: boolean, // Unused parameter, prefixed with underscore
  ): Promise<WealthboxContact> {
    const contactCrm = await this.contactService.getContact(
      contact.crmClientId,
    );

    const contactExistingEmail = contactCrm.email_addresses.find(
      (email) => email.principal || email.address === contact.email,
    );

    const contactExistingPhone = contactCrm.phone_numbers.find(
      (phone) => phone.principal || phone.address === contact.mobile,
    );

    const updatedContact = await this.contactService.updateContact(
      contact.crmClientId,
      {
        first_name: contact.firstName,
        last_name: contact.lastName,
        email_addresses: [
          {
            id: contactExistingEmail?.id,
            address: contact.email,
            principal: true,
            kind: WealthboxEmailAddressKind.Home,
          },
        ],
        phone_numbers: [
          {
            id: contactExistingPhone?.id,
            address: contact?.mobile?.replace('+1', ''),
            kind: WealthboxPhoneNumberKind.Mobile,
            principal: true,
            extension: '+1',
          },
        ],
      },
    );

    await this.notesService.upsertDataNote(contact.crmClientId, {
      content: `${contactToYAML(contact)}`,
      linked_to: [{ id: Number(contact.crmClientId), type: 'Contact' }],
      tags: ['do_not_delete'],
    });

    return updatedContact;
  }

  private async getContactRoleByName(name: ContactRolesNameEnum) {
    const roles = await this.contactService.listCustomizableCategories(
      WealthboxCustomizableCategoryType.ContactRoles,
    );
    const tags = roles?.contact_roles;

    if (isEmpty(tags)) {
      return null;
    }

    return tags.find((tag) => tag.name === name);
  }

  
  /**
   * Retrieves a contact from the CRM system.
   * @param id - The ID of the contact.
   * @param includeAccounts - Whether to include the contact's accounts.
   * @param includeAdditionalInfo - Whether to include additional information about the contact.
   * @returns A promise that resolves to a CrmContact object representing the contact.
   */
  async getContact(
    id: string,
    includeAccounts: boolean,
    _includeAdditionalInfo: boolean, // Unused parameter, prefixed with underscore
  ): Promise<CrmContact> {
    const crmContact = await this.superHttp.get<WealthboxContact>(
      `${this.baseUrl}/contacts/${id}`,
      this.tenantId,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', entityType: 'contact', operation: OperationType.GET },
      { headers: this.getCredentialsHeader() }
    );
    const accountId = await this.getAuthenticatedUserAccountId();
    return this.adapter.mapContact(
      crmContact,
      includeAccounts ? await this.getAccounts(id) : [],
      accountId,
    );
  }

  /**
   * Retrieves the accounts associated with a CRM client.
   * @param crmClientId The ID of the CRM client.
   * @returns A promise that resolves to an array of GenericCrmAccount objects.
   * @throws Error if no account data note is found.
   */
  public async getAccounts(crmClientId: string): Promise<GenericCrmAccount[]> {
    const notes = await this.notesService.listNotes({
      resource_id: Number(crmClientId),
      resource_type: 'Contact',
    });

    const accountDataNote = notes?.status_updates?.find((note) =>
      note.tags.find((tag) => tag.name === 'do_not_delete'),
    );

    if (!accountDataNote) {
      throw new Error('No account data note found');
    }

    const accountData = parseDataNoteYAML(accountDataNote.content);

    if (!accountData?.accounts) return [];

    return accountData.accounts?.map((account) => ({
      name: account.name,
      type: DBAccountType[account.type],
      beneficiaries: account.beneficiaries,
    }));
  }

  /**
   * Retrieves a list of active contacts from the CRM.
   *
   * @param pagination - Optional pagination parameters.
   * @returns A promise that resolves with the list of contacts.
   */
  async getContacts(pagination?: PaginationQueryDto): Promise<CrmContact[]> {
    const accountId = await this.getAuthenticatedUserAccountId();
    const contacts = await this.contactService.listContacts(
      {
        active: true,
        type: WealthboxContactType.Person,
        name: pagination?.search,
      },
      {
        page: pagination?.page,
        per_page: pagination?.limit,
      },
    );
    if (!contacts) return [];

    return contacts.contacts.map((contact) =>
      this.adapter.mapContact(contact, [], accountId),
    );
  }

  /**
   * Retrieves all users from the CRM.
   * @param pagination - Optional pagination query parameters.
   * @returns A promise that resolves to an array of User objects.
   */
  async getAllUsers(_pagination?: PaginationQueryDto): Promise<User[]> { // Unused parameter, prefixed with underscore
    throw new Error('No API available');
  }

  /**
   * Retrieves the type of CRM integration.
   * @returns A promise that resolves to either 'Redtail' or 'Wealthbox'.
   */
  async getType(): Promise<CRMType> {
    return this.type;
  }

  /**
   * Retrieves the main email address of a contact.
   * @param id - The ID of the contact.
   * @returns A promise that resolves to a GenericEmail object containing the main email address and its type.
   */
  async getMainEmailOfContact(id: string | number): Promise<GenericEmail> {
    const contact = await this.contactService.getContact(id.toString());
    const mainEmail =
      contact.email_addresses.find((email) => email.principal) ||
      contact.email_addresses[0];
    if (!mainEmail) return null;
    return {
      address: mainEmail.address,
      type: emailAddressKindToEmailAddressType(mainEmail.kind),
    };
  }

  /**
   * Retrieves the phone numbers of a contact.
   * @param id - The ID of the contact.
   * @returns A promise that resolves to an array of GenericPhone objects representing the phone numbers of the contact.
   */
  async getPhoneNumbersOfContact(id: string | number): Promise<GenericPhone[]> {
    const contact = await this.contactService.getContact(id.toString());
    if (!contact.phone_numbers) return [];
    return contact.phone_numbers?.map((phone) => ({
      number: phone.address,
      type: phoneNumberKindToPhoneType(phone.kind),
      ext: phone.extension,
      isPrimary: true,
    }));
  }

  /**
   * Retrieves the main mobile phone number of a contact.
   * @param id - The ID of the contact.
   * @returns A Promise that resolves to the main mobile phone number of the contact.
   */
  async getMainMobileOfContact(id: string): Promise<string> {
    const contact = await this.contactService.getContact(id);
    const mainPhone =
      contact.phone_numbers.find((phone) => phone.principal) ||
      contact.phone_numbers[0];
    const adptedPhone = this.adapter.mapPhone(mainPhone);
    return adptedPhone?.number;
  }

  /**
   * Updates the page information for a given interview, client, organisation and page update DTO.
   * @param interview - The interview object.
   * @param client - The client object.
   * @param organisation - The organisation object.
   * @param update - The page update DTO.
   * @returns The mapped page data.
   */
  public updatePageInfo(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    update: CrmPageInfoUpdateDto,
  ) {
    return this.pageUpdateMapper[update.pageName].mapPageData(
      interview,
      client,
      organisation,
      update.pageData,
    );
  }

  /**
   * Retrieves the authenticated user's ID.
   * @returns A Promise that resolves to a string representing the authenticated user's ID.
   */
  async getAuthenticatedUserId(): Promise<string> {
    const me = await this.superHttp.get<any>(
      `${this.baseUrl}/me`,
      this.tenantId,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', entityType: 'me', operation: OperationType.GET, cacheKeysObject: { token: this.getCredentialsHeader().Authorization } },
      { headers: this.getCredentialsHeader() }
    );
    return me.current_user.id;
  }

  /**
   * Retrieves the ID of the authenticated user's account. This is wealthbox specific
   * @returns A Promise that resolves to a string representing the account ID.
   */
  private async getAuthenticatedUserAccountId(): Promise<string> {
    const me = await this.superHttp.get<any>(
      `${this.baseUrl}/me`,
      this.tenantId,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', entityType: 'me', operation: OperationType.GET, cacheKeysObject: { token: this.getCredentialsHeader().Authorization } },
      { headers: this.getCredentialsHeader() }
    );
    return me.current_user.account;
  }

  /**
   * Initializes the mappers for the Wealthbox CRM integration.
   *
   * @returns {PageUpdateMapper} The initialized PageUpdateMapper.
   */
  initMappers(): PageUpdateMapper {
    this.pageUpdateMapper = wealthboxMappers.bind(this)();
    return this.pageUpdateMapper;
  }

  /**
   * Logs a communication for a CRM client.
   * @param crmClientId - The ID of the CRM client.
   * @param logCommunicationDto - The data for the communication log.
   * @returns A Promise that resolves to void.
   */
  async logCommunication(
    crmClientId: string,
    logCommunicationDto: LogCommunicationDto,
  ): Promise<void> {
    const { communicationType, communicationDetails } = logCommunicationDto;
    await this.notesService.createNote({
      content: `${communicationType}: ${communicationDetails}`,
      linked_to: [
        {
          id: Number(crmClientId),
          type: 'Contact',
        },
      ],
      tags: ['communication log'],
    });
  }
}
