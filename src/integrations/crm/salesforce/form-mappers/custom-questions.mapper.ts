import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { SalesforceCRM } from 'src/integrations/crm/salesforce/crm.salesforce';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

interface CustomQuestion {
  question: {
    question: string,
    type: string,
    options: [],
    required: boolean
  };
  answer: string;
}

export class SalesforceCustomQuestionsMapper implements FormUpdateMapper {
  constructor(private salesforceCRM: SalesforceCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: CustomQuestion,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const connectedQuestions = await this.salesforceCRM.customAccountService.getLinkedCustomQuestions(contact.crmClientId);
    const questionRecordId = connectedQuestions.records.find(el => el.fields.Custom_Question__r?.value.fields.Question__c?.value === pageData.question.question)?.fields.Custom_Question__r?.value.id;

    if (!questionRecordId) {
      const customQuestion = await this.salesforceCRM.customAccountService.createCustomQuestion({
        Question__c: pageData.question.question,
        Answer__c: pageData.answer,
      });
  
      await this.salesforceCRM.customAccountService.linkQuestionToContact(customQuestion.id, contact.crmClientId);
    }
    if (questionRecordId) {
      await this.salesforceCRM.customAccountService.updateCustomQuestion({
        Question__c: pageData.question.question,
        Answer__c: pageData.answer,
      }, questionRecordId);
    }
  }
} 