variable "name" {
  description = "The name of the Elastic Beanstalk application."
}

variable "origin" {
  description = "The origin for the API"
}

variable "solution_stack_name" {
  description = "The solution stack name for the Elastic Beanstalk environment."
}

variable "instance_type" {
  description = "The instance type for the Elastic Beanstalk environment."
}

variable "environment" {
  description = "The environment (staging, production etc)."
}

variable "commit_sha" {
  description = "The commit SHA to use for the Elastic Beanstalk application version."
  type        = string
}

variable "source_bundle_bucket" {
  description = "The S3 bucket containing the source bundle for the application"
  type        = string
}

variable "source_bundle_key" {
  description = "The S3 object key of the source bundle for the application"
  type        = string
}

variable "min_size" {
  description = "The minimum size of the auto scaling group"
  type        = number
}

variable "max_size" {
  description = "The maximum size of the auto scaling group"
  type        = number
}

variable "app_port" {
  description = "The port the application is listening on"
  type        = string
  default     = "3000"
}

variable "certificate_arn" {
  description = "The ARN of the certificate to use for HTTPS"
  type        = string
}

variable "ssm_json" {
  description = "The JSON string to pass to the application as an SSM parameter"
  type        = string
  default     = "{}"
}


variable "healthcheck_url" {
  description = "The URL path for the health check"
  type        = string
  default     = "/health"
}

variable "elastic_beanstalk_security_group_id" {
  description = "The security group ID for the Elastic Beanstalk environment"
  type        = string
}

variable "elastic_beanstalk_security_group_name" {
  description = "The security group name for the Elastic Beanstalk environment"
  type        = string
}

variable "cognito_user_pool_id" {
  description = "The ID of the Cognito user pool"
  type        = string
}

variable "cognito_user_pool_client_id" {
  description = "The ID of the Cognito user pool client"
  type        = string
}

variable "aws_region" {
  description = "The AWS region where resources exist"
  type        = string
  default     = "us-east-1"
}

variable "vpc_id" {
  description = "The VPC ID where resources exist"
  type        = string
}

variable "encryption_key_id" {
  description = "The ARN of the KMS key to use for encryption"
  type        = string
}

variable "domain_name" {
  description = "The domain name for the application"
  type        = string
}

variable "work_env" {
  description = "The environment (staging, production)."
  type        = string
}

variable "org_docs_bucket_name" {
  description = "The name of the S3 bucket for organisation documents"
  type        = string
}

variable "redis_host" {
  description = "Redis host"
  type        = string
}

variable "redis_port" {
  description = "Redis port"
  type        = string
}

variable "redis_password" {
  description = "Redis password"
  type        = string
}

variable "private_subnet_id" {
  description = "The ID of the private subnet"
  type        = string
}

variable "public_subnet_id" {
  description = "The ID of the public subnet"
  type        = string
}
