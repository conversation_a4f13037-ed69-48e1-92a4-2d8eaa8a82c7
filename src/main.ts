import { NestFactory } from '@nestjs/core';
import { AppModule } from './app/app.module';
import { ValidationPipe, VersioningType } from '@nestjs/common';
import { AllExceptionsFilter } from './utils/filters/exceptions.filter';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import cookieParser from 'cookie-parser';
import { WINSTON_MODULE_NEST_PROVIDER, WinstonModule } from 'nest-winston';
import helmet from 'helmet';
import winstonConfig from './winston.config';
import { json } from 'express';
import 'newrelic';

async function bootstrap() {
  try {
    const app = await NestFactory.create(AppModule, {
      bufferLogs: true,
      logger: WinstonModule.createLogger(winstonConfig),
    });
    
    const logger = app.get(WINSTON_MODULE_NEST_PROVIDER);
    const port = process.env.PORT || 3000;

    app.use(helmet());
    app.enableCors({
      origin: process.env.CORS_ORIGIN
        ? new RegExp(process.env.CORS_ORIGIN)
        : 'http://localhost:8080',
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
      credentials: true,
    });
    app.useGlobalPipes(
      new ValidationPipe({ transform: true, forbidNonWhitelisted: true }),
    );
    
    // Enable API versioning
    app.enableVersioning({
      type: VersioningType.URI,
      prefix: 'v',
    });
    
    app.use(json({ limit: '50mb' }));
    app.useGlobalFilters(new AllExceptionsFilter(logger));
    app.use(cookieParser());
    app.useLogger(logger);

    const config = new DocumentBuilder()
      .setTitle('Onbord API')
      .setDescription('Onbord API description')
      .setVersion('1.0')
      .addTag('onbord')
      .addBearerAuth()
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api', app, document);

    // Add shutdown hooks
    app.enableShutdownHooks();

    await app.listen(port, '0.0.0.0', () => {
      logger.log(`Application successfully started on port: ${port}`);
      logger.log(`Environment: ${process.env.WORK_ENV}`);
      logger.log(`MongoDB URL: ${process.env.DATABASE_URL?.split('@')[1]}`); // Log only the host part
      logger.log(`Redis Host: ${process.env.REDIS_HOST}`);
    });

  } catch (error) {
    console.error('Failed to start application:', error);
    process.exit(1);
  }
}

process.on('unhandledRejection', (error: Error) => {
  console.error('Unhandled rejection:', error);
  process.exit(1);
});

process.on('uncaughtException', (error: Error) => {
  console.error('Uncaught exception:', error);
  process.exit(1);
});

bootstrap();