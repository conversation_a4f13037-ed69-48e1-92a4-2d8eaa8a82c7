# OnBord Backend Platform

This project serves as the backend platform for OnBord, an API that simplifies the onboarding process for financial advisors by integrating multiple CRMs, electronic signature systems, and various financial custodians.

[Discovery Miro](https://miro.com/app/board/uXjVMRCrlWM=/?moveToWidget=3458764567726303428&cot=14)
[Architecture Miro]()

## Table of Contents

- [Technologies Used](#technologies-used)
- [Project Structure](#project-structure)
  - [Controller-Service-Repository Pattern](#controller-service-repository-pattern)
  - [Modules](#modules)
  - [Integrations](#integrations)
    - [CRMs](#crms)
    - [Electronic Signatures](#electronic-signatures)
- [Installation](#installation)
- [Running the App](#running-the-app)
- [Tests](#tests)
- [Infrastructure Readme](./infra/README.md)

## Technologies Used

- [asdf](https://asdf-vm.com/) for managing package versions
- [Docker](https://www.docker.com/) for virtualization
- [Node.js](https://nodejs.org/en) as the TypeScript runtime environment
- [Terraform](https://www.terraform.io/) as IaC provider
- [Nest.js](https://nestjs.com/) as the backend framework
- [TypeScript](https://www.typescriptlang.org/) as the programming language
- [Passport.js](https://docs.nestjs.com/recipes/passport) for authentication middleware
- [MongoDB](https://www.mongodb.com/docs/) as the database
- [Mongoose](https://mongoosejs.com/docs) as the ODM
- [JWT](https://jwt.io/) as the default authentication strategy
- [Nodemailer](https://www.npmjs.com/package/nodemailer) for email service integrations
- [Twilio](https://www.twilio.com/en-us) for SMS service integrations
- [DocuSign](https://docusign.github.io/docusign-esign-node-client/) for document e-signatures

## Project Structure

This project follows a Domain-Driven Design (DDD) approach to API development, with the following structure:

| Directory             | Description                                                                  |
| --------------------- | ---------------------------------------------------------------------------- |
| .github/workflows     | CD process.                                                                  |
| test                  | All Test files                                                               |
| src                   | All Source files                                                             |
| src/app               | Contains the App module wrapper                                              |
| src/integrations      | All files relating to the implementation of third party integration services |
| src/notifications     | Allfiles relating to the notifications service                               |
| src/templates         | All SMS or Email template files                                              |
| src/[bounded-context] | All files containing business logic for a given domain of the application    |
| src/advisors          | All services and modules related to the advisors domain                      |
| src/clients           | All services related to the clients domain                                   |

### Controller-Service-Repository pattern

The Controller-Service-Repository pattern is a common design pattern in Nest.js. It separates the concerns of handling HTTP requests, business logic, and data access into separate layers, making the codebase more modular and easier to maintain.

#### Controller

The controller layer is responsible for handling incoming HTTP requests and returning HTTP responses. It receives input from the client, validates it, and passes it to the service layer for processing. The controller layer is also responsible for mapping the service layer's output to an HTTP response format.

#### Service

The service layer contains the business logic of the application. It receives input from the controller layer, processes it, and returns the output to the controller layer. The service layer is responsible for implementing the application's use cases and enforcing business rules.

#### Repository

The repository layer is responsible for data access. It provides an abstraction over the database and allows the service layer to interact with the database without knowing the implementation details. The repository layer is responsible for querying the database, transforming the data into domain objects,and returning the results to the service layer.

### Modules

In Nest.js, modules are used to organize the codebase into cohesive units of functionality. Each module encapsulates a set of related controllers, services, and providers and is used to manage dependencies between different parts of the application.

Modules are defined using the `@Module()` decorator, which takes an options object specifying the controllers, services, and providers included in the module.
Modules can also be used to manage dependencies between different parts of the application. For example, if a service in one module depends on a service in another module, we can use the imports property of the options object to include the other module as a dependency.

### Integrations

#### CRMs

- [Redtail](https://corporate.redtailtechnology.com/crm/)
- [WealthBox](https://www.wealthbox.com/)
- [Salesforce](https://www.salesforce.com/eu/)
- [Practifi](https://www.practifi.com/)

#### Electronic Signatures

- [DocuSign](https://developers.docusign.com/platform/)

## Installation

To install this project, ensure you have installed the necessary tools and dependencies:

1. [Install asdf](https://asdf-vm.com/guide/getting-started.html)
2. [Install Docker](https://docs.docker.com/desktop/install/mac-install/)
3. [Install Postman](https://www.postman.com/downloads/) (nice to have)

## Seed Data

```bash
npm run seed
```

This will spin up a new container that will execute the nestjs-seeder configured in our application. This will create a new organization, new advisors according to what is defined in `src/advisors/seeders/advisors.json`and associate them to the newly created organisation.
For you to be able to login into the application with the seeded advisors, these must already be configured in cognito.

## Running the App

To run this project, copy the `.env.example` to `.env` and fill out the values as needed.
Since we are using Docker, we can run the following command to install all dependencies and launch a container with all the necessary dependencies for the API:

```bash
npm run start:dev
```

After this, your API will be available under `http://localhost:3000` and you can check the status of the containers via portainer at `https://localhost:9443`.

You may also seed the Atlas database dynamicaly via CLI, this populates the local application with all relevant 
data to start basic workflows:

```bash
# seed application
$ npm run seed
```

## Linting and Formatting

To ensure an alignment between developers during the development lifecycle we configured ESLint as a linting tool, and we integrated Prettier as a plugin for this linting tool for the sake of simplicity. All the configuration for linting and formatting can be checked and modified on the following file `eslintrc.js`.

We also have a script on `package.json` to run all the fixes for every file in the project, so all you need to do is:

```bash
# lint and format code
$ npm run lint 
```

But you don't need to worry too much with it because everytime you do a commit this script will run and fix your code. This setup was done with `lefthooks` package.

## Tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```
