import { Injectable } from '@nestjs/common';
import { Connection, Record as SalesforceRecord } from 'jsforce';
import { LogCommunicationDto } from '../../types/communications/log-communication.dto';
import { SendEmailOptions } from 'src/notifications/mail/mail.types';
import { EnrichedContact } from 'src/clients/dto/v1/get-clients.dto';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { generateRequestConfigs } from '../utils/generate-request-configs.util';
import { salesforceQuery, formatName } from '../utils/salesforce-query.util';
import { SalesforceEntityTypes , TaskStatus, TaskPriority } from '../types/enums';
import { OperationType } from 'src/super-http/enums';
import { commonConfigs } from '../types/metadata.types';

export interface SalesforceTaskDto {
  Subject: string;
  Description: string;
  WhoId: string;
  OwnerId?: string | number;
  Status: string;
  Priority: string;
  ActivityDate: string;
  TaskSubtype?: string;
}

@Injectable()
export class SalesforceTaskService {
  private connection: Connection;
  private baseUrl: string;
  private commonConfigs: commonConfigs;

  constructor(
    private readonly httpService: SuperHttpService,
    private tenantId: string
  ) {}

  setConnection(connection: Connection) {
    this.connection = connection;
    this.baseUrl = `${connection.instanceUrl}/services/data/v${connection.version}`;
    this.prepareRequestConfigs();
  }

  prepareRequestConfigs() {
    const requestConfigs = generateRequestConfigs(this.connection.accessToken);
    const { withInterceptors, ...configsWithoutInterceptors } = requestConfigs;
    this.commonConfigs = configsWithoutInterceptors;
  }

  async createTask(taskData: SalesforceTaskDto): Promise<SalesforceRecord> {
    const soql = `SELECT Id FROM Task WHERE Subject = '${formatName(taskData.Subject)}' AND WhoId = '${taskData.WhoId}'`;
    const result = await salesforceQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, SalesforceEntityTypes.TASK, this.tenantId);
    const recordIsPresent = result.records[0];

    if (recordIsPresent) {
      const { TaskSubtype, ...taskDataWithoutSubtype } = taskData;
      return await this.httpService.patch(
        `${this.baseUrl}/sobjects/Task/${recordIsPresent.Id}`,
        this.tenantId,
        {
          ...taskDataWithoutSubtype,
        },
        {
          rateLimiter: 'salesforce',
          integrationName: 'salesforce',
          operation: OperationType.PATCH,
          entityType: SalesforceEntityTypes.TASK,
        },
        this.commonConfigs
      );
    }

    return await this.httpService.post(
      `${this.baseUrl}/sobjects/Task/`,
      this.tenantId,
      {
        ...taskData,
      },
      {
        rateLimiter: 'salesforce',
        integrationName: 'salesforce',
        operation: OperationType.CREATE,
        entityType: SalesforceEntityTypes.TASK,
      },
      this.commonConfigs
    );
  }

  async updateTask(id: string, taskData: Partial<SalesforceTaskDto>): Promise<void> {
    return await this.httpService.patch(
      `${this.baseUrl}/sobjects/Task/${id}`,
      this.tenantId,
      {
        ...taskData,
      },
      {
        rateLimiter: 'salesforce',
        integrationName: 'salesforce',
        operation: OperationType.PATCH,
        entityType: SalesforceEntityTypes.TASK,
      },
      this.commonConfigs
    );
  }

  async getTask(id: string): Promise<SalesforceRecord> {
    return await this.httpService.get(
      `${this.baseUrl}/sobjects/Task/${id}`,
      this.tenantId,
      {
        rateLimiter: 'salesforce',
        integrationName: 'salesforce',
        operation: OperationType.GET,
        entityType: SalesforceEntityTypes.TASK,
        entityId: id,
      },
      this.commonConfigs
    );
  }

  async createCommunicationTask(
    crmClientId: string,
    logCommunicationDto: LogCommunicationDto,
  ): Promise<void> {
    const task = {
      WhoId: crmClientId,
      Subject: logCommunicationDto.communicationType || 'Communication Log',
      Description: logCommunicationDto.communicationDetails || 'Communication Log',
      Status: TaskStatus.COMPLETED,
      Priority: TaskPriority.NORMAL,
      TaskSubtype: logCommunicationDto.communicationType || 'Task',
      ActivityDate: new Date().toISOString().split('T')[0],
    };

    await this.createTask(task);
  }

  async createEmailTask(
    emailOptions: SendEmailOptions,
    contact?: EnrichedContact,
    advisorId?: string,
  ): Promise<void> {
    const task = {
      WhoId: contact?.crmClientId,
      OwnerId: advisorId,
      Subject: emailOptions.subject || 'Email Communication',
      Description: emailOptions.text || '',
      Status: TaskStatus.COMPLETED,
      Priority: TaskPriority.NORMAL,
      TaskSubtype: 'Email',
      ActivityDate: new Date().toISOString().split('T')[0],
    };

    await this.createTask(task);
  }
}