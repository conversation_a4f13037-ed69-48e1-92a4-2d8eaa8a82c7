import { Test, TestingModule } from '@nestjs/testing';
import { CsvParser } from 'nest-csv-parser';
import { CSVService } from '../csv.service';
import { parseFileAsJson } from 'src/utils/file/file.util';

// FIXME: This test is not updated
describe.skip('CsvService', () => {
  let service: CSVService;
  let mockCsvParser: any;

  beforeEach(async () => {
    mockCsvParser = {
      parse: jest.fn().mockImplementation(() => ({
        list: [
          {
            name: '<PERSON>',
            email: '<EMAIL>',
          },
        ],
      })),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [CSVService, { provide: CsvParser, useValue: mockCsvParser }],
    }).compile();

    service = module.get<CSVService>(CSVService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('parseCsv', () => {
    it('should correctly parse CSV data', async () => {
      // TODO: Add test
    });
  });

  describe('parseJson', () => {
    it('should correctly parse JSON data', () => {
      const mockJsonData = Buffer.from(JSON.stringify({ name: 'John Doe' }));
      const result = parseFileAsJson<{ name: string }>(mockJsonData);

      expect(result).toEqual({ name: 'John Doe' });
    });

    it('should throw error on invalid JSON', () => {
      const invalidJsonData = Buffer.from('invalid json');

      expect(() => parseFileAsJson(invalidJsonData)).toThrow(SyntaxError);
    });

    // Add more tests as needed
  });
});
