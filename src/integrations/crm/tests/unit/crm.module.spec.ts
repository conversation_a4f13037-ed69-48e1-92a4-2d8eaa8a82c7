import { Test, TestingModule } from '@nestjs/testing';
import { CRMService } from 'src/integrations/crm/crm.service';
import { CRMModule } from 'src/integrations/crm/crm.module';
import { HttpService } from '@nestjs/axios';
import { InterviewsModule } from 'src/interviews/interviews.module';

jest.mock('src/integrations/crm/crm.service');
jest.mock('src/interviews/interviews.module');

describe.skip('CRMModule', () => {
  let crmService: CRMService;
  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [CRMModule, InterviewsModule],
      providers: [
        {
          provide: HttpService,
          useValue: {},
        },
      ],
    }).compile();

    crmService = module.get<CRMService>(CRMService);
  });

  it('should be defined', () => {
    expect(CRMModule).toBeDefined();
  });

  it('should provide CRMService', () => {
    expect(crmService).toBeDefined();
  });
});
