import { Controller, Get, Post, Query, Res, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Response } from 'express';

import { SalesforceService } from './salesforce.service';
import { ClsService } from 'nestjs-cls';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';

@Controller('integrations/salesforce')
export class SalesforceController {
  constructor(
    private readonly salesforceService: SalesforceService,
    private readonly clsService: ClsService,
  ) {}

  @Get('callback')
  async handleCallback(
    @Query('code') code: string,
    @Query('state') state: string,
    @Res() res: Response,
  ) {
    return this.salesforceService.handleCallback(code, state, res);
  }

  @Post('link')
  @UseGuards(AuthGuard('jwt'))
  async getSalesforceAuthorisationUri(): Promise<string> {
    const advisorId = this.clsService.get(ClsDataEnum.Advisor)?._id;
    return this.salesforceService.getSalesforceAuthorisationUrl(advisorId);
  }
}
