import { <PERSON><PERSON>, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Factory } from 'nestjs-seeder';

@Schema({ _id: false })
export class Address {
  @Factory((faker) => faker.location.streetAddress())
  @Prop()
  street: string;

  @Factory((faker) => faker.location.city())
  @Prop()
  city: string;

  @Factory((faker) => faker.location.state())
  @Prop()
  state: string;

  @Factory((faker) => faker.location.zipCode())
  @Prop()
  zip: string;
}

export const AddressSchema = SchemaFactory.createForClass(Address);
