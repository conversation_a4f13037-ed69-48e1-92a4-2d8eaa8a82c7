import { v4 as uuidv4 } from 'uuid';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PractifiCRM } from '../crm.practifi';
import { BeneficiaryEnum } from '../types/enums';

interface Beneficiary {
  firstName: string;
  lastName: string;
  allocationAmount: string;
  dob?: string;
}

interface ContingentBeneficiariesPageData {
  instance: { name: string, label: string };
  beneficiaries: Beneficiary[];
}

export class PractifiContingentBeneficiariesMapper implements FormUpdateMapper {
  constructor(private practifiCRM: PractifiCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: ContingentBeneficiariesPageData,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    if (!pageData?.instance?.label) {
      throw new Error('Instance label is required');
    }

    const assetId = await this.practifiCRM.customObjectService.getAssetId(pageData.instance.label, contact.crmClientId);

    // in case the user changed name or deleted asset via Practifi (and we do not have info about this)
    if (!assetId) {
      return;
    }

    // if the user decides to change his answer get already created beneficiaries and delete them
    await this.practifiCRM.customObjectService.deleteLinkedBeneficiaries(assetId, BeneficiaryEnum.CBEN);
    
    if(!pageData?.beneficiaries?.length) {
      return;
    }
    
    // create contacts first who will be linked to the asset role
    const createBeneficiaries = await Promise.all([
      ...pageData.beneficiaries.map(async el => {
        const newDate = new Date(el.dob);
        const formattedDate = newDate.toISOString().split('T')[0];
        const idempotencyKey = uuidv4();
        return {
            result: await this.practifiCRM.contactService.createContact({
              FirstName: el.firstName,
              LastName: el.lastName,
              Birthdate: formattedDate,
              Description: `${pageData?.instance?.label} Contingent Beneficiary`
            }, idempotencyKey),
            percentage: el.allocationAmount,
          };
        }
      )
    ]);

    // create a role and link it with the contact and the asset
    await Promise.all([
      ...createBeneficiaries.map(el => {
        if (el.result.id) {
          return this.practifiCRM.customObjectService.createAssetRole({
            Name: BeneficiaryEnum.CBEN,
            practifi__Asset_Liability__c: assetId,
            practifi__Person__c: el.result.id,
            practifi__Role__c: BeneficiaryEnum.ROLE,
            practifi__Percentage__c: el.percentage,
          });
        }
        return null;
      }).filter(el => el)
    ]);
  }
} 