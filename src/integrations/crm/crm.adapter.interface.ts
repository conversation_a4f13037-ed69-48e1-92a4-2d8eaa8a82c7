import { GenericCrmAccount } from 'src/integrations/crm/types/accounts/crm-account.type';
import { GenericAddress } from 'src/integrations/crm/types/generic/address.type';
import { GenericEmail } from 'src/integrations/crm/types/generic/email.type';
import { <PERSON><PERSON><PERSON><PERSON> } from 'src/integrations/crm/types/generic/phone.type';

/**
 * Represents a CRM adapter that provides methods for mapping CRM data.
 */
export interface CrmAdapter {
  /**
   * Maps an address from CRM to a generic address format.
   * @param address - The CRM address to be mapped.
   * @returns The mapped generic address.
   */
  mapAddress(address: unknown, ...args): GenericAddress;

  /**
   * Maps an account from CRM to a generic CRM account format.
   * @param account - The CRM account to be mapped.
   * @returns The mapped generic CRM account.
   */
  mapAccount(account: unknown): GenericCrmAccount;

  /**
   * Maps a phone number from CRM to a generic phone format.
   * @param phone - The CRM phone number to be mapped.
   * @returns The mapped generic phone number.
   */
  mapPhone(phone: unknown): <PERSON>ricPhone;

  /**
   * Maps an email address from CRM to a generic email format.
   * @param email - The CRM email address to be mapped.
   * @returns The mapped generic email address.
   */
  mapEmail(email: unknown): GenericEmail;
}
