import {
  IsEmail,
  IsNotEmpty,
  IsUUID,
  IsString,
  IsBoolean,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { AddressDto } from 'src/shared/dto/address.dto';
import { ApiProperty } from '@nestjs/swagger';

export class CreateOrganisationDto {
  @IsNotEmpty()
  @IsUUID()
  @ApiProperty()
  organisationId: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  name: string;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => AddressDto)
  @ApiProperty()
  address: AddressDto;

  @IsNotEmpty()
  @IsString()
  @ApiProperty()
  phone: string;

  @IsNotEmpty()
  @IsEmail()
  @ApiProperty()
  email: string;

  @IsNotEmpty()
  @IsBoolean()
  @ApiProperty()
  setupComplete: boolean;
}
