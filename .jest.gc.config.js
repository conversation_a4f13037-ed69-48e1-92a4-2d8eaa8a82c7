/**
 * Special Jest configuration for memory leak detection
 */
const packageJson = require('./package.json');

// Extract the base Jest config from package.json
const baseJestConfig = packageJson.jest || {};

module.exports = {
  // Extend the base Jest configuration
  ...baseJestConfig,
  
  // Use ts-jest preset
  preset: 'ts-jest',
  
  // Increase timeout for memory leak detection
  testTimeout: 60000,
  
  // Run tests in sequence for better isolated memory testing
  maxWorkers: 1,
  
  // Enable heap usage logging
  logHeapUsage: true,
  
  // Set up testing environment for memory leak detection
  setupFilesAfterEnv: [
    '<rootDir>/src/test-setup.ts'
  ],
  
  // Use node environment 
  testEnvironment: 'node',
  
  // Use default transformers
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      tsconfig: 'tsconfig.json',
      isolatedModules: true // For faster transforms
    }]
  },
  
  // Force test results to be fresh each time
  cache: false,
  
  // Use a fast bail option - stop on first memory leak
  bail: 1,
  
  // Additional memory leak detection options
  detectLeaks: true,
  detectOpenHandles: true,
  forceExit: true,
  
  // Clear caches between tests
  resetMocks: true,
  resetModules: true,
  restoreMocks: true,
  
  // Advanced reporting
  verbose: true,
}; 