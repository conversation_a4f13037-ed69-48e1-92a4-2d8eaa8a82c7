locals {
  environment = "production"
}

resource "aws_route53_zone" "main" {
  name = var.domain_name
}
resource "aws_vpc" "main" {
  cidr_block = "10.0.0.0/16"
  enable_dns_support   = true
  enable_dns_hostnames = true
  tags = {
    Name = "onbord-${var.environment}-vpc"
  }
}

resource "aws_subnet" "main" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = "********/24"
  map_public_ip_on_launch = true
  availability_zone       = var.environment == "production" ? "us-east-1a" : null

  lifecycle {
    create_before_destroy = true
  }
  tags = {
    Name = "onbord-${var.environment}-public-subnet"
  }
}

resource "aws_subnet" "private_subnet" {
  vpc_id                  = aws_vpc.main.id
  cidr_block              = "********/24"
  map_public_ip_on_launch = false
  availability_zone       = var.environment == "production" ? "us-east-1a" : null

  lifecycle {
    create_before_destroy = true
  }
  tags = {
    Name = "onbord-${var.environment}-private-subnet"
  }
}

resource "aws_security_group" "eb_ec2_sg_vpc" {
  name        = "ebs-ec2-sg-${var.environment}-vpc"
  description = "Security group for Elastic Beanstalk EC2 instances"
  vpc_id      = aws_vpc.main.id
  
  ingress {
    from_port = 0
    to_port   = 0
    protocol  = "-1"
    self      = true
  }

  # Add SSH access for EC2 Connect
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
    description = "Allow SSH access for EC2 Connect"
  }

  # Add egress rule if not already present
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

module "acm" {
  source = "../../modules/acm"

  domain_name = var.domain_name
  zone_id     = aws_route53_zone.main.zone_id
}

module "elastic_beanstalk" {
  source = "../../modules/elastic_beanstalk"

  name                                  = var.app_name
  solution_stack_name                   = var.solution_stack_name
  instance_type                         = var.instance_type
  environment                           = local.environment
  ssm_json                              = file("./ssm_parameters.json")
  source_bundle_bucket                  = var.source_bundle_bucket
  source_bundle_key                     = "onbord-backend-production-bundle.${var.commit_sha}.zip"
  certificate_arn                       = module.acm.onbord_backend_certificate_arn
  min_size                              = var.min_instances
  max_size                              = var.max_instances
  app_port                              = var.app_port
  commit_sha                            = var.commit_sha
  healthcheck_url                       = var.healthcheck_url
  elastic_beanstalk_security_group_id   = aws_security_group.eb_ec2_sg_vpc.id
  elastic_beanstalk_security_group_name = aws_security_group.eb_ec2_sg_vpc.name
  cognito_user_pool_id                  = module.cognito.user_pool_id
  cognito_user_pool_client_id           = module.cognito.user_pool_client_id
  vpc_id                                = aws_vpc.main.id
  redis_host     = module.redis.redis_host
  redis_port     = module.redis.redis_port
  redis_password = module.redis.redis_password
  private_subnet_id = aws_subnet.private_subnet.id
  public_subnet_id  = aws_subnet.main.id
  origin                                = var.origin
  encryption_key_id                     = module.kms.key_id
  domain_name                           = var.domain_name
  work_env                              = var.work_env
  org_docs_bucket_name                  = "${var.app_name}-${local.environment}-organisations-assets"

  depends_on = [
    aws_security_group.eb_ec2_sg_vpc,
    module.redis
  ]
}

module "route53" {
  source = "../../modules/route53"

  zone_id     = aws_route53_zone.main.zone_id
  domain_name = var.domain_name
  cname       = module.elastic_beanstalk.onbord_backend_env_cname
}

module "atlas" {
  source = "../../modules/atlas"

  ebs_env_name = var.app_name

  cluster_name                        = var.cluster_name
  instance_size                       = var.instance_size
  elastic_beanstalk_security_group_id = aws_security_group.eb_ec2_sg_vpc.id

  mongodb_atlas_org_id_ssm                 = var.mongodb_atlas_org_id_ssm
  mongodb_atlas_api_pub_key_ssm            = var.mongodb_atlas_api_pub_key_ssm
  mongodb_atlas_api_pri_key_ssm            = var.mongodb_atlas_api_pri_key_ssm
  mongodb_atlas_database_user_username_ssm = var.mongodb_atlas_database_user_username_ssm
  mongodb_atlas_database_user_password_ssm = var.mongodb_atlas_database_user_password_ssm

  environment    = local.environment
  nat_gateway_ip = module.elastic_beanstalk.nat_gateway_ip


}

module "cognito" {
  source                          = "../../modules/cognito"
  user_pool_name                  = "onbord-pool-production"
  client_name                     = "onbord-pool-client-production"
  callback_urls                   = ["https://ui.onbord.io/auth/callback", "https://ui.onbord.io/"]
  default_redirect_uri            = "https://ui.onbord.io/"
  logout_urls                     = ["https://ui.onbord.io/"]
  invitation_email_message        = file("./email_templates/invitation.template.html")
  invitation_email_sms_text       = "Welcome to Onbord. Your username is {username} and temporary password is {####}"
  invitation_email_subject        = "Welcome Onbord"
  mfa_SMS_text                    = "Your Onbord authentication code is {####}"
  ses_verified_email_identity_arn = "arn:aws:ses:us-east-1:094396706397:identity/<EMAIL>"
}

module "kms" {
  source = "../../modules/kms"

  environment = local.environment
}

module "s3_org_documents" {
  source = "../../modules/s3/organisation_documents"

  bucket_name = "${var.app_name}-${local.environment}-organisations-assets"
}

module "redis" {
  source = "../../modules/redis"
  
  environment = local.environment
  vpc_id      = aws_vpc.main.id
  subnet_ids  = [aws_subnet.private_subnet.id]
  elastic_beanstalk_security_group_id = aws_security_group.eb_ec2_sg_vpc.id
  
  # Use a smallerinstance for production
  node_type   = "cache.t4g.micro"
}
