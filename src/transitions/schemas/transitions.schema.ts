import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Factory, DataFactory } from 'nestjs-seeder';
import { Document, Types } from 'mongoose';
import { SchemaObjectID } from 'src/shared/types/mongoose';
import { Contact } from 'src/shared/schemas/contact.schema';
import { TransitionTypeEnum } from '../types/transition-type.enum';
import { TransitionStatusEnum } from '../types/transition-status.enum';

@Schema({ timestamps: true })
export class Transition extends Document {
  @Factory((_, { organisation }) => organisation.id)
  @Prop({
    type: SchemaObjectID,
    required: true,
    ref: 'Organisation',
    index: true,
  })
  organisationId: Types.ObjectId;

  @Prop({ default: 0 })
  assetsValue: number;

  @Factory(
    (_, context) => DataFactory.createForClass(Contact).generate(1, context)[0],
  )
  @Prop({ type: Contact, required: true })
  primaryContact: Contact;

  @Factory(
    (_, context) => DataFactory.createForClass(Contact).generate(1, context)[0],
  )
  @Prop({ type: Contact })
  secondaryContact?: Contact;

  @Prop({
    type: SchemaObjectID,
    ref: 'Advisor',
    required: true,
  })
  primaryAdvisor: Types.ObjectId;

  @Prop({
    type: SchemaObjectID,
    ref: 'Advisor',
    required: true,
  })
  primaryCSA: Types.ObjectId;

  @Factory([])
  @Prop({
    type: SchemaObjectID,
    ref: 'Advisor',
    required: false,
  })
  secondaryAdvisor?: Types.ObjectId[];

  @Factory([])
  @Prop({
    type: SchemaObjectID,
    ref: 'Advisor',
    required: false,
  })
  secondaryCSA?: Types.ObjectId[];

  @Factory(TransitionTypeEnum.Protocol)
  @Prop({ required: true, enum: TransitionTypeEnum })
  transitionType: TransitionTypeEnum;

  @Factory(TransitionStatusEnum.StatementPending)
  @Prop({ required: true, enum: TransitionStatusEnum })
  status: TransitionStatusEnum;
}

export const TransitionSchema = SchemaFactory.createForClass(Transition);
