import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';
import { ConflictsOfInterestRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/conflicts-of-interest-request.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { AccountClientDocumentsEnum } from 'src/shared/types/accounts/account-documents.enum';
import { GenericUserDefinedFieldsEnum } from 'src/integrations/crm/types/generic/user-defined-fields.enum';
import {
  insertIntoUserContentField,
  removeFromUserContentField,
} from 'src/integrations/crm/wealthbox/utils/data-parsing/general-text-field';

export class WealthboxConflictOfInterestMapper implements FormUpdateMapper {
  constructor(private WealthboxCRM: WealthboxCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: ConflictsOfInterestRequestDto,
  ): Promise<void> {
    const subject = 'Obtain BD/FINRA approval letter for account opening';
    const description =
      'Client employed by or associated with B/D, FINRA or Stock Exchange. Email for approval letter and submit to Schwab.';
    const { _id: interviewId } = interview;
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    const crmClientId = contact.crmClientId;
    const crmContact = await this.WealthboxCRM.contactService.getContact(
      crmClientId,
    );
    // Remove FINRA Document from required documents in case the client changes their answer
    if (!pageData.companyName) {
      await this.WealthboxCRM.interviewsService.removeRequiredDocument(
        interviewId,
        {
          document: AccountClientDocumentsEnum.FinraDocument,
        },
      );
      const newBackgroundInfo = removeFromUserContentField(
        crmContact.background_info,
        GenericUserDefinedFieldsEnum.INDUSTRY_AFFILIATION,
      );

      if (newBackgroundInfo) {
        await this.WealthboxCRM.contactService.updateContact(
          contact.crmClientId,
          {
            background_information: newBackgroundInfo,
          },
        );
      }

      return;
    }

    // Add FINRA Document to required documents in the interview
    if (interview.docusignSelected) {
      await this.WealthboxCRM.interviewsService.addRequiredDocument(
        interviewId,
        {
          document: AccountClientDocumentsEnum.FinraDocument,
        },
      );
    }

    // Update contact background information with the company name
    await this.WealthboxCRM.contactService.updateContact(contact.crmClientId, {
      background_information: insertIntoUserContentField(
        crmContact.background_info,
        {
          [GenericUserDefinedFieldsEnum.INDUSTRY_AFFILIATION]:
            pageData.companyName,
        },
      ),
    });

    // Create a task in Wealthbox instead of an activity in Redtail
    await this.WealthboxCRM.taskService.createTask({
      // Map necessary task data here
      name: subject,
      description,
      assigned_to: Number(interview.client.primaryAdvisor.crmId),
      due_date: new Date().toISOString(),
      linked_to: [
        {
          id: Number(crmClientId),
          type: 'Contact',
        },
      ],
    });
  }
}
