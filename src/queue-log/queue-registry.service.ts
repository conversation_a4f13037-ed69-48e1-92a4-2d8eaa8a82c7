import { Injectable, Logger } from '@nestjs/common';
import { Queue } from 'bullmq';

@Injectable()
export class QueueRegistryService {
  private static readonly queuesMap = new Map<string, Queue>();
  private readonly logger = new Logger(QueueRegistryService.name);

  registerQueue(queueName: string, queue: Queue): void {
    if (!queue) {
      this.logger.error(`Attempted to register undefined queue for "${queueName}"`);
      return;
    }
    
    this.logger.log(`Registering queue: ${queueName}`);
    QueueRegistryService.queuesMap.set(queueName, queue);
    this.logger.debug(`Current registered queues: ${this.listRegisteredQueues().join(', ')}`);
  }

  getQueue(queueName: string): Queue | undefined {
    const queue = QueueRegistryService.queuesMap.get(queueName);
    if (!queue) {
      this.logger.warn(`Queue "${queueName}" not found in registry. Available queues: ${this.listRegisteredQueues().join(', ')}`);
    }
    return queue;
  }

  listRegisteredQueues(): string[] {
    return Array.from(QueueRegistryService.queuesMap.keys());
  }

  // Optional: Add method to check if a queue is registered
  isQueueRegistered(queueName: string): boolean {
    return QueueRegistryService.queuesMap.has(queueName);
  }
} 