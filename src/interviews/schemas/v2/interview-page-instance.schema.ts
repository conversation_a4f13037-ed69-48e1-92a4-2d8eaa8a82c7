import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Document, SchemaTypes } from "mongoose";

@Schema({ 
    timestamps: true, 
    collection: 'interviewpageinstances_v2' 
  })
  export class InterviewPageInstanceV2 extends Document {
    @Prop({ 
      type: SchemaTypes.ObjectId, 
      ref: 'InterviewV2', 
      required: true,
      index: true 
    })
    interviewId!: string;
  
    @Prop({ 
      type: String, 
      required: true,
      validate: {
        validator: (v: string) => /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(v),
        message: 'Invalid UUID format'
      }
    })
    pageId!: string;  // UUID from frontend
  
    @Prop({ 
      type: String, 
      required: true 
    })
    pageName!: string;  // Denormalized for easier queries
  
    @Prop({ 
      type: Number, 
      required: true 
    })
    visitOrder!: number;
  
    @Prop({ 
      type: String,
      default: null,
      validate: {
        validator: function(v: string) {
          return !v || /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(v);
        },
        message: 'Invalid UUID format'
      }
    })
    navigatedFrom?: string;  // Previous page UUID
  
    @Prop({ 
      type: String,
      default: null,
      validate: {
        validator: function(v: string) {
          return !v || /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(v);
        },
        message: 'Invalid UUID format'
      }
    })
    navigatedTo?: string;  // Next page UUID

    @Prop({ 
      type: String, 
      enum: ['pending', 'visited', 'completed', 'skipped'],
      default: 'pending'
    })
    status!: string;

    @Prop({ 
      type: String, 
      enum: ['pending', 'syncing', 'synced', 'failed'],
      default: 'pending'
    })
    syncStatus!: string;

    @Prop({ 
      type: SchemaTypes.Mixed,
      required: false
    })
    accountContext?: {
      accountId?: string;
      accountType?: string;  // 'beneficiary', 'spouse', 'dependent', etc.
      accountSubtype?: string;  // 'primary', 'secondary', etc.
      accountName?: string;  // For display purposes
    };

    @Prop({ 
      type: SchemaTypes.Mixed,
      default: {}
    })
    navigationContext?: {
      branchTaken?: string;
      timestamp?: Date;
      [key: string]: any;
    };
  }

  export const InterviewPageInstanceV2Schema = SchemaFactory.createForClass(InterviewPageInstanceV2);