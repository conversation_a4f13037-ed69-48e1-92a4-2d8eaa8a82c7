import mongoose from 'mongoose';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import {
  RedtailSSNMapper,
  SSNPageUpdateDto,
} from 'src/integrations/crm/redtail/form-mappers/ssn.mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';

jest.mock('src/integrations/crm/redtail/crm.redtail');

describe('RedtailSSNMapper', () => {
  let redtailCRM: jest.Mocked<RedtailCRM>;
  let ssnMapper: RedtailSSNMapper;

  const mockOrganisation = { 
    _id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011'),
    name: 'Test Organisation'
  } as Organisation;
  
  const mockAdvisor = { 
    _id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439012'),
    firstName: 'Test',
    lastName: 'Advisor'
  } as unknown as AdvisorWithRole;

  // Mock SuperHttpService
  const mockHttpService = {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    delete: jest.fn(),
    patch: jest.fn(),
    rateLimiters: new Map(),
    defaultRateLimiterKey: 'default',
    logger: { log: jest.fn(), error: jest.fn(), warn: jest.fn(), debug: jest.fn() },
    initialized: true,
    getRateLimiterStats: jest.fn(),
    invalidateEntityTypeCache: jest.fn(),
    invalidateSpecificCache: jest.fn(),
    checkLimiterHealth: jest.fn(),
    addInterceptor: jest.fn(),
    clearInterceptors: jest.fn()
  };

  // Mock InterviewsService
  const mockInterviewsService = {
    flowProducer: {},
    assetModel: {},
    interviewModel: {},
    interviewTemplateService: {},
    createInterview: jest.fn(),
    getInterview: jest.fn(),
    getInterviews: jest.fn(),
    updateInterview: jest.fn(),
    deleteInterview: jest.fn()
  };

  // Mock MailService
  const mockMailService = {
    mailerService: {},
    configService: {},
    logger: { log: jest.fn(), error: jest.fn(), warn: jest.fn(), debug: jest.fn() },
    advisorsCrmService: {},
    notificationsService: {},
    clientsService: {},
    organisationService: {},
    sendClientWelcomeEmail: jest.fn(),
    sendClientInterviewEmailDesktop: jest.fn(),
    sendUserMfaTokenEmail: jest.fn()
  };

  // Mock Logger
  const mockLogger = { 
    error: jest.fn(), 
    warn: jest.fn(), 
    info: jest.fn(),
    debug: jest.fn(),
    log: jest.fn()
  };

  beforeEach(() => {
    redtailCRM = new RedtailCRM(
      mockAdvisor,
      mockOrganisation,
      {
        username: 'test',
        password: 'test',
        apiKey: 'test',
        userKey: 'test',
        databaseId: 'test',
        userId: 'test'
      },
      mockHttpService as any,
      mockInterviewsService as any,
      mockMailService as any,
      mockLogger as any
    ) as jest.Mocked<RedtailCRM>;

    // Setup mock for updateContactSSN method
    redtailCRM.updateContactSSN = jest.fn().mockResolvedValue({
      id: '1',
      tax_id: '***********'
    });
    
    ssnMapper = new RedtailSSNMapper(redtailCRM);
  });

  describe('mapPageData', () => {
    it('should call updateContactSSN with correct parameters for primary contact', async () => {
      // Mock data
      const mockInterview = {
        isPrimary: true
      } as EnrichedInterview;
      
      const mockClient = {
        primaryContact: {
          crmClientId: '123'
        }
      } as EnrichedClient;
      
      const pageData: SSNPageUpdateDto = { 
        ssn: '***********' 
      };

      // Call method
      await ssnMapper.mapPageData(
        mockInterview,
        mockClient,
        mockOrganisation,
        pageData,
      );

      // Check if updateContactSSN was called with correct arguments
      expect(redtailCRM.updateContactSSN).toHaveBeenCalledWith(
        '123',
        pageData.ssn,
      );
    });

    it('should call updateContactSSN with correct parameters for secondary contact', async () => {
      // Mock data
      const mockInterview = {
        isPrimary: false
      } as EnrichedInterview;
      
      const mockClient = {
        primaryContact: {
          crmClientId: '123'
        },
        secondaryContact: {
          crmClientId: '456'
        }
      } as EnrichedClient;
      
      const pageData: SSNPageUpdateDto = { 
        ssn: '***********' 
      };

      // Call method
      await ssnMapper.mapPageData(
        mockInterview,
        mockClient,
        mockOrganisation,
        pageData,
      );

      // Check if updateContactSSN was called with correct arguments for secondary contact
      expect(redtailCRM.updateContactSSN).toHaveBeenCalledWith(
        '456',
        pageData.ssn,
      );
    });
  });
});
