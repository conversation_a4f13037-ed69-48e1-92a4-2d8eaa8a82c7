name: Production Deployment

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false # We do not want to cancel in progress steps as it might cause lock issues

on:
  push:
    branches:
      - main

env:
  AWS_REGION: us-east-1
  NEW_RELIC_APP_NAME: onbord-backend-production
  NEW_RELIC_DISTRIBUTED_TRACING_ENABLED: "true"

jobs:
  tf-plan:
    environment: Production
    permissions:
      contents: read
      pull-requests: write
      issues: write
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set environment
        run: ./infra/scripts/set-environment.sh production
        working-directory: ${{ github.workspace }}
        env:
          COMMIT_SHA: ${{ github.sha }}

      - name: Set up AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Use Node.js
        uses: actions/setup-node@v1
        with:
          node-version: 18

      - name: Setup Terraform Resources
        run: ./infra/scripts/provision-terraform-backend.sh
        working-directory: ${{ github.workspace }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.4.6

      - name: Terraform Init
        id: init
        run: terraform init
        env:
          DATADOG_API_KEY: ${{ secrets.DD_API_KEY }}
          DATADOG_APP_KEY: ${{ secrets.DD_APP_KEY }}
        working-directory: ./infra/environments/${{ env.infra_environment }}

      - name: Terraform Validate
        id: validate
        run: terraform validate -no-color
        working-directory: ./infra/environments/${{ env.infra_environment }}

      - name: Terraform Plan
        id: plan
        run: terraform plan
        continue-on-error: true
        working-directory: ./infra/environments/${{ env.infra_environment }}

  test:
    permissions:
      contents: read
      pull-requests: write
      issues: write
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: .

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up JDK 11
        uses: actions/setup-java@v2
        with:
          java-version: '11'
          distribution: 'adopt'

      - name: Cache SonarCloud packages
        uses: actions/cache@v3
        with:
          path: ~/.sonar/cache
          key: ${{ runner.os }}-sonar
          restore-keys: ${{ runner.os }}-sonar

      - name: Use Node.js
        uses: actions/setup-node@v1
        with:
          node-version: 18

      - name: Cache Node.js modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.OS }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: ${{ runner.OS }}-node-

      - name: Install Dependencies
        run: npm ci --force

      # - name: Run Lint
      #   run: npm run lint

      # - name: Run Tests
      #   run: npm run test

      # - name: SonarCloud Scan
      #   uses: SonarSource/sonarcloud-github-action@master
      #   env:
      #     GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      #     SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  deploy:
    permissions:
      contents: read
      pull-requests: write
      issues: write
    needs: [test, tf-plan]
    runs-on: ubuntu-latest
    environment: Production

    defaults:
      run:
        working-directory: .

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set environment
        run: ./infra/scripts/set-environment.sh production
        working-directory: ${{ github.workspace }}
        env:
          COMMIT_SHA: ${{ github.sha }}

      - name: Wait for manual approval
        uses: trstringer/manual-approval@v1
        timeout-minutes: 5
        with:
          secret: ${{ github.TOKEN }}
          approvers: LucasPadilha-RAD,luisvnw
          minimum-approvals: 1
          issue-title: '[INFRA] PRODUCTION DEPLOYMENT'
          issue-body: 'WARNING:     PRODUCTION INFRASTRUCTURE DEPLOYMENT       :WARNING'

      - name: Set up AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Use Node.js
        uses: actions/setup-node@v1
        with:
          node-version: 18

      - name: Cache Node.js modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.OS }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: ${{ runner.OS }}-node-

      - name: Install Dependencies
        run: npm ci --force

      - name: Configure New Relic
        run: |
          sed -i.bak "s/\"start:prod\": \"node --experimental-loader=newrelic\/esm-loader.mjs dist\/src\/main\"/\"start:prod\": \"NEW_RELIC_APP_NAME=onbord-backend-production NEW_RELIC_LICENSE_KEY=${{ secrets.NEW_RELIC_API_KEY }} node --experimental-loader=newrelic\/esm-loader.mjs dist\/src\/main\"/" package.json

      # - name: Generate Prisma Client
      #   run: npx prisma generate

      - name: Build
        run: npm run build

      - name: Move the production environment vars file to the elasticbeanstalk bundle
        run: cp production.options.config ./.ebextensions/options.config

      - name: Zip bundle
        run: zip -r -X onbord-backend-production-bundle.${{github.sha}}.zip ./.ebextensions ./.platform ./dist package.json Procfile node_modules

      - name: Setup Terraform Resources
        run: ./infra/scripts/provision-terraform-backend.sh
        working-directory: ${{ github.workspace }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.4.6

      - name: Terraform Init
        id: init
        run: terraform init
        env:
          DATADOG_API_KEY: ${{ secrets.DD_API_KEY }}
          DATADOG_APP_KEY: ${{ secrets.DD_APP_KEY }}
        working-directory: ./infra/environments/${{ env.infra_environment }}

      - name: Terraform Plan
        id: plan
        run: terraform plan
        continue-on-error: true
        working-directory: ./infra/environments/${{ env.infra_environment }}

      - name: Terraform Apply
        id: apply
        run: terraform apply -auto-approve
        working-directory: ./infra/environments/${{ env.infra_environment }}
        if: steps.plan.outcome == 'success' && steps.plan.outputs.tfplan != 'No changes. Your infrastructure matches the configuration.'

      - name: Terraform Output
        id: output
        run: terraform output -json
        working-directory: ./infra/environments/${{ env.infra_environment }}
