export enum PermissionEnum {
  CanAddCompany = 'onbord.addCompany',
  CanListCompanies = 'onbord.listCompanies',
  CanManageEmailTemplates = 'onbord.admin.emailTemplates.manage',
  CanInterview = 'onbord.interview',
  CanCreateCompanyUsers = 'onbord.company.users.create',
  CanAddClient = 'onbord.organization.addClient',
  CanAddUser = 'onbord.organization.addUser',
  CanManageConfig = 'onbord.organization.config',
  CanDeleteAnyClients = 'onbord.organization.deleteAnyClients',
  CanDisableUser = 'onbord.organization.disableUser',
  CanListAnyClients = 'onbord.organization.listAnyClients',
  CanListUsers = 'onbord.organization.listUsers',
  CanManageUserProfile = 'onbord.organization.manageUserProfile',
  CanModifyAnyClients = 'onbord.organization.modifyAnyClients',
  CanUpdateSelf = 'onbord.organization.updateSelf',
  ManageCompanyStatus = 'onbord.manageCompanyStatus',
  ManageProduct = 'onbord.config',
  AddProductAdmin = 'onbord.addProductAdmin',
  ListOwnClients = 'onbord.organization.listOwnClients',
  ModifyOwnClients = 'onbord.organization.modifyOwnClients',
  DeleteOwnClients = 'onbord.organization.deleteOwnClients',
  MonitorActivity = 'onbord.monitorActivity',
}

export interface Permission {
  id: string;
  description: string;
}
