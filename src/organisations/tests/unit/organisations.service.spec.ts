import { Test, TestingModule } from '@nestjs/testing';
import { Model } from 'mongoose';
import { getModelToken } from '@nestjs/mongoose';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { CreateOrganisationDto } from 'src/organisations/dto/create-organisation.dto';
import { UpdateOrganisationDto } from 'src/organisations/dto/update-organisation.dto';
import * as organisationMock from 'src/organisations/tests/mocks/organisations.mock.json';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { HttpException, HttpStatus } from '@nestjs/common';
import { error } from 'console';
import {
  CRMEnum,
  DocumentSigningEnum,
  IntegrationEnum,
} from 'src/shared/types/integrations';
import { Integration } from 'src/shared/schemas/integration.schema';
import { ClsService } from 'nestjs-cls';
import { advisorMock } from 'src/advisors/tests/mocks/advisor.mock';
import { Asset } from 'src/shared/schemas/asset.schema';
import { ConfigService } from '@nestjs/config/dist/config.service';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { AssetsService } from 'src/assets/assets.service';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';

jest.mock('aws-sdk', () => {
  const mKMS = {
    encrypt: jest.fn().mockReturnValue({
      promise: jest.fn().mockReturnValue({ CiphertextBlob: 'encrypted' }),
    }),
    decrypt: jest.fn().mockReturnValue({
      promise: jest.fn().mockReturnValue({ Plaintext: 'decrypted' }),
    }),
    promise: jest.fn(),
  };
  return { KMS: jest.fn(() => mKMS) };
});

describe('OrganisationsService', () => {
  const mockExec = jest.fn();

  let service: OrganisationsService;
  let docusignService: DocusignService;
  let transactionManager: TransactionManager;
  let organisationModel: Model<Organisation>;
  let assetModel: Model<Asset>;
  const mockSession = jest.fn();

  const mockAccountInfo = {
    accountId: 'accountId',
  };

  const mockSchwabTemplates = {
    envelopeTemplates: [
      {
        templateId: 'templateId',
      },
    ],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganisationsService,
        {
          provide: getModelToken(Organisation.name),
          useValue: {
            create: jest.fn().mockResolvedValue(organisationMock),
            find: jest.fn().mockResolvedValue([]),
            findOne: jest.fn(),
            findById: jest.fn().mockReturnValue({
              session: mockSession,
            }),
            findByIdAndUpdate: jest.fn().mockReturnValue({
              exec: mockSession,
            }),
            findByIdAndRemove: jest.fn().mockResolvedValue(null),
            updateOne: jest.fn(),
            countDocuments: jest.fn(),
            aggregate: jest.fn(),
          },
        },
        {
          provide: getModelToken(Asset.name),
          useValue: {
            find: jest.fn(),
            updateOne: jest.fn(),
          },
        },
        {
          provide: WINSTON_MODULE_PROVIDER,
          useValue: {
            info: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
          },
        },
        {
          provide: ClsService,
          useValue: {
            get: jest.fn().mockReturnValue(advisorMock),
          },
        },
        {
          provide: AssetsService,
          useValue: {
            uploadPrivateDocument: jest.fn(),
            downloadPrivateDocument: jest.fn(),
          },
        },
        {
          provide: AdvisorsCrudService,
          useValue: {
            findOne: jest.fn(),
            create: jest.fn(),
          },
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: DocusignService,
          useValue: {
            getAccountInfo: jest.fn().mockResolvedValue(mockAccountInfo),
            getSchwabEnvelopeTemplate: jest.fn().mockResolvedValue(mockSchwabTemplates),
          },
        },
        {
          provide: TransactionManager,
          useValue: {
            currentSession: jest.fn().mockReturnValue({}),
          },
        },
      ],
    }).compile();

    service = module.get<OrganisationsService>(OrganisationsService);
    docusignService = module.get<DocusignService>(DocusignService);
    transactionManager = module.get<TransactionManager>(TransactionManager);

    organisationModel = module.get<Model<Organisation>>(
      getModelToken(Organisation.name),
    );
    assetModel = module.get<Model<Asset>>(getModelToken(Asset.name));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const dto: CreateOrganisationDto = {
      ...organisationMock,
      selectedCRM: CRMEnum.Redtail,
    };

    it('should create a new organisation', async () => {
      const createMock = jest.fn().mockResolvedValueOnce([organisationMock]);
      const spy = jest
        .spyOn(organisationModel, 'create')
        .mockImplementation(createMock);

      const result = await service.create(dto);

      expect(spy).toHaveBeenCalled();
      expect(result).toEqual({
        message: 'New organisation created!',
        organisation: organisationMock,
      });
    });

    it.skip('should handle creation failure', async () => {
      organisationModel.create = jest.fn().mockResolvedValueOnce(error);

      const execSpy = jest
        .spyOn(organisationModel, 'create')
        .mockRejectedValueOnce(new Error('Failed to create organisation'));

      await service.create(dto);

      expect(execSpy).toHaveBeenCalled();
      await expect(service.create(dto)).rejects.toEqual(
        new Error('Failed to create organisation'),
      );
    });
  });

  describe('findAll', () => {
    it('should return an array of organisations', async () => {
      jest.spyOn(organisationModel, 'countDocuments').mockResolvedValueOnce(1);

      organisationModel.aggregate = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValue(organisationMock),
        skip: jest.fn().mockReturnThis(),
        limit: jest.fn().mockReturnThis(),
      });

      const result = await service.findAll({ limit: 10, page: 1 }, {});

      expect(result).toEqual({ result: organisationMock, totalResults: 1 });
    });
  });

  describe('findOne', () => {
    it('should find one organisation', async () => {
      mockSession.mockResolvedValueOnce(organisationMock);

      const result = await service.findOne(organisationMock.id);

      expect(mockSession).toHaveBeenCalled();
      expect(result).toEqual(organisationMock);
    });
  });

  describe('update', () => {
    const dto: CreateOrganisationDto = {
      ...organisationMock,
      selectedCRM: CRMEnum.Redtail,
    };

    it('should fail to update an organisation', async () => {
      const error = new HttpException(
        'An error occurred while updating the organisation.',
        HttpStatus.BAD_REQUEST,
      );

      organisationModel.findById = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValueOnce(null),
        session: jest.fn().mockReturnValue({}),
      });

      organisationModel.findByIdAndUpdate = jest.fn().mockReturnValue({
        exec: jest.fn().mockRejectedValue(error),
        session: jest.fn().mockRejectedValue(error),
      });

      await expect(service.update(organisationMock.id, dto)).rejects.toThrow(
        error,
      );
    });

    it('should not find organisation', async () => {
      const updateDto: CreateOrganisationDto = {
        ...organisationMock,
        selectedCRM: CRMEnum.Redtail,
      };

      organisationModel.findByIdAndUpdate = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValueOnce(null),
        session: jest.fn().mockReturnValue(null),
      });

      await expect(service.update('', updateDto)).rejects.toThrow(
        HttpException,
      );
    });

    it('should update an organisation successfully', async () => {
      const mockId = organisationMock.id;
      const updateDto: CreateOrganisationDto = {
        ...organisationMock,
        selectedCRM: CRMEnum.Redtail,
      };

      const updatedOrganisation = {
        id: mockId,
        updatedAt: new Date(),
        save: jest.fn().mockResolvedValueOnce(true),
      };

      organisationModel.findById = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValueOnce(updatedOrganisation),
        session: jest.fn().mockReturnValue({}),
      });

      organisationModel.findByIdAndUpdate = jest.fn().mockReturnValue({
        exec: jest.fn().mockResolvedValueOnce(updatedOrganisation),
        session: jest.fn().mockReturnValue({}),
      });

      const result = await service.update(mockId, updateDto);

      expect(organisationModel.findByIdAndUpdate).toHaveBeenCalledWith(
        mockId,
        updateDto,
        { new: true },
      );
      expect(result).toEqual({ message: 'Organisation updated successfully.' });
    });
  });

  describe('remove', () => {
    it('should find one organisation', async () => {
      organisationModel.findByIdAndRemove = jest.fn().mockReturnValue({
        session: jest.fn().mockReturnValue(organisationMock),
      });

      const result = await service.remove(organisationMock.id);

      expect(organisationModel.findByIdAndRemove).toHaveBeenCalled();
      expect(result).toEqual(organisationMock);
    });
  });

  describe('linkDocusign', () => {
    const accessToken = 'accessToken';
    const refreshToken = 'refreshToken';
    const expiresIn = '28000';
    const lastRefresh = new Date();

    const dto = {
      accessToken,
      refreshToken,
      expiresIn,
      lastRefresh,
    };

    const org: any = {
      ...organisationMock,
      externalAccounts: [
        {
          integrationType: IntegrationEnum.DocumentSigning,
          integrationConfig: {
            name: DocumentSigningEnum.Docusign,
            credentials: 'encrypted',
          },
        },
      ],
    };

    it('should throw an error if no accessToken is provided', async () => {
      await expect(
        service.linkDocusign('123', { ...dto, accessToken: null }),
      ).rejects.toThrow(Error('Access token or refresh token not found.'));
    });

    it('should throw an error if no refresh is provided', async () => {
      await expect(
        service.linkDocusign('123', { ...dto, refreshToken: null }),
      ).rejects.toThrow(Error('Access token or refresh token not found.'));
    });

    it('should throw an error if organisation not found', async () => {
      jest.spyOn(organisationModel, 'findOne').mockResolvedValueOnce({});

      await expect(service.linkDocusign('123', dto)).rejects.toThrow(
        Error('Cannot add integration, entity not found.'),
      );
    });

    it('should throw an error if docusign is already linked', async () => {
      jest.spyOn(organisationModel, 'findOne').mockResolvedValueOnce({
        ...organisationMock,
        externalAccounts: [
          {
            integrationType: IntegrationEnum.DocumentSigning,
            integrationConfig: {},
          },
        ],
      } as any);

      await expect(service.linkDocusign('123', dto)).rejects.toThrow(
        Error('Integration already exists.'),
      );
    });

    it('should link an organisation to docusign', async () => {
      jest.spyOn(organisationModel, 'findOne').mockResolvedValueOnce({
        ...organisationMock,
        externalAccounts: [],
      } as any);
      jest.spyOn(service, 'findOne').mockResolvedValueOnce(org);
      jest.spyOn(organisationModel, 'updateOne').mockResolvedValueOnce(org);

      await expect(service.linkDocusign('123', dto)).resolves.toStrictEqual({
        message: 'Docusign linked successfully.',
      });
    });
  });

  describe('unlinkDocusign', () => {
    const integrations = [
      {
        integrationType: IntegrationEnum.DocumentSigning,
        integrationConfig: {
          name: DocumentSigningEnum.Docusign,
          credentials: 'encrypted',
        },
      },
    ];

    it('should throw an error if organisation not found', async () => {
      mockSession.mockResolvedValueOnce(null);

      await expect(service.unlinkDocusign('123')).rejects.toThrow(
        Error('Organisation not found'),
      );
    });

    it('should successfully unlink an organisation from docusign', async () => {
      jest.spyOn(service, 'findOne').mockResolvedValueOnce({
        ...organisationMock,
        externalAccounts: integrations,
      } as any);

      jest.spyOn(service, 'update').mockResolvedValueOnce({
        ...organisationMock,
        externalAccounts: [],
      } as any);

      await expect(service.unlinkDocusign('123')).resolves.toStrictEqual({
        message: `Docusign unlinked successfully.`,
      });
    });
  });

  describe('findDocusignRefreshCandidates', () => {
    const page = 1;

    const mockOrganisations = (integration: Integration) =>
      [...Array(3).keys()].map(() => ({
        ...organisationMock,
        externalAccounts: [integration],
      }));

    beforeEach(() => {
      jest.clearAllMocks();

      jest.spyOn(organisationModel, 'find').mockImplementation(
        () =>
          ({
            skip: jest.fn().mockReturnThis(),
            limit: jest.fn().mockReturnThis(),
            exec: mockExec,
          } as any),
      );
    });

    it('should return a batch of organisations with docusign integration', async () => {
      const lastRefresh = new Date('2023-06-30T11:37:54.176Z');

      const organisations = mockOrganisations({
        integrationType: IntegrationEnum.DocumentSigning,
        integrationConfig: {
          name: DocumentSigningEnum.Docusign,
          credentials: 'encrypted',
          lastRefresh,
        },
      });

      mockExec.mockResolvedValueOnce(organisations);

      await expect(
        service.findDocusignRefreshCandidates(page),
      ).resolves.toStrictEqual(organisations);
    });

    it('should return an empty array if no organisations found', async () => {
      mockExec.mockResolvedValueOnce([]);

      await expect(
        service.findDocusignRefreshCandidates(page),
      ).resolves.toStrictEqual([]);
    });

    it('should return an empty array if no docusign integrations found', async () => {
      const organisations = mockOrganisations({
        integrationType: IntegrationEnum.Crm,
        integrationConfig: {
          name: CRMEnum.Redtail,
          credentials: 'encrypted',
        },
      });

      mockExec.mockResolvedValueOnce(organisations);

      await expect(
        service.findDocusignRefreshCandidates(page),
      ).resolves.toStrictEqual([]);
    });

    it('should return an empty array if no docusign refresh candidates found', async () => {
      jest.spyOn(global, 'Date').mockRestore();
      const lastRefresh = new Date('2123-09-30T11:37:54.176Z');

      const organisations = mockOrganisations({
        integrationType: IntegrationEnum.DocumentSigning,
        integrationConfig: {
          name: DocumentSigningEnum.Docusign,
          credentials: 'encrypted',
          lastRefresh,
        },
      });

      mockExec.mockResolvedValueOnce(organisations);

      await expect(
        service.findDocusignRefreshCandidates(page),
      ).resolves.toStrictEqual([]);
    });
  });

  describe('addMasterAccount', () => {
    const addMasterAccountDto = { masterAccount: '1234-5678' };

    it('should add a master account successfully', async () => {
      mockSession.mockResolvedValue({
        ...organisationMock,
        masterAccounts: [],
        save: jest.fn().mockResolvedValue({
          ...organisationMock,
          masterAccounts: [addMasterAccountDto.masterAccount],
        }),
      });

      const result = await service.addMasterAccount('id', addMasterAccountDto);
      expect(result.masterAccounts).toContain(
        addMasterAccountDto.masterAccount,
      );
    });

    it('should throw an error if organisation is not found', async () => {
      mockSession.mockResolvedValueOnce(null);

      await expect(
        service.addMasterAccount('id', addMasterAccountDto),
      ).rejects.toThrow(
        new HttpException('Organisation not found', HttpStatus.NOT_FOUND),
      );
    });

    it('should throw an error if master account already exists', async () => {
      mockSession.mockResolvedValueOnce({
        ...organisationMock,
        masterAccounts: [addMasterAccountDto.masterAccount],
        save: jest.fn().mockResolvedValue(true),
      });

      await expect(
        service.addMasterAccount('id', addMasterAccountDto),
      ).rejects.toThrow(
        new HttpException(
          'Master account already exists',
          HttpStatus.BAD_REQUEST,
        ),
      );
    });
  });

  describe('getMasterAccounts', () => {
    const organisationId = 'someId';

    it('should return master accounts for an existing organisation', async () => {
      const masterAccounts = ['account1', 'account2'];
      mockSession.mockResolvedValueOnce({
        ...organisationMock,
        masterAccounts,
      });

      const result = await service.getMasterAccounts(organisationId);
      expect(result).toEqual(masterAccounts);
    });

    it('should throw an error if the organisation does not exist', async () => {
      mockSession.mockResolvedValueOnce(null);

      await expect(service.getMasterAccounts(organisationId)).rejects.toThrow(
        new HttpException('Organisation not found', HttpStatus.NOT_FOUND),
      );
    });
  });

  describe('deleteMasterAccount', () => {
    const organisationId = 'someOrgId';
    const masterAccountId = 'someMasterAccountId';

    it('should delete the master account for an existing organisation', async () => {
      const masterAccounts = [masterAccountId, 'account2'];
      const orgWithMasterAccounts = {
        ...organisationMock,
        masterAccounts,
        save: jest.fn().mockResolvedValue({
          ...organisationMock,
          masterAccounts: ['account2'],
        }),
      };

      mockSession.mockResolvedValue(orgWithMasterAccounts);

      await service.deleteMasterAccount(organisationId, masterAccountId);
      expect(orgWithMasterAccounts.save).toHaveBeenCalled();
      expect(orgWithMasterAccounts.masterAccounts).toEqual(['account2']);
    });

    it('should throw an error if the organisation does not exist', async () => {
      mockSession.mockResolvedValue(null);

      await expect(
        service.deleteMasterAccount(organisationId, masterAccountId),
      ).rejects.toThrow(
        new HttpException('Organisation not found', HttpStatus.NOT_FOUND),
      );
    });

    it('should not modify master accounts if the provided master account id does not exist', async () => {
      const masterAccounts = ['account1', 'account2'];
      const orgWithMasterAccounts = {
        ...organisationMock,
        masterAccounts,
        save: jest.fn().mockResolvedValue({
          ...organisationMock,
          masterAccounts,
        }),
      };

      mockSession.mockResolvedValue(orgWithMasterAccounts);

      await service.deleteMasterAccount(organisationId, masterAccountId);
      expect(orgWithMasterAccounts.save).toHaveBeenCalled();
      expect(orgWithMasterAccounts.masterAccounts).toEqual(masterAccounts);
    });
  });
});
