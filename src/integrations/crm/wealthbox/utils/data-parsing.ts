import { ContactDto } from 'src/clients/dto/v1/create-client.dto';
import { GenericCrmAccount } from 'src/integrations/crm/types/accounts/crm-account.type';
import { DBAccountType } from 'src/integrations/crm/types/accounts/db-account-type.enum';
import { buildAccountNumber } from 'src/integrations/crm/utils/build-account-task';
import { AccountDto } from 'src/shared/types/accounts/account.dto';
import { objectToYAML } from 'src/utils/yaml-parser';
import * as yaml from 'js-yaml';

export interface WealthboxStoredClientInfo {
  additionalInfo?: {
    isUSCitizen?: boolean;
    isUSResident?: boolean;
  };
  accounts: GenericCrmAccount[];
}
const yamlInfo: yaml.LoadOptions = {
  json: true,
};

export function updateDataNoteYAML(newData: WealthboxStoredClientInfo): string {
  return objectToYAML(newData);
}

export function parseDataNoteYAML(data: string): WealthboxStoredClientInfo {
  const contactInfo: WealthboxStoredClientInfo = yaml.load(
    data,
    yamlInfo,
  ) as WealthboxStoredClientInfo;

  return {
    accounts:
      contactInfo?.accounts &&
      contactInfo?.accounts?.map((account: GenericCrmAccount) => ({
        name: account.name,
        type: account.type,
        beneficiaries: account.beneficiaries,
      })),
  };
}

export function contactToYAML(contact: ContactDto): string {
  const nameOnAccount = [contact?.firstName, contact?.lastName]
    .filter(Boolean)
    .join(' ');
  const contactInfo: WealthboxStoredClientInfo = {
    accounts:
      contact?.accounts?.map((account: AccountDto) => {
        return {
          name: buildAccountNumber(
            nameOnAccount,
            DBAccountType[account.type],
            account.label,
            account.ownership,
          ),
          type: account.type,
          beneficiaries: [],
        };
      }) ?? [],
  };
  return yaml.dump(contactInfo, {
    indent: 2,
    noArrayIndent: true,
    flowLevel: -1,
    sortKeys: true,
    lineWidth: -1,
  });
}
