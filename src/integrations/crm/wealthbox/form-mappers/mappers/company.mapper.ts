import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';
import { AddressUpdateRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/company-request.dto';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { GenericUserDefinedFieldsEnum } from 'src/integrations/crm/types/generic/user-defined-fields.enum';
import { insertIntoUserContentField } from 'src/integrations/crm/wealthbox/utils/data-parsing/general-text-field';
import {
  WealthboxAddressKind,
  WealthboxMailingAddress,
} from 'src/integrations/crm/wealthbox/types';
import { Country } from 'src/shared/types/general/country.enum';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';

export class WealthboxCompanyMapper implements FormUpdateMapper {
  constructor(private WealthboxCrm: WealthboxCRM) {}

  /**
   * Updates the contact company and address in Wealthbox CRM.
   * @param userId The ID of the user.
   * @param WealthboxContactId The ID of the contact in Wealthbox CRM.
   * @param companyName The name of the company.
   * @param address The address to update.
   * @returns A Promise that resolves to a boolean indicating success.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    update: AddressUpdateRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const crmContact = await this.WealthboxCrm.contactService.getContact(
      contact.crmClientId,
    );

    const existingWorkAddress = crmContact.street_addresses.find(
      (x) => x.kind === WealthboxAddressKind.Work,
    );

    const workAddress: WealthboxMailingAddress = {
      id: existingWorkAddress?.id,
      street_line_1: update.address.line1,
      street_line_2: update.address.line2,
      city: update.address.city,
      state: update.address.state,
      zip_code: update.address.zip,
      address: update.address.line1,
      country: Country.US,
      kind: WealthboxAddressKind.Work,
      principal: false,
    };

    await this.WealthboxCrm.contactService.updateContact(contact.crmClientId, {
      street_addresses: [workAddress],
    });

    await this.WealthboxCrm.contactService.updateContact(contact.crmClientId, {
      background_information: insertIntoUserContentField(
        crmContact.background_info,
        {
          [GenericUserDefinedFieldsEnum.COMPANY]: update.name,
        },
      ),
    });
  }
}
