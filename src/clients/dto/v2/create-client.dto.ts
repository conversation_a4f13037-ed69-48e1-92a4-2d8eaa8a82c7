import {
  IsArray,
  IsBoolean,
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { AccountDto } from 'src/shared/types/accounts/account.dto';
import { NotificationTypeEnum } from '../../types/notification-types.enum';

export class ContactDto {
  @ApiProperty()
  @IsString()
  firstName: string;

  @ApiProperty()
  @IsString()
  lastName: string;

  @ApiProperty()
  @IsOptional()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  mobile: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  skipContactInterview: boolean;

  @ApiProperty()
  @ValidateNested()
  @IsOptional()
  accounts: AccountDto[];

  @ApiProperty()
  @IsString()
  @IsOptional()
  crmClientId: string;
}

class PrimaryAdvisorDto {
  @ApiProperty()
  @IsString()
  id: number;
}

export class CreateClientDto {
  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  shouldUpdateCrm?: boolean = true;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  featuresSelected?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  docusignSelected?: boolean = false;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isAlreadyOnbord?: boolean = false;

  @ApiProperty()
  @IsBoolean()
  readyToSend: boolean;

  @ApiProperty()
  @IsBoolean()
  sendNow: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  doClientProfiling?: boolean = true;

  @ApiProperty({ type: () => ContactDto })
  @ValidateNested()
  @Type(() => ContactDto)
  primaryContact: ContactDto;

  @ApiProperty({ type: () => ContactDto })
  @ValidateNested()
  @IsOptional()
  @Type(() => ContactDto)
  secondaryContact: ContactDto;

  @ApiProperty({ type: () => PrimaryAdvisorDto })
  @ValidateNested()
  @IsOptional()
  @Type(() => PrimaryAdvisorDto)
  primaryAdvisor?: PrimaryAdvisorDto;

  @ApiProperty({ type: () => PrimaryAdvisorDto })
  @ValidateNested()
  @IsOptional()
  @Type(() => PrimaryAdvisorDto)
  primaryCSA: PrimaryAdvisorDto;

  @ApiProperty({ required: false })
  @IsOptional()
  secondaryAdvisor?: [];

  @ApiProperty({ required: false })
  @IsOptional()
  secondaryCSA?: [];

  @ApiProperty()
  @IsOptional()
  customTemplates: string[];

  @ApiProperty()
  @IsOptional()
  addAccountsSelected: boolean;

  @ApiProperty()
  @IsOptional()
  transitionId?: string;

  @ApiProperty({
    description: 'Whether to send ADV2B attachment in welcome emails',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  sendAdv2b?: boolean = true;

  @ApiProperty({
    enum: NotificationTypeEnum,
    isArray: true,
    enumName: 'NotificationTypeEnum',
    description: 'Notification methods such as email or sms.'
  })
  @IsArray()
  @IsEnum(NotificationTypeEnum, { each: true })
  @IsOptional()
  notificationMethods?: NotificationTypeEnum[];
}

