import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsBoolean, IsEnum } from 'class-validator';
import { DocusignAccountOwnershipEnum } from '../docusign.types';

export class AccountInfoDto {
  @ApiProperty()
  @IsString()
  username: string;

  @ApiProperty()
  @IsString()
  email: string;

  @ApiProperty()
  @IsString()
  accountId: string;

  @ApiProperty()
  @IsBoolean()
  isDefault: boolean;

  @ApiProperty()
  @IsString()
  accountName: string;

  @ApiProperty()
  @IsString()
  baseUri: string;

  @ApiProperty()
  @IsEnum(DocusignAccountOwnershipEnum)
  accountOwnership: DocusignAccountOwnershipEnum;

  @ApiProperty()
  accounts: {
    accountId: string;
    accountName: string;
    baseUri: string;
    isDefault: boolean;
  }[];
}
