import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

export class ContingentBeneficiariesPageVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      contingentBeneficiaryCount: {
        description: 'Number of contingent beneficiaries',
        type: VariableType.NUMBER,
        example: 1,
      },
      contingentBeneficiaryNames: {
        description: 'Names of all contingent beneficiaries',
        type: VariableType.ARRAY,
        example: ['<PERSON>'],
      },
      contingentBeneficiaryRelationships: {
        description: 'Relationships to the client',
        type: VariableType.ARRAY,
        example: ['Sister'],
      },
      contingentBeneficiaryPercentages: {
        description: 'Percentage allocations for each contingent beneficiary',
        type: VariableType.ARRAY,
        example: [100],
      },
      contingentTotalAllocation: {
        description: 'Total contingent percentage allocated (should equal 100%)',
        type: VariableType.PERCENTAGE,
        example: 100,
      },
    };
  }
}