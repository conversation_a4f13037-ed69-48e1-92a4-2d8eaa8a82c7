import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document } from 'mongoose';
import { Address, AddressSchema } from 'src/shared/schemas/address.schema';
import {
  Integration,
  IntegrationSchema,
} from 'src/shared/schemas/integration.schema';
import { OrganisationInfo } from 'src/shared/schemas/organisation-info.schema';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { UserStatusesEnum } from 'src/shared/types/rbac/statuses.enum';
import { Factory, DataFactory } from 'nestjs-seeder';
import { Asset, AssetSchema } from 'src/shared/schemas/asset.schema';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

@Schema({ _id: false })
export class AdvisorPersonalInfo {
  @Factory((faker, advisor: any) => advisor.firstName)
  @Prop()
  firstName: string;

  @Factory((faker, advisor: any) => advisor.lastName)
  @Prop()
  lastName: string;

  @Factory((_) => {
    return DataFactory.createForClass(Address).generate(1)[0];
  })
  @Prop({ type: AddressSchema })
  address: Address;

  @Factory((faker, advisor: any) => advisor.phone)
  @Prop()
  phone: string;

  @Factory((faker, advisor: any) => advisor.email)
  @Prop()
  email: string;
}

@Schema()
export class Advisor extends Document {
  @Factory((_, { organisation }) => organisation.id)
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Organisation',
    required: true,
  })
  organisation: mongoose.Types.ObjectId | Organisation;

  @Factory((_, { advisor }) => {
    return DataFactory.createForClass(AdvisorPersonalInfo).generate(
      1,
      advisor,
    )[0];
  })
  @Prop({ type: Object })
  personalInfo: AdvisorPersonalInfo;

  @Factory(UserStatusesEnum.ACTIVE)
  @Prop({
    type: String,
    enum: UserStatusesEnum,
    index: true,
  })
  status: UserStatusesEnum;

  @Factory(RolesEnum.CompanyAdmin)
  @Prop({ type: String, enum: RolesEnum, index: true })
  role: RolesEnum;

  @Factory(() => [])
  @Prop({ type: [IntegrationSchema] })
  integrations: Integration[];

  @Factory(() => [])
  @Prop({ type: [AssetSchema], default: [] })
  assets: Asset[];

  @Prop({ type: mongoose.Schema.Types.Mixed, default: null })
  crmId: number | string;

  @Prop({ type: Date, default: Date.now })
  createdAt: Date;

  @Prop({ type: Date, default: Date.now })
  updatedAt: Date;
}

export const AdvisorSchema = SchemaFactory.createForClass(Advisor);