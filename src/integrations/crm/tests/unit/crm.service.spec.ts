import { Test, TestingModule } from '@nestjs/testing';
import { CRMService } from 'src/integrations/crm/crm.service';
import { CRMType } from 'src/shared/types/integrations';
import { Credentials } from 'src/auth/auth.types';
import { HttpService } from '@nestjs/axios';
import { ClsService } from 'nestjs-cls';
import { InterviewsService } from 'src/interviews/interviews.service';
import mongoose from 'mongoose';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { MailService } from 'src/notifications/mail/mail.service';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';

describe.skip('CRMService', () => {
  let service: CRMService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CRMService,
        {
          provide: InterviewsService,
          useValue: {
            create: jest.fn(),
            update: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: ClsService,
          useValue: {
            get: jest.fn(),
          },
        },
        {
          provide: HttpService,
          useValue: {
            get: jest.fn(),
            post: jest.fn(),
            put: jest.fn(),
          },
        },
        {
          provide: SuperHttpService,
          useValue: {
            get: jest.fn(),
            post: jest.fn(),
            put: jest.fn(),
          },
        },
        {
          provide: MailService,
          useValue: {
            sendMail: jest.fn(),
          },
        },
        {
          provide: WINSTON_MODULE_PROVIDER,
          useValue: {
            info: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<CRMService>(CRMService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    let credentials: Credentials;
    let advisor: AdvisorWithRole;
    let organisation: Organisation;
    
    beforeEach(() => {
      credentials = {
        username: 'test',
        password: 'test',
      };
      
      advisor = {
        _id: new mongoose.Types.ObjectId(),
      } as AdvisorWithRole;
      
      organisation = {
        _id: new mongoose.Types.ObjectId(),
        name: 'Test Organisation',
      } as Organisation;
    });

    it('should return a CRM instance if valid type is provided', async () => {
      const crmInstance = await service.create(
        advisor,
        organisation,
        'Redtail' as CRMType,
        credentials
      );
      expect(crmInstance).toBeDefined();
    });

    it('should throw an exception if invalid type is provided', async () => {
      await expect(
        service.create(
          advisor,
          organisation,
          'InvalidCRMType' as CRMType,
          credentials
        ),
      ).rejects.toThrow(`Unsupported CRM type: InvalidCRMType`);
    });
  });
});
