FROM node:20 as development
WORKDIR /usr/src/app

# Clean install dependencies
COPY package*.json ./
RUN npm ci --legacy-peer-deps

COPY . .

# Runner stage
FROM node:20 AS runner

WORKDIR /usr/src/app

# Copy package files and install production dependencies
COPY package*.json ./
RUN npm ci --only=production --legacy-peer-deps

# Copy from development stage
COPY --from=development /usr/src/app .

# Build the application
RUN npm run build

# Expose ports
EXPOSE 3000
EXPOSE 9229

CMD ["npm", "run", "start:debug"]