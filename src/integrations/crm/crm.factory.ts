import { Injectable } from '@nestjs/common';
import { CRMEnum, CRMClasses } from '../../shared/types/integrations';
import { CRM } from './crm.interface';
import { RedtailCRM } from './redtail/crm.redtail';
import { CrmCredentials } from 'src/auth/auth.types';
import { InterviewsService } from 'src/interviews/interviews.service';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';

import { Logger } from 'winston';
import { MailService } from 'src/notifications/mail/mail.service';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { SalesforceCRM } from 'src/integrations/crm/salesforce/crm.salesforce';
import { PractifiCRM } from './practifi/crm.practifi';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

@Injectable()
export class CRMFactory {
  public static createCRM(
    advisor: AdvisorWithRole,
    organisation: Organisation,
    type: keyof typeof CRMEnum,
    credentials: CrmCredentials,
    superHttp: SuperHttpService,
    interviewsService: InterviewsService,
    mailService: MailService,
    logger: Logger,
  ): CRM {
    const crms: CRMClasses = {
      Redtail: RedtailCRM,
      Wealthbox: WealthboxCRM,
      Salesforce: SalesforceCRM,
      Practifi: PractifiCRM,
    };

    const CRMClass = crms[type];
    if (CRMClass) {
      return new CRMClass(
        advisor,
        organisation,
        credentials,
        superHttp,
        interviewsService,
        mailService,
        logger,
      );
    } else {
      throw new Error(`Unsupported CRM type: ${type}`);
    }
  }
}
