import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { WealthboxCRM } from 'src/integrations/crm/wealthbox/crm.wealthbox';
import { EmploymentStatusRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/employment-status-request.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { insertIntoUserContentField } from 'src/integrations/crm/wealthbox/utils/data-parsing/general-text-field';
import { GenericUserDefinedFieldsEnum } from 'src/integrations/crm/types/generic/user-defined-fields.enum';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';

export class WealthboxEmploymentMapper implements FormUpdateMapper {
  constructor(private WealthboxService: WealthboxCRM) {}

  /**
   * Maps employment status page data to Wealthbox CRM.
   * @param clientCrmId The client's CRM ID.
   * @param organisation The organisation object.
   * @param pageData The employment status request DTO.
   * @returns A promise that resolves with the mapped data.
   * @throws HttpException if the employment status UDF is not configured.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: EmploymentStatusRequestDto,
  ) {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const crmContact = await this.WealthboxService.contactService.getContact(
      contact.crmClientId,
    );

    // Remove company and job pages if the user is retired, a student, or a homemaker
    if (
      ['retired', 'student', 'home-maker', 'homemaker'].includes(
        pageData.status.toLowerCase(),
      )
    ) {
      await this.WealthboxService.interviewsService.removePageFromInterview(
        interview._id.toString(),
        PagesEnum.COMPANY,
      );
      await this.WealthboxService.interviewsService.removePageFromInterview(
        interview._id.toString(),
        PagesEnum.JOB,
      );
    } else {
      // Add company and job pages if the user is not retired, a student, or a homemaker
      await this.WealthboxService.interviewsService.addPageToInterview(
        interview._id.toString(),
        PagesEnum.COMPANY,
      );
      await this.WealthboxService.interviewsService.addPageToInterview(
        interview._id.toString(),
        PagesEnum.JOB,
      );
    }

    await this.WealthboxService.contactService.updateContact(
      contact.crmClientId,
      {
        background_information: insertIntoUserContentField(
          crmContact.background_info,
          {
            [GenericUserDefinedFieldsEnum.EMPLOYMENT_STATUS]: pageData.status,
          },
        ),
      },
    );
  }
}
