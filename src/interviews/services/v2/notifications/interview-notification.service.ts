import { Injectable, HttpException, HttpStatus, Inject, forwardRef } from '@nestjs/common';
import { MailService } from 'src/notifications/mail/mail.service';
import { EnrichedInterview } from '../../../dto/v1/enriched-interview-dto';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { ClientsV2Service } from 'src/clients/services/v2/clients-v2.service';
import { EmailTemplateNameEnum } from 'src/templates/mail/types';
import { DesktopInterviewEmailContext } from 'src/templates/mail/desktop-interview/desktop-interview.context';
import { NonCitizenEmailContext } from 'src/templates/mail/non-citizen/non-citizen.context';
import { ReadyToSignEmailContext } from 'src/templates/mail/ready-to-sign/ready-to-sign.context';
import { getFrontendDomain } from 'src/utils/getFrontendDomain';
import { Interview } from '../../../schemas/v1/interview.schema';
import { InjectModel } from '@nestjs/mongoose';
import { Model, ClientSession } from 'mongoose';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { isEmpty } from 'lodash';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';

@Injectable()
export class InterviewNotificationService {
  constructor(
    private readonly mailService: MailService,
    @Inject(forwardRef(() => OrganisationsService)) private readonly organisationsService: OrganisationsService,
    @Inject(forwardRef(() => ClientsV2Service)) private readonly clientsService: ClientsV2Service,
    @InjectModel(Interview.name) private readonly interviewModel: Model<Interview>,
  ) {}

  async sendDesktopInterviewEmail(id: string) {
    const interview = await this.interviewModel.findById(id).populate('client');
    if (!interview) throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);

    const client = await this.clientsService.findOne({ _id: interview.client });
    const organisation = await this.organisationsService.findOne(
      client.organisationId.toString(),
    );

    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const to: string = contact.email;
    const templateName = EmailTemplateNameEnum.DesktopInterview;
    const context: DesktopInterviewEmailContext = {
      organisationName: organisation.name,
      interviewUrl: `https://${getFrontendDomain()}/interview/${interview._id}`,
    };

    return this.mailService.sendEmail(
      { to, templateName, context, organisation },
      contact,
      client.primaryAdvisor.id?.toString(),
    );
  }

  async sendNonCitizenEmail(id: string) {
    const interview = await this.interviewModel.findById(id).populate('client');
    if (!interview) throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);

    const client = await this.clientsService.findOne({ _id: interview.client });
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const to: string = contact.email;
    const templateName = EmailTemplateNameEnum.NonCitizen;
    const context: NonCitizenEmailContext = {
      clientFirstName: contact.firstName,
      clientLastName: contact.lastName,
    };

    await this.interviewModel.findByIdAndUpdate(id, {
      $set: {
        isComplete: true,
        updatedAt: new Date(),
      },
    });

    const organisation = await this.organisationsService.findOne(
      client.organisationId.toString(),
    );

    return this.mailService.sendEmail(
      { to, templateName, context, organisation },
      contact,
      client.primaryAdvisor.id?.toString(),
    );
  }

  async sendReadyToSignEmail(interview: EnrichedInterview) {
    const client = interview.client as EnrichedClient;

    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const to: string = client.primaryCSA.email;
    const templateName = EmailTemplateNameEnum.ReadyToSign;
    const context: ReadyToSignEmailContext = {
      csaFirstName: client.primaryCSA.firstName,
      csaLastName: client.primaryCSA.lastName,
      clientFirstName: contact.firstName,
      clientLastName: contact.lastName,
      docusignAccounts: contact.accounts
        .filter((account) => !account.type.toLowerCase().includes('offline'))
        .map((account) => `<li>${account.label}</li>`)
        .join(' '),
      offlineAccounts: contact.accounts
        .filter((account) => account.type.toLowerCase().includes('offline'))
        .map((account) => `<li>${account.label}</li>`)
        .join(' '),
    };

    const organisation = await this.organisationsService.findOne(
      client.organisationId.toString(),
    );

    return this.mailService.sendEmail(
      { to, templateName, context, organisation },
      contact,
      client.primaryAdvisor.id?.toString()
    );
  }

  async sendNotification(interviewId: string, session: ClientSession) {
    const interview = await this.interviewModel.findById(interviewId).session(session);
    if (!interview) throw new HttpException('Interview not found', HttpStatus.NOT_FOUND);

    const client = await this.clientsService.findOne({ _id: interview.client }, session);

    if (!client) {
      throw new HttpException('Client not found', HttpStatus.NOT_FOUND);
    }

    if (client.status === ClientStatusEnum.PendingReview) {
      await this.sendReadyToSignEmail({ ...interview.toObject(), client } as EnrichedInterview);
    }
  }
}