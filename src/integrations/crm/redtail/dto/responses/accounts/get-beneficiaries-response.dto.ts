import { RedtailBeneficiary } from 'src/integrations/crm/redtail/types/account-beneficiary.type';
import { RedtailMetaData } from 'src/integrations/crm/redtail/types/metadata.type';
import { Camelize } from 'src/shared/types/monads';

export class GetBeneficiariesResponseDto {
  account_beneficiaries: RedtailBeneficiary[];
  meta: RedtailMetaData;
}

export type GetBeneficiariesResponse = Camelize<GetBeneficiariesResponseDto>;
