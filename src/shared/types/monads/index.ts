export type Action<T> = (arg?: T) => Action<unknown>;

export type Predicate<T> = (arg?: T | void) => boolean;

export type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

export type XOR<T, U> = T | U extends object
  ? (Without<T, U> & U) | (Without<U, T> & T)
  : T | U;

type CamelizeString<T extends PropertyKey> = T extends string
  ? string extends T
    ? string
    : T extends `${infer F}_${infer R}`
    ? `${F}${Capitalize<CamelizeString<R>>}`
    : T
  : T;

export type Camelize<T> = {
  [K in keyof T as CamelizeString<K>]: T[K] extends (...args: any[]) => any
    ? T[K]
    : T[K] extends Array<infer U>
    ? U extends object
      ? Camelize<U>[]
      : T[K]
    : T[K] extends object
    ? Camelize<T[K]>
    : T[K];
};

type SnakeCaseString<T extends string> = T extends `${infer Head}${infer Tail}`
  ? `${Head extends Uppercase<Head>
      ? '_'
      : ''}${Lowercase<Head>}${SnakeCaseString<Tail>}`
  : T;

export type SnakeCase<T> = {
  [K in keyof T as SnakeCaseString<string & K>]: T[K] extends (infer U)[]
    ? U extends object
      ? SnakeCase<U>[]
      : T[K]
    : T[K] extends object
    ? SnakeCase<T[K]>
    : T[K];
};
