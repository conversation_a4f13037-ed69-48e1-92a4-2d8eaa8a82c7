import { CrmCredentials } from 'src/auth/auth.types';
import {
  EnrichedClient,
  EnrichedContact,
} from 'src/clients/dto/v1/get-clients.dto';
import { LogCommunicationDto } from 'src/integrations/crm/types/communications/log-communication.dto';
import { CrmContact } from 'src/integrations/crm/types/contacts/crm-contact.type';
import {
  CreateContactDto,
  CreateContactResponseDto,
  UpdateContactDto,
  UpdateContactResponseDto,
} from 'src/integrations/crm/types/create-contact.dto';
import { GenericEmail } from 'src/integrations/crm/types/generic/email.type';
import { GenericPhone } from 'src/integrations/crm/types/generic/phone.type';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PaginationQueryDto } from 'src/shared/dto/pagination.dto';
import { CRMType } from 'src/shared/types/integrations';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { FormUpdateMapper } from './types/form-update-mapper';
import { CrmPageInfoUpdateDto } from './types/page-info-update.dto';
import { User } from './types/user.type';
import { SendEmailOptions } from 'src/notifications/mail/mail.types';

export type PageUpdateMapper = { [key in PagesEnum]: FormUpdateMapper };
export interface CRM {
  pageUpdateMapper: PageUpdateMapper;
  initCrm(): Promise<Map<string, string | number>>;
  authenticate(): Promise<CrmCredentials>;
  createContact(
    createContactDto: CreateContactDto,
  ): Promise<CreateContactResponseDto>;
  updateContact(
    updateContactDto: UpdateContactDto,
  ): Promise<UpdateContactResponseDto>;
  getContact(
    id: string,
    includeAccounts: boolean,
    includeAdditionalInfo: boolean,
  ): Promise<CrmContact>;
  getContacts(pagination?: PaginationQueryDto): Promise<CrmContact[]>;
  getAllUsers(pagination?: PaginationQueryDto): Promise<User[]>;
  getType(): Promise<CRMType>;
  getMainEmailOfContact(id: string | number): Promise<GenericEmail>;
  getPhoneNumbersOfContact(id: string | number): Promise<GenericPhone[]>;
  getMainMobileOfContact(id: string): Promise<string>;
  updatePageInfo(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageInfo: CrmPageInfoUpdateDto,
  ): Promise<unknown>;
  getAuthenticatedUserId(): Promise<string>;
  initMappers(): PageUpdateMapper;
  logCommunication(
    crmClientId: string,
    logCommunicationDto: LogCommunicationDto,
  ): Promise<void>;
  sendEmail(
    emailOptions: SendEmailOptions,
    contact?: EnrichedContact,
    advisorId?: string,
  ): Promise<void>;
}
