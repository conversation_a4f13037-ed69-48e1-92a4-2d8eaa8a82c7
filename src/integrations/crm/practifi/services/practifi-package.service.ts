import { Injectable } from '@nestjs/common';
import { Connection } from 'jsforce';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { generateRequestConfigs } from '../utils/generate-request-configs.util';
import { OperationType } from 'src/super-http/enums';
import { PractifiEntityTypes } from '../types/enums';

@Injectable()
export class PractifiPackageService {
    private connection: Connection;

    constructor(
      private readonly httpService: SuperHttpService,
      private tenantId: string
    ) {}

    setConnection(connection: Connection) {
      this.connection = connection;
    }

    async isPackageInstalled() {
      try {
        const salesforceBaseUrl = `${this.connection.instanceUrl}/services/data/v${this.connection.version}`;
        const salesforceAcessToken = this.connection.accessToken;
        const requestConfigs = generateRequestConfigs(salesforceAcessToken);
        const { withInterceptors, ...configsWithoutInterceptors } = requestConfigs;

        await this.httpService.get(
          `${salesforceBaseUrl}/sobjects/practifi__Component__c/`,
          this.tenantId,
          {
            rateLimiter: 'practifi',
            integrationName: 'practifi',
            operation: OperationType.GET,
            entityType: PractifiEntityTypes.ACCOUNT,
            forceFresh: true,
          },
          configsWithoutInterceptors
        );
        return true;
      } catch (error) {
        return false;
      }
    }
}