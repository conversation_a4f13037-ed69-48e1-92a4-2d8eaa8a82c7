import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Query,
  Put,
  Patch,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiAuthProtectedRoutes } from 'src/shared/decorators/api-auth.decorator';
import { Roles } from 'src/shared/guards/roles.guard';
import { OrganisationGuard } from 'src/shared/guards/organisation.guard';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { Transaction } from 'src/shared/decorators/transaction.decorator';
import { ClientSession } from 'mongoose';
import { EmailTemplatesService } from '../services/email-templates.service';
import { CreateEmailTemplateDto } from '../dto/email-template/create-email-template.dto';
import { GetEmailTemplatesQueryDto, EmailTemplatesResponseDto } from '../dto/email-template/get-email-templates.dto';
import { EmailTemplateDto } from '../dto/email-template/email-template.dto';
import { plainToInstance } from 'class-transformer';
import { ApiOperation } from '@nestjs/swagger';

@ApiAuthProtectedRoutes()
@UseGuards(AuthGuard('jwt'), OrganisationGuard)
@Controller('/organisations/:organisationId/emailTemplates')
export class EmailTemplatesController {
  constructor(
    private readonly emailTemplatesService: EmailTemplatesService,
  ) {}

  @Post()
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.SuperAdmin)
  @ApiOperation({
    summary: 'Create a new email template',
    description: 'Creates a new email template for an organisation.'
  })
  async create(
    @Param('organisationId') organisationId: string,
    @Body() createEmailTemplateDto: CreateEmailTemplateDto,
    session?: ClientSession,
  ): Promise<EmailTemplateDto> {
    const template = await this.emailTemplatesService.create(
      organisationId,
      createEmailTemplateDto,
      session,
    );
    return plainToInstance(EmailTemplateDto, template, {
      excludeExtraneousValues: true,
    });
  }

  @Get()
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.SuperAdmin, RolesEnum.Representative)
  @ApiOperation({
    summary: 'Get all email templates for an organisation',
    description: 'Retrieves all email templates for an organisation with pagination, filtering, and search capabilities. Search works on template name and content.'
  })
  async findAll(
    @Param('organisationId') organisationId: string,
    @Query() query: GetEmailTemplatesQueryDto,
  ): Promise<EmailTemplatesResponseDto> {
    const { result, totalResults } = await this.emailTemplatesService.findAll(
      organisationId,
      query,
    );
    
    return {
      result: plainToInstance(EmailTemplateDto, result, {
        excludeExtraneousValues: true,
      }),
      totalResults,
    };
  }

  @Get(':id')
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.SuperAdmin, RolesEnum.Representative)
  @ApiOperation({
    summary: 'Get a specific email template',
    description: 'Retrieves a specific email template by ID.'
  })
  async findOne(
    @Param('organisationId') organisationId: string,
    @Param('id') id: string,
  ): Promise<EmailTemplateDto> {
    const template = await this.emailTemplatesService.findOne(organisationId, id);
    return plainToInstance(EmailTemplateDto, template, {
      excludeExtraneousValues: true,
    });
  }

  @Put(':id')
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.SuperAdmin)
  @ApiOperation({
    summary: 'Update an email template',
    description: 'Updates an existing email template.'
  })
  async update(
    @Param('organisationId') organisationId: string,
    @Param('id') id: string,
    @Body() updateEmailTemplateDto: CreateEmailTemplateDto,
    session?: ClientSession,
  ): Promise<EmailTemplateDto> {
    const template = await this.emailTemplatesService.update(
      organisationId,
      id,
      updateEmailTemplateDto,
      session,
    );
    return plainToInstance(EmailTemplateDto, template, {
      excludeExtraneousValues: true,
    });
  }

  @Delete(':id')
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.SuperAdmin)
  @ApiOperation({
    summary: 'Delete an email template',
    description: 'Deletes an email template permanently.'
  })
  async remove(
    @Param('organisationId') organisationId: string,
    @Param('id') id: string,
    session?: ClientSession,
  ): Promise<void> {
    await this.emailTemplatesService.remove(organisationId, id, session);
  }

  @Patch(':id/toggle-active')
  @Roles(RolesEnum.CompanyAdmin, RolesEnum.SuperAdmin)
  @ApiOperation({
    summary: 'Toggle the active status of an email template',
    description: 'Toggles the active status of an email template between active and inactive.'
  })
  async toggleActive(
    @Param('organisationId') organisationId: string,
    @Param('id') id: string,
  ): Promise<EmailTemplateDto> {
    const template = await this.emailTemplatesService.toggleActive(organisationId, id);
    return plainToInstance(EmailTemplateDto, template, {
      excludeExtraneousValues: true,
    });
  }
} 