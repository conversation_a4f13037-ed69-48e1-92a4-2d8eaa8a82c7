// /path/to/services/wealthbox-notes.service.ts
// WealthboxNotesService in a NestJS backend for interacting with Wealthbox Notes API

import { Injectable } from '@nestjs/common';
import { Logger } from 'winston';
import { CreateNoteRequestDto } from 'src/integrations/crm/wealthbox/dto/notes/create-note.dto';
import { UpdateNoteRequestDto } from 'src/integrations/crm/wealthbox/dto/notes/update-note.dto';
import {
  GetNotesRequestDto,
  GetNotesResponseDto,
} from 'src/integrations/crm/wealthbox/dto/notes/get-notes.dto';
import { WealthboxPaginationDto } from 'src/integrations/crm/wealthbox/types/pagination.type';
import { HttpMethodsEnum } from 'src/shared/types/general/http-methods.enum';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { Note } from 'src/integrations/crm/wealthbox/types/note.type';
import { snakeifyUrlParams } from 'src/utils/urlParams';
import { OperationType } from 'src/super-http/enums';

@Injectable()
export class WealthboxNotesService {
  private readonly baseUrl: string;

  constructor(
    private httpService: SuperHttpService,
    private authHeader: object,
    private logger: Logger,
    private tenantId: string
  ) {
    this.baseUrl = process.env.WEALTHBOX_BASE_URL;
  }

  public async listNotes(
    queryParams?: GetNotesRequestDto,
    pagination: WealthboxPaginationDto = { page: 1, per_page: 25 },
  ): Promise<GetNotesResponseDto> {
    const query = snakeifyUrlParams({
      ...queryParams,
      ...pagination,
    }).toString();
    const url = `${this.baseUrl}/notes${query ? `?${query}` : ''}`;

    return this.httpService.get<GetNotesResponseDto>(
      url,
      this.tenantId,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.LIST, entityType: 'note', cacheKeysObject: { query } },
      { headers: this.authHeader }
    );
  }

  public async createNote(noteData: CreateNoteRequestDto): Promise<Note> {
    const url = `${this.baseUrl}/notes`;
    return this.httpService.post<Note>(
      url,
      this.tenantId,
      noteData,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.CREATE, entityType: 'note' },
      { headers: this.authHeader }
    );
  }

  public async updateNote(
    noteId: number,
    noteData: UpdateNoteRequestDto,
  ): Promise<Note> {
    const url = `${this.baseUrl}/notes/${noteId}`;
    return this.httpService.put<Note>(
      url,
      this.tenantId,
      noteData,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.UPDATE, entityType: 'note', entityId: noteId.toString() },
      { headers: this.authHeader }
    );
  }

  public async getNote(noteId: number): Promise<Note> {
    const url = `${this.baseUrl}/notes/${noteId}`;
    return this.httpService.get<Note>(
      url,
      this.tenantId,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.GET, entityType: 'note', entityId: noteId.toString() },
      { headers: this.authHeader }
    );
  }

  public async deleteNote(noteId: number): Promise<Note> {
    const url = `${this.baseUrl}/notes/${noteId}`;
    return this.httpService.delete<Note>(
      url,
      this.tenantId,
      { rateLimiter: 'wealthbox', integrationName: 'wealthbox', operation: OperationType.DELETE, entityType: 'note', entityId: noteId.toString() },
      { headers: this.authHeader }
    );
  }

  public async upsertDataNote(
    contactId: string,
    noteData: UpdateNoteRequestDto,
  ): Promise<Note> {
    const notes = await this.listNotes({
      resource_id: Number(contactId),
      resource_type: 'Contact',
    });

    if (!notes?.status_updates?.length) {
      return this.createNote({
        ...noteData,
        tags: ['do_not_delete'],
      });
    }

    const dataNote = notes?.status_updates?.find((note) =>
      note?.tags?.find((tag) => tag.name.toLowerCase() === 'do_not_delete'),
    );

    if (!dataNote) {
      return this.createNote({
        ...noteData,
        tags: ['do_not_delete'],
      });
    }
    return this.updateNote(dataNote.id, {
      ...noteData,
      tags: ['do_not_delete'],
    });
  }

  public async getDataNoteContent(
    contactId: string,
    queryParams?: GetNotesRequestDto,
  ): Promise<Note> {
    const notes = await this.listNotes({
      resource_id: Number(contactId),
      resource_type: 'Contact',
      ...queryParams,
    });

    if (!notes?.status_updates?.length) {
      return null;
    }

    const dataNote = notes?.status_updates?.find((note) =>
      note.tags.find((tag) => tag.name === 'do_not_delete'),
    );

    if (!dataNote) {
      return null;
    }
    
    return {
      ...dataNote,
      content: dataNote.content.replace('<div>', '').replace('</div>', ''),
    };
  }

}
