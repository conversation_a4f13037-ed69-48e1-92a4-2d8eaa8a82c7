import { DynamicModule, Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { CacheModule, CacheStore } from '@nestjs/cache-manager';
import { ClsModule } from 'nestjs-cls';
import KeyvRedis from '@keyv/redis';
import { SuperHttpModuleConfig } from './interfaces';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { redisStore } from 'cache-manager-redis-yet';

@Module({})
export class SuperHttpModule {
  static register(config: SuperHttpModuleConfig): DynamicModule {
    return {
      module: SuperHttpModule,
      imports: [
        HttpModule.register(config.axios || {}),
        ClsModule,
        CacheModule.registerAsync({
          useFactory: async () => {
            const store = await redisStore({
              socket: {
                host: process.env.REDIS_HOST,
                port: Number(process.env.REDIS_PORT),
                tls: process.env.WORK_ENV !== 'dev',
                timeout: 10000,
              },
              password: process.env.REDIS_PASSWORD,
              ttl: 1000,
            });
            return {
              store: store as unknown as CacheStore,
              url: process.env.REDIS_HOST,
              host: process.env.REDIS_HOST,
              port: Number(process.env.REDIS_PORT),
              ttl: 1000,
              isGlobal: true,
            };
          },
        }),
      ],
      providers: [
        {
          provide: 'HTTP_MODULE_CONFIG',
          useValue: config,
        },
        SuperHttpService,
      ],
      exports: [SuperHttpService],
    };
  }

  static registerAsync(configFactory: {
    useFactory: (...args: any[]) => Promise<SuperHttpModuleConfig> | SuperHttpModuleConfig;
    inject?: any[];
  }): DynamicModule {
    return {
      module: SuperHttpModule,
      imports: [
        HttpModule,
        CacheModule.registerAsync({
          useFactory: async () => {
            const store = await redisStore({
              socket: {
                host: process.env.REDIS_HOST,
                port: Number(process.env.REDIS_PORT),
                tls: process.env.WORK_ENV !== 'dev',
                timeout: 10000,
              },
              password: process.env.REDIS_PASSWORD,
              ttl: 1000,
            });
            return {
              store: store as unknown as CacheStore,
              url: process.env.REDIS_HOST,
              host: process.env.REDIS_HOST,
              port: Number(process.env.REDIS_PORT),
              ttl: 1000,
              isGlobal: true,
            };
          },
          inject: configFactory.inject || [],
        }),
      ],
      providers: [
        {
          provide: 'HTTP_MODULE_CONFIG',
          useFactory: configFactory.useFactory,
          inject: configFactory.inject || [],
        },
        SuperHttpService,
      ],
      exports: [SuperHttpService],
    };
  }
}