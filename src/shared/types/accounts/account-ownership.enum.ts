export enum AccountOwnershipEnum {
  Joint = 'joint',
  Individual = 'individual',
  JTIC = 'JTIC',
  JTWROS = 'JTWROS',
  entirety = 'entirety',
}

export enum AccountOwnerEnum {
  PrimaryContact = 'PrimaryContact',
  SecondaryContact = 'SecondaryContact',
}

export enum DocusignRoleNameEnum {
  PrimaryContact = 'PrimaryContact',
  SecondaryContact = 'SecondaryContact',
  PrimaryAdvisor = 'PrimaryAdvisor',
}

export enum DocusignTemplateRoleEnum {
  PrimaryContact = '1st Signer',
  SecondaryContact = '2nd Signer',
  PrimaryAdvisor = 'PrimaryAdvisor',
}
