import { Test } from '@nestjs/testing';
import { CognitoService } from '../../cognito.service';
import * as AWS from 'aws-sdk';
import { InviteUserDto } from 'src/auth/dto/invite.dto';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';

jest.mock('aws-sdk', () => {
  const mockAwsCognito = {
    initiateAuth: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({
        AuthenticationResult: { AccessToken: 'testToken' },
      }),
    }),
    respondToAuthChallenge: jest.fn().mockReturnValue({
      promise: jest
        .fn()
        .mockResolvedValue({ ChallengeName: 'PASSWORD_VERIFIER' }),
    }),
    adminCreateUser: jest.fn().mockReturnValue({
      promise: jest
        .fn()
        .mockResolvedValue({ User: { Username: 'testuser', Attributes: {} } }),
    }),
    adminGetUser: jest.fn().mockReturnValue({
      promise: jest
        .fn()
        .mockResolvedValue({ User: { email: '<EMAIL>' } }),
    }),
    adminResetUserPassword: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({}),
    }),
    confirmForgotPassword: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({}),
    }),
    adminUserGlobalSignOut: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({}),
    }),
    adminDeleteUser: jest.fn().mockReturnValue({
      promise: jest.fn().mockResolvedValue({}),
    }),
  };
  return {
    CognitoIdentityServiceProvider: jest.fn(() => mockAwsCognito),
  };
});

describe('CognitoService', () => {
  let service: CognitoService;
  const cognitoMock = new AWS.CognitoIdentityServiceProvider();

  beforeAll(() => {
    process.env.AWS_REGION = 'us-east-1';
    process.env.AWS_COGNITO_USER_POOL_ID = 'testPoolId';
  });

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [CognitoService],
    }).compile();

    service = module.get<CognitoService>(CognitoService);
  });

  it('should initiate auth', async () => {
    const params = {
      AuthFlow: 'USER_PASSWORD_AUTH',
      AuthParameters: {},
      ClientId: 'test',
    };
    await service.initiateAuth(params);
    expect(cognitoMock.initiateAuth).toHaveBeenCalledWith(params);
  });

  it('should respond to auth challenge', async () => {
    const params = {
      ChallengeName: 'PASSWORD_VERIFIER',
      ClientId: 'test',
      ChallengeResponses: {},
    };
    await service.respondToAuthChallenge(params);
    expect(cognitoMock.respondToAuthChallenge).toHaveBeenCalledWith(params);
  });

  it('should create user', async () => {
    const userDto: InviteUserDto = {
      email: '<EMAIL>',
      phoneNumber: '+*********',
      firstName: 'Test',
      lastName: 'User',
      organisationId: 'test',
      role: RolesEnum.CompanyAdmin,
    };
    await service.adminCreateUser(userDto);
    expect(cognitoMock.adminCreateUser).toHaveBeenCalled();
  });

  it('should reset user password', async () => {
    const params = { UserPoolId: 'test', Username: 'testuser' };
    await service.adminResetUserPassword(params);
    expect(cognitoMock.adminResetUserPassword).toHaveBeenCalledWith(params);
  });

  it('should confirm forgot password', async () => {
    const params = {
      ClientId: 'test',
      ConfirmationCode: '123456',
      Password: 'newpassword',
      Username: 'testuser',
    };
    await service.confirmForgotPassword(params);
    expect(cognitoMock.confirmForgotPassword).toHaveBeenCalledWith(params);
  });

  it('should sign out user', async () => {
    const params = { UserPoolId: 'test', Username: 'testuser' };
    await service.adminUserGlobalSignOut(params);
    expect(cognitoMock.adminUserGlobalSignOut).toHaveBeenCalledWith(params);
  });

  it('should verify if email already exists in cognito', async () => {
    const email = '<EMAIL>';
    await service.getCognitoUser(email);
    expect(cognitoMock.adminGetUser).toHaveBeenCalledWith({
      UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
      Username: email,
    });
  });

  it('should delete user', async () => {
    const username = 'testuser';
    await service.adminDeleteUser(username);
    expect(cognitoMock.adminDeleteUser).toHaveBeenCalledWith({
      UserPoolId: process.env.AWS_COGNITO_USER_POOL_ID,
      Username: username,
    });
  });
});
