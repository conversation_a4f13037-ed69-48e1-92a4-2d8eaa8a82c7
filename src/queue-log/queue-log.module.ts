import { Global, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { QueueLogService } from './queue-log.service';
import { QueueLogController } from './queue-log.controller';
import { QueueLog, QueueLogSchema } from './schemas/queue-log.schema';
import { QueueRegistryService } from './queue-registry.service';
import { ConfigModule } from '@nestjs/config';

@Global() 
@Module({
  imports: [
    MongooseModule.forFeature([{ name: QueueLog.name, schema: QueueLogSchema }]),
    ConfigModule,
  ],
  providers: [QueueLogService, QueueRegistryService],
  controllers: [QueueLogController],
  exports: [QueueLogService, QueueRegistryService],
})
export class QueueLogModule {}
