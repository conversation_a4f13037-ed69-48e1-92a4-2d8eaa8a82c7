import { Injectable } from '@nestjs/common';
import { Connection, Record as PractifiRecord } from 'jsforce';
import { LogCommunicationDto } from '../../types/communications/log-communication.dto';
import { SendEmailOptions } from 'src/notifications/mail/mail.types';
import { EnrichedContact } from 'src/clients/dto/v1/get-clients.dto';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { generateRequestConfigs } from '../utils/generate-request-configs.util';
import { OperationType } from 'src/super-http/enums';
import { practifiQuery , formatName } from '../utils/practifi-query.util';
import { PractifiEntityTypes , TaskStatus, TaskPriority } from '../types/enums';
import { commonConfigs } from '../types/types';

export interface PractifiTaskDto {
  Subject: string;
  Description: string;
  WhatId: string;
  OwnerId?: string | number;
  Status: TaskStatus;
  Priority: TaskPriority;
  ActivityDate: string;
  TaskSubtype?: string;
}

@Injectable()
export class PractifiTaskService {
  private connection: Connection;
  private baseUrl: string;
  private commonConfigs: commonConfigs;
  
  constructor(
    private readonly httpService: SuperHttpService,
    private tenantId: string
  ) {}

  setConnection(connection: Connection) {
    this.connection = connection;
    this.baseUrl = `${connection.instanceUrl}/services/data/v${connection.version}`;
    this.prepareRequestConfigs();
  }

  prepareRequestConfigs() {
    const requestConfigs = generateRequestConfigs(this.connection.accessToken);
    const { withInterceptors, ...configsWithoutInterceptors } = requestConfigs;
    this.commonConfigs = configsWithoutInterceptors;
  }

  async createTask(taskData: PractifiTaskDto): Promise<PractifiRecord> {
    const soql = `SELECT Id FROM Task WHERE Subject = '${formatName(taskData.Subject)}' AND WhatId = '${taskData.WhatId}'`;
    const result = await practifiQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, PractifiEntityTypes.TASK, this.tenantId);
    const recordIsPresent = result.records[0];

    if (recordIsPresent) {
      const { TaskSubtype, ...taskDataWithoutSubtype } = taskData;
      return await this.httpService.patch(
        `${this.baseUrl}/sobjects/Task/${recordIsPresent.Id}`,
        this.tenantId,
        {
          ...taskDataWithoutSubtype,
        },
        {
          rateLimiter: 'practifi',
          integrationName: 'practifi',
          operation: OperationType.PATCH,
          entityType: PractifiEntityTypes.TASK,
        },
        this.commonConfigs
      );
    }

    return await this.httpService.post(
      `${this.baseUrl}/sobjects/Task/`,
      this.tenantId,
      {
        ...taskData,
      },
      {
        rateLimiter: 'practifi',
        integrationName: 'practifi',
        operation: OperationType.CREATE,
        entityType: PractifiEntityTypes.TASK,
      },
      this.commonConfigs
    );
  }

  async updateTask(id: string, taskData: Partial<PractifiTaskDto>): Promise<void> {
    return await this.httpService.patch(
      `${this.baseUrl}/sobjects/Task/${id}`,
      this.tenantId,
      {
        ...taskData,
      },
      {
        rateLimiter: 'practifi',
        integrationName: 'practifi',
        operation: OperationType.PATCH,
        entityType: PractifiEntityTypes.TASK,
      },
      this.commonConfigs
    );
  }

  async getTask(id: string): Promise<PractifiRecord> {
    return await this.httpService.get(
      `${this.baseUrl}/sobjects/Task/${id}`,
      this.tenantId,
      {
        rateLimiter: 'practifi',
        integrationName: 'practifi',
        operation: OperationType.GET,
        entityType: PractifiEntityTypes.TASK,
        entityId: id,
      },
      this.commonConfigs
    );
  }

  async createCommunicationTask(
    crmClientId: string,
    logCommunicationDto: LogCommunicationDto,
  ): Promise<void> {
    let accountId = crmClientId;
    const isIndividualAccount = await this.checkIsAccount(crmClientId);

    if (!isIndividualAccount) {
      const contact = await this.getContact(accountId);

      if (contact) {
        accountId = contact.AccountId;
      }
    }

    const task = {
      WhatId: accountId,
      Subject: logCommunicationDto.communicationType || 'Communication Log',
      Description: logCommunicationDto.communicationDetails || 'Communication Log',
      Status: TaskStatus.COMPLETED,
      Priority: TaskPriority.NORMAL,
      TaskSubtype: logCommunicationDto.communicationType || 'Task',
      ActivityDate: new Date().toISOString().split('T')[0],
    };

    await this.createTask(task);
  }

  async createEmailTask(
    emailOptions: SendEmailOptions,
    contact?: EnrichedContact,
    advisorId?: string,
  ): Promise<void> {
    let accountId = contact?.crmClientId;
    const isIndividualAccount = await this.checkIsAccount(contact?.crmClientId);

    if (!isIndividualAccount) {
      const contact = await this.getContact(accountId);

      if (contact) {
        accountId = contact.AccountId;
      }
    }

    const task = {
      WhatId: accountId,
      OwnerId: advisorId,
      Subject: emailOptions.subject || 'Send Email',
      Description: emailOptions.text || '',
      Status: TaskStatus.COMPLETED,
      Priority: TaskPriority.NORMAL,
      TaskSubtype: 'Email',
      ActivityDate: new Date().toISOString().split('T')[0],
    };

    await this.createTask(task);
  }

  async checkIsAccount(id: string): Promise<boolean> {
    const soql = `SELECT Id FROM Account WHERE Id = '${id}' LIMIT 1`;
    const result = await practifiQuery(soql, this.baseUrl, this.httpService, this.commonConfigs, PractifiEntityTypes.ACCOUNT, this.tenantId);
    if (!result.records?.length) {
      return false;
    }
    return true;
  }

  async getContact(id: string): Promise<PractifiRecord> {
    return await this.httpService.get(
      `${this.baseUrl}/sobjects/Contact/${id}`,
      this.tenantId,
      {
        rateLimiter: 'practifi',
        integrationName: 'practifi',
        operation: OperationType.GET,
        entityType: PractifiEntityTypes.CONTACT,
        entityId: id,
      },
      this.commonConfigs
    );
  }
}