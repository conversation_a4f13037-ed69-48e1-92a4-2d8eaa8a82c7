import { HttpService } from '@nestjs/axios';
import { Inject, Injectable, forwardRef } from '@nestjs/common';
import { Credentials } from 'src/auth/auth.types';
import { InterviewsService } from 'src/interviews/interviews.service';
import { CRMType } from '../../shared/types/integrations';
import { CRMFactory } from './crm.factory';
import { CRM } from './crm.interface';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
import { MailService } from 'src/notifications/mail/mail.service';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

@Injectable()
export class CRMService {
  constructor(
    private readonly superHttp: SuperHttpService,
    @Inject(forwardRef(() => InterviewsService))
    private readonly interviewsService: InterviewsService,
    private readonly mailService: MailService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  create(advisor: AdvisorWithRole, organisation: Organisation, type: CRMType, credentials: Credentials): CRM {
    return CRMFactory.createCRM(
      advisor,
      organisation,
      type,
      credentials,
      this.superHttp,

      this.interviewsService,
      this.mailService,
      this.logger,
    );
  }
}
