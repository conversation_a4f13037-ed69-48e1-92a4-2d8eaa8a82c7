import { ApiProperty } from "@nestjs/swagger";
import { Expose, Type } from "class-transformer";
import { IsArray, ValidateNested } from "class-validator";
import { ObjectId } from "mongoose";
import { AccountDocumentViewDto } from "src/shared/dto/account-document";
import { ClientViewDto } from "src/shared/dto/client.dto";
import { InterviewTemplateViewDto } from "src/shared/dto/interview-template.dto";

export class InterviewViewDto {
  @ApiProperty()
  @Expose()
  @Type(() => String)
  _id: ObjectId;

  @ApiProperty({ type: ClientViewDto })
  @Expose()
  @Type(() => ClientViewDto)
  client: ClientViewDto;

  @ApiProperty()
  @Expose()
  envelopeId: string;

  @ApiProperty()
  @Expose()
  docusignSelected: boolean;

  @ApiProperty()
  @Expose()
  @Type(() => AccountDocumentViewDto)
  @IsArray()
  @ValidateNested({ each: true })
  documents: AccountDocumentViewDto[];

  @ApiProperty()
  @Expose()
  @Type(() => InterviewTemplateViewDto)
  template: InterviewTemplateViewDto;

  @ApiProperty()
  @Expose()
  createdAt: Date;

  @ApiProperty()
  @Expose()
  updatedAt: Date;

  @ApiProperty()
  @Expose()
  isPrimary: boolean;

  @ApiProperty()
  @Expose()
  isComplete: boolean;

  @ApiProperty()
  @Expose()
  organisationLogo?: string;
}