import { IsOptional, IsInt, Min, IsEnum, IsDateString, ValidateNested, IsObject } from 'class-validator';
import { Type } from 'class-transformer';
import { QueueLogStatusEnum } from '../enums/queue-log-status.enum';

class FilterOptions {
  @IsOptional()
  @IsEnum(QueueLogStatusEnum)
  status?: QueueLogStatusEnum;

  @IsOptional()
  @IsDateString()
  startDate?: string;

  @IsOptional()
  @IsDateString()
  endDate?: string;
}

export class GetJobsDto {
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 10;

  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => FilterOptions)
  filter?: FilterOptions;
}
