import { PageVariableProvider } from './page-variable-provider.interface';
import { VariableDefinition } from './types/variable-definition.interface';
import { VariableType } from './types/variable-type.enum';

export class CustomQuestionsPageVariableProvider implements PageVariableProvider {
  getAvailableVariables(): Record<string, VariableDefinition> {
    return {
      answer: {
        description: 'The user\'s answer to the custom question',
        type: VariableType.STRING,
        example: 'Growth-oriented investments',
      },
    };
  }
}