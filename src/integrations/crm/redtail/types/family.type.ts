export interface RedtailFamilyMember {
  id: number;
  family_id: number;
  contact_id: number;
  relationship: number | null;
  relationship_name: string | null;
  hoh: boolean;
  full_name: string;
  first_name: string;
  last_name: string;
  created_at: string;
  updated_at: string;
  deleted: boolean;
}

export interface RedtailFamily {
  id: number;
  name: string;
  created_at: string;
  updated_at: string;
  deleted: boolean;
  members: RedtailFamilyMember[];
}
