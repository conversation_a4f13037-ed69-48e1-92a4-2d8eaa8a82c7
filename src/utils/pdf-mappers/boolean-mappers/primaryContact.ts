import { isEmpty } from 'lodash';
import { RedtailCitizenshipEnum } from 'src/integrations/crm/redtail/types/enums';
import { GenericCrmBeneficiaryEnum } from 'src/integrations/crm/types/accounts/crm-account.type';
import { GenericEmploymentStatusEnum } from 'src/integrations/crm/types/generic/employment.status.enum';
import { JobDescription } from 'src/integrations/crm/types/job-description.enum';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';

export const primaryContactBooleanMappers = {
  'primaryContact.isEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.primaryContact?.additionalInfo?.employmentStatus ===
        GenericEmploymentStatusEnum.EMPLOYED
      );
    },
  },
  'primaryContact.isBusinessOwner': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.primaryContact?.additionalInfo?.jobDescription ===
        JobDescription.BUSINESS_OWNER
      );
    },
  },
  'primaryContact.isBusinessOwnerSelfEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const jobDescription =
        interviewData.primaryContact?.additionalInfo?.jobDescription;
      return (
        jobDescription === JobDescription.BUSINESS_OWNER ||
        jobDescription === JobDescription.SELF_EMPLOYED
      );
    },
  },
  'primaryContact.isClerical': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.CLERICAL || interviewData.secondaryContact?.additionalInfo.jobDescription === JobDescription.ADMINISTRATIVE_SERVICES,
  },
  'primaryContact.isConsultant': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.CONSULTANT,
  },
  'primaryContact.isEducator': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.EDUCATOR,
  },
  'primaryContact.isExecutive': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.EXECUTIVE,
  },
  'primaryContact.isFinancialServices': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.FINANCIAL_SERVICES,
  },
  'primaryContact.isForeignGovernmentEmployee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.FOREIGN_GOVERNMENT_EMPLOYEE,
  },
  'primaryContact.isHomemaker': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.HOMEMAKER,
  },
  'primaryContact.isInformationTechnologyProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.INFORMATION_TECHNOLOGY_PROFESSIONAL || interviewData.secondaryContact?.additionalInfo.jobDescription === JobDescription.IT_PROFESSIONAL,
  },
  'primaryContact.isLegalProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.LEGAL_PROFESSIONAL,
  },
  'primaryContact.isMedicalProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.MEDICAL_PROFESSIONAL,
  },
  'primaryContact.isMilitary': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.MILITARY,
  },
  'primaryContact.isNotEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.employmentStatus ===
      GenericEmploymentStatusEnum.NOTEMPLOYED,
  },
  'primaryContact.isOtherProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.OTHER_PROFESSIONAL,
  },
  'primaryContact.isRetired': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.employmentStatus ===
      GenericEmploymentStatusEnum.RETIRED,
  },
  'primaryContact.isSalesMarketing': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.SALES_MARKETING || interviewData.secondaryContact?.additionalInfo.jobDescription === JobDescription.SALES_MARKETING_2,
  },
  'primaryContact.isSelfEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const jobDescription =
        interviewData.primaryContact?.additionalInfo?.employmentStatus;
      return jobDescription === JobDescription.SELF_EMPLOYED;
    },
  },
  'primaryContact.isStudent': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.STUDENT,
  },
  'primaryContact.isTradeServiceCareer': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.primaryContact?.additionalInfo?.jobDescription ===
      JobDescription.TRADE_SERVICE_CAREER,
  },
  'primaryContact.isUSGovernmentEmployee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const jobDescription =
        interviewData.primaryContact?.additionalInfo?.jobDescription;
      return jobDescription === JobDescription.US_GOVERNMENT_EMPLOYEE;
    },
  },
  'primaryContact.isUSCitizen': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => interviewData.primaryContact.citizenship === RedtailCitizenshipEnum.US,
  },
  'primaryContact.isUSResident': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => true,
  },
  'primaryContact.hasPublicCompanyAssociation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return !isEmpty(
        interviewData.primaryContact?.additionalInfo?.companyAssociation,
      );
    },
  },
  'primaryContact.hasIndustryAffiliation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return !isEmpty(
        interviewData.primaryContact?.additionalInfo?.industryAffiliation,
      );
    },
  },
  'primaryContact.account#.beneficiaries#.isPrimary': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      const beneficiary = account.beneficiaries[beneficiaryIndex];
      return beneficiary.type === GenericCrmBeneficiaryEnum.Primary;
    },
  },
  'primaryContact.account#.beneficiaries#.isContingent': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.primaryContact.accounts[accountIndex];
      const beneficiary = account.beneficiaries[beneficiaryIndex];
      return beneficiary.type === GenericCrmBeneficiaryEnum.Contingent;
    },
  },
};
