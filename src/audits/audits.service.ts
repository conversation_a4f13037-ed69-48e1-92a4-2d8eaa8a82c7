import { Inject, Injectable } from '@nestjs/common';
import { CreateAuditDto } from './dto/create-audit.dto';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import { Audit } from '../audits/schemas/audit.schema';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { Logger } from 'winston';
@Injectable()
export class AuditsService {
  constructor(
    @InjectModel(Audit.name) private auditLogModel: Model<Audit>,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  public async create(createAuditDto: CreateAuditDto): Promise<Audit> {
    try {
      const auditLog = await new this.auditLogModel(createAuditDto);
      return auditLog.save();
    } catch (error) {
      this.logger.error(
        `Failed to create an ${createAuditDto.level} audit entry for ${createAuditDto.entityType}: ${createAuditDto.entityId}`,
        error,
      );
    }
  }

  public async findAll(filter?: FilterQuery<Audit>): Promise<Audit[]> {
    return this.auditLogModel.find(filter);
  }

  public async findOne(id: string): Promise<Audit> {
    return this.auditLogModel.findById(id);
  }
}
