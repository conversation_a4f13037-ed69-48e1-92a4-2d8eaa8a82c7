export function formatDate(date: Date) {
  const pad = (num) => (num < 10 ? '0' + num : num);

  const year = date.getFullYear();
  const month = pad(date.getMonth() + 1); // getMonth() returns 0-11
  const day = pad(date.getDate());

  let hour = date.getHours();
  const ampm = hour >= 12 ? 'PM' : 'AM';
  hour = hour % 12;
  hour = hour || 12; // the hour '0' should be '12'
  const minutes = pad(date.getMinutes());

  const timezoneOffset = -date.getTimezoneOffset();
  const timezoneSign = timezoneOffset >= 0 ? '+' : '-';
  const timezoneHours = pad(Math.floor(Math.abs(timezoneOffset) / 60));
  const timezoneMinutes = pad(Math.abs(timezoneOffset) % 60);

  return `${year}-${month}-${day} ${hour}:${minutes} ${ampm} ${timezoneSign}${timezoneHours}${timezoneMinutes}`;
}
