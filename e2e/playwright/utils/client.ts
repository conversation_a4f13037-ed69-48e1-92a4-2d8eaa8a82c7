import { Page } from '@playwright/test';
import { TEST_CONFIG } from '../config/environment';

interface ClientInfo {
    firstName: string;
    lastName: string;
    email: string;
    phone: {
        npa: string;
        nxx: string;
        line: string;
    };
}

interface JointClientInfo {
    clientOne: ClientInfo;
    clientTwo: ClientInfo;
}

interface BaseAccountInfo {
    type: 'IRA' | 'Individual' | 'Joint' | 'Roth IRA';
    nickname: string;
}

interface AccountInfo extends BaseAccountInfo {
    owner: string; // The full name of the client who owns the account
}

interface JointAccountsInfo {
    clientOne?: BaseAccountInfo[];
    clientTwo?: BaseAccountInfo[];
}

export async function navigateToClientCreation(page: Page) {
    await page.locator('header svg').click();
    await page.locator('header').getByText('Clients').click();
    await page.getByRole('button', { name: 'Add Client' }).click();
    await page.getByRole('menuitem', { name: 'Add Single' }).click();
    await page.getByRole('dialog').locator('a').first().click();
}

export async function fillClientInfo(page: Page, client: Partial<ClientInfo> = {}) {
    const defaultClient = TEST_CONFIG.clientDefaults.single;
    const clientData = {
        ...defaultClient,
        email: TEST_CONFIG.clientDefaults.generateEmail('single'),
        ...client
    };

    // Personal Info
    await page.locator('[data-test-id="first-name-input"]').fill(clientData.firstName);
    await page.locator('[data-test-id="last-name-input"]').fill(clientData.lastName);
    await page.getByRole('button', { name: 'Continue' }).click();

    // Contact Info
    await page.locator('[data-test-id="email-input"]').fill(clientData.email);
    await page.getByRole('button', { name: 'Continue' }).click();

    // Phone Info
    await page.locator('[data-test-id="phone-npa-input"]').fill(clientData.phone.npa);
    await page.locator('[data-test-id="phone-nxx-input"]').fill(clientData.phone.nxx);
    await page.locator('[data-test-id="phone-line-input"]').fill(clientData.phone.line);
    await page.getByRole('button', { name: 'Continue' }).click();
}

export async function fillJointClientInfo(page: Page, clients: Partial<JointClientInfo> = {}) {
    const defaultClients = TEST_CONFIG.clientDefaults.joint;
    const clientData = {
        clientOne: {
            ...defaultClients.clientOne,
            email: TEST_CONFIG.clientDefaults.generateEmail('joint1'),
            ...clients.clientOne
        },
        clientTwo: {
            ...defaultClients.clientTwo,
            email: TEST_CONFIG.clientDefaults.generateEmail('joint2'),
            ...clients.clientTwo
        }
    };

    // Client One Info
    await page.locator('[data-test-id="first-name-input"]').fill(clientData.clientOne.firstName);
    await page.locator('[data-test-id="last-name-input"]').fill(clientData.clientOne.lastName);
    await page.getByRole('button', { name: 'Continue' }).click();

    await page.locator('[data-test-id="email-input"]').fill(clientData.clientOne.email);
    await page.getByRole('button', { name: 'Continue' }).click();

    await page.locator('[data-test-id="phone-npa-input"]').fill(clientData.clientOne.phone.npa);
    await page.locator('[data-test-id="phone-nxx-input"]').fill(clientData.clientOne.phone.nxx);
    await page.locator('[data-test-id="phone-line-input"]').fill(clientData.clientOne.phone.line);
    await page.getByRole('button', { name: 'Continue' }).click();

    // Client Two Info
    await page.locator('[data-test-id="first-name-input"]').fill(clientData.clientTwo.firstName);
    await page.locator('[data-test-id="last-name-input"]').fill(clientData.clientTwo.lastName);
    await page.getByRole('button', { name: 'Continue' }).click();

    await page.locator('[data-test-id="email-input"]').fill(clientData.clientTwo.email);
    await page.getByRole('button', { name: 'Continue' }).click();

    await page.locator('[data-test-id="phone-npa-input"]').fill(clientData.clientTwo.phone.npa);
    await page.locator('[data-test-id="phone-nxx-input"]').fill(clientData.clientTwo.phone.nxx);
    await page.locator('[data-test-id="phone-line-input"]').fill(clientData.clientTwo.phone.line);
    await page.getByRole('button', { name: 'Continue' }).click();
}

export async function addAccount(page: Page, account: AccountInfo) {
    await page.locator('[data-test-id="account-selection-form"]').getByRole('button').click();
    await page.getByText(account.type, { exact: true }).last().click();
    await page.locator('[data-test-id="nickname-input"]').fill(account.nickname);
    await page.getByRole('button').nth(1).click();
    await page.getByText(account.owner, { exact: true }).last().click();
    await page.locator('[data-test-id="add-account-btn"]').click();
}

export async function addJointAccounts(page: Page, clients: JointClientInfo, accounts: JointAccountsInfo) {
    const clientOneFullName = `${clients.clientOne.firstName} ${clients.clientOne.lastName}`;
    const clientTwoFullName = `${clients.clientTwo.firstName} ${clients.clientTwo.lastName}`;

    // Add client one accounts
    if (accounts.clientOne) {
        for (const account of accounts.clientOne) {
            await addAccount(page, {
                ...account,
                owner: clientOneFullName
            });
        }
    }

    // Add client two accounts
    if (accounts.clientTwo) {
        for (const account of accounts.clientTwo) {
            await addAccount(page, {
                ...account,
                owner: clientTwoFullName
            });
        }
    }
}

export async function completeClientSetup(page: Page, { includeCustomQuestions = false }: { includeCustomQuestions?: boolean } = {}) {
    // Select None for account
    await page.locator('[data-test-id="client-one-account-0"]').getByRole('button').click();
    // await page.getByRole('option').locator('div').first().click();
    await page.getByRole('button', { name: 'Continue' }).click();
    await page.getByRole('button', { name: 'Continue' }).click();
    await page.getByRole('button', { name: 'Continue' }).click();

    // Select advisors
    await page.locator('[data-test-id="primary-advisor-btn-selectable-0"]').click();
    await page.locator('[data-test-id="primary-csa-btn-selectable-0"]').click();
    await page.getByRole('button', { name: 'Continue' }).click();

    if (includeCustomQuestions) {
        await page.getByRole('button', { name: 'Custom Question Templates' }).click();
        await page.getByRole('option').first().click();
        await page.getByRole('button', { name: 'Continue' }).click();
    }

    // Finish and notifications
    await page.getByRole('button', { name: 'Finish' }).click();
    await page.getByRole('radiogroup').getByText('Yes').click();
    await page.locator('[data-test-id="notification-method-sms"]').click();
    await page.locator('[data-test-id="notification-save-btn"]').click();
    await page.locator('[data-test-id="save-complete-btn"]').click();
    await page.getByText('Ok').click();
} 