export interface PageFlow {
    rules: Array<{
      ruleId: string;
      ruleName: string;
      priority: number;
      when: {
        logic: 'AND' | 'OR';
        conditions: Array<{
          field: string;
          operator: string;
          value?: any;
          label?: string;
        }>;
      };
      goToPageId: string;
      isActive: boolean;
      description?: string;
    }>;
    defaultNext: {
      pageId: string;
      label: string;
    };
    allowBack: boolean;
    skipIf?: {
      logic: 'AND' | 'OR';
      conditions: Array<{
        field: string;
        operator: string;
        value?: any;
        label?: string;
      }>;
    };
    isTerminal: boolean;
  }