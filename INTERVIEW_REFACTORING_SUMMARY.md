# Interview Service Refactoring Comparison Report

## Overview
The v1 InterviewsV1Service has been refactored into 6 specialized services and 1 facade service that orchestrates them.

## Service Comparison

### 1. InterviewCoreService (interview-core.service.ts)
**Purpose**: Handles core CRUD operations and basic interview management

**Public Methods**:
- ✅ `create(dto: CreateInterviewDto, session?: ClientSession)` - Matches v1
- ✅ `find(filter: FilterQuery<Interview>)` - Matches v1
- ✅ `findAll()` - Matches v1
- ✅ `findOne(filter, session?, skipEnrich?)` - Matches v1
- ✅ `findById(id, session?, skipEnrich?)` - Matches v1 (includes organisationLogo)
- ✅ `remove(id, session?)` - Matches v1

**Private Methods**:
- ✅ `setUpAdditionalPagesInInterviewTemplate()` - Matches v1

**Missing Functionality**: None

---

### 2. InterviewPagesService (interview-pages.service.ts)
**Purpose**: Manages interview page operations and completion calculations

**Public Methods**:
- ✅ `updatePages(pages, dto, status)` - Matches v1
- ✅ `calculateCompletion(interview, client)` - Renamed from `calculateCompletionPercentage` but same logic
- ✅ `validateAllSynced(interviewId)` - Renamed from `validateAllPagesAreSynced` but same logic
- ✅ `updateInterviewPages(interviewId, dto, pageStatus, session?)` - Matches v1
- ✅ `addPage(interviewId, pageName)` - Renamed from `addPageToInterview` but same logic
- ✅ `removePage(interviewId, pageName)` - Renamed from `removePageFromInterview` but same logic

**Missing Functionality**: None

---

### 3. InterviewDocumentsService (interview-documents.service.ts)
**Purpose**: Handles document uploads and S3 file operations

**Public Methods**:
- ✅ `upload(interview, files, dto?, session?)` - Modified signature (takes interview object instead of ID)
- ✅ `selectClientFilesToFetch(accountFeatures, accountId)` - Matches v1
- ✅ `getAccountAdvisoryFileTypes(accountType, accountFeatures)` - Matches v1
- ✅ `getFilesFromS3(fileNames, organisationId?)` - Matches v1

**Missing Functionality**:
- ❌ `addRequiredDocument(id, dto)` - Not implemented
- ❌ `removeRequiredDocument(id, dto)` - Not implemented
- ❌ `getAccountOpeningAdvisoryFiles()` - Not implemented

---

### 4. InterviewEnvelopeService (interview-envelope.service.ts)
**Purpose**: Manages DocuSign envelope creation and preparation

**Public Methods**:
- ✅ `createDocusignEnvelope(interviewId, session?)` - Matches v1
- ✅ `prepareDocusignEnvelope(interviewId, session)` - Matches v1
- ✅ `getEnvelopeFiles(ownership, orgId, primary, secondary)` - Simplified implementation
- ✅ `mergeAccounts(crmAccounts, dbAccounts, crmType)` - Matches v1

**Private Methods**:
- ✅ `getInterviewDataWithCrmInfo(data, advisorId, session?)` - Matches v1

**Missing Functionality**:
- ⚠️ `getEnvelopeFiles()` returns empty array - needs full implementation using InterviewDocumentsService

---

### 5. InterviewNotificationService (interview-notification.service.ts)
**Purpose**: Handles email notifications

**Public Methods**:
- ✅ `sendDesktopInterviewEmail(id)` - Matches v1
- ✅ `sendNonCitizenEmail(id)` - Matches v1
- ✅ `sendReadyToSignEmail(interview)` - Changed from private to public
- ✅ `sendNotification(interviewId, session)` - Matches v1

**Missing Functionality**: None

---

### 6. InterviewQueueService (interview-queue.service.ts)
**Purpose**: Manages queue operations and job flows

**Public Methods**:
- ✅ `enqueuePageUpdate(interviewId, dto)` - Renamed from `enqueueFlow` but same logic
- ✅ `enqueueCompletionFlow(params)` - Renamed from `enqueueInterviewCompletionFlow` but same logic
- ✅ `markInterviewAsComplete(interviewId, session)` - Matches v1

**Private Methods**:
- ✅ `getCrmQueueName(selectedCrm)` - Matches v1

**Missing Functionality**: None

---

### 7. InterviewV2Facade (interview-v2.facade.ts)
**Purpose**: Orchestrates all services and provides unified interface

**Public Methods**:
- All methods from individual services are exposed
- ✅ `complete(id, session?)` - Implements full completion workflow
- ✅ `finish(id, session?)` - Legacy alias for backward compatibility

**Missing Methods from V1**:
- ❌ `addRequiredDocument(id, dto)`
- ❌ `removeRequiredDocument(id, dto)`
- ❌ `parseCrmAuthError(message)` - Not implemented anywhere

---

## Summary of Missing Functionality

### Critical Missing Methods:
1. **`addRequiredDocument(id, dto)`** - Used to add required documents to an interview
2. **`removeRequiredDocument(id, dto)`** - Used to remove required documents from an interview
3. **`getAccountOpeningAdvisoryFiles(accounts, organisationId?)`** - Used by envelope service

### Minor Missing Methods:
4. **`parseCrmAuthError(message)`** - Error parsing utility (not currently used)

### Implementation Issues:
1. **`InterviewEnvelopeService.getEnvelopeFiles()`** - Returns empty array instead of proper implementation
2. **`InterviewDocumentsService.upload()`** - Changed signature requires facade adaptation

### Architectural Differences:
1. **Circular Dependencies**: Some services need to inject each other, requiring careful use of `forwardRef()`
2. **Session Management**: Each service handles its own transactions where the v1 service had centralized control
3. **Error Handling**: HttpException usage is inconsistent between services

## Recommendations

1. **Add Missing Methods**: Implement the 3 critical missing methods in appropriate services
2. **Fix getEnvelopeFiles**: Complete the implementation in InterviewEnvelopeService
3. **Standardize Error Handling**: Use consistent HttpException patterns across all services
4. **Document Service Boundaries**: Clearly define which service owns which operations
5. **Consider Circular Dependencies**: The envelope service needs document service methods - consider refactoring to avoid circular dependencies