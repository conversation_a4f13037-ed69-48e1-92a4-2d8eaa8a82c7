import {
  IsNotEmpty,
  IsArray,
  IsEnum,
  ValidateNested,
  IsString,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';
import { AdvisorPersonalInfoDto } from 'src/advisors/dto/advisor-personal-info.dto';
import { IntegrationDto } from 'src/shared/dto/integration.dto';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { UserStatusesEnum } from 'src/shared/types/rbac/statuses.enum';
import { ApiProperty } from '@nestjs/swagger';

export class CreateAdvisorDto {
  @IsString()
  @ApiProperty()
  organisationId: string;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => AdvisorPersonalInfoDto)
  @ApiProperty()
  personalInfo: AdvisorPersonalInfoDto;

  @IsNotEmpty()
  @IsEnum(RolesEnum)
  @ApiProperty()
  role: RolesEnum;

  @IsNotEmpty()
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => IntegrationDto)
  @ApiProperty()
  integrations: IntegrationDto[];
}
