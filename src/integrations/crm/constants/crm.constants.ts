import { CrmJobs } from "../crm.jobs.interface";

export enum CRM_JOBS {
  UPSERT_TO_CRM = 'push_to_crm',
  UPDATE_PAGE_ANSWER = 'update_page_answer',
};

export const CRM_QUEUE = {
  WEALTHBOX: {
    NAME: 'wealthbox',
    JOBS: {
      UPSERT_TO_CRM: 'upsert_to_crm',
      UPDATE_PAGE_ANSWER: 'update_page_answer',
    },
  },
  REDTAIL: {
    NAME: 'redtail',
    JOBS: {
      UPSERT_TO_CRM: 'upsert_to_crm',
      UPDATE_PAGE_ANSWER: 'update_page_answer',
    },
  },
  SALESFORCE: {
    NAME: 'salesforce',
    JOBS: {
      UPSERT_TO_CRM: 'upsert_to_crm',
      UPDATE_PAGE_ANSWER: 'update_page_answer',
    },
  },
  PRACTIFI: {
    NAME: 'practifi',
    JOBS: {
      UPSERT_TO_CRM: 'upsert_to_crm',
      UPDATE_PAGE_ANSWER: 'update_page_answer',
    },
  },
};

export type CRMType = keyof typeof CRM_QUEUE;

export const SUPPORTED_CRMS: CRMType[] = ['WEALTHBOX', 'REDTAIL', 'SALESFORCE', 'PRACTIFI'];