import { MailingAddressRequest } from 'src/integrations/crm/wealthbox/dto/address.dto';
import { ContactRoleValueRequest } from 'src/integrations/crm/wealthbox/dto/contacts/contact-role.dto';
import { CustomFieldRequest } from 'src/integrations/crm/wealthbox/dto/custom-field.dto';
import { WealthboxEmailAddressRequest } from 'src/integrations/crm/wealthbox/dto/email.dto';
import { HouseholdRequest } from 'src/integrations/crm/wealthbox/dto/household/household.dto';
import { WealthboxPhoneNumberRequest } from 'src/integrations/crm/wealthbox/dto/phone.dto';
import { WebsiteRequest } from 'src/integrations/crm/wealthbox/dto/website.dto';
import {
  WealthboxContactStatus,
  WealthboxContactType,
  DateTime,
  WealthboxDriversLicense,
  WealthboxGender,
  WealthboxInvestmentObjective,
  WealthboxMaritalStatus,
  WealthboxOccupation,
  WealthboxRiskTolerance,
  WealthboxTimeHorizon,
} from 'src/integrations/crm/wealthbox/types';

export interface CreateContactRequestDto {
  prefix?: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  suffix?: string;
  nickname?: string;
  job_title?: string;
  company_name?: string;
  twitter_name?: string;
  linkedin_url?: string;
  background_information?: string;
  birth_date?: DateTime;
  anniversary?: DateTime;
  client_since?: DateTime;
  date_of_death?: DateTime;
  assigned_to?: number;
  referred_by?: number;
  type?: WealthboxContactType;
  gender?: WealthboxGender;
  contact_source?: string;
  contact_type?: string;
  status?: WealthboxContactStatus;
  marital_status?: WealthboxMaritalStatus;
  attorney?: number;
  cpa?: number;
  doctor?: number;
  insurance?: number;
  business_manager?: number;
  family_officer?: number;
  assistant?: number;
  other?: number;
  trusted_contact?: number;
  important_information?: string;
  personal_interests?: string;
  investment_objective?: WealthboxInvestmentObjective;
  time_horizon?: WealthboxTimeHorizon;
  risk_tolerance?: WealthboxRiskTolerance;
  mutual_fund_experience?: number;
  stocks_and_bonds_experience?: number;
  partnerships_experience?: number;
  other_investing_experience?: number;
  gross_annual_income?: number;
  assets?: number;
  non_liquid_assets?: number;
  liabilities?: number;
  adjusted_gross_income?: number;
  estimated_taxes?: number;
  confirmed_by_tax_return?: boolean;
  tax_year?: number;
  tax_bracket?: number;
  birth_place?: string;
  maiden_name?: string;
  passport_number?: string;
  green_card_number?: string;
  occupation?: WealthboxOccupation;
  drivers_license?: WealthboxDriversLicense;
  retirement_date?: DateTime;
  signed_fee_agreement_date?: DateTime;
  signed_ips_agreement_date?: DateTime;
  signed_fp_agreement_date?: DateTime;
  last_adv_offering_date?: DateTime;
  initial_crs_offering_date?: DateTime;
  last_crs_offering_date?: DateTime;
  last_privacy_offering_date?: DateTime;
  company?: string;
  household?: HouseholdRequest;
  tags?: string[];
  street_addresses?: MailingAddressRequest[];
  email_addresses?: WealthboxEmailAddressRequest[];
  phone_numbers?: WealthboxPhoneNumberRequest[];
  websites?: WebsiteRequest[];
  custom_fields?: CustomFieldRequest[];
  contact_roles?: ContactRoleValueRequest[];
  visible_to?: string;
}
