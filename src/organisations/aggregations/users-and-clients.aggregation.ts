import mongoose from 'mongoose';
import { Organisation } from '../schemas/organisation.schema';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { DateRange } from 'src/shared/types/general/date-range.type';

export const OrgAdvisorOnbordCount = (
  filters: mongoose.FilterQuery<Organisation> = {},
  { toDate, fromDate }: DateRange = {},
) => [
  {
    $match: {
      ...filters,
    },
  },
  {
    $lookup: {
      from: 'advisors',
      localField: '_id',
      foreignField: 'organisation',
      as: 'advisors',
    },
  },
  {
    $lookup: {
      from: 'clients',
      localField: '_id',
      foreignField: 'organisationId',
      as: 'clients',
    },
  },
  {
    $addFields: {
      totalAdvisors: { $size: '$advisors' },
      totalCompletedClients: {
        $size: {
          $filter: {
            input: '$clients',
            as: 'client',
            cond: {
              $and: [
                {
                  $in: [
                    '$$client.status',
                    [
                      ClientStatusEnum.Complete,
                      ClientStatusEnum.Signed,
                      ClientStatusEnum.Archived,
                    ],
                  ],
                },
                // { $eq: ['$$client.completedAt', null] },
                fromDate ? { $gte: ['$$client.completedAt', fromDate] } : {},
                toDate ? { $lte: ['$$client.completedAt', toDate] } : {},
              ],
            },
          },
        },
      },
      totalIncompleteClients: {
        $size: {
          $filter: {
            input: '$clients',
            as: 'client',
            cond: {
              $and: [
                {
                  $not: {
                    $in: [
                      '$$client.status',
                      [
                        ClientStatusEnum.Complete,
                        ClientStatusEnum.Signed,
                        ClientStatusEnum.Archived,
                      ],
                    ],
                  },
                },
                fromDate ? { $gte: ['$$client.createdAt', fromDate] } : {},
                toDate ? { $lte: ['$$client.createdAt', toDate] } : {},
              ],
            },
          },
        },
      },
    },
  },
  {
    $project: {
      advisors: 0,
      clients: 0,
      externalAccounts: 0,
      masterAccounts: 0,
      configuration: 0,
      customQuestions: 0,
    },
  },
];
