import { Module, forwardRef } from '@nestjs/common';
import { CRMService } from './crm.service';
import { InterviewsModule } from 'src/interviews/interviews.module';
import { WealthboxController } from './wealthbox/wealthbox.controller';
import { WealthboxService } from './wealthbox/wealthbox.service';
import { AdvisorsModule } from 'src/advisors/advisors.module';
import { MailModule } from 'src/notifications/mail/mail.module';
import { BullModule } from '@nestjs/bullmq';
import { BullBoardModule } from '@bull-board/nestjs';
import { CRM_QUEUE } from './constants/crm.constants';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { CRMProcessor as WealthboxProcessor } from './wealthbox/crm.preprocessor';
import { CRMProcessor as RedtailProcessor } from './redtail/crm.preprocessor';
import { SalesforceCRMProcessor } from './salesforce/crm.preprocessor';
import { QueueLogModule } from 'src/queue-log/queue-log.module';
import { ClientsModule } from 'src/clients/clients.module';
import { OrganisationsModule } from 'src/organisations/organisations.module';
import { SuperHttpModule } from 'src/super-http/super-http.module';
import { HttpModule } from '@nestjs/axios';
import { SalesforceController } from './salesforce/salesforce.controller';
import { SalesforceService } from './salesforce/salesforce.service';
import { PractifiController } from './practifi/practifi.controller';
import { PractifiService } from './practifi/practifi.service';
import { PractifiCRMProcessor } from './practifi/crm.preprocessor';
import { ClsModule , ClsService } from 'nestjs-cls';

@Module({
  imports: [
    SuperHttpModule.register({
      redis: {
        host: process.env.REDIS_HOST || 'redis',
        port: Number(process.env.REDIS_PORT) || 6379,
        password: process.env.REDIS_PASSWORD,
        ttl: 60000,
        tls: process.env.WORK_ENV !== 'dev',
      },
      rateLimiters: {
        wealthbox: {
          maxConcurrent: 3,
          minTime: 333,
          highWater: 1000,
        },
        redtail: {
          maxConcurrent: 2,
          minTime: 333,
          highWater: 1000,
        },
        salesforce: {
          maxConcurrent: 3,
          minTime: 333,
          highWater: 1000,
        },
        practifi: {
          maxConcurrent: 10,
          minTime: 333,
          highWater: 1000,
        },
      },
      defaultRateLimiter: 'wealthbox'
    }),
    HttpModule,
    forwardRef(() => AdvisorsModule),
    forwardRef(() => InterviewsModule),
    forwardRef(() => MailModule),
    BullModule.registerQueue(
      {
        name: CRM_QUEUE.WEALTHBOX.NAME,
      },
      {
        name: CRM_QUEUE.REDTAIL.NAME,
      },
      {
        name: CRM_QUEUE.SALESFORCE.NAME,
      },
      {
        name: CRM_QUEUE.PRACTIFI.NAME,
      },
    ),
    BullBoardModule.forFeature(
      {
        name: CRM_QUEUE.WEALTHBOX.NAME,
        adapter: BullMQAdapter,
      },
      {
        name: CRM_QUEUE.REDTAIL.NAME,
        adapter: BullMQAdapter,
      },
      {
        name: CRM_QUEUE.SALESFORCE.NAME,
        adapter: BullMQAdapter,
      },
      {
        name: CRM_QUEUE.PRACTIFI.NAME,
        adapter: BullMQAdapter,
      },
    ),
    forwardRef(() => QueueLogModule),
    forwardRef(() => ClientsModule),
    forwardRef(() => OrganisationsModule),
    ClsModule,
  ],
  providers: [
    CRMService,
    WealthboxService,
    WealthboxProcessor,
    RedtailProcessor,
    SalesforceCRMProcessor,
    SalesforceService,
    PractifiCRMProcessor,
    PractifiService
  ],
  controllers: [WealthboxController, SalesforceController, PractifiController],
  exports: [CRMService],
})
export class CRMModule {}
