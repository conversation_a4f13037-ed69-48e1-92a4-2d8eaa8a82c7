import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { AdvisorsDocusignService } from 'src/advisors/services/advisors.docusign.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { MutexService } from 'src/mutex/mutex.service';
import {
  DocusignAuthScopesEnum,
  DocusignGrantTypesEnum,
  DocusignOAuthData,
} from 'src/integrations/docusign/docusign.types';
import { IntegrationEnum } from 'src/shared/types/integrations';
import { HttpException, HttpStatus } from '@nestjs/common';
import * as docusign from 'docusign-esign';
import * as jwt from 'jsonwebtoken';
import * as encryption from 'src/utils/encryption';
import * as utils from 'src/utils';
import { AdvisorsCrudService } from 'src/advisors/services/advisors.crud.service';
import { ClientsV1Service } from 'src/clients/clients.service';
import { InterviewsService } from 'src/interviews/interviews.service';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';

const now = Date.now();
const today = new Date();

jest.mock('jsonwebtoken', () => ({
  ...jest.requireActual('jsonwebtoken'),
  decode: jest.fn().mockReturnValue({
    iat: new Date().getTime() / 1000 - 1000000,
  }),
}));

describe.skip('DocusignService', () => {
  let service: DocusignService;
  let httpService: HttpService;
  let configService: ConfigService;
  let advisorsDocusignService: AdvisorsDocusignService;
  let advisorsCrudService: AdvisorsCrudService;
  let organisationsService: OrganisationsService;
  let mutexService: MutexService;
  let clientsService: ClientsV1Service;
  let interviewsService: InterviewsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocusignService,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('test'),
          },
        },
        {
          provide: HttpService,
          useValue: {
            axiosRef: {
              post: jest.fn().mockResolvedValue({ data: {} }),
            },
          },
        },
        {
          provide: AdvisorsDocusignService,
          useValue: {
            linkDocusign: jest.fn(),
            findDocusignRefreshCandidates: jest.fn(),
            findDocusignIntegration: jest.fn(),
          },
        },
        {
          provide: AdvisorsCrudService,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: OrganisationsService,
          useValue: {
            linkDocusign: jest.fn(),
            findDocusignRefreshCandidates: jest.fn(),
            findDocusignIntegration: jest.fn(),
          },
        },
        {
          provide: MutexService,
          useValue: {
            findOne: jest.fn(),
            lock: jest.fn(),
            unlock: jest.fn(),
            isLocked: jest.fn(),
          },
        },
        {
          provide: ClientsV1Service,
          useValue: {
            update: jest.fn(),
          },
        },
        {
          provide: InterviewsService,
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: WINSTON_MODULE_PROVIDER,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
            info: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<DocusignService>(DocusignService);
    httpService = module.get<HttpService>(HttpService);
    configService = module.get<ConfigService>(ConfigService);
    advisorsDocusignService = module.get<AdvisorsDocusignService>(
      AdvisorsDocusignService,
    );
    organisationsService =
      module.get<OrganisationsService>(OrganisationsService);
    mutexService = module.get<MutexService>(MutexService);

    jest.spyOn(global, 'Date').mockImplementation(() => today);
    jest.spyOn(docusign.ApiClient.prototype, 'setBasePath');
    jest.spyOn(docusign.ApiClient.prototype, 'getAuthorizationUri');
    jest.spyOn(utils, 'camelize').mockReturnValue({} as any);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getAuthorizationUri', () => {
    it('should call apiClient getAuthorizationUri with correct parameters and return the uri', async () => {
      const dto = {
        advisorId: 'testAdvisorId',
        organisationId: 'testOrganisationId',
      };
      const state = JSON.stringify(dto);

      const expectedScopes = [
        DocusignAuthScopesEnum.SIGNATURE,
        DocusignAuthScopesEnum.EXTENDED,
      ];

      await service.getAuthorizationUri(dto);

      jest
        .spyOn(service, 'getAuthorizationUri')
        .mockResolvedValueOnce('testUri');

      expect(
        docusign.ApiClient.prototype.getAuthorizationUri,
      ).toHaveBeenCalledWith(
        'test',
        expectedScopes,
        'test',
        DocusignGrantTypesEnum.AUTHORIZATION_CODE,
        state,
      );
    });
  });

  describe('getAccessToken', () => {
    it('should return access token', async () => {
      jest
        .spyOn(docusign.ApiClient.prototype, 'generateAccessToken')
        .mockResolvedValueOnce({
          accessToken: 'accessToken',
          refreshToken: 'refreshToken',
          expiresIn: 500,
          lastRefresh: today,
        });

      const result = {
        accessToken: 'accessToken',
        refreshToken: 'refreshToken',
        expiresIn: 500,
        lastRefresh: today,
      };

      const tokenInfo = await service.getAccessToken('authorizationCode');
      expect(tokenInfo).toEqual(result);
    });

    it('should handle errors', async () => {
      jest
        .spyOn(docusign.ApiClient.prototype, 'generateAccessToken')
        .mockImplementation(() => {
          throw new Error();
        });

      await expect(service.getAccessToken('authorizationCode')).rejects.toThrow(
        Error,
      );
    });
  });

  describe('storeAccessToken', () => {
    const oAuthData: DocusignOAuthData = {
      accessToken: 'accessToken',
      refreshToken: 'refreshToken',
      expiresIn: '1000',
      lastRefresh: new Date(),
    };

    it('should store access token for an advisor', async () => {
      const id = { advisorId: '1' };

      jest
        .spyOn(advisorsDocusignService, 'linkDocusign')
        .mockResolvedValueOnce({
          message: 'Docusign linked successfully.',
        });

      await service.storeAccessToken(id, oAuthData);

      expect(advisorsDocusignService.linkDocusign).toHaveBeenCalledWith(
        id.advisorId,
        oAuthData,
        false,
      );
    });

    it('should store access token for an organization', async () => {
      const id = { organisationId: '1' };

      jest.spyOn(organisationsService, 'linkDocusign').mockResolvedValueOnce({
        message: 'Docusign linked successfully.',
      });

      await service.storeAccessToken(id, oAuthData);

      expect(organisationsService.linkDocusign).toHaveBeenCalledWith(
        id.organisationId,
        oAuthData,
        false,
      );
    });

    it('should throw an error when no ID provided', async () => {
      const id = {};

      await expect(service.storeAccessToken(id, oAuthData)).rejects.toThrow(
        new HttpException(
          'Advisor or Organisation id not found.',
          HttpStatus.BAD_REQUEST,
        ),
      );
    });
  });

  describe('verifyAccessToken', () => {
    const accessToken = 'thisIsAnAccessToken';
    const expiresIn = '28800';
    const lastRefresh = new Date('2023-09-16T21:21:50.000Z');

    const entity: any = {
      integrations: [
        {
          integrationType: 'DocumentSigning',
          integrationConfig: {
            credentials: 'credentials',
            expiresIn,
            lastRefresh: lastRefresh.toISOString(),
          },
        },
      ],
    };

    const dto = {
      advisorId: '123',
    };

    beforeEach(() => {
      jest.spyOn(global, 'Date').mockRestore();
      jest.clearAllMocks();
    });

    it('should handle token expiry', async () => {
      jest
        .spyOn(advisorsDocusignService, 'findDocusignIntegration')
        .mockResolvedValueOnce(entity.integrations[0].integrationConfig);
      jest.spyOn(encryption, 'decryptCredentials').mockResolvedValueOnce({
        accessToken,
        expiresIn,
      });

      jest
        .spyOn(service, 'refreshTokensForEntity')
        .mockResolvedValueOnce({} as any);

      await expect(service.verifyAccessToken(dto)).resolves.toBeTruthy();

      expect(service.refreshTokensForEntity).toBeCalled();
    });

    it('should handle valid access tokens', async () => {
      jest
        .spyOn(advisorsDocusignService, 'findDocusignIntegration')
        .mockResolvedValueOnce(entity.integrations[0].integrationConfig);
      jest.spyOn(encryption, 'decryptCredentials').mockResolvedValueOnce({
        accessToken,
        expiresIn,
      });

      jest.spyOn(jwt, 'decode').mockReturnValueOnce({ iat: now / 1000 });

      jest
        .spyOn(service, 'refreshTokensForEntity')
        .mockResolvedValueOnce({} as any);

      await service.verifyAccessToken(dto);

      expect(service.refreshTokensForEntity).not.toBeCalled();
    });

    it('should handle invalid token', async () => {
      jest
        .spyOn(advisorsDocusignService, 'findDocusignIntegration')
        .mockResolvedValueOnce(entity.integrations[0].integrationConfig);
      jest.spyOn(encryption, 'decryptCredentials').mockResolvedValueOnce({
        accessToken,
        expiresIn,
      });

      (jwt.decode as jest.Mock).mockReset();
      (jwt.decode as jest.Mock).mockImplementation(() => {
        throw new Error('Invalid token');
      });

      await expect(service.verifyAccessToken(dto)).resolves.toBeFalsy();
    });
  });

  describe('refreshTokens', () => {
    beforeEach(() => {
      jest.spyOn(global, 'Date').mockRestore();
    });

    it('should exit early if mutex is locked', async () => {
      jest.spyOn(mutexService, 'lock').mockImplementationOnce(() => {
        throw new Error('Mutex is locked');
      });

      await expect(service.refreshTokens()).rejects.toThrow();
    });

    it('should lock and unlock mutex', async () => {
      jest.spyOn(mutexService, 'lock').mockResolvedValueOnce({} as any);
      jest.spyOn(mutexService, 'unlock').mockResolvedValueOnce({} as any);

      await service.refreshTokens();

      expect(mutexService.lock).toBeCalledWith('DocusignService');
      expect(mutexService.unlock).toBeCalledWith('DocusignService');
    });

    it('should break loop if no entities found for refresh', async () => {
      jest.spyOn(mutexService, 'lock').mockResolvedValueOnce({} as any);
      jest.spyOn(mutexService, 'unlock').mockResolvedValueOnce({} as any);

      jest
        .spyOn(advisorsDocusignService, 'findDocusignRefreshCandidates')
        .mockResolvedValueOnce([]);
      jest
        .spyOn(organisationsService, 'findDocusignRefreshCandidates')
        .mockResolvedValueOnce([]);

      await service.refreshTokens();

      expect(
        advisorsDocusignService.findDocusignRefreshCandidates,
      ).toBeCalledTimes(1);
      expect(
        organisationsService.findDocusignRefreshCandidates,
      ).toBeCalledTimes(1);
    });

    it('should refresh tokens for all entities', async () => {
      jest.spyOn(mutexService, 'lock').mockResolvedValueOnce({} as any);
      jest.spyOn(mutexService, 'unlock').mockResolvedValueOnce({} as any);

      const [advisor, organisation] = [
        { _id: '123', integrations: [] },
        { _id: '456', externalAccounts: [] },
      ];

      jest
        .spyOn(advisorsDocusignService, 'findDocusignRefreshCandidates')
        .mockResolvedValueOnce([advisor as any]);

      jest
        .spyOn(organisationsService, 'findDocusignRefreshCandidates')
        .mockResolvedValueOnce([organisation as any]);

      const spy = jest
        .spyOn(service, 'refreshTokensForEntity')
        .mockResolvedValueOnce();

      await service.refreshTokens();

      expect(spy).toBeCalledTimes(2);
      expect(spy).toHaveBeenNthCalledWith(1, advisor, {
        httpService,
        logger: {} as any,
      });
      expect(spy).toHaveBeenNthCalledWith(2, organisation, {
        httpService,
        logger: {} as any,
      });
    });

    it('should handle exceptions gracefully', async () => {
      jest.spyOn(mutexService, 'unlock').mockResolvedValueOnce({} as any);

      jest
        .spyOn(advisorsDocusignService, 'findDocusignRefreshCandidates')
        .mockRejectedValue(new Error('Failed to get advisors'));

      jest
        .spyOn(organisationsService, 'findDocusignRefreshCandidates')
        .mockRejectedValue(new Error('Failed to get organisations'));

      const loggerSpy = jest.spyOn({} as any, 'error');

      await service.refreshTokens();

      expect(loggerSpy).toBeCalledWith(
        expect.stringContaining('Error in refreshAccessTokens batch process'),
      );
      expect(mutexService.unlock).toBeCalledWith('DocusignService');
    });
  });

  describe('refreshTokensForEntity', () => {
    const data = {
      access_token: 'newAccessToken',
      refresh_token: 'newRefreshToken',
      expires_in: 100,
    };

    const camelized = {
      accessToken: data.access_token,
      refreshToken: data.refresh_token,
      expiresIn: data.expires_in,
    };

    const mockEntityAdvisor: any = {
      _id: 'advisorId',
      integrations: [
        {
          integrationType: IntegrationEnum.DocumentSigning,
          integrationConfig: { credentials: 'encryptedCredentials' },
        },
      ],
    };

    const mockEntityOrganisation: any = {
      _id: 'organisationId',
      externalAccounts: [
        {
          integrationType: 'DocumentSigning',
          integrationConfig: { credentials: 'some_encrypted_credentials' },
        },
      ],
    };

    const options: any = {
      httpService: {
        axiosRef: {
          post: jest.fn().mockResolvedValue({ data: {} }),
        },
      },
      logger: {
        error: jest.fn().mockImplementation((error) => console.error(error)),
      },
    };

    beforeEach(() => {
      jest.spyOn(global, 'Date').mockRestore();

      jest
        .spyOn(encryption, 'decryptCredentials')
        .mockResolvedValueOnce({ refreshToken: 'refreshToken' });

      jest.spyOn(service, 'storeAccessToken');

      jest.spyOn(utils, 'camelize').mockReturnValue(camelized);
    });

    it('should call decryptCredentials to decrypt the current refresh token', async () => {
      await service.refreshTokensForEntity(mockEntityAdvisor, options);

      expect(encryption.decryptCredentials).toHaveBeenCalledWith(
        'encryptedCredentials',
      );
    });

    it('should post to Docusign API to refresh token', async () => {
      await service.refreshTokensForEntity(mockEntityAdvisor, options);

      expect(options.httpService.axiosRef.post).toBeCalled();
    });

    it('should store new access token for Advisor entities', async () => {
      options.httpService.axiosRef.post.mockResolvedValueOnce({ data });

      await service.refreshTokensForEntity(mockEntityAdvisor, options);

      expect(service.storeAccessToken).toBeCalledWith(
        { advisorId: 'advisorId' },
        expect.objectContaining({
          accessToken: data.access_token,
          refreshToken: data.refresh_token,
          expiresIn: data.expires_in,
        }),
        true,
      );
    });

    it('should store new access token for Organisation entities', async () => {
      jest
        .spyOn(options.httpService.axiosRef, 'post')
        .mockResolvedValueOnce({ data });

      await service.refreshTokensForEntity(mockEntityOrganisation, options);

      expect(service.storeAccessToken).toBeCalledWith(
        { organisationId: mockEntityOrganisation._id },
        expect.objectContaining({
          accessToken: data.access_token,
          refreshToken: data.refresh_token,
          expiresIn: data.expires_in,
        }),
        true,
      );
    });

    it('should log an error if token refreshing fails', async () => {
      options.httpService.axiosRef.post.mockRejectedValueOnce(
        new Error('Some error'),
      );

      await service.refreshTokensForEntity(mockEntityAdvisor, options);

      expect(options.logger.error).toBeCalledWith(
        expect.stringContaining(
          'Error refreshing token for Advisor advisorId: Some error',
        ),
      );
    });
  });
});
