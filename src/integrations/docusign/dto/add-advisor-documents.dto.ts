import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>N<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';

export type Applicant = {
  name: string;
  email: string;
  phone: string;
};

export class AddAdvisorDocumentsDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  advisorId?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  organisationId?: string;

  @ApiProperty()
  @IsArray()
  files: Express.Multer.File[];

  @ApiProperty()
  @IsString()
  envelopeId: string | undefined;

  @ApiProperty()
  interviewData: InterviewDataWithCrmInfo;

  @ApiProperty()
  @IsNumber()
  previousDocumentUploadNo: number;
}
