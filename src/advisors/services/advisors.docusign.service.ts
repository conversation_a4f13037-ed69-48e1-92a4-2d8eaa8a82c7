import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  forwardRef,
} from '@nestjs/common';
import { Advisor } from 'src/advisors/schemas/advisors.schema';
import { FilterQuery, Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import {
  BATCH_SIZE,
  DocusignAccountOwnershipEnum,
  REFRESH_THRESHOLD,
} from 'src/integrations/docusign/docusign.types';
import { Integration } from 'src/shared/schemas/integration.schema';
import {
  DocumentSigningEnum,
  IntegrationConfig,
  IntegrationEnum,
} from 'src/shared/types/integrations';
import { encrypt } from 'src/utils';
import { LinkDocusignDto } from 'src/advisors/dto/link-docusign.dto';
import { addIntegration } from 'src/utils/addIntegration';
import { UpdateDocusignAccountOwnershipDto } from '../dto/update-docusign-account-ownership.dto';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { Logger } from 'winston';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';

@Injectable()
export class AdvisorsDocusignService {
  constructor(
    @InjectModel(Advisor.name)
    protected advisorModel: Model<Advisor>,
    @Inject(forwardRef(() => DocusignService))
    private readonly docusignService: DocusignService,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  async linkDocusign(
    id: string,
    linkDocusignDto: LinkDocusignDto,
    isRefresh = false,
    accountOwnership = DocusignAccountOwnershipEnum.Firm,
  ) {
    const { accessToken, refreshToken, expiresIn, lastRefresh } =
      linkDocusignDto;

    if (!accessToken || !refreshToken) {
      throw new HttpException(
        'Access token or refresh token not found.',
        HttpStatus.BAD_REQUEST,
      );
    }

    const encryptedTokens = await encrypt(
      JSON.stringify({ accessToken, refreshToken }),
    );

    const integration = {
      integrationType: IntegrationEnum.DocumentSigning,
      integrationConfig: {
        name: DocumentSigningEnum.Docusign,
        credentials: encryptedTokens,
        lastRefresh,
        expiresIn,
        accountOwnership,
        accountId: null,
        accountName: null,
        enabled: true,
      },
    };

    const filter = { _id: id };

    await addIntegration<Model<Advisor>>(
      filter,
      integration,
      this.advisorModel,
      isRefresh,
    );

    const accountInfo = await this.docusignService.getAccountInfo({
      advisorId: id,
    });

    
    const schwabTemplateId = await this.docusignService.getSchwabEnvelopeTemplate(accountInfo.accountId, { advisorId: id });
   

    const { integrations } = await this.advisorModel.findOne({ _id: id });

    await this.advisorModel.updateOne(
      { _id: id },
      {
        integrations: [
          ...integrations.filter(
            (integration) =>
              integration.integrationType !== IntegrationEnum.DocumentSigning,
          ),
          {
            ...integration,
            integrationConfig: {
              ...integration.integrationConfig,
              ...accountInfo,
              ...(schwabTemplateId && {schwabTemplateId }),
            },
          },
        ],
        updatedAt: new Date(),
      },
    );

    return { message: `Docusign linked successfully.` };
  }

  async unlinkDocusign(id: string) {
    const filter: FilterQuery<Advisor> = { _id: id };
    const { integrations } = await this.advisorModel.findOne(filter);

    if (!integrations) {
      throw new HttpException('Advisor not found.', HttpStatus.NOT_FOUND);
    }

    await this.advisorModel.updateOne(filter, {
      integrations: integrations.filter(
        ({ integrationType }) =>
          integrationType !== IntegrationEnum.DocumentSigning,
      ),
    });

    return { message: `Docusign unlinked successfully.` };
  }

  async updateDocusignAccountOwnership(
    id: string,
    updateAccountOwnership: UpdateDocusignAccountOwnershipDto,
  ) {
    const { accountOwnership } = updateAccountOwnership;

    await this.advisorModel.updateOne(
      {
        _id: id,
        'integrations.integrationType': IntegrationEnum.DocumentSigning,
      },
      {
        $set: {
          'integrations.$.integrationConfig.accountOwnership': accountOwnership,
          updatedAt: new Date(),
        },
      },
    );

    return 'DocuSign account ownership updated successfully.';
  }

  async findDocusignRefreshCandidates(page: number): Promise<Advisor[]> {
    // Find batch of advisors with docusign integration
    const limit = BATCH_SIZE;
    const skip = limit * (page - 1);

    const found = await this.advisorModel
      .find({
        'integrations.integrationConfig.name': DocumentSigningEnum.Docusign,
      })
      .skip(skip)
      .limit(limit)
      .exec();

    // Filter out advisors without a docusign integration setup or who have refreshed their token
    // within the last REFRESH_THRESHOLD days
    const refreshCandidates = found.filter((advisor) => {
      const { integrationConfig } =
        advisor.integrations.find(
          ({ integrationType }: Integration) =>
            integrationType === IntegrationEnum.DocumentSigning,
        ) || {};

      if (!integrationConfig) {
        return false;
      }

      const {
        lastRefresh,
      }: Partial<IntegrationConfig & { lastRefresh: Date }> = integrationConfig;

      const now = new Date();
      const threshold = new Date(
        lastRefresh.getTime() + REFRESH_THRESHOLD * 24 * 60 * 60 * 1000,
      );

      return now >= threshold;
    });

    return refreshCandidates;
  }

  async findDocusignIntegration(id: string) {
    if (!id) {
      return null;
    }

    const advisor = await this.advisorModel.findOne({
      _id: id,
      'integrations.integrationConfig.name': DocumentSigningEnum.Docusign,
    });

    if (!advisor) {
      return null;
    }

    const { integrationConfig } =
      advisor.integrations.find(
        ({ integrationType }: Integration) =>
          integrationType === IntegrationEnum.DocumentSigning,
      ) || null;

    return integrationConfig;
  }

  async setDocusignAccount(id: string, accountId: string) {
    const account = await this.docusignService.getAccountInfoById(accountId, {
      advisorId: id,
    });

    await this.advisorModel.updateOne(
      {
        _id: id,
        'integrations.integrationConfig.name': DocumentSigningEnum.Docusign,
      },
      {
        $set: {
          'integrations.$.integrationConfig.accountId': account.accountId,
          'integrations.$.integrationConfig.accountName': account.accountName,
          updatedAt: new Date(),
        },
      },
    );

    return 'DocuSign account ID set successfully.';
  }
}
