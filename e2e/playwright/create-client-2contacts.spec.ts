import { test, expect } from '@playwright/test';
import { login } from './utils/auth';
import { navigateToClientCreation, fillJointClientInfo, addAccount, completeClientSetup, addJointAccounts } from './utils/client';
import { TEST_CONFIG } from './config/environment';
import { generateRandomName, generateNexwaveEmail } from './utils/test-data';

test.describe('Client Creation', () => {
    test.beforeEach(async ({ page }) => {
        await login(page);
    });

    test('should create a new client with IRA account', async ({ page }) => {
        // Initial setup
        await navigateToClientCreation(page);
        await page.getByText('New Contact').first().click();
        await page.getByText('Yes', { exact: true }).click();
        await page.getByText('New Contact').nth(1).click();

        await page.getByRole('button', { name: 'Continue' }).click();
        await page.getByText('Open Accounts').click();
        await page.getByRole('button', { name: 'Continue' }).click();

        // Generate random client information
        const clientOne = generateRandomName();
        const clientTwo = generateRandomName();
        
        // Create client information
        const clientsInfo = {
            clientOne: {
                ...clientOne,
                email: generateNexwaveEmail(clientOne.firstName.toLowerCase()),
                phone: TEST_CONFIG.clientDefaults.joint.clientOne.phone
            },
            clientTwo: {
                ...clientTwo,
                email: generateNexwaveEmail(clientTwo.firstName.toLowerCase()),
                phone: TEST_CONFIG.clientDefaults.joint.clientTwo.phone
            }
        };
        
        // Fill client information
        await fillJointClientInfo(page, clientsInfo);

        // Add accounts for both clients
        await addJointAccounts(page, clientsInfo, {
            clientOne: [
                {
                    type: 'IRA',
                    nickname: `${clientOne.firstName}'s IRA`
                }
            ],
            clientTwo: [
                {
                    type: 'IRA',
                    nickname: `${clientTwo.firstName}'s IRA`
                }
            ]
        });

        // Complete setup
        await page.getByRole('button', { name: 'Continue' }).click();
        await completeClientSetup(page);

        // Wait for client creation to complete (neither Creating nor Failed)
        const statusCell = page.locator('tbody tr').first().locator('td').nth(3);
        await expect.soft(statusCell).toBeVisible({ timeout: 30000 });
        await expect(statusCell).not.toContainText(['Creating', 'Failed'], {
            timeout: 30000
        });
    });
});