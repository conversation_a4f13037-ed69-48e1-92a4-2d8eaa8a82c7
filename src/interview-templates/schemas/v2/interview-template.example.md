const exampleTemplate: InterviewTemplateV2 = {
  templateName: "Standard Client Onboarding v2",
  description: "Comprehensive onboarding with dynamic flow based on client profile",
  organisationId: "507f1f77bcf86cd799439011",
  version: 2,
  status: "published",
  isDefault: true,
  
  pages: [
    {
      pageId: "550e8400-e29b-41d4-a716-446655440001",
      pageName: "personal_information",
      pageTitle: "Personal Information",
      pageType: "standard",
      defaultOrder: 1,
      isRequired: true,
      
      flow: {
        rules: [],
        defaultNext: {
          pageId: "550e8400-e29b-41d4-a716-446655440002",
          label: "Continue to Employment"
        },
        allowBack: false,
        isTerminal: false
      },
      
      validationRules: [
        {
          field: "email",
          rule: "email",
          errorMessage: "Please enter a valid email address"
        },
        {
          field: "dateOfBirth",
          rule: "min:18",
          errorMessage: "You must be at least 18 years old"
        }
      ],
      
      uiSchema: {
        layout: "single_column",
        components: [
          {
            type: "text",
            field: "firstName",
            label: "First Name"
          },
          {
            type: "text", 
            field: "lastName",
            label: "Last Name"
          },
          {
            type: "email",
            field: "email",
            label: "Email Address"
          },
          {
            type: "date",
            field: "dateOfBirth",
            label: "Date of Birth"
          }
        ]
      }
    },
    {
      pageId: "550e8400-e29b-41d4-a716-446655440002",
      pageName: "employment_status",
      pageTitle: "Employment Information",
      pageType: "standard",
      defaultOrder: 2,
      
      flow: {
        rules: [
          {
            ruleId: "123e4567-e89b-12d3-a456-426614174001",
            ruleName: "Retired - Go to retirement income",
            priority: 1,
            when: {
              logic: "AND",
              conditions: [{
                field: "employmentStatus",
                operator: "equals",
                value: "retired",
                label: "User is retired"
              }]
            },
            goToPageId: "550e8400-e29b-41d4-a716-446655440003",
            isActive: true
          },
          {
            ruleId: "123e4567-e89b-12d3-a456-426614174002",
            ruleName: "Employed - Go to employer details",
            priority: 2,
            when: {
              logic: "AND",
              conditions: [{
                field: "employmentStatus",
                operator: "in",
                value: ["employed_full_time", "employed_part_time"],
                label: "User is employed"
              }]
            },
            goToPageId: "550e8400-e29b-41d4-a716-446655440004",
            isActive: true
          }
        ],
        defaultNext: {
          pageId: "550e8400-e29b-41d4-a716-446655440005",
          label: "Continue to Financial Information"
        },
        allowBack: true
      }
    }
  ],
  
  config: {
    navigation: {
      allowBranching: true,
      allowBackNavigation: true,
      showProgressBar: true,
      progressBarType: "steps",
      saveProgressEnabled: true
    },
    interview: {
      requireAuthentication: true,
      sessionTimeout: 30,
      allowMultipleAttempts: false
    },
    ui: {
      theme: "light",
      logoPosition: "top-left",
      showPageNumbers: true,
      showEstimatedTime: true
    },
    completion: {
      showSummary: false,
      sendConfirmationEmail: true
    }
  },
  
  accountConfig: {
    supportedTypes: ["Ira", "RothIra", "Brokerage"],
    requireBeneficiaries: true,
    beneficiaryRules: {
      allowMultiple: true,
      requirePercentage: true,
      minBeneficiaries: 1,
      maxBeneficiaries: 10
    }
  },
  
  tags: ["onboarding", "standard", "retirement"],
  complexity: "medium",
  estimatedCompletionTime: 15,
  
  publishedAt: new Date("2024-01-15"),
  publishedBy: "507f191e810c19729de860ea"
};