import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsArray,
  IsOptional,
  IsObject,
  IsBoolean,
} from 'class-validator';

export class Element {
  @IsString()
  @ApiProperty()
  elementType: string;

  @IsString()
  @ApiProperty()
  elementLabel: string;

  @IsArray()
  @IsOptional()
  @ApiProperty()
  options: string[];

  @IsBoolean()
  @ApiProperty()
  required: boolean;
}

export class InterviewPageDto {
  @IsString()
  @ApiProperty()
  name: string;

  @IsOptional()
  @ApiProperty()
  order: number;

  @IsArray()
  @IsObject({ each: true })
  @ApiProperty()
  elements: Element[];

  @IsObject()
  @IsOptional()
  data: Record<string, any>;
}

export class CreateInterviewTemplateDto {
  @IsString()
  @ApiProperty()
  templateName: string;

  @IsArray()
  @IsObject({ each: true })
  @ApiProperty()
  pages: InterviewPageDto[];

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  default: boolean;
}
