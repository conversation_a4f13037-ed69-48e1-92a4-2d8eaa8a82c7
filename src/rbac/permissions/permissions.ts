import {
  Permission,
  PermissionEnum,
} from 'src/shared/types/rbac/permissions.enum';

export const Permissions: { [key in PermissionEnum]: Permission } = {
  [PermissionEnum.CanAddCompany]: {
    id: PermissionEnum.CanAddCompany,
    description: 'Add a new company',
  },
  [PermissionEnum.CanListCompanies]: {
    id: PermissionEnum.CanListCompanies,
    description: 'List companies',
  },
  [PermissionEnum.CanManageEmailTemplates]: {
    id: PermissionEnum.CanManageEmailTemplates,
    description: 'Manage email templates',
  },
  [PermissionEnum.CanInterview]: {
    id: PermissionEnum.CanInterview,
    description: 'Fill out interview questions',
  },
  [PermissionEnum.CanAddClient]: {
    id: PermissionEnum.CanAddClient,
    description: 'Add a new client',
  },
  [PermissionEnum.CanAddUser]: {
    id: PermissionEnum.CanAddUser,
    description: 'Add company users',
  },
  [PermissionEnum.CanManageConfig]: {
    id: PermissionEnum.CanManageConfig,
    description: 'Manage company level configuration',
  },
  [PermissionEnum.CanDeleteAnyClients]: {
    id: PermissionEnum.CanDeleteAnyClients,
    description: 'Delete clients belonging to any company user',
  },
  [PermissionEnum.CanDisableUser]: {
    id: PermissionEnum.CanDisableUser,
    description: 'Disable company users',
  },
  [PermissionEnum.CanListAnyClients]: {
    id: PermissionEnum.CanListAnyClients,
    description: 'List/View clients belonging to any company user',
  },
  [PermissionEnum.CanListUsers]: {
    id: PermissionEnum.CanListUsers,
    description: 'List company users',
  },
  [PermissionEnum.CanManageUserProfile]: {
    id: PermissionEnum.CanManageUserProfile,
    description: 'Manage profile information for company users',
  },
  [PermissionEnum.CanModifyAnyClients]: {
    id: PermissionEnum.CanModifyAnyClients,
    description: 'Modify clients belonging to any company user',
  },
  [PermissionEnum.CanUpdateSelf]: {
    id: PermissionEnum.CanUpdateSelf,
    description: 'Update your own profile',
  },
  [PermissionEnum.CanCreateCompanyUsers]: {
    id: PermissionEnum.CanCreateCompanyUsers,
    description: 'Decides if the user can create company users or not',
  },
  [PermissionEnum.ManageCompanyStatus]: {
    id: PermissionEnum.ManageCompanyStatus,
    description: 'Decides if the user can manage company status or not',
  },
  [PermissionEnum.ManageProduct]: {
    id: PermissionEnum.ManageProduct,
    description: 'Decides if the user can manage product or not',
  },
  [PermissionEnum.AddProductAdmin]: {
    id: PermissionEnum.AddProductAdmin,
    description: 'Decides if the user can add product admin or not',
  },
  [PermissionEnum.ListOwnClients]: {
    id: PermissionEnum.ListOwnClients,
    description: 'Decides if the user can list own clients or not',
  },
  [PermissionEnum.ModifyOwnClients]: {
    id: PermissionEnum.ModifyOwnClients,
    description: 'Decides if the user can modify own clients or not',
  },
  [PermissionEnum.DeleteOwnClients]: {
    id: PermissionEnum.DeleteOwnClients,
    description: 'Decides if the user can delete own clients or not',
  },
  [PermissionEnum.MonitorActivity]: {
    id: PermissionEnum.MonitorActivity,
    description:
      'Decides if the user can monitor overall activity of companies, advisors and clients',
  },
};
