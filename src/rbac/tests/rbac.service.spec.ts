import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { RbacService } from '../rbac.service';
import { Role } from '../schemas/role.schema';
import { CreateRoleDto } from '../dto/create-role.dto';
import { PermissionEnum } from 'src/shared/types/rbac/permissions.enum';
import { AddPermissionDto } from '../dto/add-permission.dto';

describe('RbacService', () => {
  let service: RbacService;

  const mockRoleModel = {
    find: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue([]),
      session: jest.fn().mockReturnValue([]),
    }),
    findOne: jest.fn().mockReturnValue({
      exec: jest.fn().mockReturnThis(),
      session: jest.fn().mockReturnValue({}),
    }),
    findById: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue({}),
      session: jest.fn().mockReturnValue({}),
    }),
    findByIdAndDelete: jest.fn().mockReturnValue({
      exec: jest.fn().mockResolvedValue({}),
    }),
    create: jest.fn().mockResolvedValue({}),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RbacService,
        {
          provide: getModelToken(Role.name),
          useValue: mockRoleModel,
        },
      ],
    }).compile();

    service = module.get<RbacService>(RbacService);
    jest.clearAllMocks();
  });

  describe('findAllRoles', () => {
    it('should find all roles', async () => {
      await service.findAllRoles();
      expect(mockRoleModel.find).toHaveBeenCalled();
    });
  });

  describe('findOneRole', () => {
    it('should find one role', async () => {
      await service.findOneRole({ _id: '12345' }, {} as any);
      expect(mockRoleModel.findOne).toHaveBeenCalled();
    });
  });

  describe('createRole', () => {
    it('should create a role', async () => {
      mockRoleModel.findOne.mockResolvedValue(null);
      const createRoleDto: CreateRoleDto = {
        name: 'Admin',
        permissions: [PermissionEnum.CanCreateCompanyUsers],
      };
      await service.createRole(createRoleDto);
      expect(mockRoleModel.findOne).toHaveBeenCalled();
      expect(mockRoleModel.create).toHaveBeenCalled();
    });

    it('should not create a role when it already exists', async () => {
      mockRoleModel.findOne.mockResolvedValue({
        session: jest.fn().mockReturnValue({}),
      });
      const createRoleDto: CreateRoleDto = {
        name: 'Admin',
        permissions: [PermissionEnum.CanCreateCompanyUsers],
      };
      await expect(service.createRole(createRoleDto)).rejects.toThrow();
      expect(mockRoleModel.findOne).toHaveBeenCalled();
    });
  });

  describe('deleteRole', () => {
    it('should delete a role', async () => {
      await service.deleteRole('12345');
      expect(mockRoleModel.findByIdAndDelete).toHaveBeenCalled();
    });
  });

  describe('findOneRole', () => {
    it.skip('should find a role by ID', async () => {
      mockRoleModel.findOne.mockResolvedValue({
        session: jest.fn(),
      });
      const _id = '12345';
      await service.findOneRole({ _id });
      expect(mockRoleModel.findOne).toHaveBeenCalled();
      expect(mockRoleModel.findOne).toHaveBeenCalledWith({ _id });
    });
  });

  describe('addPermission', () => {
    it('should not update a role when it does not exist', async () => {
      const _id = '12345';
      const addPermissionDto: AddPermissionDto = {
        permissionId: PermissionEnum.CanInterview,
      };
      mockRoleModel.findById.mockResolvedValue(null);
      await expect(
        service.addPermission(_id, addPermissionDto),
      ).rejects.toThrow();
      expect(mockRoleModel.findById).toHaveBeenCalled();
      expect(mockRoleModel.findById).toHaveBeenCalledWith(_id);
    });
  });

  describe('deleteRole', () => {
    it('should not delete a role when it does not exist', async () => {
      const _id = '12345';
      mockRoleModel.findByIdAndDelete.mockResolvedValue(null);
      await expect(service.deleteRole(_id)).rejects.toThrow();
      expect(mockRoleModel.findByIdAndDelete).toHaveBeenCalled();
      expect(mockRoleModel.findByIdAndDelete).toHaveBeenCalledWith(_id);
    });
  });
});
