import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

export interface FormUpdateMapper {
  mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: unknown,
  ): Promise<void>;
}
