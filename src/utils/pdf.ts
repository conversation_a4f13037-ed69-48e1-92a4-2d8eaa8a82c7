import { InternalServerErrorException } from '@nestjs/common';
import {
  Checkbox,
  Date,
  DateSigned,
  EnvelopeRecipientTabs,
  SignHere,
  Text,
} from 'docusign-esign';
import { createReadStream } from 'fs';
import { PDFDocumentProxy, getDocument } from 'pdfjs-dist';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';
import { AccountAdvisoryDocumentsEnum } from 'src/shared/types/accounts/account-documents.enum';
import { AccountOwnershipEnum } from 'src/shared/types/accounts/account-ownership.enum';
import { contains } from 'src/utils';
import { pdfMappers } from 'src/utils/pdf-mappers';
import { getAccountByFileName } from 'src/utils/pdf-mappers/utils/getAccountByFileName';
import { Readable } from 'stream';
import { isStreamReadable, streamToBuffer } from './file/file.util';
import {
  isContactFile,
  parseContactEnumFromFileName,
} from 'src/utils/pdf-mappers/utils/getContactByFileName';

enum DocusignTabTypesEnum {
  TextTabs = 'textTabs',
  CheckboxTabs = 'checkboxTabs',
  SignHereTabs = 'signHereTabs',
  DateTabs = 'dateTabs',
  DateSignedTabs = 'dateSignedTabs',
}

enum PdfFormFieldsEnum {
  Text = 'Tx',
  Button = 'Btn',
  Signature = 'Sig',
  Date = 'Date',
  Phone = 'Tel',
  URL = 'URL',
  Email = 'Email',
  Image = 'Img',
}

type PdfFormField = {
  type: PdfFormFieldsEnum;
  name: string;
  page: number;
  value: string | number;
  pageHeight: number;
  rect: [number, number, number, number];
  required: boolean;
};

/**
 * Determines the type of Docusign tab to use based on the field type and name.
 * @param fieldType - The type of PDF form field.
 * @param name - The name of the PDF form field.
 * @returns The type of Docusign tab to use, or null if no tab type is found.
 */
const getTabType = (
  fieldType: PdfFormFieldsEnum,
  name: string,
): DocusignTabTypesEnum | null => {
  const containsDate = /((D|d)ate)/.test(name);
  const containsSignature = /((S|s)ign)/.test(name);

  const key = `${containsDate ? 'D' : ''}${containsSignature ? 'S' : ''}`;

  const tabTypeMap = {
    DS: DocusignTabTypesEnum.DateSignedTabs,
    D: DocusignTabTypesEnum.DateTabs,
    S: DocusignTabTypesEnum.SignHereTabs,
    '': DocusignTabTypesEnum.TextTabs,
  };

  const docusignTabType: DocusignTabTypesEnum = tabTypeMap[key];

  switch (fieldType) {
    case PdfFormFieldsEnum.Text:
    case PdfFormFieldsEnum.Date:
    case PdfFormFieldsEnum.Signature:
      return docusignTabType;
    case PdfFormFieldsEnum.Button:
      return DocusignTabTypesEnum.CheckboxTabs;
    case PdfFormFieldsEnum.Image:
      return null;
    default:
      return DocusignTabTypesEnum.TextTabs;
  }
};

/**
 * Retrieves the form fields from a PDF document for a given page.
 * @param pdfDocument - The PDF document to retrieve form fields from.
 * @param i - The page number to retrieve form fields from.
 * @returns A Promise that resolves to an array of PdfFormField objects.
 */
const getFields = async (
  pdfDocument: PDFDocumentProxy,
  i: number,
): Promise<PdfFormField[]> => {
  const page = await pdfDocument.getPage(i);
  const annotations = await page.getAnnotations();
  const { height: pageHeight } = page.getViewport({ scale: 1 });
  return annotations
    .filter((annotation) => annotation.fieldType)
    .map((annotation) => ({
      type: annotation.fieldType,
      name: annotation.fieldName,
      value: annotation.fieldValue,
      page: i,
      pageHeight,
      rect: annotation.rect,
      required: annotation.required,
    }));
};

/**
 * Identify and extract form fields from all the pages of a pdf file.
 *
 * Form filling in the context of a PDF refers to the ability to interactively fill out form
 * fields embedded within the PDF document. These fields can be text boxes, radio buttons,
 * checkboxes, combo boxes, etc.
 *
 * @param pdfBuffer
 * @returns the flattened array of form fields
 */
async function extractPdfFormFields(
  pdfStream: Readable,
): Promise<PdfFormField[]> {
  const buffer = await streamToBuffer(pdfStream);
  const pdfDocument = await getDocument(new Uint8Array(buffer)).promise;

  const numPages = pdfDocument.numPages;

  const pages = Array.from({ length: numPages }, (_, i) =>
    getFields(pdfDocument, i + 1),
  );
  const formFields = await Promise.all(pages);
  return formFields.flat();
}

/**
 * Parses a pdf file replacing any existing form fields with matching docusign tabs.
 * PDF's coordinate system's origin is at the bottom-left of the page, and y-values increase upwards.
 * DocuSign's origin is at the top-left, and y-values increase downwards, so their coordinates
 * must be derived from the pdf form fields' rectangle vector using the pdf's page height.
 *
 * @param file a multipart pdf file
 * @param documentId the DocuSign document id
 * @returns a docusign tab object
 */
export async function prepareDocusignTabs(
  file: Express.Multer.File,
  documentId: string,
  interviewData?: InterviewDataWithCrmInfo,
): Promise<EnvelopeRecipientTabs> {
  let fileStream: Readable;
  if (file.stream && isStreamReadable(file.stream)) fileStream = file.stream;
  else if (file.path) {
    const diskPath = file.path;
    fileStream = createReadStream(diskPath);
  } else {
    throw new InternalServerErrorException(
      `File stream error ${file.originalname}`,
    );
  }

  const formFields = await extractPdfFormFields(fileStream);

  const tabs: EnvelopeRecipientTabs = formFields.reduce(
    (acc, field, i) => {
      if (field === undefined) {
        this.logger.info(JSON.stringify({acc, field, i}))
        return acc;
      }

      const { type, name, page, pageHeight, rect, required } = field;
      const [x1, y1, x2, y2] = rect.map((floatVal: number) =>
        Math.round(floatVal),
      );

      const DELTA = 5;
      let xPosition = Math.round(x1) - DELTA;
      let yPosition = Math.round(pageHeight - y2) - DELTA;

      xPosition = xPosition < 0 ? 0 : xPosition;
      yPosition = yPosition < 0 ? 0 : yPosition;

      const width = (x2 - x1).toString();
      const height = (y2 - y1).toString();

      const tabId = `doc_${documentId}_field_${i}`;
      const tabType = getTabType(type, name);

      if (tabType) {
        const baseTab = createBaseTab(
          tabId,
          tabType,
          documentId,
          page,
          xPosition,
          yPosition,
          width,
          height,
          required,
        );

        baseTab.tooltip = field.name;

        switch (tabType) {
          case DocusignTabTypesEnum.CheckboxTabs: {
            const tabValue = getTabValue(
              file.originalname,
              field.name,
              field.value,
              interviewData,
            );
            const parsedTabValue =
              typeof tabValue === 'string'
                ? tabValue.toLowerCase() === 'yes' ||
                  tabValue.toLowerCase() === 'on'
                : tabValue;
            baseTab.selected = `${parsedTabValue}`;
            baseTab.locked = 'true';
            baseTab.shared = 'true';
            break;
          }
          case DocusignTabTypesEnum.TextTabs: {
            baseTab.value = getTabValue(
              file.originalname,
              field.name,
              field.value,
              interviewData,
            );
            baseTab.locked = 'true';
            baseTab.shared = 'true';
            break;
          }
          case DocusignTabTypesEnum.SignHereTabs:
          case DocusignTabTypesEnum.DateSignedTabs: {
            // Skip if the file is either the advisory agreement or the schwab coversheet
            if (
              contains(file.originalname, [
                AccountAdvisoryDocumentsEnum.AdvisoryAgreement,
                AccountAdvisoryDocumentsEnum.SchwabCoversheet,
              ])
            ) {
              break;
            }

            if (isContactFile(file.originalname)) {
              baseTab.tooltip = `${field.name}--PrimaryContact`;
              break;
            }

            const { account, signer } = getAccountByFileName(
              interviewData,
              file.originalname,
            );

            if (account.ownership === AccountOwnershipEnum.Individual) {
              // If tab belongs to an individual account, and it is a coapplicant tab, then skip it
              const isCoapplicantTab = contains(baseTab.tooltip, [
                'coapplicant',
                'Coapplicant',
              ]);
              const isAdvisorTab = contains(baseTab.tooltip, [
                'advisor',
                'Advisor',
              ]);

              if (isCoapplicantTab) {
                return acc;
              } else if (isAdvisorTab) {
                break;
              } else {
                // Add the signer to the tooltip
                baseTab.tooltip = `${field.name}--${signer}`;
              }
            }
            break;
          }
          default:
            break;
        }

        acc[tabType].push(baseTab);
      }

      return acc;
    },
    {
      textTabs: [] as Text[],
      dateTabs: [] as Date[],
      checkboxTabs: [] as Checkbox[],
      signHereTabs: [] as SignHere[],
      dateSignedTabs: [] as DateSigned[],
    },
  );
  return tabs;
}

/**
 * Returns the value of a nested property in an object, given its path.
 * @param obj - The object to search for the nested property.
 * @param path - The path of the nested property, separated by dots.
 * @returns The value of the nested property, or undefined if it doesn't exist.
 */
function getNestedProperty(obj: any, path: string) {
  if (!obj) {
    return;
  }

  const value = path.split('.').reduce((acc, part) => {
    if (acc && typeof acc === 'object' && part in acc) {
      return acc[part];
    } else {
      return undefined;
    }
  }, obj);

  return value;
}

/**
 * Retrieves the value of a tab in a PDF file based on the provided file name, tab name, and interview data.
 * @param fileName - The name of the PDF file.
 * @param tabName - The name of the tab in the PDF file.
 * @param interviewData - The interview data used to fill the PDF file.
 * @returns The value of the tab in the PDF file, or undefined if the tab is not found.
 */
function getTabValue(
  fileName: string,
  tabName: string,
  tabValue: string | number,
  interviewData?: InterviewDataWithCrmInfo,
) {
  // Try to match the patterns "account#" and "beneficiaries#" in the field name
  const accountMatch =
    tabName.match(/accounts(\d+)/) || tabName.match(/account(\d+)/);

  const beneficiaryMatch = tabName.match(/beneficiaries(\d+)/);

  // Parse indices, subtract 1 to convert from 1-based to 0-based indexing
  const accountIndex = accountMatch ? parseInt(accountMatch[1], 10) - 1 : null;
  const beneficiaryIndex = beneficiaryMatch
    ? parseInt(beneficiaryMatch[1], 10) - 1
    : null;

  let mappedField = tabName;

  // Replace the matched patterns with placeholders
  if (accountMatch) {
    mappedField = mappedField.replace(accountMatch[0], 'account#');
  }
  if (beneficiaryMatch) {
    mappedField = mappedField.replace(beneficiaryMatch[0], 'beneficiaries#');
  }

  // Lookup the generic mapping based on the modified tabName with placeholders
  const mappedTab = pdfMappers[mappedField];

  if (!mappedTab) {
    return tabValue;
  }

  if (typeof mappedTab.field === 'string') {
    let actualField = mappedTab.field;
    // Replace placeholders with actual indices
    if (accountIndex !== null) {
      actualField = actualField.replace(
        'account#',
        `accounts[${accountIndex}]`,
      );
    }
    if (beneficiaryIndex !== null) {
      actualField = actualField.replace(
        'beneficiaries#',
        `beneficiaries[${beneficiaryIndex}]`,
      );
    }

    return getNestedProperty(interviewData, actualField);
  } else if (Array.isArray(mappedTab.field)) {
    // Handle array of fields (concatenating them for example)
    return mappedTab.field
      .map((subField) => {
        let actualField = subField;
        if (accountIndex !== null) {
          actualField = actualField.replace(
            'account#',
            `accounts[${accountIndex}]`,
          );
        }
        if (beneficiaryIndex !== null) {
          actualField = actualField.replace(
            'beneficiaries#',
            `beneficiaries[${beneficiaryIndex}]`,
          );
        }
        return getNestedProperty(interviewData, actualField) || '';
      })
      .join(' '); // Joining with space
  } else if (mappedTab.callback) {
    try {
      return mappedTab.callback(
        fileName,
        interviewData,
        accountIndex,
        beneficiaryIndex,
      );
    } catch (error) {
      return '';
    }

    // Execute callback if present, passing accountIndex and beneficiaryIndex if they exist
    // return mappedTab.callback(interviewData, accountIndex, beneficiaryIndex);
  }

  // Static value handling
  if (mappedTab.staticValue) {
    return mappedTab.staticValue;
  }

  return undefined;
}

/**
 * Creates a base tab object with the given parameters.
 * @param tabId - The ID of the tab.
 * @param tabType - The type of the tab.
 * @param documentId - The ID of the document.
 * @param page - The page number of the tab.
 * @param xPosition - The x position of the tab.
 * @param yPosition - The y position of the tab.
 * @param width - The width of the tab.
 * @param height - The height of the tab.
 * @returns The created base tab object.
 */
function createBaseTab(
  tabId: string,
  tabType: string,
  documentId: string,
  page: number,
  xPosition: number,
  yPosition: number,
  width: string,
  height: string,
  required: boolean = false,
): any {
  return {
    tabLabel: `${tabType}_${tabId}`,
    tabId,
    documentId,
    pageNumber: page.toString(),
    xPosition: xPosition.toString(),
    yPosition: yPosition.toString(),
    width,
    height,
    font: 'helvetica',
    fontSize: 'size8',
    bold: 'false',
    required,
  };
}
