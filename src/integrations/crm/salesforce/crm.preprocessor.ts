import {
  Processor,
  WorkerHost,
  OnWorkerEvent,
} from '@nestjs/bullmq';
import { forwardRef, Inject, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { QueueLogService } from '../../../queue-log/queue-log.service';
import { QueueLogStatusEnum } from '../../../queue-log/enums/queue-log-status.enum';
import { ResourceOptionsEnum } from 'src/shared/types/queue-log/queue';
import { SaveJobsDto } from 'src/queue-log/dto/save-jobs.dto';
import { CRM_QUEUE } from '../constants/crm.constants';
import { AdvisorsCrmService } from 'src/advisors/services/advisors.crm.service';
import { CreateContactDto } from '../types/create-contact.dto';
import { ClientsV1Service } from 'src/clients/services/v1/clients-v1.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { UpdateInterviewDto } from 'src/interviews/dto/v1/update-interview.dto';
import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { InterviewsService } from 'src/interviews/interviews.service';
import { InterviewPageStatusEnum } from 'src/interview-templates/types/interview-page-status.enum';

@Processor(CRM_QUEUE.SALESFORCE.NAME)
export class SalesforceCRMProcessor extends WorkerHost {
  private readonly logger = new Logger(SalesforceCRMProcessor.name);

  constructor(
    private readonly queueLogService: QueueLogService,
    @Inject(forwardRef(() => AdvisorsCrmService))
    private readonly advisorsCrmService: AdvisorsCrmService,
    @Inject(forwardRef(() => ClientsV1Service))
    private readonly clientsService: ClientsV1Service,
    @Inject(forwardRef(() => OrganisationsService))
    private readonly organisationsService: OrganisationsService,
    @Inject(forwardRef(() => InterviewsService))
    private readonly interviewsService: InterviewsService,
  ) {
    super();
  }

  async process(job: Job<any, any, string>): Promise<any> {
    const { name } = job;

    switch (name) {
      case CRM_QUEUE.SALESFORCE.JOBS.UPSERT_TO_CRM:
        return this.upsertClientInCrm(job);
      case CRM_QUEUE.SALESFORCE.JOBS.UPDATE_PAGE_ANSWER:
        return this.updatePageAnswer(job);
      default:
        throw new Error(`Unknown job name: ${name}`);
    }
  }

  async upsertClientInCrm(
    job: Job<{ data: CreateContactDto; clientId: string; organisation: Organisation }>,
  ) {
    const { data, clientId } = job.data;
    const { primaryContact, primaryAdvisor } = data;
    const crmClientId = primaryContact?.crmClientId;

    if (crmClientId) {
        const updatedCrmContact = await this.advisorsCrmService.updateContact(data, primaryAdvisor._id);
        await this.clientsService.updateCrmClientIds(clientId, updatedCrmContact);
        return updatedCrmContact;
    }

    const crmContact = await this.advisorsCrmService.createCrmContact(data, primaryAdvisor._id);
    await this.clientsService.updateCrmClientIds(clientId, crmContact);

    return crmContact;
  }

  async updatePageAnswer(
    job: Job<{ interviewId: string; updateInterviewDto: UpdateInterviewDto }>,
  ) {
    const { interviewId, updateInterviewDto } = job.data;
    const interview = await this.interviewsService.findOne({
      _id: interviewId,
    });

    const client = <EnrichedClient>interview.client;

    const organisation = await this.organisationsService.findOne(
      client.organisationId.toString(),
    );

    const crm = await this.advisorsCrmService.getCrmInstance(
      client.primaryAdvisor.id.toString(),
    );

    await crm.updatePageInfo(interview, client, organisation, {
      pageName: updateInterviewDto.page.name,
      pageData: updateInterviewDto.page.data,
    });

    return interview;
  }

  @OnWorkerEvent('completed')
  async onCompleted(job: Job) {
    this.logger.log(`Job ${job.id} completed`);
    const { organisationId } = job.data;
    const payload: SaveJobsDto = {
      jobId: job.id,
      resource: ResourceOptionsEnum.CRM,
      queueName: CRM_QUEUE.SALESFORCE.NAME,
      status: QueueLogStatusEnum.COMPLETED,
      result: 'Client created successfully',
      organisationId,
    };
    await this.queueLogService.save(payload);
  }

  @OnWorkerEvent('failed')
  async onFailed(job: Job, error: Error) {
    this.logger.error(
      `Job ${job.id} failed with error: ${error.message}`,
      error.stack,
    );
    const { organisationId } = job.data;
    const payload: SaveJobsDto = {
      jobId: job.id,
      resource: ResourceOptionsEnum.CRM,
      queueName: CRM_QUEUE.SALESFORCE.NAME,
      status: QueueLogStatusEnum.FAILED,
      error: error.message,
      organisationId,
    };
    await this.queueLogService.save(payload);

    if (job.name?.toLowerCase() === CRM_QUEUE.SALESFORCE.JOBS.UPDATE_PAGE_ANSWER) {
      await this.interviewsService.updateInterviewPages(
        job.data.interviewId,
        job.data.updateInterviewDto,
        InterviewPageStatusEnum.FAILED,
      );
      return;
    }

    if (job.name?.toLowerCase() === CRM_QUEUE.SALESFORCE.JOBS.UPSERT_TO_CRM) {
      await this.clientsService.updateClientStatus(
        job.data.clientId,
        ClientStatusEnum.Failed,
      );
    }
  }
}