import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { SalesforceCRM } from 'src/integrations/crm/salesforce/crm.salesforce';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { AddressUpdateRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/company-request.dto';
import { Country } from 'src/shared/types/general/country.enum';

export class SalesforceCompanyMapper implements FormUpdateMapper {
  constructor(private salesforceCRM: SalesforceCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    update: AddressUpdateRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    // Standard contact - update company and other address fields
    await this.salesforceCRM.contactService.updateContact(contact.crmClientId, {
      Onbord_Company__c: update.name,
      Onbord_Company_Address__c: `${update.address.line1}, ${update.address.city}, ${update.address.state}, ${update.address.zip}, ${Country.US}`
    });
  }
} 