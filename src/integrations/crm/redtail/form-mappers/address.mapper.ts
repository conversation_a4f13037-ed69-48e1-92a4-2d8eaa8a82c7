import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { AddressMapperRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/address-request.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { AddressUpdateRequest } from 'src/integrations/crm/redtail/dto/requests/update-address.dto';
import { RedtailAddressTypeEnum } from 'src/integrations/crm/redtail/types/enums';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { Country } from 'src/shared/types/general/country.enum';
export class RedtailAddressMapper implements FormUpdateMapper {
  constructor(private redtailCrm: RedtailCRM) {}

  /**
   * Maps page data to Redtail CRM address fields for a given client.
   * @param clientCrmId The ID of the client in Redtail CRM.
   * @param organisation The organisation associated with the client.
   * @param pageData The page data to map to Redtail CRM address fields.
   * @returns A Promise that resolves when the mapping is complete.
   */
  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: AddressMapperRequestDto,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    const addressUpdates: AddressUpdateRequest[] = [
      {
        addressType: RedtailAddressTypeEnum.HOME,
        address: {
          isPrimary: true,
          streetAddress: pageData.legalAddress.line1,
          city: pageData.legalAddress.city,
          state: pageData.legalAddress.state,
          zip: pageData.legalAddress.zip,
          country: Country.US,
        },
      },
    ];

    if (pageData.mailingAddress) {
      addressUpdates.push({
        addressType: RedtailAddressTypeEnum.MAILING,
        address: {
          isPrimary: false,
          streetAddress: pageData.mailingAddress.line1,
          city: pageData.mailingAddress.city,
          state: pageData.mailingAddress.state,
          zip: pageData.mailingAddress.zip,
          country: Country.US,
        },
      });
    }

    await this.redtailCrm.redtailAddressService.updateContactAddresses(
      contact.crmClientId,
      addressUpdates,
    );
  }
}
