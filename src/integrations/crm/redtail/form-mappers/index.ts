import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { RedtailNameMapper } from './name.mapper';
import { RedtailCRM } from '../crm.redtail';
import { RedtailAddressMapper } from 'src/integrations/crm/redtail/form-mappers/address.mapper';
import { RedtailSSNMapper } from 'src/integrations/crm/redtail/form-mappers/ssn.mapper';
import { RedtailDOBMapper } from 'src/integrations/crm/redtail/form-mappers/dob.mapper';
import { RedtailPhoneMapper } from 'src/integrations/crm/redtail/form-mappers/phone.mapper';
import { RedtailConflictOfInterestMapper } from 'src/integrations/crm/redtail/form-mappers/conflict-of-interest.mapper';
import { RedtailUSCitizenMapper } from 'src/integrations/crm/redtail/form-mappers/us-citizen.mapper';
import { RedtailEmploymentMapper } from 'src/integrations/crm/redtail/form-mappers/employment.mapper';
import { RedtailPrimaryBeneficiariesMapper } from 'src/integrations/crm/redtail/form-mappers/primary-beneficiaries.mapper';
import { RedtailVIPMapper } from 'src/integrations/crm/redtail/form-mappers/vip.mapper';
import { PageUpdateMapper } from 'src/integrations/crm/crm.interface';
import { RedtailCompanyMapper } from 'src/integrations/crm/redtail/form-mappers/company.mapper';
import { RedtailJobDescriptionMapper } from 'src/integrations/crm/redtail/form-mappers/job-description.mapper';
import { RedtailContingentBeneficiariesMapper } from 'src/integrations/crm/redtail/form-mappers/contingent-beneficiaries.mapper';
import { RedtailCustomQuestionsMapper } from 'src/integrations/crm/redtail/form-mappers/custom-questions.mapper';

export function redtailMappers(
  this: InstanceType<typeof RedtailCRM>,
): PageUpdateMapper {
  return {
    [PagesEnum.NAME]: new RedtailNameMapper(this),
    [PagesEnum.ADDRESS]: new RedtailAddressMapper(this),
    [PagesEnum.SSN]: new RedtailSSNMapper(this),
    [PagesEnum.DOB]: new RedtailDOBMapper(this),
    [PagesEnum.PHONE]: new RedtailPhoneMapper(this),
    [PagesEnum.CONFLICTS_OF_INTEREST]: new RedtailConflictOfInterestMapper(
      this,
    ),
    [PagesEnum.US_CITIZEN]: new RedtailUSCitizenMapper(this),
    [PagesEnum.EMPLOYMENT]: new RedtailEmploymentMapper(this),
    [PagesEnum.PRIMARY_BENEFICIARIES]: new RedtailPrimaryBeneficiariesMapper(
      this,
    ),
    [PagesEnum.CONTINGENT_BENEFICIARIES]:
      new RedtailContingentBeneficiariesMapper(this),
    [PagesEnum.COMPANY]: new RedtailCompanyMapper(this),
    [PagesEnum.VIP]: new RedtailVIPMapper(this),
    [PagesEnum.JOB]: new RedtailJobDescriptionMapper(this),
    [PagesEnum.CUSTOM_QUESTIONS]: new RedtailCustomQuestionsMapper(this),
  };
}
