import { CrmAdapter } from 'src/integrations/crm/crm.adapter.interface';
import { Record as PractifiRecord } from 'jsforce';
import { CrmContact } from '../types/contacts/crm-contact.type';
import { GenericPhone, PhoneTypeEnum } from '../types/generic/phone.type';
import { GenericEmail, EmailTypeEnum } from '../types/generic/email.type';
import { GenericAddress, AddressTypeEnum } from '../types/generic/address.type';
import { GenericCrmAccount } from '../types/accounts/crm-account.type';
import { USCitizen } from './types/enums';

export class PractifiAdapter implements CrmAdapter {
  public mapContact(contact: PractifiRecord): CrmContact {
    if (!contact) throw new Error('Contact is undefined');
    
    return {
      id: contact.Id,
      firstName: contact.FirstName || '',
      middleName: contact.practifi__Middle_Name__c || '',
      lastName: contact.LastName || '',
      suffix: contact.practifi__Suffix__c || '',
      taxId: contact.practifi__SSN__c || '',
      dob: contact.Birthdate || '',
      citizenship: contact.practifi__Citizenship_Status__c === USCitizen.US_CITIZEN ? USCitizen.US : contact.practifi__Country_Of_Citizenship__c || '',
      residence: contact.practifi__Country_Of_Residence__c || '',
      accounts: [],
      additionalInfo: {
        employmentStatus: contact.practifi__Employment_Status__c || '',
        phones: this.extractPhones(contact, true),
        addresses: this.extractAddresses(contact, true),
        emails: this.extractEmails(contact, true),
        jobDescription: contact.Title || '',
        companyAssociation: contact.Description?.split('\n').find(substr => substr.includes('Company name:')) || '',
        industryAffiliation: contact.Description?.split('\n').find(substr => substr.includes('employed by a company')) || '',
      },
    };    
  }

  public mapPractifiAccount(account: PractifiRecord): CrmContact {
    if (!account) throw new Error('Account is undefined');

    return {
      id: account.Id,
      firstName: account.FirstName || '',
      middleName: account.practifi__Middle_Name__pc || '',
      lastName: account.LastName || '',
      suffix: account.practifi__Suffix__pc || '',
      taxId: account.practifi__SSN__pc || '',
      dob: account.PersonBirthdate || '',
      citizenship: account.practifi__Citizenship_Status__pc === USCitizen.US_CITIZEN ? USCitizen.US : account.practifi__Country_Of_Citizenship__pc || '',
      residence: account.practifi__Country_Of_Residence__pc || '',
      accounts: [],
      additionalInfo: {
        employmentStatus: account.practifi__Employment_Status__pc || '',
        phones: this.extractPhones(account, false),
        addresses: this.extractAddresses(account, false),
        emails: this.extractEmails(account, false),
        jobDescription: account.practifi__Occupation__pc || '',
        companyAssociation: account.practifi__Employer__pc || '',
        industryAffiliation: account.Description || '',
      },
    };
  }

  public mapAccount(account: GenericCrmAccount): GenericCrmAccount {
    throw new Error('Method not implemented.');
  }

  private extractPhones(contact: PractifiRecord, isContact: boolean): GenericPhone[] {
    const phones: GenericPhone[] = [];

    if (isContact) {
      if (contact.practifi__Work_Phone__c) {
        phones.push(this.mapPhone({ type: 'work', number: contact.practifi__Work_Phone__c }));
      }
      if (contact.MobilePhone) {
        phones.push(this.mapPhone({ type: 'mobile', number: contact.MobilePhone, isPrimary: true }));
      }
      if (contact.HomePhone) {
        phones.push(this.mapPhone({ type: 'home', number: contact.HomePhone }));
      }
      if (contact.OtherPhone) {
        phones.push(this.mapPhone({ type: 'other', number: contact.OtherPhone }));
      }
    }
    if (!isContact) {
      if (contact.practifi__Work_Phone__pc) {
        phones.push(this.mapPhone({ type: 'work', number: contact.practifi__Work_Phone__pc }));
      }
      if (contact.PersonMobilePhone) {
        phones.push(this.mapPhone({ type: 'mobile', number: contact.PersonMobilePhone, isPrimary: true }));
      }
      if (contact.PersonHomePhone) {
        phones.push(this.mapPhone({ type: 'home', number: contact.PersonHomePhone }));
      }
      if (contact.PersonOtherPhone) {
        phones.push(this.mapPhone({ type: 'other', number: contact.PersonOtherPhone }));
      }
    }

    return phones;
  }

  public mapPhone(phone: { type: string; number: string; ext?: string; isPrimary?: boolean }): GenericPhone {
    return {
      number: phone.number,
      type: this.mapPhoneType(phone.type),
      ext: phone.ext,
      isPrimary: phone.isPrimary,
    };
  }

  private mapPhoneType(type: string): PhoneTypeEnum {
    switch (type.toLowerCase()) {
      case 'work':
        return PhoneTypeEnum.WORK;
      case 'mobile':
        return PhoneTypeEnum.MOBILE;
      case 'home':
        return PhoneTypeEnum.HOME;
      case 'other':
        return PhoneTypeEnum.OTHER;
      default:
        return PhoneTypeEnum.OTHER;
    }
  }

  private extractEmails(contact: PractifiRecord, isContact: boolean): GenericEmail[] {
    const emails: GenericEmail[] = [];

    if (isContact) {
      if (contact.Email) {
        emails.push(this.mapEmail({ address: contact.Email, isPrimary: true }));
      }
    }
    if (!isContact) {
      if (contact.PersonEmail) {
        emails.push(this.mapEmail({ address: contact.PersonEmail, isPrimary: true }));
      }
    }

    return emails;
  }

  public mapEmail(email: { address: string; type?: string; isPrimary?: boolean }): GenericEmail {
    return {
      address: email.address,
      type: EmailTypeEnum.HOME,
      isPrimary: email.isPrimary || false,
    };
  }

  private extractAddresses(contact: PractifiRecord, isContact: boolean): GenericAddress[] {
    const addresses: GenericAddress[] = [];

    if (isContact) {
      if (contact.MailingStreet || contact.MailingCity || contact.MailingState || contact.MailingPostalCode || contact.MailingCountry) {
        addresses.push(this.mapAddress({
          street: contact.MailingStreet || '',
          city: contact.MailingCity || '',
          state: contact.MailingState || '',
          zipCode: contact.MailingPostalCode || '',
          country: contact.MailingCountry || '',
        }));
      }
    }
    if (!isContact) {
      if (contact.PersonMailingStreet || contact.PersonMailingCity || contact.PersonMailingState || contact.PersonMailingPostalCode || contact.PersonMailingCountry) {
        addresses.push(this.mapAddress({
          street: contact.PersonMailingStreet,
          city: contact.PersonMailingCity,
          state: contact.PersonMailingState,
          zipCode: contact.PersonMailingPostalCode,
          country: contact.PersonMailingCountry,
        }));
      }
    }

    return addresses;
  }

  public mapAddress(address: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
    customTypeTitle?: string;
  }): GenericAddress {
    return {
      street: address.street || '',
      city: address.city || '',
      zip: address.zipCode || '',
      state: address.state || '',
      country: address.country || '',
      type: address.customTypeTitle ? AddressTypeEnum.WORK : AddressTypeEnum.MAILING,
      ...(address.customTypeTitle && { title: address.customTypeTitle}),
    };
  }

  toGenericEmail(contact: any, isContact: boolean): GenericEmail {
    if (isContact) {
      if (!contact.Email) {
        return null;
      }
      return {
        address: contact.Email,
        type: EmailTypeEnum.HOME,
        isPrimary: true
      };
    }
    if (!isContact) {
      if (!contact.PersonEmail) {
        return null;
      }
      return {
        address: contact.PersonEmail,
        type: EmailTypeEnum.HOME,
        isPrimary: true
      };
    }
  }

  toGenericPhones(contact: any, isContact: boolean): GenericPhone[] {
    const phones: GenericPhone[] = [];

    if (isContact) {
      if (contact.practifi__Work_Phone__c) {
        phones.push({
          number: contact.practifi__Work_Phone__c,
          type: PhoneTypeEnum.HOME,
          isPrimary: false,
          ext: ''
        });
      }
  
      if (contact.MobilePhone) {
        phones.push({
          number: contact.MobilePhone,
          type: PhoneTypeEnum.MOBILE,
          isPrimary: true,
          ext: ''
        });
      }
  
      if (contact.OtherPhone) {
        phones.push({
          number: contact.OtherPhone,
          type: PhoneTypeEnum.OTHER,
          isPrimary: false,
          ext: ''
        });
      }
    }

    if (!isContact) {
      if (contact.practifi__Work_Phone__pc) {
        phones.push({
          number: contact.practifi__Work_Phone__pc,
          type: PhoneTypeEnum.HOME,
          isPrimary: false,
          ext: ''
        });
      }
  
      if (contact.PersonMobilePhone) {
        phones.push({
          number: contact.PersonMobilePhone,
          type: PhoneTypeEnum.MOBILE,
          isPrimary: true,
          ext: ''
        });
      }
  
      if (contact.PersonOtherPhone) {
        phones.push({
          number: contact.PersonOtherPhone,
          type: PhoneTypeEnum.OTHER,
          isPrimary: false,
          ext: ''
        });
      }
    }

    return phones;
  }

  toMainMobilePhone(contact: any, isContact: boolean): string {
    if (isContact) {
      return contact.MobilePhone || contact.practifi__Work_Phone__c || contact.OtherPhone || null;
    }
    if (!isContact) {
      return contact.PersonMobilePhone || contact.practifi__Work_Phone__pc || contact.PersonOtherPhone || null;
    }
  }
} 