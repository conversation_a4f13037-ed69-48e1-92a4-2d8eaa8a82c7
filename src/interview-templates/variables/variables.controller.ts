import { <PERSON>, Get, Param } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { VariablesService } from './variables.service';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { VariableDefinition } from './types/variable-definition.interface';

@ApiTags('Interview Templates - Variables')
@Controller('interview-templates/available-variables')
export class VariablesController {
  constructor(private readonly service: VariablesService) {}

  @Get(':pageName')
  @ApiOperation({
    summary: 'Get available variables for a specific interview page',
    description: 'Returns all available template variables with their types and descriptions that can be used in the context of the specified page',
  })
  @ApiParam({
    name: 'pageName',
    description: 'The name of the interview page',
    enum: PagesEnum,
  })
  @ApiResponse({
    status: 200,
    description: 'Available variables for the specified page with type information',
    schema: {
      type: 'object',
      additionalProperties: {
        type: 'object',
        properties: {
          description: {
            type: 'string',
            description: 'Human-readable description of the variable',
          },
          type: {
            type: 'string',
            enum: ['string', 'number', 'boolean', 'date', 'array', 'percentage', 'phone', 'email', 'address', 'currency'],
            description: 'Data type of the variable',
          },
          example: {
            description: 'Example value for documentation',
          },
          isConditional: {
            type: 'boolean',
            description: 'Whether this variable is always available or conditional',
          },
        },
        required: ['description', 'type'],
      },
      example: {
        firstName: {
          description: 'First name of the client',
          type: 'string',
          example: 'John',
        },
        lastName: {
          description: 'Last name of the client',
          type: 'string',
          example: 'Smith',
        },
        age: {
          description: 'Calculated age based on date of birth',
          type: 'number',
          example: 38,
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Page not found or no variables available',
  })
  getVariables(@Param('pageName') pageName: PagesEnum): Record<string, VariableDefinition> {
    return this.service.findByPage(pageName);
  }
}