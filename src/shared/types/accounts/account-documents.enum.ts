export enum AccountAdvisoryDocumentsEnum {
  AdvisoryAgreement = 'AdvisoryAgreement',
  SchwabIraDistributionForm = 'SchwabIraDistributionForm',
  SchwabIraAccountApplication = 'SchwabIraAccountApplication',
  SchwabCoversheet = 'SchwabCoversheet',
  SchwabMoneyLinkElectronicTransferForm = 'SchwabMoneyLinkElectronicTransferForm',
  SchwabTransferAccountForm = 'SchwabTransferAccountForm',
  SchwabOnePersonalAccountApplication = 'SchwabOnePersonalAccountApplication',
}

export enum AccountClientDocumentsEnum {
  CancelledCheck = 'CancelledCheck',
  FinraDocument = 'FinraDocument',
  BrokerageStatement = 'BrokerageStatement',
}
