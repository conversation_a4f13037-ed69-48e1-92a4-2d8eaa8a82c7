import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { CsvParser } from 'nest-csv-parser';
import * as streamifier from 'streamifier';
import { CsvRow } from './csv.interface';
import { TransitionTypeEnum } from 'src/transitions/types/transition-type.enum';

@Injectable()
export class CSVService {
  constructor(private readonly csvParser: CsvParser) {}

  async parseCsv<T>(
    fileBuffer: Buffer,
    rowClass: CsvRow,
    options?: any,
  ): Promise<T[]> {
    try {
      const fileStream = streamifier.createReadStream(fileBuffer);
      const records = await this.csvParser.parse(
        fileStream,
        rowClass,
        undefined,
        undefined,
        options || { separator: ',' },
      );

      const csvRows = [];
      const errors = [];

      // Iterate over each parsed object and attempt to create a validated CsvRow instance
      records.list.forEach((csvObject, index) => {
        try {
          const row = rowClass.fromCsvObject(csvObject); // Validation occurs inside fromCsvObject
          csvRows.push(row);
        } catch (error) {
          // Collect errors with row index for better error reporting
          errors.push(`Row ${index + 1}: ${error.message}`);
        }
      });

      if (errors.length > 0 && csvRows.length === 0) {
        throw new Error(
          `Invalid data found or empty file: ${errors.join(', ')}`,
        );
      }

      return csvRows;
    } catch (error) {
      const errorMessage = error?.errors[0]?.message || error.message;
      throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
    }
  }

  createClientCsvTemplate() {
    const headers = [
      'First Name',
      'Last Name',
      'Mobile',
      'Email',
      'Coapplicant First Name',
      'Coapplicant Last Name',
      'Coapplicant Mobile',
      'Coapplicant Email',
    ];
    const rows = [];

    // Add header row
    const headerRow = headers.join(',');
    rows.push(headerRow);

    // Add an empty row
    const emptyRow = headers.map(() => '').join(','); // Create an empty row with the same number of columns as the headers
    rows.push(emptyRow);

    return rows.join('\n');
  }

  createTransitionCsvTemplate(transitionType: TransitionTypeEnum): string {
    // Initialize headers array with common headers
    let headers = ['First Name', 'Last Name', 'Mobile'];

    // Add 'Email' header for Protocol transitions
    if (transitionType === TransitionTypeEnum.Protocol) {
      headers.push('Email');
    }

    // Continue adding headers
    headers = headers.concat([
      'Coapplicant First Name',
      'Coapplicant Last Name',
      'Coapplicant Mobile',
    ]);

    // Add 'Coapplicant Email' header for Protocol transitions
    if (transitionType === TransitionTypeEnum.Protocol) {
      headers.push('Coapplicant Email');
    }

    // Add remaining headers
    headers = headers.concat([
      'Primary Advisor Email',
      'Primary CSA Email',
      'Asset Value',
    ]);

    const rows = [headers.join(',')]; // Header row

    // Add an empty row for the template
    const emptyRow = headers.map(() => '').join(',');
    rows.push(emptyRow);

    return rows.join('\n');
  }

  async isCsvEmpty<T>(fileBuffer: Buffer, rowClass: CsvRow): Promise<boolean> {
    const fileStream = streamifier.createReadStream(fileBuffer);

    try {
        const { list } = await this.csvParser.parse(
          fileStream, 
          rowClass, 
          undefined, 
          undefined,
          { hasHeader: true }
          );

        for (const row of list) {
          const rowData = Object.values(row);
          if (rowData.some(value => value.toString().trim().replace(/,/g, '') !== '')) {
            return false;
          }
        }

      return true; 
    } catch (error) {
        console.error('Error parsing CSV:', error, '#####%%%');
        return true;
    }
  }
}
