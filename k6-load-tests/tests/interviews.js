import http from 'k6/http';
import { check, sleep } from 'k6';
import { Trend } from 'k6/metrics';

import { ERROR_RATE, VUs } from "../src/config/constants/k6.js";
import { BASE_URL } from "../src/config/constants/shared.js";
import { getAuthToken } from '../src/utils/auth.js';
import { calculateThresholds, getDynamicSleep } from '../src/utils/k6-utilities.js';
import { generateClientData, waitForClientProcessing } from '../src/utils/clients.js';
import {
  getInterviewByClientId,
  getInterviewById,
  updatePages,
  processUpdatePage,
  ensureAllPagesSynced,
  finishInterview,
  ensureBeneficiaryPagesSynced
} from '../src/utils/interviews.js';

const iraFlowTrend = new Trend('ira_flow_duration');
const interviewCompletionTrend = new Trend('interview_completion_duration');

const _VUs = VUs['interview'];

export const options = {
  scenarios: {
    ira_requirements: {
      executor: 'shared-iterations',
      vus: _VUs,
      iterations: _VUs,
      maxDuration: '10m'
    }
  },
  thresholds: calculateThresholds(_VUs),
};

// Mock data for IRA accounts - using lowercase to match backend expectations
const iraAccountData = {
  type: 'ira',
  label: 'Test IRA Account',
  features: ['MoneyLink', 'Acat', 'IraDistribution'],
  advisoryRate: 1,
  masterAccountNumber: '1111-2222',
  ownership: 'Individual'
};

const rothIraAccountData = {
  type: 'roth_ira',
  label: 'Test Roth IRA Account',
  features: ['MoneyLink', 'Acat', 'IraDistribution'],
  advisoryRate: 1,
  masterAccountNumber: '1111-2222',
  ownership: 'Individual'
};

// Test scenarios
const scenarios = {
  iraRequirements: async (data) => {
    const startTime = Date.now();

    // Determine which IRA type to test based on VU number
    const accountToTest = _VUs % 2 === 0 ? iraAccountData : rothIraAccountData;
    console.log(`Testing ${accountToTest.type} account type for VU ${_VUs}`);

    // Create client with selected IRA account type
    const clientData = generateClientData(data);
    clientData.primaryContact.accounts = [accountToTest];

    const createResponse = http.post(
      `${BASE_URL}/organisations/${data.organisationId}/clients`,
      JSON.stringify(clientData),
      { headers: data.headers, tags: { type: 'createClientWithIRA' } }
    );

    const clientCreationSuccess = check(createResponse, {
      'client created successfully': (r) => r.status === 201,
      'client has valid response structure': (r) => {
        try {
          const body = JSON.parse(r.body);
          return body._id && body.primaryContact && body.primaryContact.accounts;
        } catch (e) {
          console.log('Invalid client response structure:', e);
          return false;
        }
      }
    });

    if (!clientCreationSuccess) {
      console.log(`Failed to create client: ${createResponse.body}`);
      ERROR_RATE.add(1);
      return;
    }

    const clientId = JSON.parse(createResponse.body)._id;

    // Ensure client is fully processed before proceeding
    console.log(`Waiting for client ${clientId} to be processed...`);
    const clientProcessed = waitForClientProcessing(clientId, data.headers, data.organisationId);
    if (!clientProcessed) {
      console.log(`Client ${clientId} processing timed out`);
      ERROR_RATE.add(1);
      return;
    }

    // Get interview for the client and validate association
    const interview = getInterviewByClientId(clientId, data.headers);

    const interviewValidation = check(interview, {
      'interview exists': (i) => i !== null && i !== undefined,
      'interview has correct client association': (i) => i.client && i.client._id === clientId,
      'interview has template': (i) => i.template && Array.isArray(i.template.pages),
      'interview is not already complete': (i) => i.isComplete === false
    });

    if (!interviewValidation) {
      console.log('Invalid interview state or missing client association');
      console.log('Interview data:', JSON.stringify(interview, null, 2));
      ERROR_RATE.add(1);
      return;
    }

    sleep(getDynamicSleep(2, _VUs)); // Give time for interview setup

    // Verify IRA-specific pages exist
    const hasRequiredPages = check(interview, {
      'has primary beneficiary page': (i) => i.template.pages.some(p => p.name === 'primary_beneficiaries'),
      'has contingent beneficiary page': (i) => i.template.pages.some(p => p.name === 'contingent_beneficiaries'),
    });

    if (!hasRequiredPages) {
      console.log('Missing required IRA pages');
      console.log('Available pages:', interview.template.pages.map(p => p.name));
      ERROR_RATE.add(1);
      return;
    }

    // Extract the specific beneficiary pages to get their IDs
    const primaryBeneficiaryPage = interview.template.pages.find(p => p.name === 'primary_beneficiaries');
    const contingentBeneficiaryPage = interview.template.pages.find(p => p.name === 'contingent_beneficiaries');

    const updatePayload = [
      {
        page: {
          ...primaryBeneficiaryPage,
          data: {
            ...primaryBeneficiaryPage.data,
            instance: {
              name: accountToTest.type,
              label: accountToTest.label
            },
            beneficiaries: [{
              firstName: 'John',
              lastName: 'Doe',
              dob: '01/01/1980',
              allocationAmount: '100'
            }]
          }
        }
      },
      {
        page: {
          ...contingentBeneficiaryPage,
          data: {
            ...contingentBeneficiaryPage.data,
            instance: {
              name: accountToTest.type,
              label: accountToTest.label
            },
            beneficiaries: [{
              firstName: 'Jane',
              lastName: 'Doe',
              dob: '01/01/1990',
              allocationAmount: '100'
            }]
          }
        }
      }
    ];

    // Update the beneficiary pages while preserving existing page data
    console.log('Updating beneficiary pages with payload:', JSON.stringify(updatePayload, null, 2));
    const updateSuccess = processUpdatePage(interview._id, updatePayload, data.headers);

    if (!updateSuccess) {
      console.log('Failed to update beneficiary pages');
      ERROR_RATE.add(1);
      return;
    }

    // Ensure beneficiary pages are synced
    const synced = ensureBeneficiaryPagesSynced(interview._id, updatePayload, data.headers);
    if (!synced) {
      ERROR_RATE.add(1);
      return;
    }

    iraFlowTrend.add(Date.now() - startTime);
  },

  interviewCompletion: async (data) => {
    const startTime = Date.now();

    // Create client and get interview
    const clientData = generateClientData(data);
    const createResponse = http.post(
      `${BASE_URL}/organisations/${data.organisationId}/clients`,
      JSON.stringify(clientData),
      { headers: data.headers }
    );

    const clientId = JSON.parse(createResponse.body)._id;
    await waitForClientProcessing(clientId, data.headers, data.organisationId);

    const interview = getInterviewByClientId(clientId, data.headers);

    // Complete all required pages
    const pagesSuccess = updatePages(interview, clientId, data.headers);
    if (!pagesSuccess) {
      console.log('Failed to update pages');
      ERROR_RATE.add(1);
      return;
    }

    sleep(getDynamicSleep(2, _VUs));

    // Submit interview
    const completionSuccess = finishInterview(interview._id, data.headers);
    if (!completionSuccess) {
      console.log('Failed to complete interview');
      ERROR_RATE.add(1);
      return;
    }

    // Verify final state
    const finalInterview = getInterviewById(interview._id, data.headers);
    console.log('Final interview:', JSON.stringify(finalInterview, null, 2));
    const verifyCompletion = check(finalInterview, {
      'interview is complete': (i) => i.isComplete === true
    });

    if (!verifyCompletion) {
      ERROR_RATE.add(1);
    }

    interviewCompletionTrend.add(Date.now() - startTime);
  }
};

export function setup() {
  const body = getAuthToken();
  if (!body) {
    throw new Error('Failed to get auth token');
  }

  return {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${body.token}`
    },
    advisorId: body.advisorId,
    organisationId: body.organisationId
  };
}

export default function(data) {
  const scenarioName = __ENV.SCENARIO;

  if (!scenarios[scenarioName]) {
    console.error(`Unknown scenario: ${scenarioName}`);
    ERROR_RATE.add(1);
    return;
  }

  scenarios[scenarioName](data);

  // Add dynamic sleep between iterations
  sleep(getDynamicSleep(3, _VUs));
}