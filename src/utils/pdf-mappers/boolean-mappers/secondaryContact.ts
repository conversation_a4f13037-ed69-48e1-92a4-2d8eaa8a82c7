import { isEmpty } from 'lodash';
import { RedtailCitizenshipEnum } from 'src/integrations/crm/redtail/types/enums';
import { GenericCrmBeneficiaryEnum } from 'src/integrations/crm/types/accounts/crm-account.type';
import { GenericEmploymentStatusEnum } from 'src/integrations/crm/types/generic/employment.status.enum';
import { JobDescription } from 'src/integrations/crm/types/job-description.enum';
import { InterviewDataWithCrmInfo } from 'src/interviews/types/interview-data.type';

export const secondaryContactBooleanMappers = {
  'secondaryContact.isEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.secondaryContact?.additionalInfo?.employmentStatus ===
        GenericEmploymentStatusEnum.EMPLOYED
      );
    },
  },
  'secondaryContact.isBusinessOwner': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return (
        interviewData.secondaryContact?.additionalInfo.jobDescription ===
        JobDescription.BUSINESS_OWNER
      );
    },
  },
  'secondaryContact.isBusinessOwnerSelfEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const jobDescription =
        interviewData.secondaryContact?.additionalInfo.jobDescription;
      return (
        jobDescription === JobDescription.BUSINESS_OWNER ||
        jobDescription === JobDescription.SELF_EMPLOYED
      );
    },
  },
  'secondaryContact.isClerical': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.CLERICAL || interviewData.secondaryContact?.additionalInfo.jobDescription === JobDescription.ADMINISTRATIVE_SERVICES,
  },
  'secondaryContact.isConsultant': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.CONSULTANT,
  },
  'secondaryContact.isEducator': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.EDUCATOR,
  },
  'secondaryContact.isExecutive': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.EXECUTIVE,
  },
  'secondaryContact.isFinancialServices': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.FINANCIAL_SERVICES,
  },
  'secondaryContact.isForeignGovernmentEmployee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.FOREIGN_GOVERNMENT_EMPLOYEE,
  },
  'secondaryContact.isHomemaker': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.HOMEMAKER,
  },
  'secondaryContact.isInformationTechnologyProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.INFORMATION_TECHNOLOGY_PROFESSIONAL || interviewData.secondaryContact?.additionalInfo.jobDescription === JobDescription.IT_PROFESSIONAL,
  },
  'secondaryContact.isLegalProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.LEGAL_PROFESSIONAL,
  },
  'secondaryContact.isMedicalProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.MEDICAL_PROFESSIONAL,
  },
  'secondaryContact.isMilitary': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.MILITARY,
  },
  'secondaryContact.isNotEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo?.employmentStatus ===
      GenericEmploymentStatusEnum.NOTEMPLOYED,
  },
  'secondaryContact.isOtherProfessional': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.OTHER_PROFESSIONAL,
  },
  'secondaryContact.isRetired': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo?.employmentStatus ===
      GenericEmploymentStatusEnum.RETIRED,
  },
  'secondaryContact.isSalesMarketing': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.SALES_MARKETING || interviewData.secondaryContact?.additionalInfo.jobDescription === JobDescription.SALES_MARKETING_2,
  },
  'secondaryContact.isSelfEmployed': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const jobDescription =
        interviewData.secondaryContact?.additionalInfo.employmentStatus;
      return jobDescription === JobDescription.SELF_EMPLOYED;
    },
  },
  'secondaryContact.isStudent': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.STUDENT,
  },
  'secondaryContact.isTradeServiceCareer': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact?.additionalInfo.jobDescription ===
      JobDescription.TRADE_SERVICE_CAREER,
  },
  'secondaryContact.isUSGovernmentEmployee': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      const jobDescription =
        interviewData.secondaryContact?.additionalInfo.jobDescription;
      return jobDescription === JobDescription.US_GOVERNMENT_EMPLOYEE;
    },
  },
  'secondaryContact.isUSCitizen': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) =>
      interviewData.secondaryContact.citizenship === RedtailCitizenshipEnum.US,
  },
  'secondaryContact.isUSResident': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      if (!isEmpty(interviewData.secondaryContact)) {
        return true;
      }
    },
  },
  'secondaryContact.hasPublicCompanyAssociation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return !isEmpty(
        interviewData.secondaryContact.additionalInfo.companyAssociation,
      );
    },
  },
  'secondaryContact.hasIndustryAffiliation': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
    ) => {
      return !isEmpty(
        interviewData.secondaryContact.additionalInfo.industryAffiliation,
      );
    },
  },
  'secondaryContact.account#.beneficiaries#.isPrimary': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.secondaryContact.accounts[accountIndex];
      const beneficiary = account.beneficiaries[beneficiaryIndex];
      return beneficiary.type === GenericCrmBeneficiaryEnum.Primary;
    },
  },
  'secondaryContact.account#.beneficiaries#.isContingent': {
    callback: (
      fileName: string,
      interviewData: InterviewDataWithCrmInfo,
      accountIndex: number,
      beneficiaryIndex: number,
    ) => {
      const account = interviewData.secondaryContact.accounts[accountIndex];
      const beneficiary = account.beneficiaries[beneficiaryIndex];
      return beneficiary.type === GenericCrmBeneficiaryEnum.Contingent;
    },
  },
};
