import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { AuditsService } from '../audits.service';
import { CreateAuditDto } from '../dto/create-audit.dto';
import { Audit } from '../schemas/audit.schema';
import { AuditSource } from 'src/audits/types/audit';
import mongoose from 'mongoose';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';

describe('AuditsService', () => {
  let service: AuditsService;
  let model: any;
  let save: jest.Mock;
  let find: jest.Mock;
  let findById: jest.Mock;

  beforeEach(async () => {
    save = jest.fn().mockResolvedValue({});
    find = jest.fn().mockResolvedValue([]);
    findById = jest.fn().mockResolvedValue({});

    function MockModel(dto: CreateAuditDto) {
      this.data = dto;
      this.save = save;
    }

    MockModel.find = find;
    MockModel.findById = findById;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuditsService,
        {
          provide: WINSTON_MODULE_PROVIDER,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
            warn: jest.fn(),
            debug: jest.fn(),
            verbose: jest.fn(),
            info: jest.fn(),
          },
        },
        {
          provide: getModelToken(Audit.name),
          useValue: MockModel,
        },
      ],
    }).compile();

    service = module.get<AuditsService>(AuditsService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    const dto: CreateAuditDto = {
      message: 'Test message',
      context: AuditSource.BACKEND,
      level: 'error',
      organisationId: new mongoose.Types.ObjectId(),
    };
    it('should save an audit', async () => {
      await service.create(dto);
      expect(save).toHaveBeenCalled();
    });

    it('should throw an error if saving fails', async () => {
      save.mockRejectedValue(new Error('Database error'));
      await expect(service.create(dto)).rejects.toThrow('Database error');
    });
  });

  describe('findAll', () => {
    it('should retrieve all audits', async () => {
      await service.findAll();
      expect(find).toHaveBeenCalled();
    });

    it('should handle errors during retrieval of all audits', async () => {
      find.mockRejectedValue(new Error('Database error'));
      await expect(service.findAll()).rejects.toThrow('Database error');
    });
  });

  describe('findOne', () => {
    const id = 'testId';

    it('should retrieve an audit by its ID', async () => {
      await service.findOne(id);
      expect(findById).toHaveBeenCalledWith(id);
    });

    it('should handle errors during retrieval by ID', async () => {
      findById.mockRejectedValue(new Error('Database error'));
      await expect(service.findOne(id)).rejects.toThrow('Database error');
    });
  });
});
