export enum WealthboxCustomizableCategoryType {
  OpportunityStages = 'opportunity_stages',
  OpportunityPipelines = 'opportunity_pipelines',
  ContactTypes = 'contact_types',
  ContactSources = 'contact_sources',
  TaskCategories = 'task_categories',
  EventCategories = 'event_categories',
  FileCategories = 'file_categories',
  InvestmentObjectives = 'investment_objectives',
  FinancialAccountTypes = 'financial_account_types',
  EmailTypes = 'email_types',
  PhoneTypes = 'phone_types',
  AddressTypes = 'address_types',
  WebsiteTypes = 'website_types',
  ContactRoles = 'contact_roles',
}

export enum OnBordWealthBoxContactRoles {
  Advisor = 'Advisor',
}
