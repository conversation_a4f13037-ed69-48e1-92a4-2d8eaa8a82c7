region               = "us-east-1"
app_name             = "onbord-backend"
solution_stack_name  = "64bit Amazon Linux 2 v5.8.2 running Node.js 18"
instance_type        = "t3.medium"
min_instances        = 2
max_instances        = 5
source_bundle_bucket = "onbord-backend-production-bundle"
domain_name          = "api.onbord.io"

app_port = "3000"
origin   = "https://ui.onbord.io"
work_env = "production"

instance_size                            = "M10"
cluster_name                             = "onbord-db-production"
mongodb_atlas_org_id_ssm                 = "/onbord/backend/MONGODB_ATLAS_ORG_ID"
mongodb_atlas_api_pub_key_ssm            = "/onbord/backend/MONGODB_ATLAS_API_PUB_KEY"
mongodb_atlas_api_pri_key_ssm            = "/onbord/backend/MONGODB_ATLAS_API_PRI_KEY"
mongodb_atlas_database_user_username_ssm = "/onbord/backend/production/MONGODB_ATLAS_DB_USERNAME"
mongodb_atlas_database_user_password_ssm = "/onbord/backend/production/MONGODB_ATLAS_DB_PASSWORD"


dd_app_key_ssm = "/onbord/backend/DD_APP_KEY"
dd_api_key_ssm = "/onbord/backend/DD_API_KEY"

request_count_threshold      = 5000
healthy_host_count_threshold = 3
latency_threshold            = 200