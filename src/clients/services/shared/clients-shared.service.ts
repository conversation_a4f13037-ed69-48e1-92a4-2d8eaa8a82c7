import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery } from 'mongoose';
import { Client } from '../../schemas/clients.schema';
import { DraftClient } from '../../schemas/draft-client.schema';

/**
 * Shared service containing common business logic for all versions
 * of the clients API. This promotes code reuse and maintains
 * consistency across versions.
 */
@Injectable()
export class ClientsSharedService {
  constructor(
    @InjectModel(Client.name) private clientModel: Model<Client>,
    @InjectModel(DraftClient.name) private draftClientModel: Model<DraftClient>,
  ) {}

  /**
   * Common method to find clients with filtering logic
   */
  async findWithFilter(filter: FilterQuery<Client>) {
    return this.clientModel.find(filter).exec();
  }

  /**
   * Common method to find draft clients with filtering logic
   */
  async findDraftWithFilter(filter: FilterQuery<DraftClient>) {
    return this.draftClientModel.find(filter).exec();
  }

  /**
   * Common validation logic that can be reused across versions
   */
  async validateClientExists(clientId: string): Promise<Client> {
    const client = await this.clientModel.findById(clientId).exec();
    if (!client) {
      throw new Error(`Client with ID ${clientId} not found`);
    }
    return client;
  }

  /**
   * Common method to check if client belongs to organization
   */
  async validateClientOwnership(
    clientId: string,
    organisationId: string,
  ): Promise<boolean> {
    const client = await this.clientModel
      .findOne({
        _id: clientId,
        organisationId,
      })
      .exec();
    
    return !!client;
  }

  /**
   * Common client status retrieval
   */
  async getClientStatusesByIds(
    organisationId: string,
    clientIds: string[],
  ): Promise<any[]> {
    if (clientIds.length === 0) {
      return [];
    }

    return this.clientModel
      .find({
        _id: { $in: clientIds },
        organisationId,
      })
      .select('_id status interviewStatus')
      .exec();
  }
}