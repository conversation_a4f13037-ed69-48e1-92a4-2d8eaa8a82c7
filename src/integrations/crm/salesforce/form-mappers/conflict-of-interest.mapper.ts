import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { SalesforceCRM } from 'src/integrations/crm/salesforce/crm.salesforce';
import { ConflictsOfInterestRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/conflicts-of-interest-request.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { AccountClientDocumentsEnum } from 'src/shared/types/accounts/account-documents.enum';
import { TaskStatus, TaskPriority } from '../types/enums';

export class SalesforceConflictOfInterestMapper implements FormUpdateMapper {
  constructor(private salesforceCRM: SalesforceCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: ConflictsOfInterestRequestDto,
  ): Promise<void> {
    const subject = 'Obtain BD/FINRA approval letter for account opening';
    const description =
      'Client employed by or associated with B/D, FINRA or Stock Exchange. Email for approval letter and submit to Schwab.';
    const { _id: interviewId } = interview;
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;
    const crmClientId = contact.crmClientId;   

    // Remove FINRA Document from required documents in case the client changes their answer
    if (!pageData.companyName) {
      await this.salesforceCRM.interviewsService.removeRequiredDocument(
        interviewId,
        {
          document: AccountClientDocumentsEnum.FinraDocument,
        },
      );

      // Remove industry affiliation from Salesforce contact
      await this.salesforceCRM.contactService.updateContact(crmClientId, {
        Onbord_Industry_Affiliation__c: null,
      });

      return;
    }

    // Add FINRA Document to required documents in the interview
    if (interview.docusignSelected) {
      await this.salesforceCRM.interviewsService.addRequiredDocument(
        interviewId,
        {
          document: AccountClientDocumentsEnum.FinraDocument,
        },
      );
    }

    // Update contact with industry affiliation
    await this.salesforceCRM.contactService.updateContact(crmClientId, {
      Onbord_Industry_Affiliation__c: pageData.companyName,
    });

    // Create a task in Salesforce
    await this.salesforceCRM.taskService.createTask({
      Subject: subject,
      Description: description,
      WhoId: crmClientId, // Related To ID (Contact)
      OwnerId: client.primaryAdvisor.crmId, // Assigned To ID
      Status: TaskStatus.NOT_STARTED,
      Priority: TaskPriority.HIGH,
      ActivityDate: new Date().toISOString().split('T')[0], // Due Date
    });
  }
}
