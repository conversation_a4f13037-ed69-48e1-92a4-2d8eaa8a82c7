import { ApiProperty } from '@nestjs/swagger';

export class NewPasswordRequiredResponseDto {
  @ApiProperty()
  newPasswordRequired: boolean;

  @ApiProperty()
  session: string;
}

export class SetNewPasswordDto {
  @ApiProperty()
  email: string;

  @ApiProperty()
  oldPassword: string;

  @ApiProperty()
  newPassword: string;
}

export class NonMfaLoginResponseDto {
  @ApiProperty()
  advisorId: string;

  @ApiProperty()
  organisationId: any;
}
