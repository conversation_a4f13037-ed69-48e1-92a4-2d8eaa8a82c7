import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { RedtailCRM } from 'src/integrations/crm/redtail/crm.redtail';
import { PhoneMapperRequestDto } from 'src/integrations/crm/redtail/form-mappers/dto/phone-request.dto';
import { RedtailPhoneMapper } from 'src/integrations/crm/redtail/form-mappers/phone.mapper';
import { RedtailPhoneTypeEnum } from 'src/integrations/crm/redtail/types/enums';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import mongoose from 'mongoose';

jest.mock('src/integrations/crm/redtail/crm.redtail');
const organisation = {
  configuration: {},
} as any;

describe('RedtailPhoneMapper', () => {
  let redtailCrmService: jest.Mocked<RedtailCRM>;
  let mapper: RedtailPhoneMapper;

  beforeEach(() => {
    redtailCrmService = new RedtailCRM(
      {
        _id: new mongoose.Types.ObjectId(),
      } as any,
      {} as any,
      {} as any,
      {} as any,
      {} as any,
      {} as any,
      {} as any,
    ) as jest.Mocked<RedtailCRM>;
    // Ensuring `redtailPhoneNumberService.upsertPhone` is defined and a jest.fn
    redtailCrmService.redtailPhoneNumberService = {
      upsertPhone: jest.fn(),
    } as any;

    mapper = new RedtailPhoneMapper(redtailCrmService);
  });

  it('should call createMobileNumber with correct parameters for each phone', async () => {
    const interview = { isPrimary: true } as EnrichedInterview;
    const client = { primaryContact: { crmClientId: '1' } } as EnrichedClient;
    const pageData: PhoneMapperRequestDto = {
      alternatePhones: [
        { number: '************', type: 'MOBILE' },
        { number: '************', type: 'WORK' },
      ],
    };

    const upsertPhoneSpy = jest.spyOn(
      redtailCrmService.redtailPhoneNumberService,
      'upsertPhone',
    );

    await mapper.mapPageData(interview, client, organisation, pageData);

    for (const phone of pageData.alternatePhones) {
      expect(upsertPhoneSpy).toHaveBeenCalledWith(
        client.primaryContact.crmClientId,
        phone.number,
        RedtailPhoneTypeEnum[phone.type],
        false,
        false,
      );
    }
  });
});
