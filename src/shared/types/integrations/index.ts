import { Api<PERSON>ey, Credentials } from 'src/auth/auth.types';
import { CRM } from '../../../integrations/crm/crm.interface';
import { XOR } from '../monads';
import { HttpService } from '@nestjs/axios';
import { InterviewsService } from 'src/interviews/interviews.service';
import { Logger } from 'winston';
import { MailService } from 'src/notifications/mail/mail.service';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import { SuperHttpService } from 'src/super-http/super-http.service';
import { Organisation } from 'src/organisations/schemas/organisation.schema';

export enum CRMEnum {
  Redtail = 'Redtail',
  Wealthbox = 'Wealthbox',
  Salesforce = 'Salesforce',
  Practifi = 'Practifi'
}

export enum DocumentSigningEnum {
  Docusign = 'Docusign',
}

export enum IntegrationEnum {
  Crm = 'CRM',
  DocumentSigning = 'DocumentSigning',
}

export type CRMType = keyof typeof CRMEnum;

export type CRMClasses = {
  [key: string]: new (
    advisor: AdvisorWithRole,
    organisation: Organisation,
    payload: XOR<Credentials, ApiKey>,
    httpService: SuperHttpService,
    interviewsService: InterviewsService,
    mailService: MailService,
    logger: Logger,
  ) => CRM;
};

export type IntegrationName = CRMEnum | DocumentSigningEnum;

export type IntegrationConfig = {
  name: IntegrationName;
  credentials: string;
  [key: string]: any;
};
