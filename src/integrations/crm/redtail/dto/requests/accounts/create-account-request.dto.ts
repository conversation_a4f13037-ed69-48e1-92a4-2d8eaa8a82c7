import { RedtailDBAccountType } from 'src/integrations/crm/redtail/types/enums';
import { AccountFeaturesEnum } from 'src/shared/types/accounts/account-features.enum';

export class RedtailCreateAccountRequestDto {
  number?: string;
  account_type_id?: number;
  status?: number;
  company?: string;
  product?: string;
  taxqualified_type: number;
  taxqualified?: boolean;
  account_type?: string;
  is_linked?: boolean;
  status_id?: number;
  balance?: string;
  feed_aggregator?: string;
  feed_account_number?: string;
  registration?: string;
  taxqualified_type_id?: number;
  discretionary?: boolean;
  held_away?: boolean;
}

export class CreateAccountRequestDto {
  nameOnAccount: string;
  accountType: RedtailDBAccountType;
  accountLabel: string;
  ownership: string;
  advisoryRate?: number;
  features?: AccountFeaturesEnum[];
}
