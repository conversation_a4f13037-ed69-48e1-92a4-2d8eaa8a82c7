import { Modu<PERSON> } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { CognitoService } from '../shared/services/cognito.service';
import { AdvisorsModule } from 'src/advisors/advisors.module';
import { ClsModule } from 'nestjs-cls';
import { ConfigModule } from '@nestjs/config';
import { OrganisationsModule } from 'src/organisations/organisations.module';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    AdvisorsModule,
    OrganisationsModule,
    ClsModule,
    ConfigModule,
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy, CognitoService],
  exports: [AuthService],
})
export class AuthModule {}
