import {
  Injectable,
  Inject,
  forwardRef,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { isEmpty } from 'lodash';
import mongoose, { ClientSession, FilterQuery, Model, PipelineStage } from 'mongoose';
import { WINSTON_MODULE_PROVIDER } from 'nest-winston';
import { ClsService } from 'nestjs-cls';
import { AdvisorWithRole } from 'src/advisors/dto/advisor-with-role.dto';
import {
  EnrichedClientWithInterviewStatus,
  EnrichedContactWithInterviewStatus,
  GetClientsQueryDto,
} from '../../dto/v1/get-clients.dto';
import { Client } from 'src/clients/schemas/clients.schema';
import { contactInterviewStatusAggregateQuery } from 'src/clients/schemas/queries/interview-status-aggregate.query';
import { DocusignService } from 'src/integrations/docusign/docusign.service';
import { InterviewsService } from 'src/interviews/interviews.service';
import { MailService } from 'src/notifications/mail/mail.service';
import { SmsService } from 'src/notifications/sms/sms.service';
import { TransactionManager } from 'src/shared/services/transaction-manager.service';
import { ClientStatusEnum } from 'src/shared/types/clients/client-status.type';
import { ClsDataEnum } from 'src/shared/types/general/cls.enum';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';
import { Logger } from 'winston';
import { DraftClient } from '../../schemas/draft-client.schema';
import { BaseClientContact } from '../../schemas/clients.base.schema';
import { PaginationQueryDto, PaginationResponseDto } from "src/shared/dto/pagination.dto";

@Injectable()
export class ClientContactV1Service {
  constructor(
    @InjectModel(Client.name) private readonly clientModel: Model<Client>,
    @InjectModel(DraftClient.name)
    private readonly draftClientModel: Model<DraftClient>,
    @Inject(forwardRef(() => InterviewsService))
    private interviewsService: InterviewsService,
    private clsService: ClsService,
    private transactionManager: TransactionManager,
    @Inject(WINSTON_MODULE_PROVIDER) private readonly logger: Logger,
  ) {}

  /**
   * Retrieves all clients with interview status based on the provided filters.
   * @param filters - Optional filters to apply when querying the clients.
   * @param enrich - Flag indicating whether to enrich the result with additional data.
   * @param pagination - Pagination parameters (page, limit).
   * @returns A promise that resolves to an array of enriched clients with interview status.
   */
  async findAllWithInterviewStatus(
    filters?: FilterQuery<Client>,
    enrich = true,
    pagination?: PaginationQueryDto,
  ): Promise<EnrichedClientWithInterviewStatus[]> {
    const aggregationPipeline: PipelineStage[] = [
      {
        $match: filters ?? {}
      },
      ...contactInterviewStatusAggregateQuery,
      {
        $sort: { createdAt: -1 }
      }
    ];

    if (pagination?.page && pagination?.limit) {
      const skip = (pagination.page - 1) * pagination.limit;
      aggregationPipeline.push(
        { $skip: skip } as PipelineStage,
        { $limit: pagination.limit } as PipelineStage
      );
    }

    return this.clientModel.aggregate(aggregationPipeline);
  }


  /**
   * Retrieves draft clients based on filters with optional pagination support.
   * @param filters - Query filters to apply when searching for draft clients.
   * @param pagination - Pagination parameters (page, limit).
   * @param session - Optional MongoDB session for transaction support.
   * @returns A promise that resolves to an array of draft clients in plain JavaScript objects.
   */
  async findAllDraftClients(
    filters: FilterQuery<DraftClient>,
    pagination?: PaginationQueryDto,
    session?: ClientSession,
  ) {
    let query = this.draftClientModel
      .find(filters)
      .sort({ createdAt: -1 });

    if (pagination?.page && pagination?.limit) {
      const skip = (pagination.page - 1) * pagination.limit;
      query = query
        .skip(skip)
        .limit(pagination.limit);
    }

    if (session) {
      query = query.session(session);
    }

    return query.lean();
  }

  /**
   * Retrieves all contacts for the logged-in advisor based on the provided query and organization ID.
   *
   * @param query - The query parameters for filtering the contacts.
   * @param organisationId - The ID of the organization.
   * @returns A promise that resolves to an array of enriched clients with interview status.
   * @throws HttpException if the advisor information is not available or if the advisor role is invalid.
   */
  async findAllContactsByLoggedInAdvisor(
    query: GetClientsQueryDto,
    organisationId: string,
  ): Promise<PaginationResponseDto<Array<EnrichedClientWithInterviewStatus | DraftClient>>> {
    // Ensure advisor is not undefined
    const advisor: AdvisorWithRole = this.clsService.get(ClsDataEnum.Advisor); // safe cls
    if (!advisor || !advisor.role || !advisor._id) {
      throw new HttpException(
        'Advisor information is not available',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const filter: FilterQuery<Client> = {
      organisationId: new mongoose.Types.ObjectId(organisationId),
    };

    // Handle archived and active filters
    if (query?.include === 'archived') {
      filter.$or = [
        { archivedAt: { $ne: null } },
        { status: ClientStatusEnum.Archived },
      ];
    } else if (query?.include === 'active') {
      filter.$and = [
        { archivedAt: null },
        {
          $or: [
            { status: { $ne: ClientStatusEnum.Archived } },
            { status: ClientStatusEnum.Draft },
          ],
        },
      ];
    }

    // Handle filtering based on advisor's role
    if (advisor.role.name === RolesEnum.Representative) {
      filter.$or = [
        { 'primaryAdvisor.id': advisor._id },
        { 'primaryCSA.id': advisor._id },
        { secondaryAdvisor: { id: advisor._id.toString() } },
        { secondaryCSA: { id: advisor._id.toString() } },
        { createdBy: advisor._id },
        ...(filter.$or || []), // Safely include archival conditions if they exist
      ];
    } else if (![RolesEnum.SuperAdmin, RolesEnum.CompanyAdmin].includes(advisor.role.name)) {
      throw new HttpException(
        'Unauthorized: Invalid Advisor Role',
        HttpStatus.UNAUTHORIZED,
      );
    }

    const page = query.page || 1;
    const limit = query.limit || 10;
    const skip = (page - 1) * limit;

    const [clientsCount, draftClientsCount] = await Promise.all([
      this.clientModel.countDocuments(filter),
      this.draftClientModel.countDocuments(filter)
    ]);

    const totalCount = clientsCount + draftClientsCount;

    const [clients, draftClients] = await Promise.all([
      this.findAllWithInterviewStatus(filter, false, { page, limit }),
      this.findAllDraftClients(filter, { page, limit })
    ]);

    const combinedResults = [
      ...this.clientsToContactsList(draftClients),
      ...this.clientsToContactsList(clients)
    ];

    return {
      result: combinedResults,
      totalResults: totalCount
    };
  }

  /**
   * Converts a list of enriched clients with interview status to a list of contacts.
   * @param clients - The list of enriched clients with interview status.
   * @returns The list of contacts.
   */
  private clientsToContactsList(
    clients: Array<EnrichedClientWithInterviewStatus | DraftClient>,
  ): EnrichedClientWithInterviewStatus[] {
    const contacts = clients.reduce((acc, client) => {
      if (!isEmpty(client.primaryContact)) {
        acc.push({
          ...client,
          firstName: client.primaryContact.firstName,
          lastName: client.primaryContact.lastName,
          completionPercentage: this.isEnrichedContactWithInterviewStatus(
            client.primaryContact,
          )
            ? client.primaryContact.completionPercentage
            : 0,
          contactType: 'Primary', // TODO: Replace with enum from staging
          interviewComplete: this.isEnrichedContactWithInterviewStatus(
            client.primaryContact,
          )
            ? client.primaryContact.interviewComplete
            : false,
          belongsToHousehold: !isEmpty(client.secondaryContact),
        });
      }
      if (!isEmpty(client.secondaryContact)) {
        acc.push({
          ...client,
          firstName: client.secondaryContact.firstName,
          lastName: client.secondaryContact.lastName,
          completionPercentage: this.isEnrichedContactWithInterviewStatus(
            client.secondaryContact,
          )
            ? client.secondaryContact.completionPercentage
            : 0,
          contactType: 'Secondary', // TODO: Replace with enum from staging
          interviewComplete: this.isEnrichedContactWithInterviewStatus(
            client.secondaryContact,
          )
            ? client.secondaryContact.interviewComplete
            : false,
          belongsToHousehold: true,
        });
      }
      return acc;
    }, []);
    return contacts;
  }

  isEnrichedContactWithInterviewStatus = (
    contact: BaseClientContact | EnrichedContactWithInterviewStatus,
  ): contact is EnrichedContactWithInterviewStatus => {
    return 'completionPercentage' in contact;
  };

  /**
   * Deletes a contact from a client.
   * @param organisationId - The ID of the organization.
   * @param clientId - The ID of the client.
   * @param contactType - The type of contact to delete.
   * @param session - Optional session for MongoDB transaction.
   * @returns A Promise that resolves to the updated client object.
   */
  async deleteContactFromClient(
    organisationId: string,
    clientId: string,
    contactType: string,
    session?: ClientSession,
  ): Promise<Client> {
    const client = await this.clientModel.findById(clientId).session(session);

    if (!contactType) {
      throw new HttpException(
        'Contact type is required',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (contactType === 'Primary') {
      client.primaryContact = null;
    } else {
      client.secondaryContact = null;
    }

    const interview = await this.interviewsService.findOne(
      {
        client: clientId,
        isPrimary: contactType === 'Primary',
      },
      session,
    );

    await this.interviewsService.remove(interview._id.toString(), session);

    await client.save();

    return client;
  }
}
