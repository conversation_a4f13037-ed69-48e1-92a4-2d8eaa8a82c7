import { Injectable, Logger } from '@nestjs/common';
import { EmailTypesResponseDto } from '../dto/email-types.dto';
import { emailTemplateTypes } from '../email-template-types.config';
import { emailTemplateMapper } from '../index';
import { EmailTemplateNameEnum } from '../types';

@Injectable()
export class EmailTypesService {
  private readonly logger = new Logger(EmailTypesService.name);

  findAll(): EmailTypesResponseDto {
    const emailTypes = emailTemplateTypes.map(emailType => {
      try {
        const defaultTemplate = emailTemplateMapper(emailType.emailType);
        return {
          ...emailType,
          defaultTemplate: defaultTemplate.html
        };
      } catch (error) {
        this.logger.error(`Error retrieving default template for ${emailType.emailType}: ${error.message}`);
        return {
          ...emailType,
          defaultTemplate: '<p>Default template not available</p>'
        };
      }
    });

    return {
      emailTypes,
    };
  }
} 