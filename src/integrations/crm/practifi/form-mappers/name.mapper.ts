import { EnrichedClient } from 'src/clients/dto/v1/get-clients.dto';
import { FormUpdateMapper } from 'src/integrations/crm/types/form-update-mapper';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { PractifiCRM } from '../crm.practifi';

interface NamePageData {
  firstName: string;
  lastName: string;
  middleName?: string;
  suffix?: string;
}

export class PractifiNameMapper implements FormUpdateMapper {
  constructor(private practifiCRM: PractifiCRM) {}

  async mapPageData(
    interview: EnrichedInterview,
    client: EnrichedClient,
    organisation: Organisation,
    pageData: NamePageData,
  ): Promise<void> {
    const contact = interview.isPrimary
      ? client.primaryContact
      : client.secondaryContact;

    await this.practifiCRM.contactService.updateContact(contact.crmClientId, {
      FirstName: pageData.firstName,
      LastName: pageData.lastName,
      practifi__Middle_Name__c: pageData.middleName,
      practifi__Suffix__c: pageData.suffix,
    });
  }
}