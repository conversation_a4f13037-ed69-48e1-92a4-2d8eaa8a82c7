import { Injectable } from '@nestjs/common';
import { Connection, Record as SalesforceRecord } from 'jsforce';
import { SalesforceFinancialAccountDto } from '../dto/salesforce.dto';

@Injectable()
export class SalesforceFinancialAccountService {
  private connection: Connection;

  setConnection(connection: Connection) {
    this.connection = connection;
  }

  async createFinancialAccount(accountData: SalesforceFinancialAccountDto): Promise<SalesforceRecord> {
    return this.connection.sobject('FinancialAccount').create(accountData);
  }

  async updateFinancialAccount(id: string, accountData: Partial<SalesforceFinancialAccountDto>): Promise<void> {
    await this.connection.sobject('FinancialAccount').update({
      Id: id,
      ...accountData,
    });
  }

  async getFinancialAccount(id: string): Promise<SalesforceRecord> {
    return this.connection.sobject('FinancialAccount').retrieve(id);
  }

  async getFinancialAccountsByOwner(ownerId: string): Promise<SalesforceRecord[]> {
    return this.connection
      .sobject('FinancialAccount')
      .find({ OwnerId: ownerId })
      .execute();
  }

  async getFinancialAccountsByPrimaryOwner(primaryOwnerId: string): Promise<SalesforceRecord[]> {
    return this.connection
      .sobject('FinancialAccount')
      .find({ PrimaryOwnerId: primaryOwnerId })
      .execute();
  }

  async linkFinancialAccountToContact(accountId: string, contactId: string, isPrimaryOwner: boolean = false): Promise<void> {
    await this.connection.sobject('FinancialAccountRole').create({
      FinancialAccountId: accountId,
      ContactId: contactId,
      Role: isPrimaryOwner ? 'Primary Owner' : 'Joint Owner',
      StartDate: new Date().toISOString().split('T')[0],
    });
  }
} 