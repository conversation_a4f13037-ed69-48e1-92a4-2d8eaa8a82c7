import { Injectable, Logger } from '@nestjs/common';
import { 
  ConditionGroup, 
  FieldCondition,
  NavigationFlowRule 
} from 'src/interviews/schemas/v2/navigation-rules.schema';

export interface FlowEvaluationResult {
  targetPageId: string;
  ruleName: string;
  ruleId: string;
}

@Injectable()
export class FlowEvaluationService {
  private readonly logger = new Logger(FlowEvaluationService.name);

  /**
   * Evaluate flow rules and return the target page
   */
  async evaluateFlowRules(
    rules: NavigationFlowRule[],
    answers: Record<string, any>,
  ): Promise<FlowEvaluationResult | null> {
    // Sort by priority
    const sortedRules = [...rules]
      .filter(r => r.isActive)
      .sort((a, b) => a.priority - b.priority);

    for (const rule of sortedRules) {
      try {
        const matches = await this.evaluateConditionGroup(rule.when, answers);
        
        if (matches) {
          this.logger.debug(`Rule matched: ${rule.ruleName}`);
          return {
            targetPageId: rule.goToPageId,
            ruleName: rule.ruleName,
            ruleId: rule.ruleId,
          };
        }
      } catch (error) {
        this.logger.error(`Error evaluating rule ${rule.ruleName}`, {
          error: error.message,
          rule,
        });
      }
    }

    return null;
  }

  /**
   * Evaluate a condition group
   */
  async evaluateConditionGroup(
    group: ConditionGroup,
    answers: Record<string, any>,
  ): Promise<boolean> {
    const results = await Promise.all(
      group.conditions.map(condition => 
        this.evaluateCondition(condition, answers)
      )
    );

    return group.logic === 'AND'
      ? results.every(r => r === true)
      : results.some(r => r === true);
  }

  /**
   * Evaluate a single condition
   */
  private async evaluateCondition(
    condition: FieldCondition,
    answers: Record<string, any>,
  ): Promise<boolean> {
    const fieldValue = this.getNestedValue(answers, condition.field);

    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value;
        
      case 'not_equals':
        return fieldValue !== condition.value;
        
      case 'contains':
        if (Array.isArray(fieldValue)) {
          return fieldValue.includes(condition.value);
        }
        return String(fieldValue).includes(String(condition.value));
        
      case 'not_contains':
        if (Array.isArray(fieldValue)) {
          return !fieldValue.includes(condition.value);
        }
        return !String(fieldValue).includes(String(condition.value));
        
      case 'in':
        return Array.isArray(condition.value) && 
               condition.value.includes(fieldValue);
        
      case 'not_in':
        return Array.isArray(condition.value) && 
               !condition.value.includes(fieldValue);
        
      case 'greater_than':
        return Number(fieldValue) > Number(condition.value);
        
      case 'less_than':
        return Number(fieldValue) < Number(condition.value);
        
      case 'exists':
        return fieldValue !== undefined && fieldValue !== null;
        
      case 'not_exists':
        return fieldValue === undefined || fieldValue === null;
        
      default:
        this.logger.warn(`Unknown operator: ${condition.operator}`);
        return false;
    }
  }

  /**
   * Get nested value from object using dot notation
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, prop) => current?.[prop], obj);
  }
}