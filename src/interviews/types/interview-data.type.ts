import { ContactDto } from 'src/clients/dto/v1/create-client.dto';
import { PrimaryAdvisor } from 'src/clients/schemas/clients.base.schema';
import { GenericCrmAccount } from 'src/integrations/crm/types/accounts/crm-account.type';
import { CrmContact } from 'src/integrations/crm/types/contacts/crm-contact.type';
import { EnrichedInterview } from 'src/interviews/dto/v1/enriched-interview-dto';
import { Organisation } from 'src/organisations/schemas/organisation.schema';
import { AccountDto } from 'src/shared/types/accounts/account.dto';

export type MergedContact = (Omit<ContactDto, 'accounts'> &
  Omit<CrmContact, 'accounts'>) & {
  accounts: (GenericCrmAccount & AccountDto)[];
};

export type InterviewData = {
  primaryContact: ContactDto;
  secondaryContact?: ContactDto;
  primaryAdvisor: PrimaryAdvisor;
  interview: EnrichedInterview;
};

export type InterviewDataWithCrmInfo = {
  organisation: Organisation;
  primaryContact: MergedContact;
  secondaryContact?: MergedContact;
  primaryAdvisor: PrimaryAdvisor;
  interview: EnrichedInterview;
};

export type Applicant = {
  name: string;
  email: string;
  phone: string;
};

export type InterviewCompletionParams = {
  interview: EnrichedInterview;
  clientId: string;
  organisationId: string;
  advisorId: string;
  primaryContact: ContactDto;
  secondaryContact?: ContactDto;
  primaryAdvisor: PrimaryAdvisor;
  envelopeId?: string;
  docusignSelected: boolean;
  isPrimary: boolean;
};

