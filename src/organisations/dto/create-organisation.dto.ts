import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsOptional,
  IsArray,
  IsString,
  IsEmail,
  Length,
  IsNumberString,
  IsBoolean,
  IsEnum,
  ValidateNested,
} from 'class-validator';
import { AdvisorPersonalInfoDto } from 'src/advisors/dto/advisor-personal-info.dto';
import { Address } from 'src/shared/schemas/address.schema';
import { Asset } from 'src/shared/schemas/asset.schema';
import { Integration } from 'src/shared/schemas/integration.schema';
import { CRMEnum } from 'src/shared/types/integrations';
import { RolesEnum } from 'src/shared/types/rbac/roles.enum';

export class CreateOrganisationDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty()
  name: string;

  @IsString()
  @IsNotEmpty()
  @IsOptional()
  @ApiProperty()
  riaName: string;

  @IsOptional()
  @ApiProperty()
  address: Address;

  @IsString()
  @IsOptional()
  @ApiProperty()
  @Length(10)
  @IsNumberString()
  phone: string;

  @IsEmail()
  @IsString()
  @IsOptional()
  @ApiProperty()
  email: string;

  @IsArray()
  @IsOptional()
  @ApiProperty()
  externalAccounts: Integration[];

  @IsArray()
  @IsOptional()
  @ApiProperty()
  assets: Asset[];

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  docusign?: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  transitions?: boolean;

  @IsNotEmpty()
  @ValidateNested()
  @Type(() => AdvisorPersonalInfoDto)
  @ApiProperty()
  personalInfo: AdvisorPersonalInfoDto;

  @IsEnum(CRMEnum)
  @ApiProperty()
  @IsOptional()
  selectedCRM?: CRMEnum;
}
