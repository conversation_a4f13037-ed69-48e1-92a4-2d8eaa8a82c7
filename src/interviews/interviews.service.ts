import { Injectable } from '@nestjs/common';
import { InterviewsV1Service } from './services/v1/interviews-v1.service';
import { CreateInterviewDto } from './dto/v1/create-interview.dto';
import { RequiredDocumentDto } from './dto/v1/required-document.dto';
import { UpdateInterviewDto } from './dto/v1/update-interview.dto';
import { UploadDocumentDto } from './dto/v1/upload-document.dto';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { FilterQuery, ClientSession } from 'mongoose';
import { Interview } from './schemas/v1/interview.schema';

/**
 * V1 Compatibility Layer Service
 * 
 * This service maintains backward compatibility for V1 API consumers
 * by delegating all operations to the InterviewsV1Service.
 * 
 * New development should use InterviewV2Facade or the modular services directly.
 * 
 * @deprecated Use InterviewV2Facade for new work. This service exists solely
 * to support existing V1 controller endpoints.
 */
@Injectable()
export class InterviewsService {
  constructor(private readonly v1Service: InterviewsV1Service) {}

  create(dto: CreateInterviewDto, session?: ClientSession) { 
    return this.v1Service.create(dto, session); 
  }
  
  find(filter: FilterQuery<Interview>) { 
    return this.v1Service.find(filter); 
  }
  
  findAll() { 
    return this.v1Service.findAll(); 
  }
  
  findOne(filter: FilterQuery<Interview>, session?: ClientSession, skipEnrich = false) { 
    return this.v1Service.findOne(filter, session, skipEnrich); 
  }
  
  findById(id: string, session?: ClientSession, skipEnrich = false) { 
    return this.v1Service.findById(id, session, skipEnrich); 
  }
  
  remove(id: string, session?: ClientSession) { 
    return this.v1Service.remove(id, session); 
  }
  
  enqueueFlow(interviewId: string, dto: UpdateInterviewDto) { 
    return this.v1Service.enqueueFlow(interviewId, dto); 
  }
  
  updateInterviewPages(id: string, dto: UpdateInterviewDto, pageStatus: any, session?: ClientSession) {
    return this.v1Service.updateInterviewPages(id, dto, pageStatus, session);
  }
  
  addPageToInterview(id: string, pageName: PagesEnum) {
    return this.v1Service.addPageToInterview(id, pageName);
  }
  
  removePageFromInterview(id: string, pageName: string) {
    return this.v1Service.removePageFromInterview(id, pageName);
  }
  
  upload(id: string, files: Express.Multer.File[], dto?: UploadDocumentDto, session?: ClientSession) { 
    return this.v1Service.upload(id, files, dto, session); 
  }
  
  complete(id: string, session?: ClientSession) { 
    return this.v1Service.complete(id, session); 
  }
  
  finish(id: string, session?: ClientSession) { 
    return this.v1Service.finish(id, session); 
  }
  
  validateAllPagesAreSynced(interviewId: string) {
    return this.v1Service.validateAllPagesAreSynced(interviewId);
  }
  
  markInterviewAsComplete(interviewId: string, session: ClientSession) {
    return this.v1Service.markInterviewAsComplete(interviewId, session);
  }
  
  enqueueInterviewCompletionFlow(params: any) {
    return this.v1Service.enqueueInterviewCompletionFlow(params);
  }
  
  sendDesktopInterviewEmail(id: string) { 
    return this.v1Service.sendDesktopInterviewEmail(id); 
  }
  
  sendNonCitizenEmail(id: string) { 
    return this.v1Service.sendNonCitizenEmail(id); 
  }
  
  sendNotification(interviewId: string, session: ClientSession) {
    return this.v1Service.sendNotification(interviewId, session);
  }
  
  createDocusignEnvelope(interviewId: string, session?: ClientSession) {
    return this.v1Service.createDocusignEnvelope(interviewId, session);
  }
  
  prepareDocusignEnvelope(interviewId: string, session: ClientSession) {
    return this.v1Service.prepareDocusignEnvelope(interviewId, session);
  }
  
  addRequiredDocument(id: string, dto: RequiredDocumentDto) {
    return this.v1Service.addRequiredDocument(id, dto);
  }
  
  removeRequiredDocument(id: string, dto: RequiredDocumentDto) {
    return this.v1Service.removeRequiredDocument(id, dto);
  }
  
  updatePages(pages: any, dto: any, status?: any) {
    return this.v1Service.updatePages(pages, dto, status);
  }
  
  calculateCompletionPercentage(interview: any, client: any) {
    return this.v1Service.calculateCompletionPercentage(interview, client);
  }
}