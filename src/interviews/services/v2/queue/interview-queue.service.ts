// src/interviews/services/v2/interview-v2-queue.service.ts
import { 
  Injectable, 
  Inject, 
  forwardRef, 
  Logger 
} from '@nestjs/common';
import { FlowProducer, Queue } from 'bullmq';
import { InjectQueue } from '@nestjs/bullmq';
import { INTERVIEW_V2_QUEUE } from 'src/interviews/constants/interview-v2-queue.constant';
import { CLIENT_QUEUE } from 'src/shared/constants/client.constant';
import { CRM_JOBS, CRM_QUEUE } from 'src/integrations/crm/constants/crm.constants';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { CRMEnum } from 'src/shared/types/integrations';
import { ClientQueueJobType } from 'src/clients/types/client-queue-job.enum';

// Job types
export enum InterviewV2QueueJobType {
  SYNC_PAGE_TO_CRM = 'SYNC_PAGE_TO_CRM_V2',
  COMPLETE_INTERVIEW = 'COMPLETE_INTERVIEW_V2',
  SEND_COMPLETION_NOTIFICATION = 'SEND_COMPLETION_NOTIFICATION_V2',
}

// Job data interfaces
export interface SyncPageToCrmJobData {
  interviewId: string;
  pageInstanceId: string;
  pageId: string;
  pageName: string;
  answers: Record<string, any>;
  clientId: string;
  contactType: 'primary' | 'secondary';
  accountId?: string;
  metadata?: any;
}

export interface PageSubmissionData {
  interviewId: string;
  pageInstanceId: string;
  pageId: string;
  pageName: string;
  answers: Record<string, any>;
  clientId: string;
  contactType: 'primary' | 'secondary';
  accountId?: string;
  metadata?: any;
}

@Injectable()
export class InterviewV2QueueService {
  private readonly logger = new Logger(InterviewV2QueueService.name);
  private readonly flowProducer: FlowProducer;
  
  constructor(
    @InjectQueue(INTERVIEW_V2_QUEUE.NAME) 
    private readonly interviewQueue: Queue,
    
    @InjectQueue(CLIENT_QUEUE.NAME) 
    private readonly clientQueue: Queue,
    
    @Inject(forwardRef(() => OrganisationsService)) 
    private readonly organisationsService: OrganisationsService,
  ) {
    this.flowProducer = new FlowProducer({ 
      connection: this.interviewQueue.opts.connection 
    });
  }

  /**
   * Queue page submission for CRM sync only
   * Navigation has already been computed synchronously
   */
  async queuePageSubmission(data: PageSubmissionData): Promise<void> {
    const organisation = await this.organisationsService.findOne(
      await this.getOrganisationIdFromClient(data.clientId)
    );

    if (!organisation?.selectedCRM) {
      this.logger.warn('No CRM configured for organisation');
      return;
    }

    const jobId = `sync-${data.interviewId}-${data.pageId}-${Date.now()}`;

    try {
      await this.interviewQueue.add(
        InterviewV2QueueJobType.SYNC_PAGE_TO_CRM,
        {
          ...data,
          organisationId: organisation._id.toString(),
          crmType: organisation.selectedCRM,
        } as SyncPageToCrmJobData,
        {
          jobId,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
          removeOnComplete: true,
          removeOnFail: false,
        }
      );

      this.logger.debug(`Queued CRM sync: ${jobId}`);
    } catch (error) {
      this.logger.error('Failed to queue CRM sync', {
        error: error.message,
        data,
      });
      // Don't throw - CRM sync failure shouldn't block user navigation
    }
  }

  /**
   * Queue interview completion tasks
   */
  async queueInterviewCompletion(interviewId: string): Promise<void> {
    const jobId = `complete-${interviewId}-${Date.now()}`;

    try {
      await this.flowProducer.add({
        name: InterviewV2QueueJobType.COMPLETE_INTERVIEW,
        queueName: INTERVIEW_V2_QUEUE.NAME,
        data: { interviewId },
        children: [
          {
            name: InterviewV2QueueJobType.SEND_COMPLETION_NOTIFICATION,
            queueName: INTERVIEW_V2_QUEUE.NAME,
            data: { interviewId },
            opts: {
              delay: 1000,
            },
          },
        ],
        opts: {
          jobId,
          attempts: 3,
        },
      });

      this.logger.log(`Queued interview completion: ${jobId}`);
    } catch (error) {
      this.logger.error('Failed to queue interview completion', {
        error: error.message,
        interviewId,
      });
    }
  }

  /**
   * Get CRM queue name for routing
   */
  getCrmQueueName(selectedCrm: CRMEnum): string {
    const queueMap = {
      [CRMEnum.Redtail]: CRM_QUEUE.REDTAIL.NAME,
      [CRMEnum.Wealthbox]: CRM_QUEUE.WEALTHBOX.NAME,
      [CRMEnum.Salesforce]: CRM_QUEUE.SALESFORCE.NAME,
      [CRMEnum.Practifi]: CRM_QUEUE.PRACTIFI.NAME,
    };

    const queueName = queueMap[selectedCrm];
    if (!queueName) {
      throw new Error(`Unsupported CRM type: ${selectedCrm}`);
    }

    return queueName;
  }

  private async getOrganisationIdFromClient(clientId: string): Promise<string> {
    // This would typically fetch from client service
    // Simplified for this example
    return 'organisation-id';
  }
}