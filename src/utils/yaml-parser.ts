import * as yaml from 'js-yaml';

/**
 * Converts an object to a YAML string.
 * @param data - The object to be converted.
 * @returns The YAML string representation of the object.
 */
export function objectToYAML(data: object): string {
  if (!data) {
    throw new Error('Data is required.');
  }
  return yaml.dump(data, { indent: 2 });
}

/**
 * Converts a YAML string to an object.
 *
 * @param yamlStr - The YAML string to be converted.
 * @returns The object representation of the YAML string.
 */
export function yamlToObject(yamlStr: string): object {
  try {
    const result = yaml.load(yamlStr);
    if (typeof result === 'object' && result !== null) {
      return result;
    }
    throw new Error('YAML did not resolve to an object.');
  } catch (error) {
    throw new Error(`Error parsing YAML: ${error}`);
  }
}
