import { Test, TestingModule } from '@nestjs/testing';
import { CreateInterviewDto } from 'src/interviews/dto/v1/create-interview.dto';
import { InterviewsController } from 'src/interviews/interviews.controller';
import { InterviewsService } from 'src/interviews/interviews.service';
import { OrganisationsService } from 'src/organisations/organisations.service';
import { PagesEnum } from 'src/shared/types/pages/pages.enum';
import { INTERVIEW_QUEUE } from '../constants/interview-queue.constant';
import { createMockQueueProvider } from '../../shared/testing/mock-queue.provider';

describe('InterviewsController', () => {
  let controller: InterviewsController;
  let mockService;

  beforeEach(async () => {
    mockService = {
      create: jest.fn(),
      findAll: jest.fn(),
      findOne: jest.fn(),
      findById: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
      enqueueFlow: jest.fn().mockResolvedValue('updatedInterview'),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [InterviewsController],
      providers: [
        {
          provide: InterviewsService,
          useValue: mockService,
        },
        createMockQueueProvider(INTERVIEW_QUEUE.NAME),
        {
          provide: 'CACHE_MANAGER',
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            del: jest.fn(),
            reset: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<InterviewsController>(InterviewsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should create an interview', async () => {
    const dto: CreateInterviewDto = {
      docusignSelected: true,
      clientId: '1',
      email: '<EMAIL>',
      customTemplates: [],
      name: 'test',
      accounts: [],
    };
    mockService.create.mockResolvedValue('createdInterview');

    const result = await controller.create(dto);

    expect(result).toEqual('createdInterview');
    expect(mockService.create).toHaveBeenCalledWith(dto);
  });

  it('should find one interview', async () => {
    const id = '1';
    mockService.findById.mockResolvedValue('interview1');

    const result = await controller.findOne(id);

    expect(result).toEqual('interview1');
    expect(mockService.findById).toHaveBeenCalledWith(id, undefined, true);
  });

  it('should update an interview', async () => {
    const id = '1';
    const dto = { page: { name: PagesEnum.NAME, data: {} } };
    mockService.enqueueFlow.mockResolvedValue('updatedInterview');

    const result = await controller.update(id, dto);

    expect(result).toEqual('updatedInterview');
    expect(mockService.enqueueFlow).toHaveBeenCalledWith(id, dto);
  });

  it('should remove an interview', async () => {
    const id = '1';
    mockService.remove.mockResolvedValue('removedInterview');

    const result = await controller.remove(id);

    expect(result).toEqual('removedInterview');
    expect(mockService.remove).toHaveBeenCalled();
  });
});
