    
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { InterviewAuditV2 } from 'src/audits/schemas/interview-audit.schema';

export interface PageNavigationAudit {
  interviewId: string;
  pageId: string;
  pageName: string;
  navigatedTo?: string | null;
  visitOrder: number;
  metadata?: any;
}

export interface PageCompletedAudit {
  interviewId: string;
  pageId: string;
  pageName: string;
  metadata?: any;
}

export interface InterviewCompletedAudit {
  interviewId: string;
  clientId: string;
  organisationId: string;
}

@Injectable()
export class InterviewV2AuditService {
  constructor(
    @InjectModel(InterviewAuditV2.name)
    private readonly auditModel: Model<InterviewAuditV2>,
  ) {}

  async logPageNavigation(data: PageNavigationAudit): Promise<void> {
    await this.auditModel.create({
      interviewId: data.interviewId,
      eventType: 'page_navigation',
      pageId: data.pageId,
      pageName: data.pageName,
      eventData: {
        navigatedTo: data.navigatedTo,
        visitOrder: data.visitOrder,
        isBackNavigation: data.visitOrder < 0,
      },
      metadata: data.metadata,
    });
  }

  async logPageCompleted(data: PageCompletedAudit): Promise<void> {
    await this.auditModel.create({
      interviewId: data.interviewId,
      eventType: 'page_completed',
      pageId: data.pageId,
      pageName: data.pageName,
      metadata: data.metadata,
    });
  }

  async logInterviewCompleted(data: InterviewCompletedAudit): Promise<void> {
    await this.auditModel.create({
      interviewId: data.interviewId,
      clientId: data.clientId,
      eventType: 'interview_completed',
      eventData: {
        organisationId: data.organisationId,
      },
    });
  }

  async logCrmSyncSuccess(
    interviewId: string,
    pageId: string,
    syncDuration: number,
  ): Promise<void> {
    await this.auditModel.create({
      interviewId,
      eventType: 'crm_sync_success',
      pageId,
      eventData: {
        duration: syncDuration,
      },
    });
  }

  async logCrmSyncFailure(
    interviewId: string,
    pageId: string,
    error: string,
  ): Promise<void> {
    await this.auditModel.create({
      interviewId,
      eventType: 'crm_sync_failure',
      pageId,
      eventData: {
        errorMessage: error,
      },
    });
  }

  async getInterviewAuditTrail(interviewId: string): Promise<InterviewAuditV2[]> {
    return this.auditModel
      .find({ interviewId })
      .sort({ createdAt: 1 })
      .exec();
  }
}