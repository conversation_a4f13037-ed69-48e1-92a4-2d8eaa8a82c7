import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Observable } from 'rxjs';
import { RolesEnum } from '../types/rbac/roles.enum';

/**
 * Self Guard
 *
 * This guard is used to check if the user is trying to access their own resource,
 * unless they are a super or company admin.
 */
@Injectable()
export class SelfGuard implements CanActivate {
  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    switch (user.role.name) {
      case RolesEnum.SuperAdmin:
      case RolesEnum.CompanyAdmin:
        return true;
      default:
        break;
    }

    const { advisorId } = request.params || {};

    return advisorId ? user._id.toString() === advisorId : false;
  }
}
