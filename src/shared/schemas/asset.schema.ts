import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { AssetType } from 'aws-sdk/clients/inspector';
import { AssetTypeEnum } from 'src/shared/types/general/asset-types.enum';

@Schema({ _id: false, timestamps: true })
export class Asset {
  @Prop()
  assetId: string;

  @Prop({ type: String, enum: AssetTypeEnum })
  assetType: AssetTypeEnum;

  @Prop()
  assetKey: string;

  @Prop()
  assetLocation: string;

  @Prop({ default: false })
  isPublic: boolean;
}

export const AssetSchema = SchemaFactory.createForClass(Asset);
